import{F as v}from"./bootstrap-CYivmKoJ.js";import{b as F}from"./use-form-common-props-DZjBwEkr.js";import{i as g}from"./aria-DGfENwCE.js";import{x,Q as w,r as C,w as A,a as h,p as E}from"../jse/index-index-SSqEGcIT.js";function y(l,{beforeFocus:n,afterFocus:a,beforeBlur:c,afterBlur:i}={}){const r=x(),{emit:p}=r,o=w(),u=F(),e=C(!1),d=t=>{h(n)&&n(t)||e.value||(e.value=!0,p("focus",t),a==null||a())},f=t=>{var s;h(c)&&c(t)||t.relatedTarget&&((s=o.value)!=null&&s.contains(t.relatedTarget))||(e.value=!1,p("blur",t),i==null||i())},b=t=>{var s,m;(s=o.value)!=null&&s.contains(document.activeElement)&&o.value!==document.activeElement||g(t.target)||u.value||(m=l.value)==null||m.focus()};return A([o,u],([t,s])=>{t&&(s?t.removeAttribute("tabindex"):t.setAttribute("tabindex","-1"))}),v(o,"focus",d,!0),v(o,"blur",f,!0),v(o,"click",b,!0),{isFocused:e,wrapperRef:o,handleFocus:d,handleBlur:f}}const _=l=>/([\uAC00-\uD7AF\u3130-\u318F])+/gi.test(l);function B({afterComposition:l,emit:n}){const a=C(!1),c=o=>{n==null||n("compositionstart",o),a.value=!0},i=o=>{var u;n==null||n("compositionupdate",o);const e=(u=o.target)==null?void 0:u.value,d=e[e.length-1]||"";a.value=!_(d)},r=o=>{n==null||n("compositionend",o),a.value&&(a.value=!1,E(()=>l(o)))};return{isComposing:a,handleComposition:o=>{o.type==="compositionend"?r(o):i(o)},handleCompositionStart:c,handleCompositionUpdate:i,handleCompositionEnd:r}}export{B as a,y as u};
