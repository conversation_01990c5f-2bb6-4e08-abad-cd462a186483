var De=Object.defineProperty,Ce=Object.defineProperties;var Ee=Object.getOwnPropertyDescriptors;var Z=Object.getOwnPropertySymbols;var Re=Object.prototype.hasOwnProperty,Ae=Object.prototype.propertyIsEnumerable;var Q=(a,s,t)=>s in a?De(a,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[s]=t,y=(a,s)=>{for(var t in s||(s={}))Re.call(s,t)&&Q(a,t,s[t]);if(Z)for(var t of Z(s))Ae.call(s,t)&&Q(a,t,s[t]);return a},K=(a,s)=>Ce(a,Ee(s));import{d as ne,v as we,e as S,r as ae,f as Oe,h as xe,g as Ie,j as h,n as f,u as r,t as O,E as x,ai as R,i as L,a9 as Ve,U as Me}from"../jse/index-index-SSqEGcIT.js";import{t as Ne,P as Fe,u as Ue,a as ye,T as W,b as Ke,c as Le,d as He,e as Be,D as je,C as qe,f as Ye}from"./panel-time-pick-BH4wKgRy.js";import{n as Kn,o as Ln,g as Hn,h as Bn,i as jn,j as qn,k as Yn,l as $n,m as Gn,p as Jn,r as zn,s as Zn,w as Qn,q as Wn,v as Xn}from"./panel-time-pick-BH4wKgRy.js";import{k as $e,l as Ge,m as Je,r as ze,n as X,aF as Ze,w as Qe}from"./bootstrap-CYivmKoJ.js";import{b as We,U as ee}from"./index-DIXeP0hR.js";import{b as Xe,i as en}from"./isArrayLikeObject-BfUnw-Gv.js";import{d as H,s as te,e as nn,f as an}from"./isEqual-racMrmQ-.js";import{b as tn}from"./_baseFindIndex-D7XfJLKM.js";import"./index-C3SWEjhj.js";import"./browser-CSPQ6ERn.js";import"./use-form-item-iUVikjOD.js";import"./use-form-common-props-DZjBwEkr.js";import"./index-CdkCbLvc.js";import"./aria-DGfENwCE.js";import"./error-CYrjCQ5V.js";import"./index-owS4PRxE.js";import"./index-pMAz7VMb.js";import"./index-pE9ts8eW.js";function sn(){}function on(a){return a!==a}function rn(a,s,t){for(var c=t-1,d=a.length;++c<d;)if(a[c]===s)return c;return-1}function ln(a,s,t){return s===s?rn(a,s,t):tn(a,on,t)}function cn(a,s){var t=a==null?0:a.length;return!!t&&ln(a,s,0)>-1}var un=1/0,pn=H&&1/te(new H([,-0]))[1]==un?function(a){return new H(a)}:sn,dn=200;function mn(a,s,t){var c=-1,d=cn,v=a.length,o=!0,b=[],m=b;if(v>=dn){var _=pn(a);if(_)return te(_);o=!1,d=an,m=new nn}else m=b;e:for(;++c<v;){var k=a[c],T=k;if(k=k!==0?k:0,o&&T===T){for(var A=m.length;A--;)if(m[A]===T)continue e;b.push(k)}else d(m,T,t)||(m!==b&&m.push(T),b.push(k))}return b}var B=Xe(function(a){return mn(We(a,1,en,!0))});const fn=$e(K(y({},Ne),{parsedValue:{type:Ge(Array)}})),vn=ne({__name:"panel-time-range",props:fn,emits:["pick","select-range","set-picker-option"],setup(a,{emit:s}){const t=a,c=(e,n)=>{const i=[];for(let l=e;l<=n;l++)i.push(l);return i},{t:d,lang:v}=ze(),o=X("time"),b=X("picker"),m=we(Fe),{arrowControl:_,disabledHours:k,disabledMinutes:T,disabledSeconds:A,defaultValue:I}=m.props,se=S(()=>[o.be("range-picker","body"),o.be("panel","content"),o.is("arrow",_),C.value?"has-seconds":""]),oe=S(()=>[o.be("range-picker","body"),o.be("panel","content"),o.is("arrow",_),C.value?"has-seconds":""]),P=S(()=>t.parsedValue[0]),D=S(()=>t.parsedValue[1]),re=Ue(t),ie=()=>{s("pick",re.value,!1)},C=S(()=>t.format.includes("ss")),j=S(()=>t.format.includes("A")?"A":t.format.includes("a")?"a":""),le=(e=!1)=>{s("pick",[P.value,D.value],e)},ce=e=>{q(e.millisecond(0),D.value)},ue=e=>{q(P.value,e.millisecond(0))},pe=e=>{const n=e.map(l=>R(l).locale(v.value)),i=$(n);return n[0].isSame(i[0])&&n[1].isSame(i[1])},q=(e,n)=>{t.visible&&s("pick",[e,n],!0)},de=S(()=>P.value>D.value),w=ae([0,2]),me=(e,n)=>{s("select-range",e,n,"min"),w.value=[e,n]},Y=S(()=>C.value?11:8),fe=(e,n)=>{s("select-range",e,n,"max");const i=r(Y);w.value=[e+i,n+i]},ve=e=>{const n=C.value?[0,3,6,11,14,17]:[0,3,8,11],i=["hours","minutes"].concat(C.value?["seconds"]:[]),u=(n.indexOf(w.value[0])+e+n.length)%n.length,p=n.length/2;u<p?F.start_emitSelectRange(i[u]):F.end_emitSelectRange(i[u-p])},be=e=>{const n=e.code,{left:i,right:l,up:u,down:p}=Ze;if([i,l].includes(n)){ve(n===i?-1:1),e.preventDefault();return}if([u,p].includes(n)){const g=n===u?-1:1,E=w.value[0]<Y.value?"start":"end";F[`${E}_scrollDown`](g),e.preventDefault();return}},V=(e,n)=>{const i=k?k(e):[],l=e==="start",p=(n||(l?D.value:P.value)).hour(),g=l?c(p+1,23):c(0,p-1);return B(i,g)},M=(e,n,i)=>{const l=T?T(e,n):[],u=n==="start",p=i||(u?D.value:P.value),g=p.hour();if(e!==g)return l;const E=p.minute(),U=u?c(E+1,59):c(0,E-1);return B(l,U)},N=(e,n,i,l)=>{const u=A?A(e,n,i):[],p=i==="start",g=l||(p?D.value:P.value),E=g.hour(),U=g.minute();if(e!==E||n!==U)return u;const z=g.second(),Pe=p?c(z+1,59):c(0,z-1);return B(u,Pe)},$=([e,n])=>[G(e,"start",!0,n),G(n,"end",!1,e)],{getAvailableHours:ge,getAvailableMinutes:he,getAvailableSeconds:ke}=Ke(V,M,N),{timePickerOptions:F,getAvailableTime:G,onSetOption:J}=ye({getAvailableHours:ge,getAvailableMinutes:he,getAvailableSeconds:ke}),Te=e=>e?L(e)?e.map(n=>R(n,t.format).locale(v.value)):R(e,t.format).locale(v.value):null,Se=e=>e?L(e)?e.map(n=>n.format(t.format)):e.format(t.format):null,_e=()=>{if(L(I))return I.map(n=>R(n).locale(v.value));const e=R(I).locale(v.value);return[e,e.add(60,"m")]};return s("set-picker-option",["formatToString",Se]),s("set-picker-option",["parseUserInput",Te]),s("set-picker-option",["isValidValue",pe]),s("set-picker-option",["handleKeydownInput",be]),s("set-picker-option",["getDefaultValue",_e]),s("set-picker-option",["getRangeAvailableTime",$]),(e,n)=>e.actualVisible?(Ie(),Oe("div",{key:0,class:f([r(o).b("range-picker"),r(b).b("panel")])},[h("div",{class:f(r(o).be("range-picker","content"))},[h("div",{class:f(r(o).be("range-picker","cell"))},[h("div",{class:f(r(o).be("range-picker","header"))},O(r(d)("el.datepicker.startTime")),3),h("div",{class:f(r(se))},[x(W,{ref:"minSpinner",role:"start","show-seconds":r(C),"am-pm-mode":r(j),"arrow-control":r(_),"spinner-date":r(P),"disabled-hours":V,"disabled-minutes":M,"disabled-seconds":N,onChange:ce,onSetOption:r(J),onSelectRange:me},null,8,["show-seconds","am-pm-mode","arrow-control","spinner-date","onSetOption"])],2)],2),h("div",{class:f(r(o).be("range-picker","cell"))},[h("div",{class:f(r(o).be("range-picker","header"))},O(r(d)("el.datepicker.endTime")),3),h("div",{class:f(r(oe))},[x(W,{ref:"maxSpinner",role:"end","show-seconds":r(C),"am-pm-mode":r(j),"arrow-control":r(_),"spinner-date":r(D),"disabled-hours":V,"disabled-minutes":M,"disabled-seconds":N,onChange:ue,onSetOption:r(J),onSelectRange:fe},null,8,["show-seconds","am-pm-mode","arrow-control","spinner-date","onSetOption"])],2)],2)],2),h("div",{class:f(r(o).be("panel","footer"))},[h("button",{type:"button",class:f([r(o).be("panel","btn"),"cancel"]),onClick:i=>ie()},O(r(d)("el.datepicker.cancel")),11,["onClick"]),h("button",{type:"button",class:f([r(o).be("panel","btn"),"confirm"]),disabled:r(de),onClick:i=>le()},O(r(d)("el.datepicker.confirm")),11,["disabled","onClick"])],2)],2)):xe("v-if",!0)}});var bn=Je(vn,[["__file","panel-time-range.vue"]]);R.extend(Ye);var gn=ne({name:"ElTimePicker",install:null,props:K(y({},Le),{isRange:{type:Boolean,default:!1}}),emits:[ee],setup(a,s){const t=ae(),[c,d]=a.isRange?["timerange",bn]:["time",He],v=o=>s.emit(ee,o);return Ve(Be,a.popperOptions),s.expose({focus:()=>{var o;(o=t.value)==null||o.focus()},blur:()=>{var o;(o=t.value)==null||o.blur()},handleOpen:()=>{var o;(o=t.value)==null||o.handleOpen()},handleClose:()=>{var o;(o=t.value)==null||o.handleClose()}}),()=>{var o;const b=(o=a.format)!=null?o:je;return x(qe,Me(a,{ref:t,type:c,format:b,"onUpdate:modelValue":v}),{default:m=>x(d,m,null)})}}});const Fn=Qe(gn);export{qe as CommonPicker,Kn as DEFAULT_FORMATS_DATE,Ln as DEFAULT_FORMATS_DATEPICKER,je as DEFAULT_FORMATS_TIME,Fn as ElTimePicker,Fe as PICKER_BASE_INJECTION_KEY,Be as PICKER_POPPER_OPTIONS_INJECTION_KEY,He as TimePickPanel,Hn as buildTimeList,Bn as dateEquals,jn as dayOrDaysToDate,Fn as default,qn as extractDateFormat,Yn as extractTimeFormat,$n as formatter,Gn as makeList,Jn as parseDate,zn as rangeArr,Le as timePickerDefaultProps,Zn as timePickerRangeTriggerProps,Qn as timePickerRngeTriggerProps,Wn as timeUnits,Xn as valueEquals};
