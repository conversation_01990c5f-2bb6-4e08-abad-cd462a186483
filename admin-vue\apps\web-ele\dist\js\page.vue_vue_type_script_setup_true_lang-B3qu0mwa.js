var y=(t,d,o)=>new Promise((u,r)=>{var h=e=>{try{n(o.next(e))}catch(s){r(s)}},c=e=>{try{n(o.throw(e))}catch(s){r(s)}},n=e=>e.done?u(e.value):Promise.resolve(e.value).then(h,c);n((o=o.apply(t,d)).next())});import{C as k}from"./bootstrap-CYivmKoJ.js";import{d as H,r as g,c as C,e as R,o as T,f as a,g as l,h as i,j as $,n as m,u as p,k as v,l as f,t as b,m as O,p as S}from"../jse/index-index-SSqEGcIT.js";const B={class:"relative"},A={class:"flex-auto"},E={key:0,class:"mb-2 flex text-lg font-semibold"},N={key:0,class:"text-muted-foreground"},V={key:0},L=H({name:"Page",__name:"page",props:{title:{},description:{},contentClass:{},autoContentHeight:{type:Boolean,default:!1},headerClass:{},footerClass:{},heightOffset:{default:0}},setup(t){const d=g(0),o=g(0),u=g(!1),r=C("headerRef"),h=C("footerRef"),c=R(()=>t.autoContentHeight?{height:`calc(var(${k}) - ${d.value}px - ${typeof t.heightOffset=="number"?`${t.heightOffset}px`:t.heightOffset})`,overflowY:u.value?"auto":"unset"}:{});function n(){return y(this,null,function*(){var e,s;t.autoContentHeight&&(yield S(),d.value=((e=r.value)==null?void 0:e.offsetHeight)||0,o.value=((s=h.value)==null?void 0:s.offsetHeight)||0,setTimeout(()=>{u.value=!0},30))})}return T(()=>{n()}),(e,s)=>(l(),a("div",B,[e.description||e.$slots.description||e.title||e.$slots.title||e.$slots.extra?(l(),a("div",{key:0,ref_key:"headerRef",ref:r,class:m(p(v)("bg-card border-border relative flex items-end border-b px-6 py-4",e.headerClass))},[$("div",A,[f(e.$slots,"title",{},()=>[e.title?(l(),a("div",E,b(e.title),1)):i("",!0)]),f(e.$slots,"description",{},()=>[e.description?(l(),a("p",N,b(e.description),1)):i("",!0)])]),e.$slots.extra?(l(),a("div",V,[f(e.$slots,"extra")])):i("",!0)],2)):i("",!0),$("div",{class:m(p(v)("h-full p-4",e.contentClass)),style:O(c.value)},[f(e.$slots,"default")],6),e.$slots.footer?(l(),a("div",{key:1,ref_key:"footerRef",ref:h,class:m(p(v)("bg-card align-center absolute bottom-0 left-0 right-0 flex px-6 py-4",e.footerClass))},[f(e.$slots,"footer")],2)):i("",!0)]))}});export{L as _};
