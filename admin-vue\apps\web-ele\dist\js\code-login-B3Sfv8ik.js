var T=(m,u,n)=>new Promise((l,t)=>{var c=a=>{try{s(n.next(a))}catch(r){t(r)}},b=a=>{try{s(n.throw(a))}catch(r){t(r)}},s=a=>a.done?l(a.value):Promise.resolve(a.value).then(c,b);s((n=n.apply(m,u)).next())});import{f as S,J as w,$ as e,g as v,K as B}from"./bootstrap-CYivmKoJ.js";import{T as N}from"./auth-title-Cs6tZ2yh.js";import{d as $,L as V,e as k,f as x,g as L,E as f,D as p,l as _,G as h,t as g,u as i,j as y,n as E,r as P,q as A}from"../jse/index-index-SSqEGcIT.js";const D={class:"text-muted-foreground"},F=$({name:"AuthenticationCodeLogin",__name:"code-login",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(m,{expose:u,emit:n}){const l=m,t=n,c=S(),[b,s]=w(V({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:k(()=>l.formSchema),showDefaultActions:!1}));function a(){return T(this,null,function*(){const{valid:o}=yield s.validate(),d=yield s.getValues();o&&t("submit",d)})}function r(){c.push(l.loginPath)}return u({getFormApi:()=>s}),(o,d)=>(L(),x("div",null,[f(N,null,{desc:p(()=>[y("span",D,[_(o.$slots,"subTitle",{},()=>[h(g(o.subTitle||i(e)("authentication.codeSubtitle")),1)])])]),default:p(()=>[_(o.$slots,"title",{},()=>[h(g(o.title||i(e)("authentication.welcomeBack"))+" 📲 ",1)])]),_:3}),f(i(b)),f(i(v),{class:E([{"cursor-wait":o.loading},"w-full"]),loading:o.loading,onClick:a},{default:p(()=>[_(o.$slots,"submitButtonText",{},()=>[h(g(o.submitButtonText||i(e)("common.login")),1)])]),_:3},8,["class","loading"]),f(i(v),{class:"mt-4 w-full",variant:"outline",onClick:d[0]||(d[0]=q=>r())},{default:p(()=>[h(g(i(e)("common.back")),1)]),_:1})]))}}),C=6,z=$({name:"CodeLogin",__name:"code-login",setup(m){const u=P(!1),n=k(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.mobile")},fieldName:"phoneNumber",label:e("authentication.mobile"),rules:B().min(1,{message:e("authentication.mobileTip")}).refine(t=>/^\d{11}$/.test(t),{message:e("authentication.mobileErrortip")})},{component:"VbenPinInput",componentProps:{codeLength:C,createText:t=>t>0?e("authentication.sendText",[t]):e("authentication.sendCode"),placeholder:e("authentication.code")},fieldName:"code",label:e("authentication.code"),rules:B().length(C,{message:e("authentication.codeTip",[C])})}]);function l(t){return T(this,null,function*(){console.log(t)})}return(t,c)=>(L(),A(i(F),{"form-schema":n.value,loading:u.value,onSubmit:l},null,8,["form-schema","loading"]))}});export{z as default};
