import type { UserInfo } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get<UserInfo>(
    '/api/v1/auth/me',
    {
      // The Vben template expects the user info object directly.
      // Our API returns { code, message, data: { ... } }.
      // The transform will handle extracting the user info from the nested data object.
      transform: (res) => res.data,
    },
  );
}
