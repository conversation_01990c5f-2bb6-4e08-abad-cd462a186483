import{P as s,b as f}from"./bootstrap-BmSDnAET.js";import{a4 as O,a5 as h,x as w,P as b,Y as k,J as S}from"../jse/index-index-BAMHRxBA.js";var z=function(n,a){var o={};for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&a.indexOf(t)<0&&(o[t]=n[t]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var u=0,t=Object.getOwnPropertySymbols(n);u<t.length;u++)a.indexOf(t[u])<0&&Object.prototype.propertyIsEnumerable.call(n,t[u])&&(o[t[u]]=n[t[u]]);return o};const K=O({compatConfig:{MODE:3},props:{disabled:s.looseBool,type:s.string,value:s.any,tag:{type:String,default:"input"},size:s.string,onChange:Function,onInput:Function,onBlur:Function,onFocus:Function,onKeydown:Function,onCompositionstart:Function,onCompositionend:Function,onKeyup:Function,onPaste:Function,onMousedown:Function},emits:["change","input","blur","keydown","focus","compositionstart","compositionend","keyup","paste","mousedown"],setup(n,a){let{expose:o}=a;const t=h(null);return o({focus:()=>{t.value&&t.value.focus()},blur:()=>{t.value&&t.value.blur()},input:t,setSelectionRange:(l,c,v)=>{var d;(d=t.value)===null||d===void 0||d.setSelectionRange(l,c,v)},select:()=>{var l;(l=t.value)===null||l===void 0||l.select()},getSelectionStart:()=>{var l;return(l=t.value)===null||l===void 0?void 0:l.selectionStart},getSelectionEnd:()=>{var l;return(l=t.value)===null||l===void 0?void 0:l.selectionEnd},getScrollTop:()=>{var l;return(l=t.value)===null||l===void 0?void 0:l.scrollTop}}),()=>{const{tag:l,value:c}=n,v=z(n,["tag","value"]);return w(l,f(f({},v),{},{ref:t,value:c}),null)}}});function M(n){return Object.keys(n).reduce((a,o)=>{const t=n[o];return typeof t=="undefined"||t===null||(a+=`${o}: ${n[o]};`),a},"")}var D=function(n,a){var o={};for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&a.indexOf(t)<0&&(o[t]=n[t]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var u=0,t=Object.getOwnPropertySymbols(n);u<t.length;u++)a.indexOf(t[u])<0&&Object.prototype.propertyIsEnumerable.call(n,t[u])&&(o[t[u]]=n[t[u]]);return o};const A=O({compatConfig:{MODE:3},inheritAttrs:!1,props:{disabled:s.looseBool,type:s.string,value:s.any,lazy:s.bool.def(!0),tag:{type:String,default:"input"},size:s.string,style:s.oneOfType([String,Object]),class:s.string},emits:["change","input","blur","keydown","focus","compositionstart","compositionend","keyup","paste","mousedown"],setup(n,a){let{emit:o,attrs:t,expose:u}=a;const r=h(null),y=b(),i=b(!1);k([()=>n.value,i],()=>{i.value||(y.value=n.value)},{immediate:!0});const l=e=>{o("change",e)},c=e=>{i.value=!0,e.target.composing=!0,o("compositionstart",e)},v=e=>{i.value=!1,e.target.composing=!1,o("compositionend",e);const p=document.createEvent("HTMLEvents");p.initEvent("input",!0,!0),e.target.dispatchEvent(p),l(e)},d=e=>{if(i.value&&n.lazy){y.value=e.target.value;return}o("input",e)},_=e=>{o("blur",e)},P=e=>{o("focus",e)},C=()=>{r.value&&r.value.focus()},F=()=>{r.value&&r.value.blur()},j=e=>{o("keydown",e)},E=e=>{o("keyup",e)},T=(e,p,g)=>{var m;(m=r.value)===null||m===void 0||m.setSelectionRange(e,p,g)},x=()=>{var e;(e=r.value)===null||e===void 0||e.select()};u({focus:C,blur:F,input:S(()=>{var e;return(e=r.value)===null||e===void 0?void 0:e.input}),setSelectionRange:T,select:x,getSelectionStart:()=>{var e;return(e=r.value)===null||e===void 0?void 0:e.getSelectionStart()},getSelectionEnd:()=>{var e;return(e=r.value)===null||e===void 0?void 0:e.getSelectionEnd()},getScrollTop:()=>{var e;return(e=r.value)===null||e===void 0?void 0:e.getScrollTop()}});const B=e=>{o("mousedown",e)},I=e=>{o("paste",e)},R=S(()=>n.style&&typeof n.style!="string"?M(n.style):n.style);return()=>{const{style:e,lazy:p}=n,g=D(n,["style","lazy"]);return w(K,f(f(f({},g),t),{},{style:R.value,onInput:d,onChange:l,onBlur:_,onFocus:P,ref:r,value:y.value,onCompositionstart:c,onCompositionend:v,onKeyup:E,onKeydown:j,onPaste:I,onMousedown:B}),null)}}});export{A as B};
