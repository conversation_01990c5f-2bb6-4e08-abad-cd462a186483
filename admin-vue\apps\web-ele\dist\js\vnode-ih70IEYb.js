import{b as r,F as N,C as o,i as S}from"../jse/index-index-SSqEGcIT.js";var _=(E=>(E[E.TEXT=1]="TEXT",E[E.CLASS=2]="CLASS",E[E.STYLE=4]="STYLE",E[E.PROPS=8]="PROPS",E[E.FULL_PROPS=16]="FULL_PROPS",E[E.HYDRATE_EVENTS=32]="HYDRATE_EVENTS",E[E.STABLE_FRAGMENT=64]="STABLE_FRAGMENT",E[E.KEYED_FRAGMENT=128]="KEYED_FRAGMENT",E[E.UNKEYED_FRAGMENT=256]="UNKEYED_FRAGMENT",E[E.NEED_PATCH=512]="NEED_PATCH",E[E.DYNAMIC_SLOTS=1024]="DYNAMIC_SLOTS",E[E.HOISTED=-1]="HOISTED",E[E.BAIL=-2]="BAIL",E))(_||{});function L(E){return r(E)&&E.type===N}function p(E){return r(E)&&E.type===o}function R(E){return r(E)&&!L(E)&&!p(E)}const n=E=>{const u=S(E)?E:[E],T=[];return u.forEach(e=>{var A;S(e)?T.push(...n(e)):r(e)&&((A=e.component)!=null&&A.subTree)?T.push(e,...n(e.component.subTree)):r(e)&&S(e.children)?T.push(...n(e.children)):r(e)&&e.shapeFlag===2?T.push(...n(e.type())):T.push(e)}),T};export{_ as P,R as a,n as f,L as i};
