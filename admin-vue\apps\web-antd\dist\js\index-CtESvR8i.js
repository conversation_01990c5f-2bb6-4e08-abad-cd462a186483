import{b,d as B,e as T,f as w,c as A,_ as v,g as y,N as S,i as V,h as C,a as F}from"./layout.vue_vue_type_script_setup_true_lang-8PZxy_uG.js";import{A as N,_ as O,a as U}from"./authentication-DCq2Qyub.js";import{i as E,h as G}from"./theme-toggle.vue_vue_type_script_setup_true_lang-XcsohWBu.js";import{bk as o}from"./bootstrap-BmSDnAET.js";import{P as a,av as t,ab as s}from"../jse/index-index-BAMHRxBA.js";import"./avatar.vue_vue_type_script_setup_true_lang-BBfkvUsj.js";import"./use-vben-form-DKopJKB3.js";import"./render-content.vue_vue_type_script_lang-CTn4O0b5.js";import"./TabsList.vue_vue_type_script_setup_true_lang-BKUkKJ6M.js";import"./rotate-cw-Bnbb2AMH.js";import"./index-dhlZeuSA.js";import"./index-CkUpMVVs.js";const r=a(!1);function P(){function e(){r.value=!0}return{handleOpenPreference:e,openPreferences:r}}const n={};function c(e,i){return s(),t("div")}const k=o(n,[["render",c]]);export{N as AuthPageLayout,O as AuthenticationColorToggle,U as AuthenticationLayoutToggle,b as BasicLayout,B as Breadcrumb,T as CheckUpdates,w as GlobalSearch,A as IFrameRouterView,k as IFrameView,E as LanguageToggle,v as LockScreen,y as LockScreenModal,S as Notification,V as Preferences,C as PreferencesButton,G as ThemeToggle,F as UserDropdown,P as useOpenPreferences};
