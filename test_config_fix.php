<?php
/**
 * 测试舌诊配置获取修复
 */

// 引入ThinkPHP框架
require_once 'shangchengquan/shangcheng/vendor/autoload.php';

// 初始化应用
$app = new think\App();
$app->initialize();

// 引入SheZhen类
use app\common\SheZhen;

echo "<h2>舌诊配置获取测试</h2>";

// 测试不同的aid值
$testAids = [0, 62, 1];

foreach ($testAids as $aid) {
    echo "<h3>测试 AID = $aid</h3>";
    
    try {
        $config = SheZhen::getConfig($aid);
        
        if ($config) {
            echo "<p style='color: green;'>✓ 成功获取配置</p>";
            echo "<h4>配置内容：</h4>";
            echo "<ul>";
            
            // 显示关键配置项
            $keyFields = [
                'aliyun_app_code' => 'AppCode',
                'aliyun_endpoint' => 'API端点',
                'is_enable' => '是否启用',
                'is_open' => '是否开启',
                'daily_limit' => '每日限制',
                'price' => '价格'
            ];
            
            foreach ($keyFields as $field => $label) {
                if (isset($config[$field])) {
                    $value = $config[$field];
                    if ($field === 'aliyun_app_code' && strlen($value) > 8) {
                        $value = substr($value, 0, 8) . '***'; // 隐藏敏感信息
                    }
                    echo "<li>$label: $value</li>";
                }
            }
            echo "</ul>";
            
            // 检查配置完整性
            echo "<h4>配置完整性检查：</h4>";
            $isComplete = true;
            
            if (empty($config['aliyun_app_code'])) {
                echo "<p style='color: red;'>✗ 缺少 aliyun_app_code</p>";
                $isComplete = false;
            } else {
                echo "<p style='color: green;'>✓ aliyun_app_code 已配置</p>";
            }
            
            if (empty($config['aliyun_endpoint'])) {
                echo "<p style='color: red;'>✗ 缺少 aliyun_endpoint</p>";
                $isComplete = false;
            } else {
                echo "<p style='color: green;'>✓ aliyun_endpoint 已配置</p>";
            }
            
            if ($isComplete) {
                echo "<p style='color: green;'><strong>✓ 配置完整，可以正常调用API</strong></p>";
            } else {
                echo "<p style='color: red;'><strong>✗ 配置不完整，无法调用API</strong></p>";
            }
            
        } else {
            echo "<p style='color: red;'>✗ 未找到配置</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ 获取配置时出错: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// 测试API连接
echo "<h3>测试API连接</h3>";
try {
    $testResult = SheZhen::testApiConnection(62); // 使用aid=62测试
    
    if ($testResult['status'] == 1) {
        echo "<p style='color: green;'>✓ API连接测试成功: " . $testResult['msg'] . "</p>";
    } else {
        echo "<p style='color: red;'>✗ API连接测试失败: " . $testResult['msg'] . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ API连接测试异常: " . $e->getMessage() . "</p>";
}

echo "<h3>修复总结</h3>";
echo "<ul>";
echo "<li>✓ 修复了配置获取方法，支持新旧两种配置方式</li>";
echo "<li>✓ 优先使用新的 shezhen_set 表配置</li>";
echo "<li>✓ 回退到旧的 sysset 表配置</li>";
echo "<li>✓ 添加了详细的日志记录</li>";
echo "</ul>";

echo "<h3>下一步建议</h3>";
echo "<ol>";
echo "<li>确保配置表中有正确的API配置信息</li>";
echo "<li>检查 aliyun_app_code 和 aliyun_endpoint 是否正确</li>";
echo "<li>测试实际的舌诊分析接口调用</li>";
echo "</ol>";
?>
