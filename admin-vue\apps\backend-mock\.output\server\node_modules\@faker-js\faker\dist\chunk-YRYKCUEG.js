import{a as n}from"./chunk-KERBADJJ.js";import{n as a,o as i}from"./chunk-YQYVFZYE.js";var e=["###","##","#"];var t=["<PERSON><PERSON><PERSON>","Helsinki","<PERSON>yvink\xE4\xE4","<PERSON><PERSON><PERSON><PERSON>","Joensu<PERSON>","Jyv\xE4skyl\xE4","Kok<PERSON><PERSON>","Kuop<PERSON>","<PERSON>hti","<PERSON>ulu","<PERSON><PERSON>","Porvoo","Raisio","Rovaniemi","Sastamala","Tampere","Turku","<PERSON><PERSON><PERSON>","Valkeakos<PERSON>","<PERSON>ta<PERSON>"];var o=["{{location.city_name}}"];var r=["#####"];var l=["A","B","C","A #","A ##","B #","B ##","C #","C ##"];var k=["<PERSON><PERSON><PERSON><PERSON><PERSON>","Etel\xE4-<PERSON><PERSON><PERSON><PERSON>","Etel\xE4-<PERSON>hjan<PERSON><PERSON>","Etel\xE4-Savo","<PERSON><PERSON><PERSON>","<PERSON><PERSON>-<PERSON>\xE4me","Keski-Pohjanmaa","<PERSON>ski-<PERSON>omi","Kymenlaakso","Lappi","P\xE4ij\xE4t-H\xE4me","Pirkanmaa","Pohjanmaa","Pohjois-Karjala","Pohjois-Pohjanmaa","Pohjois-Savo","Satakunta","Uusimaa","Varsinais-Suomi"];var m={normal:"{{location.street}} {{location.buildingNumber}}",full:"{{location.street}} {{location.buildingNumber}} {{location.secondaryAddress}}"};var s=["{{person.first_name.generic}}{{location.street_suffix}}","{{person.last_name.generic}}{{location.street_suffix}}"];var u=["katu","tie","kuja","polku","kaari","linja","raitti","rinne","penger","ranta","v\xE4yl\xE4"];var h={building_number:e,city_name:t,city_pattern:o,postcode:r,secondary_address:l,state:k,street_address:m,street_pattern:s,street_suffix:u},p=h;var A={title:"Finnish",code:"fi",language:"fi",endonym:"suomi",dir:"ltr",script:"Latn"},f=A;var M={generic:["Aino","Aleksi","Anja","Anna","Anne","Anneli","Annikki","Antero","Antti","Ari","Eero","Eeva","Elina","Elisabet","Emilia","Ensio","Erik","Erkki","Eveliina","Hanna","Hannele","Hannu","Heikki","Helena","Henrik","Ilmari","Inkeri","Irmeli","Jaakko","Janne","Jari","Johanna","Johannes","Juha","Juhani","Juho","Jukka","Kaarina","Kalervo","Kalevi","Kari","Karoliina","Katariina","Kristian","Kristiina","Kyllikki","Laura","Lauri","Leena","Liisa","Maarit","Maija","Mari","Maria","Marika","Marja","Marjatta","Markku","Marko","Markus","Martti","Matias","Matti","Mika","Mikael","Mikko","Minna","Olavi","Orvokki","Oskari","Pauliina","Pekka","Pentti","Petri","Petteri","Pirjo","Pirkko","P\xE4ivi","Riitta","Ritva","Sakari","Sami","Sari","Satu","Seppo","Sinikka","Sofia","Susanna","Tapani","Tapio","Tarja","Tellervo","Tiina","Timo","Tuomas","Tuula","Tuulikki","Valtteri","Veikko","Ville"],female:["Aino","Anja","Anna","Anne","Anneli","Annikki","Eeva","Elina","Elisabet","Emilia","Eveliina","Hanna","Hannele","Helena","Inkeri","Irmeli","Johanna","Kaarina","Karoliina","Katariina","Kristiina","Kyllikki","Laura","Leena","Liisa","Maarit","Maija","Mari","Maria","Marika","Marja","Marjatta","Minna","Orvokki","Pauliina","Pirjo","Pirkko","P\xE4ivi","Riitta","Ritva","Sari","Satu","Sinikka","Sofia","Susanna","Tarja","Tellervo","Tiina","Tuula","Tuulikki"],male:["Aleksi","Antero","Antti","Ari","Eero","Ensio","Erik","Erkki","Hannu","Heikki","Henrik","Ilmari","Jaakko","Janne","Jari","Johannes","Juha","Juhani","Juho","Jukka","Kalervo","Kalevi","Kari","Kristian","Lauri","Markku","Marko","Markus","Martti","Matias","Matti","Mika","Mikael","Mikko","Olavi","Oskari","Pekka","Pentti","Petri","Petteri","Sakari","Sami","Seppo","Tapani","Tapio","Timo","Tuomas","Valtteri","Veikko","Ville"]};var d={generic:["Aaltonen","Ahonen","Anttila","Hakala","Heikkil\xE4","Heikkinen","Heinonen","Hiltunen","Hirvonen","H\xE4m\xE4l\xE4inen","Jokinen","J\xE4rvinen","Kallio","Karjalainen","Kinnunen","Koivisto","Korhonen","Koskinen","Laakso","Laaksonen","Lahtinen","Laine","Laitinen","Lehtinen","Lehto","Lehtonen","Leinonen","Lepp\xE4nen","Manninen","Mattila","Miettinen","Mustonen","M\xE4kel\xE4","M\xE4kinen","Niemi","Nieminen","Ojala","Pitk\xE4nen","Rantanen","R\xE4s\xE4nen","Saarinen","Salminen","Salo","Salonen","Savolainen","Toivonen","Tuominen","Turunen","Virtanen","V\xE4is\xE4nen"]};var c={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var v=[{value:"{{person.firstName}} {{person.lastName}}",weight:1}];var S={first_name:M,last_name:d,last_name_pattern:c,name:v},K=S;var j={location:p,metadata:f,person:K},P=j;var la=new a({locale:[P,n,i]});export{P as a,la as b};
