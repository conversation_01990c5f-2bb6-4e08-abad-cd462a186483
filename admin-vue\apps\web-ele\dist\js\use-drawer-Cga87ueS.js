var Ne=Object.defineProperty,je=Object.defineProperties;var We=Object.getOwnPropertyDescriptors;var X=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable;var Q=(o,t,s)=>t in o?Ne(o,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):o[t]=s,y=(o,t)=>{for(var s in t||(t={}))ne.call(t,s)&&Q(o,s,t[s]);if(X)for(var s of X(t))le.call(t,s)&&Q(o,s,t[s]);return o},z=(o,t)=>je(o,We(t));var R=(o,t)=>{var s={};for(var a in o)ne.call(o,a)&&t.indexOf(a)<0&&(s[a]=o[a]);if(o!=null&&X)for(var a of X(o))t.indexOf(a)<0&&le.call(o,a)&&(s[a]=o[a]);return s};var V=(o,t,s)=>Q(o,typeof t!="symbol"?t+"":t,s);var H=(o,t,s)=>new Promise((a,i)=>{var l=v=>{try{_(s.next(v))}catch(c){i(c)}},f=v=>{try{_(s.throw(v))}catch(c){i(c)}},_=v=>v.done?a(v.value):Promise.resolve(v.value).then(l,f);_((s=s.apply(o,t)).next())});import{d as w,e as P,q as u,g as r,u as e,U as J,k as x,D as p,f as E,h as g,t as I,n as $,S as ye,T as he,l as h,v as ve,r as A,E as k,m as Ue,w as be,j as N,F as Ke,R as Xe,ao as He,ap as Je,G as M,B as re,a9 as _e,aq as qe,a as Ye,L as Ge,H as ie,p as ge}from"../jse/index-index-SSqEGcIT.js";import{c as Qe,aO as Ze,X as et,S as we,aP as tt,aQ as st,aR as ot,aS as at,ae as nt,aT as lt,aU as rt,aV as it,_ as dt,aW as ct,aX as ut,aY as pt,aZ as ft,a_ as mt,aD as de,a$ as yt,b0 as ce,g as ue,b1 as ht,b2 as vt}from"./bootstrap-CYivmKoJ.js";const pe=Qe("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),bt=w({__name:"Separator",props:{orientation:{},decorative:{type:Boolean},asChild:{type:Boolean},as:{},class:{},label:{}},setup(o){const t=o,s=P(()=>{const l=t,{class:a}=l;return R(l,["class"])});return(a,i)=>(r(),u(e(Ze),J(s.value,{class:e(x)("bg-border relative shrink-0",t.orientation==="vertical"?"h-full w-px":"h-px w-full",t.class)}),{default:p(()=>[t.label?(r(),E("span",{key:0,class:$(e(x)("text-muted-foreground bg-background absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 items-center justify-center text-xs",t.orientation==="vertical"?"w-[1px] px-1 py-2":"h-[1px] px-2 py-1"))},I(t.label),3)):g("",!0)]),_:1},16,["class"]))}}),_t=et("bg-background shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 border-border",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t border-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left ",right:"inset-y-0 right-0 w-3/4 border-l  data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),gt=w({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:t}){const i=we(o,t);return(l,f)=>(r(),u(e(tt),ye(he(e(i))),{default:p(()=>[h(l.$slots,"default")]),_:3},16))}}),fe=w({__name:"SheetClose",props:{asChild:{type:Boolean},as:{}},setup(o){const t=o;return(s,a)=>(r(),u(e(st),ye(he(t)),{default:p(()=>[h(s.$slots,"default")]),_:3},16))}}),wt=["data-dismissable-drawer"],Ct=w({__name:"SheetOverlay",setup(o){ot();const t=ve("DISMISSABLE_DRAWER_ID");return(s,a)=>(r(),E("div",{"data-dismissable-drawer":e(t),class:"bg-overlay z-popup inset-0"},null,8,wt))}}),xt=w({inheritAttrs:!1,__name:"SheetContent",props:{appendTo:{default:"body"},class:{},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},side:{},zIndex:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(o,{emit:t}){const s=o,a=t,i=P(()=>{const T=s,{class:d,modal:m,open:b,side:B}=T;return R(T,["class","modal","open","side"])});function l(){return s.appendTo==="body"||s.appendTo===document.body||!s.appendTo}const f=P(()=>l()?"fixed":"absolute"),_=we(i,a),v=A(null);function c(d){var m;d.target===((m=v.value)==null?void 0:m.$el)&&(s.open?a("opened"):a("closed"))}return(d,m)=>(r(),u(e(at),{to:d.appendTo},{default:p(()=>[k(nt,{name:"fade"},{default:p(()=>[d.open&&d.modal?(r(),u(Ct,{key:0,style:Ue(z(y({},d.zIndex?{zIndex:d.zIndex}:{}),{position:f.value,backdropFilter:d.overlayBlur&&d.overlayBlur>0?`blur(${d.overlayBlur}px)`:"none"}))},null,8,["style"])):g("",!0)]),_:1}),k(e(lt),J({ref_key:"contentRef",ref:v,class:e(x)("z-popup",e(_t)({side:d.side}),s.class),style:z(y({},d.zIndex?{zIndex:d.zIndex}:{}),{position:f.value}),onAnimationend:c},y(y({},e(_)),d.$attrs)),{default:p(()=>[h(d.$slots,"default")]),_:3},16,["class","style"])]),_:3},8,["to"]))}}),Z=w({__name:"SheetDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,s=P(()=>{const l=t,{class:a}=l;return R(l,["class"])});return(a,i)=>(r(),u(e(rt),J({class:e(x)("text-muted-foreground text-sm",t.class)},s.value),{default:p(()=>[h(a.$slots,"default")]),_:3},16,["class"]))}}),Bt=w({__name:"SheetFooter",props:{class:{}},setup(o){const t=o;return(s,a)=>(r(),E("div",{class:$(e(x)("flex flex-row flex-col-reverse justify-end gap-x-2",t.class))},[h(s.$slots,"default")],2))}}),Ot=w({__name:"SheetHeader",props:{class:{}},setup(o){const t=o;return(s,a)=>(r(),E("div",{class:$(e(x)("flex flex-col text-center sm:text-left",t.class))},[h(s.$slots,"default")],2))}}),ee=w({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,s=P(()=>{const l=t,{class:a}=l;return R(l,["class"])});return(a,i)=>(r(),u(e(it),J({class:e(x)("text-foreground font-medium",t.class)},s.value),{default:p(()=>[h(a.$slots,"default")]),_:3},16,["class"]))}}),kt={class:"dot relative inline-block size-9 text-3xl"},Dt={key:1,class:"text-primary mt-4 text-xs"},St=w({name:"VbenLoading",__name:"loading",props:{class:{},minLoadingTime:{default:50},spinning:{type:Boolean},text:{default:""}},setup(o){const t=o,s=A(!1),a=A(!1),i=A();be(()=>t.spinning,f=>{if(!f){s.value=!1,clearTimeout(i.value);return}i.value=setTimeout(()=>{s.value=!0,s.value&&(a.value=!0)},t.minLoadingTime)},{immediate:!0});function l(){s.value||(a.value=!1)}return(f,_)=>(r(),E("div",{class:$(e(x)("z-100 dark:bg-overlay bg-overlay-content absolute left-0 top-0 flex size-full flex-col items-center justify-center transition-all duration-500",{"invisible opacity-0":!s.value},t.class)),onTransitionend:l},[a.value?h(f.$slots,"icon",{key:0},()=>[N("span",kt,[(r(),E(Ke,null,Xe(4,v=>N("i",{key:v,class:"bg-primary absolute block size-4 origin-[50%_50%] scale-75 rounded-full opacity-30"})),64))])],!0):g("",!0),f.text?(r(),E("div",Dt,I(f.text),1)):g("",!0),h(f.$slots,"default",{},void 0,!0)],34))}}),$t=dt(St,[["__scopeId","data-v-2e723625"]]),At={class:"flex items-center"},Tt={class:"flex-center"},It=w({__name:"drawer",props:{drawerApi:{default:void 0},appendToMain:{type:Boolean,default:!1},cancelText:{},class:{},closable:{type:Boolean},closeIconPlacement:{default:"right"},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},footer:{type:Boolean},footerClass:{},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},placement:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean,default:!1},title:{},titleTooltip:{},zIndex:{default:1e3}},setup(o){var U,ae;const t=o,s=ct.getComponents(),a=He();_e("DISMISSABLE_DRAWER_ID",a);const i=A(),{$t:l}=ut(),{isMobile:f}=pt(),_=(ae=(U=t.drawerApi)==null?void 0:U.useStore)==null?void 0:ae.call(U),{appendToMain:v,cancelText:c,class:d,closable:m,closeIconPlacement:b,closeOnClickModal:B,closeOnPressEscape:D,confirmLoading:T,confirmText:q,contentClass:Ce,description:j,destroyOnClose:xe,footer:Be,footerClass:Oe,header:ke,headerClass:De,loading:te,modal:Se,openAutoFocus:$e,overlayBlur:Ae,placement:F,showCancelButton:Te,showConfirmButton:Ie,submitting:O,title:W,titleTooltip:se,zIndex:Ee}=ft(t,_);Je(()=>{var n;v.value||(n=t.drawerApi)==null||n.close()});function Pe(n){(!B.value||O.value)&&n.preventDefault()}function ze(n){(!D.value||O.value)&&n.preventDefault()}function Re(n){const C=n.target,K=C==null?void 0:C.dataset.dismissableDrawer;(O.value||!B.value||K!==a)&&n.preventDefault()}function Fe(n){$e.value||n==null||n.preventDefault()}function oe(n){n.preventDefault(),n.stopPropagation()}const Le=P(()=>v.value?`#${mt}>div:not(.absolute)>div`:void 0),Y=A(!1),G=A(!0);be(()=>{var n;return(n=_==null?void 0:_.value)==null?void 0:n.isOpen},n=>{G.value=!1,n&&!e(Y)&&(Y.value=!0)});function Ve(){var n;G.value=!0,(n=t.drawerApi)==null||n.onClosed()}const Me=P(()=>!e(xe)&&e(Y));return(n,C)=>{var K;return r(),u(e(gt),{modal:!1,open:(K=e(_))==null?void 0:K.isOpen,"onUpdate:open":C[3]||(C[3]=()=>{var L;return(L=n.drawerApi)==null?void 0:L.close()})},{default:p(()=>{var L;return[k(e(xt),{"append-to":Le.value,class:$(e(x)("flex w-[520px] flex-col",e(d),{"!w-full":e(f)||e(F)==="bottom"||e(F)==="top","max-h-[100vh]":e(F)==="bottom"||e(F)==="top",hidden:G.value})),modal:e(Se),open:(L=e(_))==null?void 0:L.isOpen,side:e(F),"z-index":e(Ee),"force-mount":Me.value,"overlay-blur":e(Ae),onCloseAutoFocus:oe,onClosed:Ve,onEscapeKeyDown:ze,onFocusOutside:oe,onInteractOutside:Pe,onOpenAutoFocus:Fe,onOpened:C[2]||(C[2]=()=>{var S;return(S=n.drawerApi)==null?void 0:S.onOpened()}),onPointerDownOutside:Re},{default:p(()=>[e(ke)?(r(),u(e(Ot),{key:0,class:$(e(x)("!flex flex-row items-center justify-between border-b px-6 py-5",e(De),{"px-4 py-3":e(m),"pl-2":e(m)&&e(b)==="left"}))},{default:p(()=>[N("div",At,[e(m)&&e(b)==="left"?(r(),u(e(fe),{key:0,"as-child":"",disabled:e(O),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:p(()=>[h(n.$slots,"close-icon",{},()=>[k(e(de),null,{default:p(()=>[k(e(pe),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):g("",!0),e(m)&&e(b)==="left"?(r(),u(e(bt),{key:1,class:"ml-1 mr-2 h-8",decorative:"",orientation:"vertical"})):g("",!0),e(W)?(r(),u(e(ee),{key:2,class:"text-left"},{default:p(()=>[h(n.$slots,"title",{},()=>[M(I(e(W))+" ",1),e(se)?(r(),u(e(yt),{key:0,"trigger-class":"pb-1"},{default:p(()=>[M(I(e(se)),1)]),_:1})):g("",!0)])]),_:3})):g("",!0),e(j)?(r(),u(e(Z),{key:3,class:"mt-1 text-xs"},{default:p(()=>[h(n.$slots,"description",{},()=>[M(I(e(j)),1)])]),_:3})):g("",!0)]),!e(W)||!e(j)?(r(),u(e(ce),{key:0},{default:p(()=>[e(W)?g("",!0):(r(),u(e(ee),{key:0})),e(j)?g("",!0):(r(),u(e(Z),{key:1}))]),_:1})):g("",!0),N("div",Tt,[h(n.$slots,"extra"),e(m)&&e(b)==="right"?(r(),u(e(fe),{key:0,"as-child":"",disabled:e(O),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:p(()=>[h(n.$slots,"close-icon",{},()=>[k(e(de),null,{default:p(()=>[k(e(pe),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):g("",!0)])]),_:3},8,["class"])):(r(),u(e(ce),{key:1},{default:p(()=>[k(e(ee)),k(e(Z))]),_:1})),N("div",{ref_key:"wrapperRef",ref:i,class:$(e(x)("relative flex-1 overflow-y-auto p-3",e(Ce),{"pointer-events-none":e(te)||e(O)}))},[h(n.$slots,"default")],2),e(te)||e(O)?(r(),u(e($t),{key:2,spinning:""})):g("",!0),e(Be)?(r(),u(e(Bt),{key:3,class:$(e(x)("w-full flex-row items-center justify-end border-t p-2 px-3",e(Oe)))},{default:p(()=>[h(n.$slots,"prepend-footer"),h(n.$slots,"footer",{},()=>[e(Te)?(r(),u(re(e(s).DefaultButton||e(ue)),{key:0,variant:"ghost",disabled:e(O),onClick:C[0]||(C[0]=()=>{var S;return(S=n.drawerApi)==null?void 0:S.onCancel()})},{default:p(()=>[h(n.$slots,"cancelText",{},()=>[M(I(e(c)||e(l)("cancel")),1)])]),_:3},8,["disabled"])):g("",!0),h(n.$slots,"center-footer"),e(Ie)?(r(),u(re(e(s).PrimaryButton||e(ue)),{key:1,loading:e(T)||e(O),onClick:C[1]||(C[1]=()=>{var S;return(S=n.drawerApi)==null?void 0:S.onConfirm()})},{default:p(()=>[h(n.$slots,"confirmText",{},()=>[M(I(e(q)||e(l)("confirm")),1)])]),_:3},8,["loading"])):g("",!0)]),h(n.$slots,"append-footer")]),_:3},8,["class"])):g("",!0)]),_:3},8,["append-to","class","modal","open","side","z-index","force-mount","overlay-blur"])]}),_:3},8,["open"])}}});class Et{constructor(t={}){V(this,"sharedData",{payload:{}});V(this,"store");V(this,"api");V(this,"state");const m=t,{connectedComponent:s,onBeforeClose:a,onCancel:i,onClosed:l,onConfirm:f,onOpenChange:_,onOpened:v}=m,c=R(m,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),d={class:"",closable:!0,closeIconPlacement:"right",closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",footer:!0,header:!0,isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,placement:"right",showCancelButton:!0,showConfirmButton:!0,submitting:!1,title:""};this.store=new ht(y(y({},d),c),{onUpdate:()=>{var B,D,T;const b=this.store.state;(b==null?void 0:b.isOpen)===((B=this.state)==null?void 0:B.isOpen)?this.state=b:(this.state=b,(T=(D=this.api).onOpenChange)==null||T.call(D,!!(b!=null&&b.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:a,onCancel:i,onClosed:l,onConfirm:f,onOpenChange:_,onOpened:v},qe(this)}close(){return H(this,null,function*(){var s,a,i;((i=yield(a=(s=this.api).onBeforeClose)==null?void 0:a.call(s))!=null?i:!0)&&this.store.setState(l=>z(y({},l),{isOpen:!1,submitting:!1}))})}getData(){var t,s;return(s=(t=this.sharedData)==null?void 0:t.payload)!=null?s:{}}lock(t=!0){return this.setState({submitting:t})}onCancel(){var t,s;this.api.onCancel?(s=(t=this.api).onCancel)==null||s.call(t):this.close()}onClosed(){var t,s;this.state.isOpen||(s=(t=this.api).onClosed)==null||s.call(t)}onConfirm(){var t,s;(s=(t=this.api).onConfirm)==null||s.call(t)}onOpened(){var t,s;this.state.isOpen&&((s=(t=this.api).onOpened)==null||s.call(t))}open(){this.store.setState(t=>z(y({},t),{isOpen:!0}))}setData(t){return this.sharedData.payload=t,this}setState(t){return Ye(t)?this.store.setState(t):this.store.setState(s=>y(y({},s),t)),this}unlock(){return this.lock(!1)}}const me=Symbol("VBEN_DRAWER_INJECT"),Pt={};function Vt(o={}){var v;const{connectedComponent:t}=o;if(t){const c=Ge({}),d=A(!0);return[w((b,{attrs:B,slots:D})=>(_e(me,{extendApi(q){Object.setPrototypeOf(c,q)},options:o,reCreateDrawer(){return H(this,null,function*(){d.value=!1,yield ge(),d.value=!0})}}),zt(c,y(y(y({},b),B),D)),()=>ie(d.value?t:"div",y(y({},b),B),D)),{name:"VbenParentDrawer",inheritAttrs:!1}),c]}const s=ve(me,{}),a=y(y(y({},Pt),s.options),o);a.onOpenChange=c=>{var d,m,b;(d=o.onOpenChange)==null||d.call(o,c),(b=(m=s.options)==null?void 0:m.onOpenChange)==null||b.call(m,c)};const i=a.onClosed;a.onClosed=()=>{var c;i==null||i(),a.destroyOnClose&&((c=s.reCreateDrawer)==null||c.call(s))};const l=new Et(a),f=l;f.useStore=c=>vt(l.store,c);const _=w((c,{attrs:d,slots:m})=>()=>ie(It,z(y(y({},c),d),{drawerApi:f}),m),{name:"VbenDrawer",inheritAttrs:!1});return(v=s.extendApi)==null||v.call(s,f),[_,f]}function zt(o,t){return H(this,null,function*(){var i;if(!t||Object.keys(t).length===0)return;yield ge();const s=(i=o==null?void 0:o.store)==null?void 0:i.state;if(!s)return;const a=new Set(Object.keys(s));for(const l of Object.keys(t))a.has(l)&&!["class"].includes(l)&&console.warn(`[Vben Drawer]: When 'connectedComponent' exists, do not set props or slots '${l}', which will increase complexity. If you need to modify the props of Drawer, please use useVbenDrawer or api.`)})}export{$t as V,pe as X,Vt as u};
