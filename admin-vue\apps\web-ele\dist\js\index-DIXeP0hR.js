import{b3 as s,b4 as o,aA as h,b5 as d,ax as v,b6 as m,az as p,b7 as b,k as P}from"./bootstrap-CYivmKoJ.js";import{i as g,a as S,b as y}from"./error-CYrjCQ5V.js";function T(n){return n}function w(n,e,a){switch(a.length){case 0:return n.call(e);case 1:return n.call(e,a[0]);case 2:return n.call(e,a[0],a[1]);case 3:return n.call(e,a[0],a[1],a[2])}return n.apply(e,a)}var x=800,E=16,N=Date.now;function O(n){var e=0,a=0;return function(){var t=N(),r=E-(t-a);if(a=t,r>0){if(++e>=x)return arguments[0]}else e=0;return n.apply(void 0,arguments)}}function A(n){return function(){return n}}var C=s?function(n,e){return s(n,"toString",{configurable:!0,enumerable:!1,value:A(e),writable:!0})}:T,_=O(C),f=Math.max;function I(n,e,a){return e=f(e===void 0?n.length-1:e,0),function(){for(var t=arguments,r=-1,i=f(t.length-e,0),u=Array(i);++r<i;)u[r]=t[e+r];r=-1;for(var l=Array(e+1);++r<e;)l[r]=t[r];return l[e]=a(u),w(n,this,l)}}var c=o?o.isConcatSpreadable:void 0;function H(n){return h(n)||g(n)||!!(c&&n&&n[c])}function U(n,e,a,t,r){var i=-1,u=n.length;for(a||(a=H),r||(r=[]);++i<u;){var l=n[i];a(l)?S(r,l):t||(r[r.length]=l)}return r}function V(n){var e=n==null?0:n.length;return e?U(n):[]}function k(n){return _(I(n,void 0,V),n+"")}function D(n,e){return n!=null&&e in Object(n)}function L(n,e,a){e=d(e,n);for(var t=-1,r=e.length,i=!1;++t<r;){var u=v(e[t]);if(!(i=n!=null&&a(n,u)))break;n=n[u]}return i||++t!=r?i:(r=n==null?0:n.length,!!r&&y(r)&&m(u,r)&&(h(n)||g(n)))}function M(n,e){return n!=null&&L(n,e,D)}function z(n,e,a){for(var t=-1,r=e.length,i={};++t<r;){var u=e[t],l=p(n,u);a(l,u)&&b(i,d(u,n),l)}return i}function F(n,e){return z(n,e,function(a,t){return M(n,t)})}var G=k(function(n,e){return n==null?{}:F(n,e)});const $="update:modelValue",q="change",J="input",R=P({ariaLabel:String,ariaOrientation:{type:String,values:["horizontal","vertical","undefined"]},ariaControls:String}),Q=n=>G(R,n);export{q as C,J as I,$ as U,U as b,V as f,M as h,T as i,I as o,G as p,_ as s,Q as u};
