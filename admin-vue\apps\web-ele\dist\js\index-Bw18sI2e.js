var $=Object.defineProperty,x=Object.defineProperties;var R=Object.getOwnPropertyDescriptors;var v=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var k=(e,s,o)=>s in e?$(e,s,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[s]=o,C=(e,s)=>{for(var o in s||(s={}))V.call(s,o)&&k(e,o,s[o]);if(v)for(var o of v(s))P.call(s,o)&&k(e,o,s[o]);return e},_=(e,s)=>x(e,R(s));import{b as D}from"./_baseFindIndex-D7XfJLKM.js";import{b as F}from"./_baseIteratee-DIAZWcrk.js";import{k as U,P as j,m as q,n as H,ad as y,B as I,y as S,ae as K,w as L,ab as O}from"./bootstrap-CYivmKoJ.js";import{u as Q}from"./use-form-common-props-DZjBwEkr.js";import{d as z,e as w,f as A,q as f,g as m,j as g,h as B,l as T,n as c,u as t,D as b,E,m as M,Q as G,r as J}from"../jse/index-index-SSqEGcIT.js";function re(e,s,o){var l=e==null?0:e.length;if(!l)return-1;var n=l-1;return D(e,F(s),n,!0)}const ie=(e="")=>e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d"),X=U({type:{type:String,values:["primary","success","info","warning","danger"],default:"primary"},closable:Boolean,disableTransitions:Boolean,hit:Boolean,color:String,size:{type:String,values:j},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),Y={close:e=>e instanceof MouseEvent,click:e=>e instanceof MouseEvent},Z=z({name:"ElTag"}),ee=z(_(C({},Z),{props:X,emits:Y,setup(e,{emit:s}){const o=e,l=Q(),n=H("tag"),r=w(()=>{const{type:a,hit:u,effect:d,closable:p,round:W}=o;return[n.b(),n.is("closable",p),n.m(a||"primary"),n.m(l.value),n.m(d),n.is("hit",u),n.is("round",W)]}),i=a=>{s("close",a)},h=a=>{s("click",a)},N=a=>{var u,d,p;(p=(d=(u=a==null?void 0:a.component)==null?void 0:u.subTree)==null?void 0:d.component)!=null&&p.bum&&(a.component.subTree.component.bum=null)};return(a,u)=>a.disableTransitions?(m(),A("span",{key:0,class:c(t(r)),style:M({backgroundColor:a.color}),onClick:h},[g("span",{class:c(t(n).e("content"))},[T(a.$slots,"default")],2),a.closable?(m(),f(t(S),{key:0,class:c(t(n).e("close")),onClick:I(i,["stop"])},{default:b(()=>[E(t(y))]),_:1},8,["class","onClick"])):B("v-if",!0)],6)):(m(),f(K,{key:1,name:`${t(n).namespace.value}-zoom-in-center`,appear:"",onVnodeMounted:N},{default:b(()=>[g("span",{class:c(t(r)),style:M({backgroundColor:a.color}),onClick:h},[g("span",{class:c(t(n).e("content"))},[T(a.$slots,"default")],2),a.closable?(m(),f(t(S),{key:0,class:c(t(n).e("close")),onClick:I(i,["stop"])},{default:b(()=>[E(t(y))]),_:1},8,["class","onClick"])):B("v-if",!0)],6)]),_:3},8,["name"]))}}));var se=q(ee,[["__file","tag.vue"]]);const ue=L(se);function de(){const e=G(),s=J(0),o=11,l=w(()=>({minWidth:`${Math.max(s.value,o)}px`}));return O(e,()=>{var r,i;s.value=(i=(r=e.value)==null?void 0:r.getBoundingClientRect().width)!=null?i:0}),{calculatorRef:e,calculatorWidth:s,inputStyle:l}}export{ue as E,ie as e,re as f,X as t,de as u};
