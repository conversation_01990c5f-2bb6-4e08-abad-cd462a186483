var se=Object.defineProperty,re=Object.defineProperties;var ne=Object.getOwnPropertyDescriptors;var V=Object.getOwnPropertySymbols;var ie=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable;var M=Math.pow,G=(i,l,a)=>l in i?se(i,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):i[l]=a,A=(i,l)=>{for(var a in l||(l={}))ie.call(l,a)&&G(i,a,l[a]);if(V)for(var a of V(l))ce.call(l,a)&&G(i,a,l[a]);return i},I=(i,l)=>re(i,ne(l));import{k as K,m as U,n as te,F as W,B as ue,ar as ve,ae as fe,a as me,o as B,l as de,T as X,ab as pe,w as he}from"./bootstrap-CYivmKoJ.js";import{B as be,r as ye,G as L}from"./index-owS4PRxE.js";import{t as ge}from"./error-CYrjCQ5V.js";import{d as O,v as ae,r as f,e as C,aa as we,y as Y,q as D,g as N,D as le,N as Se,j,n as P,u as g,m as $,f as oe,E as J,F as ze,w as Q,a9 as Ee,L as _e,ab as He,o as Te,p as Z,M as Le,h as Ce,l as ke,B as Be,a3 as Ne}from"../jse/index-index-SSqEGcIT.js";import{u as Pe}from"./index-DIXeP0hR.js";const q=Symbol("scrollbarContextKey"),Re=K({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean}),Me="Thumb",Oe=O({__name:"thumb",props:Re,setup(i){const l=i,a=ae(q),s=te("scrollbar");a||ge(Me,"can not inject scrollbar context");const v=f(),m=f(),p=f({}),u=f(!1);let c=!1,d=!1,T=0,o=0,r=me?document.onselectstart:null;const t=C(()=>be[l.vertical?"vertical":"horizontal"]),h=C(()=>ye({size:l.size,move:l.move,bar:t.value})),S=C(()=>M(v.value[t.value.offset],2)/a.wrapElement[t.value.scrollSize]/l.ratio/m.value[t.value.offset]),_=n=>{var y;if(n.stopPropagation(),n.ctrlKey||[1,2].includes(n.button))return;(y=window.getSelection())==null||y.removeAllRanges(),z(n);const H=n.currentTarget;H&&(p.value[t.value.axis]=H[t.value.offset]-(n[t.value.client]-H.getBoundingClientRect()[t.value.direction]))},w=n=>{if(!m.value||!v.value||!a.wrapElement)return;const y=Math.abs(n.target.getBoundingClientRect()[t.value.direction]-n[t.value.client]),H=m.value[t.value.offset]/2,x=(y-H)*100*S.value/v.value[t.value.offset];a.wrapElement[t.value.scroll]=x*a.wrapElement[t.value.scrollSize]/100},z=n=>{n.stopImmediatePropagation(),c=!0,T=a.wrapElement.scrollHeight,o=a.wrapElement.scrollWidth,document.addEventListener("mousemove",R),document.addEventListener("mouseup",k),r=document.onselectstart,document.onselectstart=()=>!1},R=n=>{if(!v.value||!m.value||c===!1)return;const y=p.value[t.value.axis];if(!y)return;const H=(v.value.getBoundingClientRect()[t.value.direction]-n[t.value.client])*-1,x=m.value[t.value.offset]-y,F=(H-x)*100*S.value/v.value[t.value.offset];t.value.scroll==="scrollLeft"?a.wrapElement[t.value.scroll]=F*o/100:a.wrapElement[t.value.scroll]=F*T/100},k=()=>{c=!1,p.value[t.value.axis]=0,document.removeEventListener("mousemove",R),document.removeEventListener("mouseup",k),b(),d&&(u.value=!1)},E=()=>{d=!1,u.value=!!l.size},e=()=>{d=!0,u.value=c};we(()=>{b(),document.removeEventListener("mouseup",k)});const b=()=>{document.onselectstart!==r&&(document.onselectstart=r)};return W(Y(a,"scrollbarElement"),"mousemove",E),W(Y(a,"scrollbarElement"),"mouseleave",e),(n,y)=>(N(),D(fe,{name:g(s).b("fade"),persisted:""},{default:le(()=>[Se(j("div",{ref_key:"instance",ref:v,class:P([g(s).e("bar"),g(s).is(g(t).key)]),onMousedown:w,onClick:ue(()=>{},["stop"])},[j("div",{ref_key:"thumb",ref:m,class:P(g(s).e("thumb")),style:$(g(h)),onMousedown:_},null,38)],42,["onClick"]),[[ve,n.always||u.value]])]),_:1},8,["name"]))}});var ee=U(Oe,[["__file","thumb.vue"]]);const xe=K({always:{type:Boolean,default:!0},minSize:{type:Number,required:!0}}),Ae=O({__name:"bar",props:xe,setup(i,{expose:l}){const a=i,s=ae(q),v=f(0),m=f(0),p=f(""),u=f(""),c=f(1),d=f(1);return l({handleScroll:r=>{if(r){const t=r.offsetHeight-L,h=r.offsetWidth-L;m.value=r.scrollTop*100/t*c.value,v.value=r.scrollLeft*100/h*d.value}},update:()=>{const r=s==null?void 0:s.wrapElement;if(!r)return;const t=r.offsetHeight-L,h=r.offsetWidth-L,S=M(t,2)/r.scrollHeight,_=M(h,2)/r.scrollWidth,w=Math.max(S,a.minSize),z=Math.max(_,a.minSize);c.value=S/(t-S)/(w/(t-w)),d.value=_/(h-_)/(z/(h-z)),u.value=w+L<t?`${w}px`:"",p.value=z+L<h?`${z}px`:""}}),(r,t)=>(N(),oe(ze,null,[J(ee,{move:v.value,ratio:d.value,size:p.value,always:r.always},null,8,["move","ratio","size","always"]),J(ee,{move:m.value,ratio:c.value,size:u.value,vertical:"",always:r.always},null,8,["move","ratio","size","always"])],64))}});var We=U(Ae,[["__file","bar.vue"]]);const De=K(A({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:{type:Boolean,default:!1},wrapStyle:{type:de([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20},tabindex:{type:[String,Number],default:void 0},id:String,role:String},Pe(["ariaLabel","ariaOrientation"]))),je={"end-reached":i=>["left","right","top","bottom"].includes(i),scroll:({scrollTop:i,scrollLeft:l})=>[i,l].every(B)},$e="ElScrollbar",Ke=O({name:$e}),Ue=O(I(A({},Ke),{props:De,emits:je,setup(i,{expose:l,emit:a}){const s=i,v=te("scrollbar");let m,p,u=0,c=0,d="";const T=f(),o=f(),r=f(),t=f(),h=C(()=>{const e={};return s.height&&(e.height=X(s.height)),s.maxHeight&&(e.maxHeight=X(s.maxHeight)),[s.wrapStyle,e]}),S=C(()=>[s.wrapClass,v.e("wrap"),{[v.em("wrap","hidden-default")]:!s.native}]),_=C(()=>[v.e("view"),s.viewClass]),w=()=>{var e;if(o.value){(e=t.value)==null||e.handleScroll(o.value);const b=u,n=c;u=o.value.scrollTop,c=o.value.scrollLeft;const y={bottom:u+o.value.clientHeight>=o.value.scrollHeight,top:u<=0&&b!==0,right:c+o.value.clientWidth>=o.value.scrollWidth&&n!==c,left:c<=0&&n!==0};b!==u&&(d=u>b?"bottom":"top"),n!==c&&(d=c>n?"right":"left"),a("scroll",{scrollTop:u,scrollLeft:c}),y[d]&&a("end-reached",d)}};function z(e,b){Ne(e)?o.value.scrollTo(e):B(e)&&B(b)&&o.value.scrollTo(e,b)}const R=e=>{B(e)&&(o.value.scrollTop=e)},k=e=>{B(e)&&(o.value.scrollLeft=e)},E=()=>{var e;(e=t.value)==null||e.update()};return Q(()=>s.noresize,e=>{e?(m==null||m(),p==null||p()):({stop:m}=pe(r,E),p=W("resize",E))},{immediate:!0}),Q(()=>[s.maxHeight,s.height],()=>{s.native||Z(()=>{var e;E(),o.value&&((e=t.value)==null||e.handleScroll(o.value))})}),Ee(q,_e({scrollbarElement:T,wrapElement:o})),He(()=>{o.value&&(o.value.scrollTop=u,o.value.scrollLeft=c)}),Te(()=>{s.native||Z(()=>{E()})}),Le(()=>E()),l({wrapRef:o,update:E,scrollTo:z,setScrollTop:R,setScrollLeft:k,handleScroll:w}),(e,b)=>(N(),oe("div",{ref_key:"scrollbarRef",ref:T,class:P(g(v).b())},[j("div",{ref_key:"wrapRef",ref:o,class:P(g(S)),style:$(g(h)),tabindex:e.tabindex,onScroll:w},[(N(),D(Be(e.tag),{id:e.id,ref_key:"resizeRef",ref:r,class:P(g(_)),style:$(e.viewStyle),role:e.role,"aria-label":e.ariaLabel,"aria-orientation":e.ariaOrientation},{default:le(()=>[ke(e.$slots,"default")]),_:3},8,["id","class","style","role","aria-label","aria-orientation"]))],46,["tabindex"]),e.native?Ce("v-if",!0):(N(),D(We,{key:0,ref_key:"barRef",ref:t,always:e.always,"min-size":e.minSize},null,8,["always","min-size"]))],2))}}));var qe=U(Ue,[["__file","scrollbar.vue"]]);const Je=he(qe);export{Je as E,je as s};
