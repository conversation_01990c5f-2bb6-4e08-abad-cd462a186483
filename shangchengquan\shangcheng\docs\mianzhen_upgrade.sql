-- 面诊功能数据库升级脚本
-- 2025-01-17 扩展舌诊系统支持面诊和综合诊疗

-- 1. 扩展舌诊记录表，增加面诊相关字段
ALTER TABLE `ddwx_shezhen_record`
ADD COLUMN `face_image` varchar(500) DEFAULT '' COMMENT '面部图片URL' AFTER `tongue_image`,
ADD COLUMN `sublingual_image` varchar(500) DEFAULT '' COMMENT '舌下脉络图片URL' AFTER `face_image`,
ADD COLUMN `diagnosis_type` tinyint(1) DEFAULT 1 COMMENT '诊疗类型：1=舌诊，2=面诊，3=综合诊疗' AFTER `sublingual_image`,
ADD COLUMN `face_features` text COMMENT '面部特征分析结果JSON' AFTER `analysis_result`,
ADD COLUMN `sublingual_features` text COMMENT '舌下脉络特征分析结果JSON' AFTER `face_features`,
ADD COLUMN `comprehensive_analysis` text COMMENT '综合分析结果JSON' AFTER `sublingual_features`;

-- 2. 扩展舌诊报告表，增加面诊相关字段
ALTER TABLE `ddwx_shezhen_report`
ADD COLUMN `face_analysis` text COMMENT '面部分析结果' AFTER `constitution_analysis`,
ADD COLUMN `sublingual_analysis` text COMMENT '舌下脉络分析结果' AFTER `face_analysis`,
ADD COLUMN `comprehensive_score` decimal(5,2) DEFAULT 0.00 COMMENT '综合健康评分' AFTER `constitution_score`,
ADD COLUMN `diagnosis_type` tinyint(1) DEFAULT 1 COMMENT '诊疗类型：1=舌诊，2=面诊，3=综合诊疗' AFTER `comprehensive_score`;

-- 3. 扩展舌诊设置表，增加面诊配置
ALTER TABLE `ddwx_shezhen_set`
ADD COLUMN `face_diagnosis_enable` tinyint(1) DEFAULT 1 COMMENT '是否启用面诊功能' AFTER `is_open`,
ADD COLUMN `comprehensive_diagnosis_enable` tinyint(1) DEFAULT 1 COMMENT '是否启用综合诊疗功能' AFTER `face_diagnosis_enable`,
ADD COLUMN `face_price` decimal(10,2) DEFAULT 0.00 COMMENT '面诊单价' AFTER `single_price`,
ADD COLUMN `comprehensive_price` decimal(10,2) DEFAULT 0.00 COMMENT '综合诊疗单价' AFTER `face_price`,
ADD COLUMN `face_free_level` varchar(100) DEFAULT '' COMMENT '面诊免费会员等级' AFTER `free_level`,
ADD COLUMN `comprehensive_free_level` varchar(100) DEFAULT '' COMMENT '综合诊疗免费会员等级' AFTER `face_free_level`,
ADD COLUMN `face_daily_free_count` int(11) DEFAULT 1 COMMENT '面诊每日免费次数' AFTER `daily_free_count`,
ADD COLUMN `comprehensive_daily_free_count` int(11) DEFAULT 1 COMMENT '综合诊疗每日免费次数' AFTER `face_daily_free_count`;

-- 4. 扩展推荐商品表，增加诊疗类型字段
ALTER TABLE `ddwx_shezhen_recommend_product`
ADD COLUMN `diagnosis_type` tinyint(1) DEFAULT 1 COMMENT '适用诊疗类型：1=舌诊，2=面诊，3=综合诊疗，0=全部' AFTER `recommend_type`;

-- 5. 扩展API调用日志表，增加诊疗类型字段
ALTER TABLE `ddwx_mianzhen_call_log`
ADD COLUMN `diagnosis_type` tinyint(1) DEFAULT 1 COMMENT '诊疗类型：1=舌诊，2=面诊，3=综合诊疗' AFTER `api_type`;

-- 6. 扩展session表，增加诊疗类型字段
ALTER TABLE `ddwx_mianzhen_sessions`
ADD COLUMN `diagnosis_type` tinyint(1) DEFAULT 1 COMMENT '诊疗类型：1=舌诊，2=面诊，3=综合诊疗' AFTER `session_id`;

-- 7. 创建面诊特征配置表（可选，用于配置面部特征分析规则）
CREATE TABLE IF NOT EXISTS `ddwx_face_features_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT 0 COMMENT '应用ID',
  `feature_name` varchar(50) NOT NULL COMMENT '特征名称',
  `feature_category` varchar(30) NOT NULL COMMENT '特征分类',
  `description` text COMMENT '特征描述',
  `analysis_rules` text COMMENT '分析规则JSON',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `createtime` int(11) DEFAULT 0 COMMENT '创建时间',
  `updatetime` int(11) DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `aid` (`aid`),
  KEY `feature_category` (`feature_category`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='面诊特征配置表';

-- 8. 插入默认面诊特征配置数据
INSERT INTO `ddwx_face_features_config` (`aid`, `feature_name`, `feature_category`, `description`, `analysis_rules`, `sort`, `status`, `createtime`, `updatetime`) VALUES
(0, '面色', '面部色泽', '面部整体颜色', '{"normal": "红润有光泽", "abnormal": ["苍白", "潮红", "暗黄", "青紫"]}', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(0, '眼神', '五官特征', '眼部神态', '{"normal": "有神明亮", "abnormal": ["无神", "呆滞", "浮肿"]}', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(0, '唇色', '五官特征', '嘴唇颜色', '{"normal": "淡红润泽", "abnormal": ["苍白", "深红", "紫暗", "干燥"]}', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(0, '面部光泽', '面部色泽', '面部光泽度', '{"normal": "明润有光", "abnormal": ["晦暗无光", "油腻", "干燥"]}', 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 9. 更新现有数据的诊疗类型（将现有记录标记为舌诊）
UPDATE `ddwx_shezhen_record` SET `diagnosis_type` = 1 WHERE `diagnosis_type` IS NULL OR `diagnosis_type` = 0;
UPDATE `ddwx_shezhen_report` SET `diagnosis_type` = 1 WHERE `diagnosis_type` IS NULL OR `diagnosis_type` = 0;
UPDATE `ddwx_shezhen_recommend_product` SET `diagnosis_type` = 1 WHERE `diagnosis_type` IS NULL OR `diagnosis_type` = 0;
UPDATE `ddwx_mianzhen_call_log` SET `diagnosis_type` = 1 WHERE `diagnosis_type` IS NULL OR `diagnosis_type` = 0;
UPDATE `ddwx_mianzhen_sessions` SET `diagnosis_type` = 1 WHERE `diagnosis_type` IS NULL OR `diagnosis_type` = 0;

-- 10. 创建索引优化查询性能
ALTER TABLE `ddwx_shezhen_record` ADD INDEX `idx_diagnosis_type` (`diagnosis_type`);
ALTER TABLE `ddwx_shezhen_report` ADD INDEX `idx_diagnosis_type` (`diagnosis_type`);
ALTER TABLE `ddwx_shezhen_recommend_product` ADD INDEX `idx_diagnosis_type` (`diagnosis_type`);
ALTER TABLE `ddwx_mianzhen_call_log` ADD INDEX `idx_diagnosis_type` (`diagnosis_type`);
ALTER TABLE `ddwx_mianzhen_sessions` ADD INDEX `idx_diagnosis_type` (`diagnosis_type`);