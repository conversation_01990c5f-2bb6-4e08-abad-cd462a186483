var Qe=Object.defineProperty,Ze=Object.defineProperties;var Je=Object.getOwnPropertyDescriptors;var xe=Object.getOwnPropertySymbols;var et=Object.prototype.hasOwnProperty,tt=Object.prototype.propertyIsEnumerable;var we=(a,l,n)=>l in a?Qe(a,l,{enumerable:!0,configurable:!0,writable:!0,value:n}):a[l]=n,D=(a,l)=>{for(var n in l||(l={}))et.call(l,n)&&we(a,n,l[n]);if(xe)for(var n of xe(l))tt.call(l,n)&&we(a,n,l[n]);return a},Se=(a,l)=>Ze(a,Je(l));var G=(a,l,n)=>new Promise((o,d)=>{var c=r=>{try{b(n.next(r))}catch(x){d(x)}},i=r=>{try{b(n.throw(r))}catch(x){d(x)}},b=r=>r.done?o(r.value):Promise.resolve(r.value).then(c,i);b((n=n.apply(a,l)).next())});import{o as Ce,k as ot,a6 as at,l as Q,R as Ee,q as nt,a7 as st,m as lt,n as Ie,a8 as rt,a9 as it,aa as ut,p as dt,ab as ct,y as B,ac as pt,B as ft,a as vt,w as mt}from"./bootstrap-CYivmKoJ.js";import{i as ht}from"./browser-CSPQ6ERn.js";import{U as ae,u as yt,I as Pe,C as ze}from"./index-DIXeP0hR.js";import{P as Z,e as f,x as gt,d as Be,a0 as bt,a1 as xt,Q as J,r as ee,w as te,o as wt,p as O,y as St,f as w,g as p,h as v,F as oe,j as T,n as m,u as t,l as j,q as C,D as R,B as K,U as Ne,E as Ct,a2 as Et,t as U,m as Te,a3 as ke}from"../jse/index-index-SSqEGcIT.js";import{u as It,a as Pt}from"./use-form-item-iUVikjOD.js";import{u as zt,b as Nt}from"./use-form-common-props-DZjBwEkr.js";import{u as Tt,a as kt}from"./index-CdkCbLvc.js";import{d as Fe}from"./error-CYrjCQ5V.js";let g;const Ft={height:"0",visibility:"hidden",overflow:ht()?"":"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0"},Vt=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing"];function Mt(a){const l=window.getComputedStyle(a),n=l.getPropertyValue("box-sizing"),o=Number.parseFloat(l.getPropertyValue("padding-bottom"))+Number.parseFloat(l.getPropertyValue("padding-top")),d=Number.parseFloat(l.getPropertyValue("border-bottom-width"))+Number.parseFloat(l.getPropertyValue("border-top-width"));return{contextStyle:Vt.map(i=>[i,l.getPropertyValue(i)]),paddingSize:o,borderSize:d,boxSizing:n}}function Ve(a,l=1,n){var o;g||(g=document.createElement("textarea"),document.body.appendChild(g));const{paddingSize:d,borderSize:c,boxSizing:i,contextStyle:b}=Mt(a);b.forEach(([y,k])=>g==null?void 0:g.style.setProperty(y,k)),Object.entries(Ft).forEach(([y,k])=>g==null?void 0:g.style.setProperty(y,k,"important")),g.value=a.value||a.placeholder||"";let r=g.scrollHeight;const x={};i==="border-box"?r=r+c:i==="content-box"&&(r=r-d),g.value="";const h=g.scrollHeight-d;if(Ce(l)){let y=h*l;i==="border-box"&&(y=y+d+c),r=Math.max(y,r),x.minHeight=`${y}px`}if(Ce(n)){let y=h*n;i==="border-box"&&(y=y+d+c),r=Math.min(y,r)}return x.height=`${r}px`,(o=g.parentNode)==null||o.removeChild(g),g=void 0,x}const Oe=ot(D({id:{type:String,default:void 0},size:nt,disabled:Boolean,modelValue:{type:Q([String,Number,Object]),default:""},maxlength:{type:[String,Number]},minlength:{type:[String,Number]},type:{type:String,default:"text"},resize:{type:String,values:["none","both","horizontal","vertical"]},autosize:{type:Q([Boolean,Object]),default:!1},autocomplete:{type:String,default:"off"},formatter:{type:Function},parser:{type:Function},placeholder:{type:String},form:{type:String},readonly:Boolean,clearable:Boolean,showPassword:Boolean,showWordLimit:Boolean,suffixIcon:{type:Ee},prefixIcon:{type:Ee},containerRole:{type:String,default:void 0},tabindex:{type:[String,Number],default:0},validateEvent:{type:Boolean,default:!0},inputStyle:{type:Q([Object,Array,String]),default:()=>at({})},autofocus:Boolean,rows:{type:Number,default:2}},yt(["ariaLabel"]))),Re={[ae]:a=>Z(a),input:a=>Z(a),change:a=>Z(a),focus:a=>a instanceof FocusEvent,blur:a=>a instanceof FocusEvent,clear:()=>!0,mouseleave:a=>a instanceof MouseEvent,mouseenter:a=>a instanceof MouseEvent,keydown:a=>a instanceof Event,compositionstart:a=>a instanceof CompositionEvent,compositionupdate:a=>a instanceof CompositionEvent,compositionend:a=>a instanceof CompositionEvent},Bt=["class","style"],Ot=/^on[A-Z]/,Rt=(a={})=>{const{excludeListeners:l=!1,excludeKeys:n}=a,o=f(()=>((n==null?void 0:n.value)||[]).concat(Bt)),d=gt();return d?f(()=>{var c;return st(Object.entries((c=d.proxy)==null?void 0:c.$attrs).filter(([i])=>!o.value.includes(i)&&!(l&&Ot.test(i))))}):f(()=>({}))};function Lt(a){let l;function n(){if(a.value==null)return;const{selectionStart:d,selectionEnd:c,value:i}=a.value;if(d==null||c==null)return;const b=i.slice(0,Math.max(0,d)),r=i.slice(Math.max(0,c));l={selectionStart:d,selectionEnd:c,value:i,beforeTxt:b,afterTxt:r}}function o(){if(a.value==null||l==null)return;const{value:d}=a.value,{beforeTxt:c,afterTxt:i,selectionStart:b}=l;if(c==null||i==null||b==null)return;let r=d.length;if(d.endsWith(i))r=d.length-i.length;else if(d.startsWith(c))r=c.length;else{const x=c[b-1],h=d.indexOf(x,b-1);h!==-1&&(r=h+1)}a.value.setSelectionRange(r,r)}return[n,o]}const Ht="ElInput",$t=Be({name:Ht,inheritAttrs:!1}),At=Be(Se(D({},$t),{props:Oe,emits:Re,setup(a,{expose:l,emit:n}){const o=a,d=bt(),c=Rt(),i=xt(),b=f(()=>[o.type==="textarea"?ne.b():s.b(),s.m(k.value),s.is("disabled",E.value),s.is("exceed",je.value),{[s.b("group")]:i.prepend||i.append,[s.m("prefix")]:i.prefix||o.prefixIcon,[s.m("suffix")]:i.suffix||o.suffixIcon||o.clearable||o.showPassword,[s.bm("suffix","password-clear")]:A.value&&Y.value,[s.b("hidden")]:o.type==="hidden"},d.class]),r=f(()=>[s.e("wrapper"),s.is("focus",W.value)]),{form:x,formItem:h}=It(),{inputId:y}=Pt(o,{formItemContext:h}),k=zt(),E=Nt(),s=Ie("input"),ne=Ie("textarea"),L=J(),S=J(),_=ee(!1),H=ee(!1),se=ee(),$=J(o.inputStyle),P=f(()=>L.value||S.value),{wrapperRef:Le,isFocused:W,handleFocus:He,handleBlur:$e}=Tt(P,{beforeFocus(){return E.value},afterBlur(){var e;o.validateEvent&&((e=h==null?void 0:h.validate)==null||e.call(h,"blur").catch(u=>Fe()))}}),le=f(()=>{var e;return(e=x==null?void 0:x.statusIcon)!=null?e:!1}),F=f(()=>(h==null?void 0:h.validateState)||""),re=f(()=>F.value&&rt[F.value]),Ae=f(()=>H.value?it:ut),De=f(()=>[d.style]),ie=f(()=>[o.inputStyle,$.value,{resize:o.resize}]),I=f(()=>dt(o.modelValue)?"":String(o.modelValue)),A=f(()=>o.clearable&&!E.value&&!o.readonly&&!!I.value&&(W.value||_.value)),Y=f(()=>o.showPassword&&!E.value&&!!I.value),z=f(()=>o.showWordLimit&&!!o.maxlength&&(o.type==="text"||o.type==="textarea")&&!E.value&&!o.readonly&&!o.showPassword),X=f(()=>I.value.length),je=f(()=>!!z.value&&X.value>Number(o.maxlength)),Ke=f(()=>!!i.suffix||!!o.suffixIcon||A.value||o.showPassword||z.value||!!F.value&&le.value),[ue,de]=Lt(L);ct(S,e=>{if(Ue(),!z.value||o.resize!=="both")return;const u=e[0],{width:N}=u.contentRect;se.value={right:`calc(100% - ${N+15+6}px)`}});const V=()=>{const{type:e,autosize:u}=o;if(!(!vt||e!=="textarea"||!S.value))if(u){const N=ke(u)?u.minRows:void 0,ge=ke(u)?u.maxRows:void 0,be=Ve(S.value,N,ge);$.value=D({overflowY:"hidden"},be),O(()=>{S.value.offsetHeight,$.value=be})}else $.value={minHeight:Ve(S.value).minHeight}},Ue=(e=>{let u=!1;return()=>{var N;if(u||!o.autosize)return;((N=S.value)==null?void 0:N.offsetParent)===null||(e(),u=!0)}})(V),M=()=>{const e=P.value,u=o.formatter?o.formatter(I.value):I.value;!e||e.value===u||(e.value=u)},q=e=>G(null,null,function*(){ue();let{value:u}=e.target;if(o.formatter&&o.parser&&(u=o.parser(u)),!pe.value){if(u===I.value){M();return}n(ae,u),n(Pe,u),yield O(),M(),de()}}),ce=e=>{let{value:u}=e.target;o.formatter&&o.parser&&(u=o.parser(u)),n(ze,u)},{isComposing:pe,handleCompositionStart:fe,handleCompositionUpdate:ve,handleCompositionEnd:me}=kt({emit:n,afterComposition:q}),_e=()=>{ue(),H.value=!H.value,setTimeout(de)},We=()=>{var e;return(e=P.value)==null?void 0:e.focus()},Ye=()=>{var e;return(e=P.value)==null?void 0:e.blur()},Xe=e=>{_.value=!1,n("mouseleave",e)},qe=e=>{_.value=!0,n("mouseenter",e)},he=e=>{n("keydown",e)},Ge=()=>{var e;(e=P.value)==null||e.select()},ye=()=>{n(ae,""),n(ze,""),n("clear"),n(Pe,"")};return te(()=>o.modelValue,()=>{var e;O(()=>V()),o.validateEvent&&((e=h==null?void 0:h.validate)==null||e.call(h,"change").catch(u=>Fe()))}),te(I,()=>M()),te(()=>o.type,()=>G(null,null,function*(){yield O(),M(),V()})),wt(()=>{!o.formatter&&o.parser,M(),O(V)}),l({input:L,textarea:S,ref:P,textareaStyle:ie,autosize:St(o,"autosize"),isComposing:pe,focus:We,blur:Ye,select:Ge,clear:ye,resizeTextarea:V}),(e,u)=>(p(),w("div",{class:m([t(b),{[t(s).bm("group","append")]:e.$slots.append,[t(s).bm("group","prepend")]:e.$slots.prepend}]),style:Te(t(De)),onMouseenter:qe,onMouseleave:Xe},[v(" input "),e.type!=="textarea"?(p(),w(oe,{key:0},[v(" prepend slot "),e.$slots.prepend?(p(),w("div",{key:0,class:m(t(s).be("group","prepend"))},[j(e.$slots,"prepend")],2)):v("v-if",!0),T("div",{ref_key:"wrapperRef",ref:Le,class:m(t(r))},[v(" prefix slot "),e.$slots.prefix||e.prefixIcon?(p(),w("span",{key:0,class:m(t(s).e("prefix"))},[T("span",{class:m(t(s).e("prefix-inner"))},[j(e.$slots,"prefix"),e.prefixIcon?(p(),C(t(B),{key:0,class:m(t(s).e("icon"))},{default:R(()=>[(p(),C(K(e.prefixIcon)))]),_:1},8,["class"])):v("v-if",!0)],2)],2)):v("v-if",!0),T("input",Ne({id:t(y),ref_key:"input",ref:L,class:t(s).e("inner")},t(c),{minlength:e.minlength,maxlength:e.maxlength,type:e.showPassword?H.value?"text":"password":e.type,disabled:t(E),readonly:e.readonly,autocomplete:e.autocomplete,tabindex:e.tabindex,"aria-label":e.ariaLabel,placeholder:e.placeholder,style:e.inputStyle,form:e.form,autofocus:e.autofocus,role:e.containerRole,onCompositionstart:t(fe),onCompositionupdate:t(ve),onCompositionend:t(me),onInput:q,onChange:ce,onKeydown:he}),null,16,["id","minlength","maxlength","type","disabled","readonly","autocomplete","tabindex","aria-label","placeholder","form","autofocus","role","onCompositionstart","onCompositionupdate","onCompositionend"]),v(" suffix slot "),t(Ke)?(p(),w("span",{key:1,class:m(t(s).e("suffix"))},[T("span",{class:m(t(s).e("suffix-inner"))},[!t(A)||!t(Y)||!t(z)?(p(),w(oe,{key:0},[j(e.$slots,"suffix"),e.suffixIcon?(p(),C(t(B),{key:0,class:m(t(s).e("icon"))},{default:R(()=>[(p(),C(K(e.suffixIcon)))]),_:1},8,["class"])):v("v-if",!0)],64)):v("v-if",!0),t(A)?(p(),C(t(B),{key:1,class:m([t(s).e("icon"),t(s).e("clear")]),onMousedown:ft(t(Et),["prevent"]),onClick:ye},{default:R(()=>[Ct(t(pt))]),_:1},8,["class","onMousedown"])):v("v-if",!0),t(Y)?(p(),C(t(B),{key:2,class:m([t(s).e("icon"),t(s).e("password")]),onClick:_e},{default:R(()=>[(p(),C(K(t(Ae))))]),_:1},8,["class"])):v("v-if",!0),t(z)?(p(),w("span",{key:3,class:m(t(s).e("count"))},[T("span",{class:m(t(s).e("count-inner"))},U(t(X))+" / "+U(e.maxlength),3)],2)):v("v-if",!0),t(F)&&t(re)&&t(le)?(p(),C(t(B),{key:4,class:m([t(s).e("icon"),t(s).e("validateIcon"),t(s).is("loading",t(F)==="validating")])},{default:R(()=>[(p(),C(K(t(re))))]),_:1},8,["class"])):v("v-if",!0)],2)],2)):v("v-if",!0)],2),v(" append slot "),e.$slots.append?(p(),w("div",{key:1,class:m(t(s).be("group","append"))},[j(e.$slots,"append")],2)):v("v-if",!0)],64)):(p(),w(oe,{key:1},[v(" textarea "),T("textarea",Ne({id:t(y),ref_key:"textarea",ref:S,class:[t(ne).e("inner"),t(s).is("focus",t(W))]},t(c),{minlength:e.minlength,maxlength:e.maxlength,tabindex:e.tabindex,disabled:t(E),readonly:e.readonly,autocomplete:e.autocomplete,style:t(ie),"aria-label":e.ariaLabel,placeholder:e.placeholder,form:e.form,autofocus:e.autofocus,rows:e.rows,role:e.containerRole,onCompositionstart:t(fe),onCompositionupdate:t(ve),onCompositionend:t(me),onInput:q,onFocus:t(He),onBlur:t($e),onChange:ce,onKeydown:he}),null,16,["id","minlength","maxlength","tabindex","disabled","readonly","autocomplete","aria-label","placeholder","form","autofocus","rows","role","onCompositionstart","onCompositionupdate","onCompositionend","onFocus","onBlur"]),t(z)?(p(),w("span",{key:0,style:Te(se.value),class:m(t(s).e("count"))},U(t(X))+" / "+U(e.maxlength),7)):v("v-if",!0)],64))],38))}}));var Dt=lt(At,[["__file","input.vue"]]);const Me=mt(Dt),Zt=Object.freeze(Object.defineProperty({__proto__:null,ElInput:Me,default:Me,inputEmits:Re,inputProps:Oe},Symbol.toStringTag,{value:"Module"}));export{Me as E,Zt as i,Rt as u};
