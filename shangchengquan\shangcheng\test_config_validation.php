<?php
/**
 * 测试配置验证修复
 * 2025-07-17 验证双重配置的完整性检查
 */

// 引入ThinkPHP框架
require_once 'vendor/autoload.php';

// 初始化应用
$app = new think\App();
$app->initialize();

use app\common\SheZhen;

echo "<h1>舌诊配置验证测试</h1>";

// 测试图片数据
$testImageUrl = "https://kuaifengimg.azheteng.cn/upload/62/20250718/fed0d7bd030b57e056de3dba260e6e36.jpg";
$imageData = [
    'tf_image' => $testImageUrl,
    'gender' => '男'
];

echo "<h2>测试数据</h2>";
echo "<pre>" . json_encode($imageData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// 测试不同的aid
$testAids = [62, 1, 0];

foreach ($testAids as $aid) {
    echo "<h2>测试 AID = $aid</h2>";
    
    // 第一步：获取配置
    echo "<h3>1. 配置获取</h3>";
    try {
        $config = SheZhen::getConfig($aid);
        
        if ($config) {
            echo "<p style='color: green;'>✓ 成功获取配置</p>";
            
            // 显示配置类型
            if (isset($config['use_own_aliyun']) && $config['use_own_aliyun']) {
                echo "<p style='color: blue;'><strong>配置类型：用户自有阿里云配置</strong></p>";
                echo "<ul>";
                echo "<li>aliyun_access_key: " . (empty($config['aliyun_access_key']) ? '<span style="color:red;">未配置</span>' : substr($config['aliyun_access_key'], 0, 8) . '***') . "</li>";
                echo "<li>aliyun_secret_key: " . (empty($config['aliyun_secret_key']) ? '<span style="color:red;">未配置</span>' : substr($config['aliyun_secret_key'], 0, 8) . '***') . "</li>";
                echo "<li>aliyun_endpoint: " . (empty($config['aliyun_endpoint']) ? '<span style="color:red;">未配置</span>' : $config['aliyun_endpoint']) . "</li>";
                echo "</ul>";
            } else {
                echo "<p style='color: orange;'><strong>配置类型：平台公共API配置</strong></p>";
                echo "<ul>";
                echo "<li>aliyun_app_code: " . (empty($config['aliyun_app_code']) ? '<span style="color:red;">未配置</span>' : substr($config['aliyun_app_code'], 0, 8) . '***') . "</li>";
                echo "<li>aliyun_endpoint: " . (empty($config['aliyun_endpoint']) ? '<span style="color:red;">未配置</span>' : $config['aliyun_endpoint']) . "</li>";
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>✗ 未找到配置</p>";
            continue;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ 配置获取异常: " . $e->getMessage() . "</p>";
        continue;
    }
    
    // 第二步：测试API调用
    echo "<h3>2. API调用测试</h3>";
    try {
        $result = SheZhen::callDetectApi($imageData, $aid);
        
        if ($result['status'] == 1) {
            echo "<p style='color: green;'>✓ API调用成功</p>";
            echo "<p>Session ID: " . ($result['session_id'] ?? '未获取到') . "</p>";
        } else {
            echo "<p style='color: red;'>✗ API调用失败</p>";
            echo "<p><strong>错误信息:</strong> " . $result['msg'] . "</p>";
            
            // 分析错误原因
            if (strpos($result['msg'], '配置不完整') !== false) {
                echo "<p style='color: orange;'><strong>分析:</strong> 配置验证失败，请检查对应的配置项</p>";
            } elseif (strpos($result['msg'], '次数不足') !== false) {
                echo "<p style='color: orange;'><strong>分析:</strong> 平台次数不足，需要充值</p>";
            } elseif (strpos($result['msg'], '401') !== false) {
                echo "<p style='color: orange;'><strong>分析:</strong> 认证失败，请检查AppCode或密钥是否正确</p>";
            }
        }
        
        // 显示详细响应（仅在调试时）
        if (isset($_GET['debug'])) {
            echo "<h4>详细响应：</h4>";
            echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ API调用异常: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

echo "<h2>修复总结</h2>";
echo "<ul>";
echo "<li>✓ 修复了配置完整性检查逻辑</li>";
echo "<li>✓ 支持用户自有配置和平台配置的不同验证规则</li>";
echo "<li>✓ 用户自有配置检查：aliyun_access_key, aliyun_secret_key, aliyun_endpoint</li>";
echo "<li>✓ 平台配置检查：aliyun_app_code, aliyun_endpoint</li>";
echo "<li>✓ 提供了更详细的错误信息</li>";
echo "</ul>";

echo "<h2>下一步建议</h2>";
echo "<ol>";
echo "<li>确保数据库中有正确的配置信息</li>";
echo "<li>如果使用平台配置，检查 sysset 表中的 mianzhen_set 配置</li>";
echo "<li>如果使用用户配置，检查 shezhen_set 表中对应 aid 的配置</li>";
echo "<li>验证 API 密钥和端点的有效性</li>";
echo "</ol>";

if (!isset($_GET['debug'])) {
    echo "<p><a href='?debug=1'>查看详细调试信息</a></p>";
}
?>
