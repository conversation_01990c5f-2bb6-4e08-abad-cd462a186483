import{_ as z}from"./page.vue_vue_type_script_setup_true_lang-BEpByXUN.js";import{_ as d}from"./render-content.vue_vue_type_script_lang-CTn4O0b5.js";import{aV as b,aW as D,aX as I}from"./bootstrap-BmSDnAET.js";import{a4 as _,k as f,aa as y,ab as l,a7 as i,ac as h,aB as e,av as r,F as p,aC as u,aj as c,x as m,ai as A}from"../jse/index-index-BAMHRxBA.js";var U={authorEmail:"<EMAIL>",authorName:"vben",authorUrl:"https://github.com/anncwb",buildTime:"2025-07-17 00:49:07",dependencies:{"@faker-js/faker":"^9.9.0",jsonwebtoken:"^9.0.2",nitropack:"^2.11.13","@vben/access":"5.5.7","@vben/common-ui":"5.5.7","@vben/constants":"5.5.7","@vben/hooks":"5.5.7","@vben/icons":"5.5.7","@vben/layouts":"5.5.7","@vben/locales":"5.5.7","@vben/plugins":"5.5.6","@vben/preferences":"5.5.7","@vben/request":"5.5.7","@vben/stores":"5.5.7","@vben/styles":"5.5.7","@vben/types":"5.5.7","@vben/utils":"5.5.7","@vueuse/core":"^13.4.0","ant-design-vue":"^4.2.6",dayjs:"^1.11.13",pinia:"^3.0.3",vue:"^3.5.17","vue-router":"^4.5.1","element-plus":"^2.10.2","naive-ui":"^2.42.0","@vben-core/shadcn-ui":"5.5.7","lucide-vue-next":"^0.507.0","medium-zoom":"^1.1.0","radix-vue":"^1.9.17","vitepress-plugin-group-icons":"^1.6.1","@commitlint/cli":"^19.8.1","@commitlint/config-conventional":"^19.8.1","@vben/node-utils":"5.5.7","commitlint-plugin-function-rules":"^4.0.2","cz-git":"^1.11.2",czg:"^1.11.1","eslint-config-turbo":"^2.5.4","eslint-plugin-command":"^3.3.1","eslint-plugin-import-x":"^4.16.1",prettier:"^3.6.2","prettier-plugin-tailwindcss":"^0.6.13","@stylistic/stylelint-plugin":"^3.1.3","stylelint-config-recess-order":"^6.1.0","stylelint-scss":"^6.12.1","@changesets/git":"^3.0.4","@manypkg/get-packages":"^3.0.0",chalk:"^5.4.1",consola:"^3.4.2",execa:"^9.6.0","find-up":"^7.0.0",ora:"^8.2.0","pkg-types":"^2.2.0",rimraf:"^6.0.1","@iconify/json":"^2.2.354","@iconify/tailwind":"^1.2.0","@tailwindcss/nesting":"0.0.0-insiders.565cd3e","@tailwindcss/typography":"^0.5.16",autoprefixer:"^10.4.21",cssnano:"^7.0.7",postcss:"^8.5.6","postcss-antd-fixes":"^0.2.0","postcss-import":"^16.1.1","postcss-preset-env":"^10.2.4",tailwindcss:"^3.4.17","tailwindcss-animate":"^1.0.7",vite:"^6.3.5","@intlify/unplugin-vue-i18n":"^6.0.8","@jspm/generator":"^2.6.2",archiver:"^7.0.1",cheerio:"^1.1.0","get-port":"^7.1.0","html-minifier-terser":"^7.2.0","resolve.exports":"^2.0.3","vite-plugin-pwa":"^1.0.1","vite-plugin-vue-devtools":"^7.7.7","@iconify/vue":"^5.0.0","@ctrl/tinycolor":"^4.1.0","@tanstack/vue-store":"^0.7.1","@vue/shared":"^3.5.17",clsx:"^2.1.1",defu:"^6.1.4","lodash.clonedeep":"^4.5.0","lodash.get":"^4.4.2","lodash.isequal":"^4.5.0","lodash.set":"^4.3.2",nprogress:"^0.2.0","tailwind-merge":"^2.6.0","theme-colors":"^0.1.0","@vben-core/shared":"5.5.7",sortablejs:"^1.15.6","@vben-core/typings":"5.5.7","@vben-core/composables":"5.5.7","@vben-core/icons":"5.5.7","@vee-validate/zod":"^4.15.1","vee-validate":"^4.15.1",zod:"^3.25.67","zod-defaults":"^0.1.3","class-variance-authority":"^0.7.1","@vben-core/form-ui":"5.5.7","@vben-core/popup-ui":"5.2.1","@vben-core/preferences":"5.5.7","@vueuse/integrations":"^13.4.0","json-bigint":"^1.0.0",qrcode:"^1.5.4","tippy.js":"^6.3.7","vue-json-viewer":"^3.0.4","vue-tippy":"^6.7.1","watermark-js-plus":"^1.6.2","@vben-core/layout-ui":"5.5.7","@vben-core/menu-ui":"5.5.7","@vben-core/tabs-ui":"5.5.7","@vueuse/motion":"^3.0.3",echarts:"^5.6.0","vxe-pc-ui":"^4.6.42","vxe-table":"^4.13.51",axios:"^1.10.0",qs:"^6.14.0","@intlify/core-base":"^11.1.7","vue-i18n":"^11.1.7","pinia-plugin-persistedstate":"^4.4.1","secure-ls":"^2.0.0","@vben-core/design":"5.5.7","@tanstack/vue-query":"^5.81.5","@clack/prompts":"^0.10.1",cac:"^6.7.14","circular-dependency-scanner":"^2.3.0",depcheck:"^1.4.7",publint:"^0.3.12"},devDependencies:{"@types/jsonwebtoken":"^9.0.10",h3:"^1.15.3","unplugin-element-plus":"^0.10.0","@nolebase/vitepress-plugin-git-changelog":"^2.18.0","@vben/vite-config":"5.5.6","@vite-pwa/vitepress":"^1.0.0",vitepress:"^1.6.3",vue:"^3.5.17","@eslint/js":"^9.30.1","@types/eslint":"^9.6.1","@typescript-eslint/eslint-plugin":"^8.35.1","@typescript-eslint/parser":"^8.35.1",eslint:"^9.30.1","eslint-plugin-eslint-comments":"^3.2.0","eslint-plugin-jsdoc":"^50.8.0","eslint-plugin-jsonc":"^2.20.1","eslint-plugin-n":"^17.20.0","eslint-plugin-no-only-tests":"^3.3.0","eslint-plugin-perfectionist":"^4.15.0","eslint-plugin-prettier":"^5.5.1","eslint-plugin-regexp":"^2.9.0","eslint-plugin-unicorn":"^59.0.1","eslint-plugin-unused-imports":"^4.1.4","eslint-plugin-vitest":"^0.5.4","eslint-plugin-vue":"^10.2.0",globals:"^16.3.0","jsonc-eslint-parser":"^2.4.0","vue-eslint-parser":"^10.2.0",postcss:"^8.5.6","postcss-html":"^1.8.0","postcss-scss":"^4.0.9",prettier:"^3.6.2",stylelint:"^16.21.0","stylelint-config-recommended":"^16.0.0","stylelint-config-recommended-scss":"^14.1.0","stylelint-config-recommended-vue":"^1.6.1","stylelint-config-standard":"^38.0.0","stylelint-order":"^7.0.0","stylelint-prettier":"^5.0.3","@types/postcss-import":"^14.0.3","@pnpm/workspace.read-manifest":"^1000.2.0","@types/archiver":"^6.0.3","@types/html-minifier-terser":"^7.0.2","@vben/node-utils":"5.5.7","@vitejs/plugin-vue":"^5.2.4","@vitejs/plugin-vue-jsx":"^4.2.0",dayjs:"^1.11.13",dotenv:"^16.6.1",rollup:"^4.44.1","rollup-plugin-visualizer":"^5.14.0",sass:"^1.89.2",vite:"^6.3.5","vite-plugin-compression":"^0.5.1","vite-plugin-dts":"^4.5.4","vite-plugin-html":"^3.2.2","vite-plugin-lazy-import":"^1.0.7","@types/lodash.clonedeep":"^4.5.9","@types/lodash.get":"^4.4.9","@types/lodash.isequal":"^4.5.8","@types/lodash.set":"^4.3.9","@types/nprogress":"^0.2.3","@types/sortablejs":"^1.15.8","@types/qrcode":"^1.5.5","@types/qs":"^6.14.0","axios-mock-adapter":"^2.1.0","@types/json-bigint":"^1.0.4"},homepage:"https://vben.pro",license:"MIT",version:"5.5.7"};const C={class:"text-foreground mt-3 text-sm leading-6"},L=["href"],$={class:"card-box p-5"},R={class:"mt-4"},S={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},M={class:"text-foreground text-sm font-medium leading-6"},O={class:"text-foreground mt-1 text-sm leading-6 sm:mt-2"},F={class:"card-box mt-6 p-5"},G={class:"mt-4"},W={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},H={class:"text-foreground text-sm"},P={class:"text-foreground/80 mt-1 text-sm sm:mt-2"},X={class:"card-box mt-6 p-5"},J={class:"mt-4"},K={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},Q={class:"text-foreground text-sm"},Y={class:"text-foreground/80 mt-1 text-sm sm:mt-2"},Z=_({name:"AboutUI",__name:"about",props:{description:{default:"是一个现代化开箱即用的中后台解决方案，采用最新的技术栈，包括 Vue 3.0、Vite、TailwindCSS 和 TypeScript 等前沿技术，代码规范严谨，提供丰富的配置选项，旨在为中大型项目的开发提供现成的开箱即用解决方案及丰富的示例，同时，它也是学习和深入前端技术的一个极佳示例。"},name:{default:"Vben Admin"},title:{default:"关于项目"}},setup(x){const o=(t,n)=>f("a",{href:t,target:"_blank",class:"vben-link"},{default:()=>n}),{authorEmail:a,authorName:k,authorUrl:j,buildTime:w,dependencies:v={},devDependencies:g={},homepage:V,license:B,version:E}=U||{},N=[{content:E,title:"版本号"},{content:B,title:"开源许可协议"},{content:w,title:"最后构建时间"},{content:o(V,"点击查看"),title:"主页"},{content:o(D,"点击查看"),title:"文档地址"},{content:o(I,"点击查看"),title:"预览地址"},{content:o(b,"点击查看"),title:"Github"},{content:f("div",[o(j,`${k}  `),o(`mailto:${a}`,a)]),title:"作者"}],T=Object.keys(v).map(t=>({content:v[t],title:t})),q=Object.keys(g).map(t=>({content:g[t],title:t}));return(t,n)=>(l(),y(i(z),{title:t.title},{description:h(()=>[e("p",C,[e("a",{href:i(b),class:"vben-link",target:"_blank"},c(t.name),9,L),A(" "+c(t.description),1)])]),default:h(()=>[e("div",$,[n[0]||(n[0]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"基本信息")],-1)),e("div",R,[e("dl",S,[(l(),r(p,null,u(N,s=>e("div",{key:s.title,class:"border-border border-t px-4 py-6 sm:col-span-1 sm:px-0"},[e("dt",M,c(s.title),1),e("dd",O,[m(i(d),{content:s.content},null,8,["content"])])])),64))])])]),e("div",F,[n[1]||(n[1]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"生产环境依赖")],-1)),e("div",G,[e("dl",W,[(l(!0),r(p,null,u(i(T),s=>(l(),r("div",{key:s.title,class:"border-border border-t px-4 py-3 sm:col-span-1 sm:px-0"},[e("dt",H,c(s.title),1),e("dd",P,[m(i(d),{content:s.content},null,8,["content"])])]))),128))])])]),e("div",X,[n[2]||(n[2]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"开发环境依赖")],-1)),e("div",J,[e("dl",K,[(l(!0),r(p,null,u(i(q),s=>(l(),r("div",{key:s.title,class:"border-border border-t px-4 py-3 sm:col-span-1 sm:px-0"},[e("dt",Q,c(s.title),1),e("dd",Y,[m(i(d),{content:s.content},null,8,["content"])])]))),128))])])])]),_:1},8,["title"]))}}),ie=_({name:"About",__name:"index",setup(x){return(o,a)=>(l(),y(i(Z)))}});export{ie as default};
