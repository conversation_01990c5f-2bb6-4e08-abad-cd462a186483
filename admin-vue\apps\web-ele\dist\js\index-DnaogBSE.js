var ee=Object.defineProperty,ae=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var O=Object.getOwnPropertySymbols;var te=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable;var U=(i,c,n)=>c in i?ee(i,c,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[c]=n,S=(i,c)=>{for(var n in c||(c={}))te.call(c,n)&&U(i,n,c[n]);if(O)for(var n of O(c))ne.call(c,n)&&U(i,n,c[n]);return i},L=(i,c)=>ae(i,ie(c));import{P as se,Q as V,o as T,k as oe,l as le,R as w,n as ce,T as re,t as ue,y as m,U as de,B as ve,m as fe,w as pe}from"./bootstrap-CYivmKoJ.js";import{I as B,C as A,U as z,u as me}from"./index-DIXeP0hR.js";import{P as E,d as j,e as f,r as C,w as R,o as he,f as p,g as t,j as N,h as u,u as a,n as v,q as d,D as h,B as b,t as P,l as W,E as ye,m as be,p as ke,W as $}from"../jse/index-index-SSqEGcIT.js";import{u as Ie,a as ge}from"./use-form-item-iUVikjOD.js";import{u as we,b as Ve}from"./use-form-common-props-DZjBwEkr.js";import{t as Se,d as Te}from"./error-CYrjCQ5V.js";const Ee=i=>["",...se].includes(i),Ce=oe(S({modelValue:{type:[Boolean,String,Number],default:!1},disabled:Boolean,loading:Boolean,size:{type:String,validator:Ee},width:{type:[String,Number],default:""},inlinePrompt:Boolean,inactiveActionIcon:{type:w},activeActionIcon:{type:w},activeIcon:{type:w},inactiveIcon:{type:w},activeText:{type:String,default:""},inactiveText:{type:String,default:""},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},name:{type:String,default:""},validateEvent:{type:Boolean,default:!0},beforeChange:{type:le(Function)},id:String,tabindex:{type:[String,Number]}},me(["ariaLabel"]))),Ne={[z]:i=>V(i)||E(i)||T(i),[A]:i=>V(i)||E(i)||T(i),[B]:i=>V(i)||E(i)||T(i)},q="ElSwitch",Pe=j({name:q}),Be=j(L(S({},Pe),{props:Ce,emits:Ne,setup(i,{expose:c,emit:n}){const o=i,{formItem:k}=Ie(),G=we(),s=ce("switch"),{inputId:H}=ge(o,{formItemContext:k}),I=Ve(f(()=>o.loading)),D=C(o.modelValue!==!1),y=C(),Q=C(),J=f(()=>[s.b(),s.m(G.value),s.is("disabled",I.value),s.is("checked",l.value)]),X=f(()=>[s.e("label"),s.em("label","left"),s.is("active",!l.value)]),Y=f(()=>[s.e("label"),s.em("label","right"),s.is("active",l.value)]),Z=f(()=>({width:re(o.width)}));R(()=>o.modelValue,()=>{D.value=!0});const K=f(()=>D.value?o.modelValue:!1),l=f(()=>K.value===o.activeValue);[o.activeValue,o.inactiveValue].includes(K.value)||(n(z,o.inactiveValue),n(A,o.inactiveValue),n(B,o.inactiveValue)),R(l,e=>{var r;y.value.checked=e,o.validateEvent&&((r=k==null?void 0:k.validate)==null||r.call(k,"change").catch(x=>Te()))});const g=()=>{const e=l.value?o.inactiveValue:o.activeValue;n(z,e),n(A,e),n(B,e),ke(()=>{y.value.checked=l.value})},F=()=>{if(I.value)return;const{beforeChange:e}=o;if(!e){g();return}const r=e();[$(r),V(r)].includes(!0)||Se(q,"beforeChange must return type `Promise<boolean>` or `boolean`"),$(r)?r.then(M=>{M&&g()}).catch(M=>{}):r&&g()},_=()=>{var e,r;(r=(e=y.value)==null?void 0:e.focus)==null||r.call(e)};return he(()=>{y.value.checked=l.value}),c({focus:_,checked:l}),(e,r)=>(t(),p("div",{class:v(a(J)),onClick:ve(F,["prevent"])},[N("input",{id:a(H),ref_key:"input",ref:y,class:v(a(s).e("input")),type:"checkbox",role:"switch","aria-checked":a(l),"aria-disabled":a(I),"aria-label":e.ariaLabel,name:e.name,"true-value":e.activeValue,"false-value":e.inactiveValue,disabled:a(I),tabindex:e.tabindex,onChange:g,onKeydown:ue(F,["enter"])},null,42,["id","aria-checked","aria-disabled","aria-label","name","true-value","false-value","disabled","tabindex","onKeydown"]),!e.inlinePrompt&&(e.inactiveIcon||e.inactiveText)?(t(),p("span",{key:0,class:v(a(X))},[e.inactiveIcon?(t(),d(a(m),{key:0},{default:h(()=>[(t(),d(b(e.inactiveIcon)))]),_:1})):u("v-if",!0),!e.inactiveIcon&&e.inactiveText?(t(),p("span",{key:1,"aria-hidden":a(l)},P(e.inactiveText),9,["aria-hidden"])):u("v-if",!0)],2)):u("v-if",!0),N("span",{ref_key:"core",ref:Q,class:v(a(s).e("core")),style:be(a(Z))},[e.inlinePrompt?(t(),p("div",{key:0,class:v(a(s).e("inner"))},[e.activeIcon||e.inactiveIcon?(t(),d(a(m),{key:0,class:v(a(s).is("icon"))},{default:h(()=>[(t(),d(b(a(l)?e.activeIcon:e.inactiveIcon)))]),_:1},8,["class"])):e.activeText||e.inactiveText?(t(),p("span",{key:1,class:v(a(s).is("text")),"aria-hidden":!a(l)},P(a(l)?e.activeText:e.inactiveText),11,["aria-hidden"])):u("v-if",!0)],2)):u("v-if",!0),N("div",{class:v(a(s).e("action"))},[e.loading?(t(),d(a(m),{key:0,class:v(a(s).is("loading"))},{default:h(()=>[ye(a(de))]),_:1},8,["class"])):a(l)?W(e.$slots,"active-action",{key:1},()=>[e.activeActionIcon?(t(),d(a(m),{key:0},{default:h(()=>[(t(),d(b(e.activeActionIcon)))]),_:1})):u("v-if",!0)]):a(l)?u("v-if",!0):W(e.$slots,"inactive-action",{key:2},()=>[e.inactiveActionIcon?(t(),d(a(m),{key:0},{default:h(()=>[(t(),d(b(e.inactiveActionIcon)))]),_:1})):u("v-if",!0)])],2)],6),!e.inlinePrompt&&(e.activeIcon||e.activeText)?(t(),p("span",{key:1,class:v(a(Y))},[e.activeIcon?(t(),d(a(m),{key:0},{default:h(()=>[(t(),d(b(e.activeIcon)))]),_:1})):u("v-if",!0),!e.activeIcon&&e.activeText?(t(),p("span",{key:1,"aria-hidden":!a(l)},P(e.activeText),9,["aria-hidden"])):u("v-if",!0)],2)):u("v-if",!0)],10,["onClick"]))}}));var Ae=fe(Be,[["__file","switch.vue"]]);const Le=pe(Ae);export{Le as ElSwitch,Le as default,Ne as switchEmits,Ce as switchProps};
