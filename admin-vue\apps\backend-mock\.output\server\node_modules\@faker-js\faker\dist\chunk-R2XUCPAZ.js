import{a as o}from"./chunk-KERBADJJ.js";import{n,o as e}from"./chunk-YQYVFZYE.js";var a=["Co. Ltd.","Ltd."];var M={legal_entity_type:a},i=M;var t=["com","com.hk","hk","org.hk"];var P={domain_suffix:t},r=P;var u=["###","##","#"];var m=["Aberdeen","Ap Lei Chau","Causeway Bay","Chai Wan","Cheung Chau","Cheung Fat","Cheung Sha Wan","Choi Hung Chuen","Choi Ming","Chuk Yuen","Cyberport","Discovery Bay","Fairview Park","Fanling","Fo Tan","Fu Shan","Fu Shin","Fu Tai","Happy Valley","Heng Fa Chuen","Heng On","Hi<PERSON> Keng","Ho Man Tin","Hung Hom Bay","Kam Tai","Kam Tin","Kennedy Town","Kowloon","Kowloon Bay","Kowloon Central","Kowloon City","Kowloon East","Kwai Chung","Kwai Fong","Kwai Shing","Kwong Yuen","Kwun Tong","<PERSON> King","<PERSON> <PERSON>k","<PERSON> <PERSON>","<PERSON>ma","<PERSON> On","Lei Muk Shue","Lei <PERSON>ng","Leung King","Lok Fu","Ma On <PERSON>","<PERSON> Foo <PERSON> <PERSON>en","<PERSON> <PERSON>","Mong Kok","<PERSON>i <PERSON>o","<PERSON>au <PERSON> <PERSON>","Ngau <PERSON> Kok","Oi Man","Peak","<PERSON>g <PERSON>u","Po <PERSON>","Pok <PERSON> Lam","Repulse Bay","Sai Kung","Sai Ying Pun","San Tin","Sau Mau Ping","Sha Kok","Sha Tau Kok","Sha Tin","Sham Shui Po","Shau Kei Wan","Shek Kip Mei","Shek Lei","Shek Wai Kok","Shek Wu Hui","Sheung Tak","Sheung Wan","Shun Lee","Siu Sai Wan","So Uk","Stanley","Sun Chui","Tai Hing","Tai Kok Tsui","Tai Koo Shing","Tai O","Tai Po","Tin Yiu","Tin Yuet","To Kwa Wan","Tsat Tsz Mui","Tseung Kwan O","Tsim Sha Tsui","Tsing Yi","Tsuen Wan","Tsz Wan Shan","Tuen Mun","Tung Chung","Wah Fu","Wah Ming","Wan Chai","Wan Tau Tong","Wo Che","Wong Tai Sin","Yau Tong","Yau Yat Tsuen","Yuen Long"];var g=["{{location.city_name}}"];var p=null;var h=null;var l=["Hong Kong Island","Kowloon","New Territories"];var f=["HK","KLN","NT"];var s=["Wan","On","Tai","Man","Fung","Cheung","Tung","Hing","Po","Wah","Tak","Shing","Lung","Yuen","Wing","Hong","Yip","King","Kwong","Hoi","Ming","Wa","Lok","Yan","Wai","Chi","Fuk","Lai","Lee","Fu","Tin","Kai","Sai","Shun","Ping","Yee","Wo","Chung","Hang","Ning","Wong","Yue","Choi","Wang","Ching","Sau","Shan","Tsui","Tau","Sheung","Lam","Fat","Hung","Chuk","Shek","Kok","Cheong","Fong","Nam","Lei","Yu","Mei","Pak","Fai","Kwai","Sing","Kung","Chau","Tong","San","Chiu","Chun","Yin","Yuk","Ting","Kam","Lun","Oi"];var C=["Aldrich","Arran","Austin","Baker","Battery","Bel-Air","Bonham","Boundary","Bowen","Breezy","Caine","Cameron","Canal","Cape","Chatham","Church","College","Comet","Connaught","Cornwall","Cox's","Cross","Douglas","Dragon","Eastern","Electric","Expo","Findlay","First","Garden","Gillies","Greig","Hospital","Jardine's","Jordan","Kennedy","Kimberley","Leighton","Maidstone","Maple","Marsh","Monmouth","Oaklands","Peel","Poplar","Rose","Second","Seymour","Stewart","Third","Village","Water","Waterloo","Wylie"];var T=["{{location.street_english_part}} {{location.street_suffix}}","{{location.street_cantonese_part}} {{location.street_cantonese_part}} {{location.street_suffix}}"];var d=null;var K=["Street","Road","Lane","Path","Terrace","Avenue","Drive","Crescent","Court"];var D={building_number:u,city_name:m,city_pattern:g,postcode:p,postcode_by_state:h,state:l,state_abbr:f,street_cantonese_part:s,street_english_part:C,street_pattern:T,street_prefix:d,street_suffix:K},S=D;var b={title:"English (Hong Kong)",code:"en_HK",country:"HK",language:"en",endonym:"English (Hong Kong)",dir:"ltr",script:"Latn"},c=b;var L={generic:["Au","Chan","Chang","Chen","Cheng","Cheuk","Cheung","Chiu","Cho","Choi","Chong","Chow","Choy","Chu","Chui","Chung","Fan","Fok","Fu","Fung","He","Ho","Hong","Hu","Huang","Hui","Ip","Kan","Keung","Ko","Kong","Kwan","Kwok","Kwong","Lai","Lam","Lau","Law","Lee","Leung","Li","Liang","Lin","Ling","Liu","Lu","Lui","Luk","Lung","Ma","Mak","Man","Mok","Ng","Ngai","Pang","Poon","Pun","Shiu","Shum","Sin","Siu","So","Suen","Sun","Sze","Szeto","Tai","Tam","Tan","Tang","Tong","Tsang","Tse","Tsoi","Tsui","Wan","Wang","Wong","Wu","Xu","Yan","Yang","Yeung","Yim","Yin","Yip","Yiu","Yu","Yue","Yuen","Yung","Zhang","Zhao","Zheng","Zhou","Zhu"]};var y={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var k=[{value:"{{person.firstName}} {{person.lastName}}",weight:1}];var N={last_name:L,last_name_pattern:y,name:k},x=N;var _=["2### ####","3### ####","4### ####","5### ####","6### ####","7### ####","9### ####"];var W=["+8522#######","+8523#######","+8524#######","+8525#######","+8526#######","+8527#######","+8529#######"];var w=["2### ####","3### ####","4### ####","5### ####","6### ####","7### ####","9### ####"];var B={human:_,international:W,national:w},H=B;var O={format:H},Y=O;var A={company:i,internet:r,location:S,metadata:c,person:x,phone_number:Y},F=A;var Rn=new n({locale:[F,o,e]});export{F as a,Rn as b};
