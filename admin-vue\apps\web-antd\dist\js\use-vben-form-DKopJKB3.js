var As=Object.defineProperty,Ns=Object.defineProperties;var Vs=Object.getOwnPropertyDescriptors;var Ke=Object.getOwnPropertySymbols;var jt=Object.prototype.hasOwnProperty,Zt=Object.prototype.propertyIsEnumerable;var Mt=Math.pow,mt=(s,e,t)=>e in s?As(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,l=(s,e)=>{for(var t in e||(e={}))jt.call(e,t)&&mt(s,t,e[t]);if(Ke)for(var t of Ke(e))Zt.call(e,t)&&mt(s,t,e[t]);return s},x=(s,e)=>Ns(s,Vs(e));var pt=(s,e)=>{var t={};for(var r in s)jt.call(s,r)&&e.indexOf(r)<0&&(t[r]=s[r]);if(s!=null&&Ke)for(var r of Ke(s))e.indexOf(r)<0&&Zt.call(s,r)&&(t[r]=s[r]);return t};var Y=(s,e,t)=>mt(s,typeof e!="symbol"?e+"":e,t);var T=(s,e,t)=>new Promise((r,n)=>{var a=c=>{try{o(t.next(c))}catch(u){n(u)}},i=c=>{try{o(t.throw(c))}catch(u){n(u)}},o=c=>c.done?r(c.value):Promise.resolve(c.value).then(a,i);o((t=t.apply(s,e)).next())});import{R as Pt,a4 as G,aa as P,ab as R,a7 as f,ac as V,a8 as j,av as le,aV as Q,aW as q,an as Es,aF as Is,J as I,ad as ne,aX as $s,x as re,a_ as js,a$ as us,aB as Ge,ai as tt,aj as Le,P as ue,Y as be,am as Zs,r as Se,by as Ms,bn as Ps,U as Dt,i as J,bz as We,bA as Ds,bB as Bt,o as Rt,bC as Bs,bD as Ls,as as zs,bE as _t,a as de,aq as B,F as bt,ah as He,aw as ds,bF as Us,bG as yt,bx as fs,ax as qs,aA as Ws,b6 as hs,aC as Ft,af as ye,ag as ve,n as nt,bH as Hs,aP as Gs,aQ as Ys,a9 as ms,b3 as Lt,b5 as Js,a3 as Qs,aI as Xs,V as Ks,az as er,k as tr}from"../jse/index-index-BAMHRxBA.js";import{aR as ps,aZ as sr,a_ as rr,a$ as nr,b0 as ys,b1 as ar,b2 as ir,b3 as or,b4 as lr,b5 as vs,b6 as cr,b7 as At,b8 as ur,b9 as dr,ba as st,bb as fr,bc as hr,bd as gs,be as mr,a8 as pr,a7 as yr,bf as vr,bg as gr,bh as _r,bi as br,bj as kr}from"./bootstrap-BmSDnAET.js";import{_ as ze}from"./render-content.vue_vue_type_script_lang-CTn4O0b5.js";const xr=ps("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);const wr=ps("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),_s=Symbol();function ut(){const s=Pt(ar),e=Pt(_s);if(!s)throw new Error("useFormField should be used within <FormField>");const{name:t}=s,r=e,n={error:ys(t),isDirty:nr(t),isTouched:rr(t),valid:sr(t)};return l({formDescriptionId:`${r}-form-item-description`,formItemId:`${r}-form-item`,formMessageId:`${r}-form-item-message`,id:r,name:t},n)}const Cr=G({__name:"FormControl",setup(s){const{error:e,formDescriptionId:t,formItemId:r,formMessageId:n}=ut();return(a,i)=>(R(),P(f(ir),{id:f(r),"aria-describedby":f(e)?`${f(t)} ${f(n)}`:`${f(t)}`,"aria-invalid":!!f(e)},{default:V(()=>[j(a.$slots,"default")]),_:3},8,["id","aria-describedby","aria-invalid"]))}}),Sr=["id"],Or=G({__name:"FormDescription",props:{class:{}},setup(s){const e=s,{formDescriptionId:t}=ut();return(r,n)=>(R(),le("p",{id:f(t),class:Q(f(q)("text-muted-foreground text-sm",e.class))},[j(r.$slots,"default")],10,Sr))}}),Tr=G({__name:"FormItem",props:{class:{}},setup(s){const e=s,t=Es();return Is(_s,t),(r,n)=>(R(),le("div",{class:Q(f(q)(e.class))},[j(r.$slots,"default")],2))}}),Rr=G({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,t=I(()=>{const a=e,{class:r}=a;return pt(a,["class"])});return(r,n)=>(R(),P(f(or),ne(t.value,{class:f(q)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e.class)}),{default:V(()=>[j(r.$slots,"default")]),_:3},16,["class"]))}}),Fr=G({__name:"FormLabel",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,{formItemId:t}=ut();return(r,n)=>(R(),P(f(Rr),{class:Q(f(q)(e.class)),for:f(t)},{default:V(()=>[j(r.$slots,"default")]),_:3},8,["class","for"]))}}),zt=G({__name:"FormMessage",setup(s){const{formMessageId:e,name:t}=ut();return(r,n)=>(R(),P(f(lr),{id:f(e),name:$s(f(t)),as:"p",class:"text-destructive text-[0.8rem]"},null,8,["id","name"]))}}),Ar=G({inheritAttrs:!1,__name:"help-tooltip",props:{triggerClass:{}},setup(s){return(e,t)=>(R(),P(vs,{"delay-duration":300,side:"right"},{trigger:V(()=>[j(e.$slots,"trigger",{},()=>[re(f(wr),{class:Q(f(q)("text-foreground/80 hover:text-foreground inline-flex size-5 cursor-pointer",e.triggerClass))},null,8,["class"])])]),default:V(()=>[j(e.$slots,"default")]),_:3}))}}),Nr=G({__name:"expandable-arrow",props:js({class:{}},{modelValue:{default:!1},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const e=s,t=us(s,"modelValue");return(r,n)=>(R(),le("div",{class:Q(f(q)("vben-link inline-flex items-center",e.class)),onClick:n[0]||(n[0]=a=>t.value=!t.value)},[j(r.$slots,"default",{isExpanded:t.value},()=>[tt(Le(t.value),1)]),Ge("div",{class:Q([{"rotate-180":!t.value},"transition-transform duration-300"])},[j(r.$slots,"icon",{},()=>[re(f(cr),{class:"size-4"})])],2)],2))}}),Ne=new WeakMap,rt=new WeakMap,at={current:[]};let vt=!1;const et=new Set,Ut=new Map;function bs(s){const e=Array.from(s).sort((t,r)=>t instanceof Ve&&t.options.deps.includes(r)?1:r instanceof Ve&&r.options.deps.includes(t)?-1:0);for(const t of e){if(at.current.includes(t))continue;at.current.push(t),t.recompute();const r=rt.get(t);if(r)for(const n of r){const a=Ne.get(n);a&&bs(a)}}}function Vr(s){s.listeners.forEach(e=>e({prevVal:s.prevState,currentVal:s.state}))}function Er(s){s.listeners.forEach(e=>e({prevVal:s.prevState,currentVal:s.state}))}function Ir(s){var e;if(et.add(s),!vt)try{for(vt=!0;et.size>0;){const t=Array.from(et);et.clear();for(const r of t){const n=(e=Ut.get(r))!=null?e:r.prevState;r.prevState=n,Vr(r)}for(const r of t){const n=Ne.get(r);n&&(at.current.push(r),bs(n))}for(const r of t){const n=Ne.get(r);if(n)for(const a of n)Er(a)}}}finally{vt=!1,at.current=[],Ut.clear()}}function $r(s){return typeof s=="function"}class kt{constructor(e,t){this.listeners=new Set,this.subscribe=r=>{var n,a;this.listeners.add(r);const i=(a=(n=this.options)==null?void 0:n.onSubscribe)==null?void 0:a.call(n,r,this);return()=>{this.listeners.delete(r),i==null||i()}},this.prevState=e,this.state=e,this.options=t}setState(e){var t,r,n;this.prevState=this.state,(t=this.options)!=null&&t.updateFn?this.state=this.options.updateFn(this.prevState)(e):$r(e)?this.state=e(this.prevState):this.state=e,(n=(r=this.options)==null?void 0:r.onUpdate)==null||n.call(r),Ir(this)}}class Ve{constructor(e){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{var n;const t=[],r=[];for(const a of this.options.deps)t.push(a.prevState),r.push(a.state);return this.lastSeenDepValues=r,{prevDepVals:t,currDepVals:r,prevVal:(n=this.prevState)!=null?n:void 0}},this.recompute=()=>{var t,r;this.prevState=this.state;const{prevDepVals:n,currDepVals:a,prevVal:i}=this.getDepVals();this.state=this.options.fn({prevDepVals:n,currDepVals:a,prevVal:i}),(r=(t=this.options).onUpdate)==null||r.call(t)},this.checkIfRecalculationNeededDeeply=()=>{for(const a of this.options.deps)a instanceof Ve&&a.checkIfRecalculationNeededDeeply();let t=!1;const r=this.lastSeenDepValues,{currDepVals:n}=this.getDepVals();for(let a=0;a<n.length;a++)if(n[a]!==r[a]){t=!0;break}t&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const t of this._subscriptions)t()}),this.subscribe=t=>{var r,n;this.listeners.add(t);const a=(n=(r=this.options).onSubscribe)==null?void 0:n.call(r,t,this);return()=>{this.listeners.delete(t),a==null||a()}},this.options=e,this.state=e.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(e=this.options.deps){for(const t of e)if(t instanceof Ve)t.registerOnGraph(),this.registerOnGraph(t.options.deps);else if(t instanceof kt){let r=Ne.get(t);r||(r=new Set,Ne.set(t,r)),r.add(this);let n=rt.get(this);n||(n=new Set,rt.set(this,n)),n.add(t)}}unregisterFromGraph(e=this.options.deps){for(const t of e)if(t instanceof Ve)this.unregisterFromGraph(t.options.deps);else if(t instanceof kt){const r=Ne.get(t);r&&r.delete(this);const n=rt.get(this);n&&n.delete(t)}}}function jr(s,e=t=>t){const t=ue(e(s.state));return be(()=>s,(r,n,a)=>{const i=r.subscribe(()=>{const o=e(r.state);Zr(Se(t.value),o)||(t.value=o)});a(()=>{i()})},{immediate:!0}),Zs(t)}function Zr(s,e){if(Object.is(s,e))return!0;if(typeof s!="object"||s===null||typeof e!="object"||e===null)return!1;if(s instanceof Map&&e instanceof Map){if(s.size!==e.size)return!1;for(const[r,n]of s)if(!e.has(r)||!Object.is(n,e.get(r)))return!1;return!0}if(s instanceof Set&&e instanceof Set){if(s.size!==e.size)return!1;for(const r of s)if(!e.has(r))return!1;return!0}const t=Object.keys(s);if(t.length!==Object.keys(e).length)return!1;for(let r=0;r<t.length;r++)if(!Object.prototype.hasOwnProperty.call(e,t[r])||!Object.is(s[t[r]],e[t[r]]))return!1;return!0}function Mr(){return{actionWrapperClass:"",collapsed:!1,collapsedRows:1,collapseTriggerResize:!1,commonConfig:{},handleReset:void 0,handleSubmit:void 0,handleValuesChange:void 0,layout:"horizontal",resetButtonOptions:{},schema:[],scrollToFirstError:!1,showCollapseButton:!1,showDefaultActions:!0,submitButtonOptions:{},submitOnChange:!1,submitOnEnter:!1,wrapperClass:"grid-cols-1"}}class Pr{constructor(e={}){Y(this,"form",{});Y(this,"isMounted",!1);Y(this,"state",null);Y(this,"stateHandler");Y(this,"store");Y(this,"componentRefMap",new Map);Y(this,"latestSubmissionValues",null);Y(this,"prevState",null);Y(this,"handleArrayToStringFields",e=>{var n;const t=(n=this.state)==null?void 0:n.arrayToStringFields;if(!t||!Array.isArray(t))return;const r=(a,i=",")=>{this.processFields(a,i,e,(o,c)=>Array.isArray(o)?o.join(c):o)};if(t.every(a=>typeof a=="string")){const a=t[t.length-1]||"",i=a.length===1?t.slice(0,-1):t,o=a.length===1?a:",";r(i,o);return}t.forEach(a=>{if(Array.isArray(a)){const[i,o=","]=a;if(!Array.isArray(i)){console.warn(`Invalid field configuration: fields should be an array of strings, got ${typeof i}`);return}r(i,o)}})});Y(this,"handleRangeTimeValue",e=>{var n;const t=l({},e),r=(n=this.state)==null?void 0:n.fieldMappingTime;return this.handleStringToArrayFields(t),!r||!Array.isArray(r)||r.forEach(([a,[i,o],c="YYYY-MM-DD"])=>{if(i&&o&&t[a]===null&&(Reflect.deleteProperty(t,i),Reflect.deleteProperty(t,o)),!t[a]){Reflect.deleteProperty(t,a);return}const[u,h]=t[a];if(c===null)t[i]=u,t[o]=h;else if(J(c))t[i]=c(u,i),t[o]=c(h,o);else{const[_,g]=Array.isArray(c)?c:[c,c];t[i]=u?Bt(u,_):void 0,t[o]=h?Bt(h,g):void 0}Reflect.deleteProperty(t,a)}),t});Y(this,"handleStringToArrayFields",e=>{var n;const t=(n=this.state)==null?void 0:n.arrayToStringFields;if(!t||!Array.isArray(t))return;const r=(a,i=",")=>{this.processFields(a,i,e,(o,c)=>{if(typeof o!="string")return o;if(o==="")return[];const u=c.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`);return o.split(new RegExp(u))})};if(t.every(a=>typeof a=="string")){const a=t[t.length-1]||"",i=a.length===1?t.slice(0,-1):t,o=a.length===1?a:",";r(i,o);return}t.forEach(a=>{if(Array.isArray(a)){const[i,o=","]=a;if(Array.isArray(i))r(i,o);else if(typeof e[i]=="string"){const c=e[i];if(c==="")e[i]=[];else{const u=o.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`);e[i]=c.split(new RegExp(u))}}}})});Y(this,"processFields",(e,t,r,n)=>{e.forEach(a=>{const i=r[a];i!=null&&(r[a]=n(i,t))})});const t=pt(e,[]),r=Mr();this.store=new kt(l(l({},r),t),{onUpdate:()=>{this.prevState=this.state,this.state=this.store.state,this.updateState()}}),this.state=this.store.state,this.stateHandler=new Ms,Ps(this)}getFieldComponentRef(e){var r,n;let t=this.componentRefMap.has(e)?this.componentRefMap.get(e):void 0;return t&&t.$.type.name==="AsyncComponentWrapper"&&t.$.subTree.ref&&(Array.isArray(t.$.subTree.ref)?t.$.subTree.ref.length>0&&Dt((r=t.$.subTree.ref[0])==null?void 0:r.r)&&(t=(n=t.$.subTree.ref[0])==null?void 0:n.r.value):Dt(t.$.subTree.ref.r)&&(t=t.$.subTree.ref.r.value)),t}getFocusedField(){for(const e of this.componentRefMap.keys()){const t=this.getFieldComponentRef(e);if(t){let r=null;if(t instanceof HTMLElement?r=t:t.$el instanceof HTMLElement&&(r=t.$el),!r)continue;if(r===document.activeElement||r.contains(document.activeElement))return e}}}getLatestSubmissionValues(){return this.latestSubmissionValues||{}}getState(){return this.state}getValues(){return T(this,null,function*(){const e=yield this.getForm();return e.values?this.handleRangeTimeValue(e.values):{}})}isFieldValid(e){return T(this,null,function*(){return(yield this.getForm()).isFieldValid(e)})}merge(e){const t=[this,e],r=new Proxy(e,{get(n,a){return a==="merge"?i=>(t.push(i),r):a==="submitAllForm"?(i=!0)=>T(null,null,function*(){try{const o=yield Promise.all(t.map(c=>T(null,null,function*(){return(yield c.validate()).valid?Se((yield c.getValues())||{}):void 0})));return i?Object.assign({},...o):o}catch(o){console.error("Validation error:",o)}}):n[a]}});return r}mount(e,t){this.isMounted||(Object.assign(this.form,e),this.stateHandler.setConditionTrue(),this.setLatestSubmissionValues(l({},Se(this.handleRangeTimeValue(this.form.values)))),this.componentRefMap=t,this.isMounted=!0)}removeSchemaByFields(e){return T(this,null,function*(){var a,i;const t=new Set(e),n=((i=(a=this.state)==null?void 0:a.schema)!=null?i:[]).filter(o=>!t.has(o.fieldName));this.setState({schema:n})})}resetForm(e,t){return T(this,null,function*(){return(yield this.getForm()).resetForm(e,t)})}resetValidate(){return T(this,null,function*(){const e=yield this.getForm();Object.keys(e.errors.value).forEach(r=>{e.setFieldError(r,void 0)})})}scrollToFirstError(e){const t=typeof e=="string"?e:Object.keys(e)[0];if(!t)return;let r=document.querySelector(`[name="${t}"]`);if(!r){const n=this.getFieldComponentRef(t);n&&n.$el instanceof HTMLElement&&(r=n.$el)}r&&r.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"})}setFieldValue(e,t,r){return T(this,null,function*(){(yield this.getForm()).setFieldValue(e,t,r)})}setLatestSubmissionValues(e){this.latestSubmissionValues=l({},Se(e))}setState(e){J(e)?this.store.setState(t=>We(e(t),t)):this.store.setState(t=>We(e,t))}setValues(e,t=!0,r=!1){return T(this,null,function*(){const n=yield this.getForm();if(!t){n.setValues(e,r);return}const a=Ds((o,c,u)=>(c in o&&(o[c]=!Array.isArray(o[c])&&Rt(o[c])&&!Bs(o[c])&&!Ls(o[c])?a(o[c],u):u),!0)),i=a(e,n.values);this.handleStringToArrayFields(i),n.setValues(i,r)})}submitForm(e){return T(this,null,function*(){var n,a;e==null||e.preventDefault(),e==null||e.stopPropagation(),yield(yield this.getForm()).submitForm();const r=Se(yield this.getValues());return this.handleArrayToStringFields(r),yield(a=(n=this.state)==null?void 0:n.handleSubmit)==null?void 0:a.call(n,r),r})}unmount(){var e,t;(t=(e=this.form)==null?void 0:e.resetForm)==null||t.call(e),this.latestSubmissionValues=null,this.isMounted=!1,this.stateHandler.reset()}updateSchema(e){var i,o;const t=[...e];if(!t.every(c=>Reflect.has(c,"fieldName")&&c.fieldName)){console.error("All items in the schema array must have a valid `fieldName` property to be updated");return}const n=[...(o=(i=this.state)==null?void 0:i.schema)!=null?o:[]],a={};t.forEach(c=>{c.fieldName&&(a[c.fieldName]=c)}),n.forEach((c,u)=>{const h=a[c.fieldName];h&&(n[u]=We(h,c))}),this.setState({schema:n})}validate(e){return T(this,null,function*(){var n,a;const r=yield(yield this.getForm()).validate(e);return Object.keys((n=r==null?void 0:r.errors)!=null?n:{}).length>0&&(console.error("validate error",r==null?void 0:r.errors),(a=this.state)!=null&&a.scrollToFirstError&&this.scrollToFirstError(r.errors)),r})}validateAndSubmitForm(){return T(this,null,function*(){var n;const e=yield this.getForm(),{valid:t,errors:r}=yield e.validate();if(!t){(n=this.state)!=null&&n.scrollToFirstError&&this.scrollToFirstError(r);return}return yield this.submitForm()})}validateField(e,t){return T(this,null,function*(){var a,i;const n=yield(yield this.getForm()).validateField(e,t);return Object.keys((a=n==null?void 0:n.errors)!=null?a:{}).length>0&&(console.error("validate error",n==null?void 0:n.errors),(i=this.state)!=null&&i.scrollToFirstError&&this.scrollToFirstError(e)),n})}getForm(){return T(this,null,function*(){var e;if(this.isMounted||(yield this.stateHandler.waitForCondition()),!((e=this.form)!=null&&e.meta))throw new Error("<VbenForm /> is not mounted");return this.form})}updateState(){var r,n,a,i,o,c;const e=(n=(r=this.state)==null?void 0:r.schema)!=null?n:[],t=(i=(a=this.prevState)==null?void 0:a.schema)!=null?i:[];if(e.length<t.length){const u=new Set(e.map(_=>_.fieldName)),h=t.filter(_=>!u.has(_.fieldName));for(const _ of h)(c=(o=this.form)==null?void 0:o.setFieldValue)==null||c.call(o,_.fieldName,void 0)}}}var F;(function(s){s.assertEqual=n=>{};function e(n){}s.assertIs=e;function t(n){throw new Error}s.assertNever=t,s.arrayToEnum=n=>{const a={};for(const i of n)a[i]=i;return a},s.getValidEnumValues=n=>{const a=s.objectKeys(n).filter(o=>typeof n[n[o]]!="number"),i={};for(const o of a)i[o]=n[o];return s.objectValues(i)},s.objectValues=n=>s.objectKeys(n).map(function(a){return n[a]}),s.objectKeys=typeof Object.keys=="function"?n=>Object.keys(n):n=>{const a=[];for(const i in n)Object.prototype.hasOwnProperty.call(n,i)&&a.push(i);return a},s.find=(n,a)=>{for(const i of n)if(a(i))return i},s.isInteger=typeof Number.isInteger=="function"?n=>Number.isInteger(n):n=>typeof n=="number"&&Number.isFinite(n)&&Math.floor(n)===n;function r(n,a=" | "){return n.map(i=>typeof i=="string"?`'${i}'`:i).join(a)}s.joinValues=r,s.jsonStringifyReplacer=(n,a)=>typeof a=="bigint"?a.toString():a})(F||(F={}));var qt;(function(s){s.mergeShapes=(e,t)=>l(l({},e),t)})(qt||(qt={}));const y=F.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ge=s=>{switch(typeof s){case"undefined":return y.undefined;case"string":return y.string;case"number":return Number.isNaN(s)?y.nan:y.number;case"boolean":return y.boolean;case"function":return y.function;case"bigint":return y.bigint;case"symbol":return y.symbol;case"object":return Array.isArray(s)?y.array:s===null?y.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?y.promise:typeof Map!="undefined"&&s instanceof Map?y.map:typeof Set!="undefined"&&s instanceof Set?y.set:typeof Date!="undefined"&&s instanceof Date?y.date:y.object;default:return y.unknown}},d=F.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class he extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},r={_errors:[]},n=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(n);else if(i.code==="invalid_return_type")n(i.returnTypeError);else if(i.code==="invalid_arguments")n(i.argumentsError);else if(i.path.length===0)r._errors.push(t(i));else{let o=r,c=0;for(;c<i.path.length;){const u=i.path[c];c===i.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(t(i))):o[u]=o[u]||{_errors:[]},o=o[u],c++}}};return n(this),r}static assert(e){if(!(e instanceof he))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,F.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},r=[];for(const n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}he.create=s=>new he(s);const xt=(s,e)=>{let t;switch(s.code){case d.invalid_type:s.received===y.undefined?t="Required":t=`Expected ${s.expected}, received ${s.received}`;break;case d.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,F.jsonStringifyReplacer)}`;break;case d.unrecognized_keys:t=`Unrecognized key(s) in object: ${F.joinValues(s.keys,", ")}`;break;case d.invalid_union:t="Invalid input";break;case d.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${F.joinValues(s.options)}`;break;case d.invalid_enum_value:t=`Invalid enum value. Expected ${F.joinValues(s.options)}, received '${s.received}'`;break;case d.invalid_arguments:t="Invalid function arguments";break;case d.invalid_return_type:t="Invalid function return type";break;case d.invalid_date:t="Invalid date";break;case d.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:F.assertNever(s.validation):s.validation!=="regex"?t=`Invalid ${s.validation}`:t="Invalid";break;case d.too_small:s.type==="array"?t=`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?t=`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?t=`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?t=`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:t="Invalid input";break;case d.too_big:s.type==="array"?t=`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?t=`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?t=`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?t=`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?t=`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:t="Invalid input";break;case d.custom:t="Invalid input";break;case d.invalid_intersection_types:t="Intersection results could not be merged";break;case d.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case d.not_finite:t="Number must be finite";break;default:t=e.defaultError,F.assertNever(s)}return{message:t}};let Dr=xt;function Br(){return Dr}const Lr=s=>{const{data:e,path:t,errorMaps:r,issueData:n}=s,a=[...t,...n.path||[]],i=x(l({},n),{path:a});if(n.message!==void 0)return x(l({},n),{path:a,message:n.message});let o="";const c=r.filter(u=>!!u).slice().reverse();for(const u of c)o=u(i,{data:e,defaultError:o}).message;return x(l({},n),{path:a,message:o})};function m(s,e){const t=Br(),r=Lr({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===xt?void 0:xt].filter(n=>!!n)});s.common.issues.push(r)}class W{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const r=[];for(const n of t){if(n.status==="aborted")return k;n.status==="dirty"&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static mergeObjectAsync(e,t){return T(this,null,function*(){const r=[];for(const n of t){const a=yield n.key,i=yield n.value;r.push({key:a,value:i})}return W.mergeObjectSync(e,r)})}static mergeObjectSync(e,t){const r={};for(const n of t){const{key:a,value:i}=n;if(a.status==="aborted"||i.status==="aborted")return k;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value!=="__proto__"&&(typeof i.value!="undefined"||n.alwaysSet)&&(r[a.value]=i.value)}return{status:e.value,value:r}}}const k=Object.freeze({status:"aborted"}),Ue=s=>({status:"dirty",value:s}),ee=s=>({status:"valid",value:s}),Wt=s=>s.status==="aborted",Ht=s=>s.status==="dirty",Ee=s=>s.status==="valid",it=s=>typeof Promise!="undefined"&&s instanceof Promise;var v;(function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(v||(v={}));class ce{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Gt=(s,e)=>{if(Ee(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new he(s.common.issues);return this._error=t,this._error}}};function w(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:r,description:n}=s;if(e&&(t||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:n}:{errorMap:(i,o)=>{var u,h;const{message:c}=s;return i.code==="invalid_enum_value"?{message:c!=null?c:o.defaultError}:typeof o.data=="undefined"?{message:(u=c!=null?c:r)!=null?u:o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:(h=c!=null?c:t)!=null?h:o.defaultError}},description:n}}class O{get description(){return this._def.description}_getType(e){return ge(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ge(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new W,ctx:{common:e.parent.common,data:e.data,parsedType:ge(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(it(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){var a;const r={common:{issues:[],async:(a=t==null?void 0:t.async)!=null?a:!1,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ge(e)},n=this._parseSync({data:e,path:r.path,parent:r});return Gt(r,n)}"~validate"(e){var r,n;const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ge(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:t});return Ee(a)?{value:a.value}:{issues:t.common.issues}}catch(a){(n=(r=a==null?void 0:a.message)==null?void 0:r.toLowerCase())!=null&&n.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(a=>Ee(a)?{value:a.value}:{issues:t.common.issues})}parseAsync(e,t){return T(this,null,function*(){const r=yield this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error})}safeParseAsync(e,t){return T(this,null,function*(){const r={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ge(e)},n=this._parse({data:e,path:r.path,parent:r}),a=yield it(n)?n:Promise.resolve(n);return Gt(r,a)})}refine(e,t){const r=n=>typeof t=="string"||typeof t=="undefined"?{message:t}:typeof t=="function"?t(n):t;return this._refinement((n,a)=>{const i=e(n),o=()=>a.addIssue(l({code:d.custom},r(n)));return typeof Promise!="undefined"&&i instanceof Promise?i.then(c=>c?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,t){return this._refinement((r,n)=>e(r)?!0:(n.addIssue(typeof t=="function"?t(r,n):t),!1))}_refinement(e){return new oe({schema:this,typeName:b.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return fe.create(this,this._def)}nullable(){return je.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ie.create(this)}promise(){return ct.create(this,this._def)}or(e){return Ie.create([this,e],this._def)}and(e){return Te.create(this,e,this._def)}transform(e){return new oe(x(l({},w(this._def)),{schema:this,typeName:b.ZodEffects,effect:{type:"transform",transform:e}}))}default(e){const t=typeof e=="function"?e:()=>e;return new Ze(x(l({},w(this._def)),{innerType:this,defaultValue:t,typeName:b.ZodDefault}))}brand(){return new fn(l({typeName:b.ZodBranded,type:this},w(this._def)))}catch(e){const t=typeof e=="function"?e:()=>e;return new Ct(x(l({},w(this._def)),{innerType:this,catchValue:t,typeName:b.ZodCatch}))}describe(e){const t=this.constructor;return new t(x(l({},this._def),{description:e}))}pipe(e){return Nt.create(this,e)}readonly(){return St.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const zr=/^c[^\s-]{8,}$/i,Ur=/^[0-9a-z]+$/,qr=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Wr=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Hr=/^[a-z0-9_-]{21}$/i,Gr=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Yr=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Jr=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Qr="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let gt;const Xr=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Kr=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,en=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,tn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,sn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,rn=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ks="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",nn=new RegExp(`^${ks}$`);function xs(s){let e="[0-5]\\d";s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`);const t=s.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${t}`}function an(s){return new RegExp(`^${xs(s)}$`)}function on(s){let e=`${ks}T${xs(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function ln(s,e){return!!((e==="v4"||!e)&&Xr.test(s)||(e==="v6"||!e)&&en.test(s))}function cn(s,e){if(!Gr.test(s))return!1;try{const[t]=s.split("."),r=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),n=JSON.parse(atob(r));return!(typeof n!="object"||n===null||"typ"in n&&(n==null?void 0:n.typ)!=="JWT"||!n.alg||e&&n.alg!==e)}catch(t){return!1}}function un(s,e){return!!((e==="v4"||!e)&&Kr.test(s)||(e==="v6"||!e)&&tn.test(s))}class ae extends O{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==y.string){const a=this._getOrReturnCtx(e);return m(a,{code:d.invalid_type,expected:y.string,received:a.parsedType}),k}const r=new W;let n;for(const a of this._def.checks)if(a.kind==="min")e.data.length<a.value&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),r.dirty());else if(a.kind==="max")e.data.length>a.value&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),r.dirty());else if(a.kind==="length"){const i=e.data.length>a.value,o=e.data.length<a.value;(i||o)&&(n=this._getOrReturnCtx(e,n),i?m(n,{code:d.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):o&&m(n,{code:d.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),r.dirty())}else if(a.kind==="email")Jr.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"email",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="emoji")gt||(gt=new RegExp(Qr,"u")),gt.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"emoji",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="uuid")Wr.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"uuid",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="nanoid")Hr.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"nanoid",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="cuid")zr.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"cuid",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="cuid2")Ur.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"cuid2",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="ulid")qr.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"ulid",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="url")try{new URL(e.data)}catch(i){n=this._getOrReturnCtx(e,n),m(n,{validation:"url",code:d.invalid_string,message:a.message}),r.dirty()}else a.kind==="regex"?(a.regex.lastIndex=0,a.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"regex",code:d.invalid_string,message:a.message}),r.dirty())):a.kind==="trim"?e.data=e.data.trim():a.kind==="includes"?e.data.includes(a.value,a.position)||(n=this._getOrReturnCtx(e,n),m(n,{code:d.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),r.dirty()):a.kind==="toLowerCase"?e.data=e.data.toLowerCase():a.kind==="toUpperCase"?e.data=e.data.toUpperCase():a.kind==="startsWith"?e.data.startsWith(a.value)||(n=this._getOrReturnCtx(e,n),m(n,{code:d.invalid_string,validation:{startsWith:a.value},message:a.message}),r.dirty()):a.kind==="endsWith"?e.data.endsWith(a.value)||(n=this._getOrReturnCtx(e,n),m(n,{code:d.invalid_string,validation:{endsWith:a.value},message:a.message}),r.dirty()):a.kind==="datetime"?on(a).test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{code:d.invalid_string,validation:"datetime",message:a.message}),r.dirty()):a.kind==="date"?nn.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{code:d.invalid_string,validation:"date",message:a.message}),r.dirty()):a.kind==="time"?an(a).test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{code:d.invalid_string,validation:"time",message:a.message}),r.dirty()):a.kind==="duration"?Yr.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"duration",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="ip"?ln(e.data,a.version)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"ip",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="jwt"?cn(e.data,a.alg)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"jwt",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="cidr"?un(e.data,a.version)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"cidr",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="base64"?sn.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"base64",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="base64url"?rn.test(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{validation:"base64url",code:d.invalid_string,message:a.message}),r.dirty()):F.assertNever(a);return{status:r.value,value:e.data}}_regex(e,t,r){return this.refinement(n=>e.test(n),l({validation:t,code:d.invalid_string},v.errToObj(r)))}_addCheck(e){return new ae(x(l({},this._def),{checks:[...this._def.checks,e]}))}email(e){return this._addCheck(l({kind:"email"},v.errToObj(e)))}url(e){return this._addCheck(l({kind:"url"},v.errToObj(e)))}emoji(e){return this._addCheck(l({kind:"emoji"},v.errToObj(e)))}uuid(e){return this._addCheck(l({kind:"uuid"},v.errToObj(e)))}nanoid(e){return this._addCheck(l({kind:"nanoid"},v.errToObj(e)))}cuid(e){return this._addCheck(l({kind:"cuid"},v.errToObj(e)))}cuid2(e){return this._addCheck(l({kind:"cuid2"},v.errToObj(e)))}ulid(e){return this._addCheck(l({kind:"ulid"},v.errToObj(e)))}base64(e){return this._addCheck(l({kind:"base64"},v.errToObj(e)))}base64url(e){return this._addCheck(l({kind:"base64url"},v.errToObj(e)))}jwt(e){return this._addCheck(l({kind:"jwt"},v.errToObj(e)))}ip(e){return this._addCheck(l({kind:"ip"},v.errToObj(e)))}cidr(e){return this._addCheck(l({kind:"cidr"},v.errToObj(e)))}datetime(e){var t,r;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck(l({kind:"datetime",precision:typeof(e==null?void 0:e.precision)=="undefined"?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!=null?t:!1,local:(r=e==null?void 0:e.local)!=null?r:!1},v.errToObj(e==null?void 0:e.message)))}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck(l({kind:"time",precision:typeof(e==null?void 0:e.precision)=="undefined"?null:e==null?void 0:e.precision},v.errToObj(e==null?void 0:e.message)))}duration(e){return this._addCheck(l({kind:"duration"},v.errToObj(e)))}regex(e,t){return this._addCheck(l({kind:"regex",regex:e},v.errToObj(t)))}includes(e,t){return this._addCheck(l({kind:"includes",value:e,position:t==null?void 0:t.position},v.errToObj(t==null?void 0:t.message)))}startsWith(e,t){return this._addCheck(l({kind:"startsWith",value:e},v.errToObj(t)))}endsWith(e,t){return this._addCheck(l({kind:"endsWith",value:e},v.errToObj(t)))}min(e,t){return this._addCheck(l({kind:"min",value:e},v.errToObj(t)))}max(e,t){return this._addCheck(l({kind:"max",value:e},v.errToObj(t)))}length(e,t){return this._addCheck(l({kind:"length",value:e},v.errToObj(t)))}nonempty(e){return this.min(1,v.errToObj(e))}trim(){return new ae(x(l({},this._def),{checks:[...this._def.checks,{kind:"trim"}]}))}toLowerCase(){return new ae(x(l({},this._def),{checks:[...this._def.checks,{kind:"toLowerCase"}]}))}toUpperCase(){return new ae(x(l({},this._def),{checks:[...this._def.checks,{kind:"toUpperCase"}]}))}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}ae.create=s=>{var e;return new ae(l({checks:[],typeName:b.ZodString,coerce:(e=s==null?void 0:s.coerce)!=null?e:!1},w(s)))};function dn(s,e){const t=(s.toString().split(".")[1]||"").length,r=(e.toString().split(".")[1]||"").length,n=t>r?t:r,a=Number.parseInt(s.toFixed(n).replace(".","")),i=Number.parseInt(e.toFixed(n).replace(".",""));return a%i/Mt(10,n)}class Oe extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==y.number){const a=this._getOrReturnCtx(e);return m(a,{code:d.invalid_type,expected:y.number,received:a.parsedType}),k}let r;const n=new W;for(const a of this._def.checks)a.kind==="int"?F.isInteger(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{code:d.invalid_type,expected:"integer",received:"float",message:a.message}),n.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):a.kind==="multipleOf"?dn(e.data,a.value)!==0&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{code:d.not_finite,message:a.message}),n.dirty()):F.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,v.toString(t))}gt(e,t){return this.setLimit("min",e,!1,v.toString(t))}lte(e,t){return this.setLimit("max",e,!0,v.toString(t))}lt(e,t){return this.setLimit("max",e,!1,v.toString(t))}setLimit(e,t,r,n){return new Oe(x(l({},this._def),{checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:v.toString(n)}]}))}_addCheck(e){return new Oe(x(l({},this._def),{checks:[...this._def.checks,e]}))}int(e){return this._addCheck({kind:"int",message:v.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:v.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:v.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:v.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:v.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:v.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:v.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:v.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:v.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&F.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(t===null||r.value>t)&&(t=r.value):r.kind==="max"&&(e===null||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Oe.create=s=>new Oe(l({checks:[],typeName:b.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1},w(s)));class Ye extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch(a){return this._getInvalidInput(e)}if(this._getType(e)!==y.bigint)return this._getInvalidInput(e);let r;const n=new W;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):F.assertNever(a);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:y.bigint,received:t.parsedType}),k}gte(e,t){return this.setLimit("min",e,!0,v.toString(t))}gt(e,t){return this.setLimit("min",e,!1,v.toString(t))}lte(e,t){return this.setLimit("max",e,!0,v.toString(t))}lt(e,t){return this.setLimit("max",e,!1,v.toString(t))}setLimit(e,t,r,n){return new Ye(x(l({},this._def),{checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:v.toString(n)}]}))}_addCheck(e){return new Ye(x(l({},this._def),{checks:[...this._def.checks,e]}))}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:v.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:v.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:v.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:v.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:v.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Ye.create=s=>{var e;return new Ye(l({checks:[],typeName:b.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!=null?e:!1},w(s)))};class ot extends O{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==y.boolean){const r=this._getOrReturnCtx(e);return m(r,{code:d.invalid_type,expected:y.boolean,received:r.parsedType}),k}return ee(e.data)}}ot.create=s=>new ot(l({typeName:b.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1},w(s)));class lt extends O{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==y.date){const a=this._getOrReturnCtx(e);return m(a,{code:d.invalid_type,expected:y.date,received:a.parsedType}),k}if(Number.isNaN(e.data.getTime())){const a=this._getOrReturnCtx(e);return m(a,{code:d.invalid_date}),k}const r=new W;let n;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),r.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),r.dirty()):F.assertNever(a);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new lt(x(l({},this._def),{checks:[...this._def.checks,e]}))}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:v.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:v.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}lt.create=s=>new lt(l({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:b.ZodDate},w(s)));class Yt extends O{_parse(e){if(this._getType(e)!==y.symbol){const r=this._getOrReturnCtx(e);return m(r,{code:d.invalid_type,expected:y.symbol,received:r.parsedType}),k}return ee(e.data)}}Yt.create=s=>new Yt(l({typeName:b.ZodSymbol},w(s)));class Jt extends O{_parse(e){if(this._getType(e)!==y.undefined){const r=this._getOrReturnCtx(e);return m(r,{code:d.invalid_type,expected:y.undefined,received:r.parsedType}),k}return ee(e.data)}}Jt.create=s=>new Jt(l({typeName:b.ZodUndefined},w(s)));class Qt extends O{_parse(e){if(this._getType(e)!==y.null){const r=this._getOrReturnCtx(e);return m(r,{code:d.invalid_type,expected:y.null,received:r.parsedType}),k}return ee(e.data)}}Qt.create=s=>new Qt(l({typeName:b.ZodNull},w(s)));class Xt extends O{constructor(){super(...arguments),this._any=!0}_parse(e){return ee(e.data)}}Xt.create=s=>new Xt(l({typeName:b.ZodAny},w(s)));class Kt extends O{constructor(){super(...arguments),this._unknown=!0}_parse(e){return ee(e.data)}}Kt.create=s=>new Kt(l({typeName:b.ZodUnknown},w(s)));class ke extends O{_parse(e){const t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:y.never,received:t.parsedType}),k}}ke.create=s=>new ke(l({typeName:b.ZodNever},w(s)));class es extends O{_parse(e){if(this._getType(e)!==y.undefined){const r=this._getOrReturnCtx(e);return m(r,{code:d.invalid_type,expected:y.void,received:r.parsedType}),k}return ee(e.data)}}es.create=s=>new es(l({typeName:b.ZodVoid},w(s)));class ie extends O{_parse(e){const{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==y.array)return m(t,{code:d.invalid_type,expected:y.array,received:t.parsedType}),k;if(n.exactLength!==null){const i=t.data.length>n.exactLength.value,o=t.data.length<n.exactLength.value;(i||o)&&(m(t,{code:i?d.too_big:d.too_small,minimum:o?n.exactLength.value:void 0,maximum:i?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(n.minLength!==null&&t.data.length<n.minLength.value&&(m(t,{code:d.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),n.maxLength!==null&&t.data.length>n.maxLength.value&&(m(t,{code:d.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>n.type._parseAsync(new ce(t,i,t.path,o)))).then(i=>W.mergeArray(r,i));const a=[...t.data].map((i,o)=>n.type._parseSync(new ce(t,i,t.path,o)));return W.mergeArray(r,a)}get element(){return this._def.type}min(e,t){return new ie(x(l({},this._def),{minLength:{value:e,message:v.toString(t)}}))}max(e,t){return new ie(x(l({},this._def),{maxLength:{value:e,message:v.toString(t)}}))}length(e,t){return new ie(x(l({},this._def),{exactLength:{value:e,message:v.toString(t)}}))}nonempty(e){return this.min(1,e)}}ie.create=(s,e)=>new ie(l({type:s,minLength:null,maxLength:null,exactLength:null,typeName:b.ZodArray},w(e)));function Ae(s){if(s instanceof N){const e={};for(const t in s.shape){const r=s.shape[t];e[t]=fe.create(Ae(r))}return new N(x(l({},s._def),{shape:()=>e}))}else return s instanceof ie?new ie(x(l({},s._def),{type:Ae(s.element)})):s instanceof fe?fe.create(Ae(s.unwrap())):s instanceof je?je.create(Ae(s.unwrap())):s instanceof xe?xe.create(s.items.map(e=>Ae(e))):s}class N extends O{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=F.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==y.object){const u=this._getOrReturnCtx(e);return m(u,{code:d.invalid_type,expected:y.object,received:u.parsedType}),k}const{status:r,ctx:n}=this._processInputParams(e),{shape:a,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof ke&&this._def.unknownKeys==="strip"))for(const u in n.data)i.includes(u)||o.push(u);const c=[];for(const u of i){const h=a[u],_=n.data[u];c.push({key:{status:"valid",value:u},value:h._parse(new ce(n,_,n.path,u)),alwaysSet:u in n.data})}if(this._def.catchall instanceof ke){const u=this._def.unknownKeys;if(u==="passthrough")for(const h of o)c.push({key:{status:"valid",value:h},value:{status:"valid",value:n.data[h]}});else if(u==="strict")o.length>0&&(m(n,{code:d.unrecognized_keys,keys:o}),r.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const h of o){const _=n.data[h];c.push({key:{status:"valid",value:h},value:u._parse(new ce(n,_,n.path,h)),alwaysSet:h in n.data})}}return n.common.async?Promise.resolve().then(()=>T(this,null,function*(){const u=[];for(const h of c){const _=yield h.key,g=yield h.value;u.push({key:_,value:g,alwaysSet:h.alwaysSet})}return u})).then(u=>W.mergeObjectSync(r,u)):W.mergeObjectSync(r,c)}get shape(){return this._def.shape()}strict(e){return v.errToObj,new N(l(x(l({},this._def),{unknownKeys:"strict"}),e!==void 0?{errorMap:(t,r)=>{var a,i,o,c;const n=(o=(i=(a=this._def).errorMap)==null?void 0:i.call(a,t,r).message)!=null?o:r.defaultError;return t.code==="unrecognized_keys"?{message:(c=v.errToObj(e).message)!=null?c:n}:{message:n}}}:{}))}strip(){return new N(x(l({},this._def),{unknownKeys:"strip"}))}passthrough(){return new N(x(l({},this._def),{unknownKeys:"passthrough"}))}extend(e){return new N(x(l({},this._def),{shape:()=>l(l({},this._def.shape()),e)}))}merge(e){return new N({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>l(l({},this._def.shape()),e._def.shape()),typeName:b.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new N(x(l({},this._def),{catchall:e}))}pick(e){const t={};for(const r of F.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new N(x(l({},this._def),{shape:()=>t}))}omit(e){const t={};for(const r of F.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new N(x(l({},this._def),{shape:()=>t}))}deepPartial(){return Ae(this)}partial(e){const t={};for(const r of F.objectKeys(this.shape)){const n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}return new N(x(l({},this._def),{shape:()=>t}))}required(e){const t={};for(const r of F.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let a=this.shape[r];for(;a instanceof fe;)a=a._def.innerType;t[r]=a}return new N(x(l({},this._def),{shape:()=>t}))}keyof(){return ws(F.objectKeys(this.shape))}}N.create=(s,e)=>new N(l({shape:()=>s,unknownKeys:"strip",catchall:ke.create(),typeName:b.ZodObject},w(e)));N.strictCreate=(s,e)=>new N(l({shape:()=>s,unknownKeys:"strict",catchall:ke.create(),typeName:b.ZodObject},w(e)));N.lazycreate=(s,e)=>new N(l({shape:s,unknownKeys:"strip",catchall:ke.create(),typeName:b.ZodObject},w(e)));class Ie extends O{_parse(e){const{ctx:t}=this._processInputParams(e),r=this._def.options;function n(a){for(const o of a)if(o.result.status==="valid")return o.result;for(const o of a)if(o.result.status==="dirty")return t.common.issues.push(...o.ctx.common.issues),o.result;const i=a.map(o=>new he(o.ctx.common.issues));return m(t,{code:d.invalid_union,unionErrors:i}),k}if(t.common.async)return Promise.all(r.map(a=>T(this,null,function*(){const i=x(l({},t),{common:x(l({},t.common),{issues:[]}),parent:null});return{result:yield a._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}}))).then(n);{let a;const i=[];for(const c of r){const u=x(l({},t),{common:x(l({},t.common),{issues:[]}),parent:null}),h=c._parseSync({data:t.data,path:t.path,parent:u});if(h.status==="valid")return h;h.status==="dirty"&&!a&&(a={result:h,ctx:u}),u.common.issues.length&&i.push(u.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const o=i.map(c=>new he(c));return m(t,{code:d.invalid_union,unionErrors:o}),k}}get options(){return this._def.options}}Ie.create=(s,e)=>new Ie(l({options:s,typeName:b.ZodUnion},w(e)));function wt(s,e){const t=ge(s),r=ge(e);if(s===e)return{valid:!0,data:s};if(t===y.object&&r===y.object){const n=F.objectKeys(e),a=F.objectKeys(s).filter(o=>n.indexOf(o)!==-1),i=l(l({},s),e);for(const o of a){const c=wt(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}else if(t===y.array&&r===y.array){if(s.length!==e.length)return{valid:!1};const n=[];for(let a=0;a<s.length;a++){const i=s[a],o=e[a],c=wt(i,o);if(!c.valid)return{valid:!1};n.push(c.data)}return{valid:!0,data:n}}else return t===y.date&&r===y.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}class Te extends O{_parse(e){const{status:t,ctx:r}=this._processInputParams(e),n=(a,i)=>{if(Wt(a)||Wt(i))return k;const o=wt(a.value,i.value);return o.valid?((Ht(a)||Ht(i))&&t.dirty(),{status:t.value,value:o.data}):(m(r,{code:d.invalid_intersection_types}),k)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([a,i])=>n(a,i)):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}Te.create=(s,e,t)=>new Te(l({left:s,right:e,typeName:b.ZodIntersection},w(t)));class xe extends O{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==y.array)return m(r,{code:d.invalid_type,expected:y.array,received:r.parsedType}),k;if(r.data.length<this._def.items.length)return m(r,{code:d.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),k;!this._def.rest&&r.data.length>this._def.items.length&&(m(r,{code:d.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...r.data].map((i,o)=>{const c=this._def.items[o]||this._def.rest;return c?c._parse(new ce(r,i,r.path,o)):null}).filter(i=>!!i);return r.common.async?Promise.all(a).then(i=>W.mergeArray(t,i)):W.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new xe(x(l({},this._def),{rest:e}))}}xe.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new xe(l({items:s,typeName:b.ZodTuple,rest:null},w(e)))};class Re extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==y.object)return m(r,{code:d.invalid_type,expected:y.object,received:r.parsedType}),k;const n=[],a=this._def.keyType,i=this._def.valueType;for(const o in r.data)n.push({key:a._parse(new ce(r,o,r.path,o)),value:i._parse(new ce(r,r.data[o],r.path,o)),alwaysSet:o in r.data});return r.common.async?W.mergeObjectAsync(t,n):W.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return t instanceof O?new Re(l({keyType:e,valueType:t,typeName:b.ZodRecord},w(r))):new Re(l({keyType:ae.create(),valueType:e,typeName:b.ZodRecord},w(t)))}}class ts extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==y.map)return m(r,{code:d.invalid_type,expected:y.map,received:r.parsedType}),k;const n=this._def.keyType,a=this._def.valueType,i=[...r.data.entries()].map(([o,c],u)=>({key:n._parse(new ce(r,o,r.path,[u,"key"])),value:a._parse(new ce(r,c,r.path,[u,"value"]))}));if(r.common.async){const o=new Map;return Promise.resolve().then(()=>T(this,null,function*(){for(const c of i){const u=yield c.key,h=yield c.value;if(u.status==="aborted"||h.status==="aborted")return k;(u.status==="dirty"||h.status==="dirty")&&t.dirty(),o.set(u.value,h.value)}return{status:t.value,value:o}}))}else{const o=new Map;for(const c of i){const u=c.key,h=c.value;if(u.status==="aborted"||h.status==="aborted")return k;(u.status==="dirty"||h.status==="dirty")&&t.dirty(),o.set(u.value,h.value)}return{status:t.value,value:o}}}}ts.create=(s,e,t)=>new ts(l({valueType:e,keyType:s,typeName:b.ZodMap},w(t)));class Je extends O{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==y.set)return m(r,{code:d.invalid_type,expected:y.set,received:r.parsedType}),k;const n=this._def;n.minSize!==null&&r.data.size<n.minSize.value&&(m(r,{code:d.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),n.maxSize!==null&&r.data.size>n.maxSize.value&&(m(r,{code:d.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const u=new Set;for(const h of c){if(h.status==="aborted")return k;h.status==="dirty"&&t.dirty(),u.add(h.value)}return{status:t.value,value:u}}const o=[...r.data.values()].map((c,u)=>a._parse(new ce(r,c,r.path,u)));return r.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Je(x(l({},this._def),{minSize:{value:e,message:v.toString(t)}}))}max(e,t){return new Je(x(l({},this._def),{maxSize:{value:e,message:v.toString(t)}}))}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Je.create=(s,e)=>new Je(l({valueType:s,minSize:null,maxSize:null,typeName:b.ZodSet},w(e)));class ss extends O{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ss.create=(s,e)=>new ss(l({getter:s,typeName:b.ZodLazy},w(e)));class rs extends O{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return m(t,{received:t.data,code:d.invalid_literal,expected:this._def.value}),k}return{status:"valid",value:e.data}}get value(){return this._def.value}}rs.create=(s,e)=>new rs(l({value:s,typeName:b.ZodLiteral},w(e)));function ws(s,e){return new $e(l({values:s,typeName:b.ZodEnum},w(e)))}class $e extends O{_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),r=this._def.values;return m(t,{expected:F.joinValues(r),received:t.parsedType,code:d.invalid_type}),k}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),r=this._def.values;return m(t,{received:t.data,code:d.invalid_enum_value,options:r}),k}return ee(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return $e.create(e,l(l({},this._def),t))}exclude(e,t=this._def){return $e.create(this.options.filter(r=>!e.includes(r)),l(l({},this._def),t))}}$e.create=ws;class ns extends O{_parse(e){const t=F.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==y.string&&r.parsedType!==y.number){const n=F.objectValues(t);return m(r,{expected:F.joinValues(n),received:r.parsedType,code:d.invalid_type}),k}if(this._cache||(this._cache=new Set(F.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const n=F.objectValues(t);return m(r,{received:r.data,code:d.invalid_enum_value,options:n}),k}return ee(e.data)}get enum(){return this._def.values}}ns.create=(s,e)=>new ns(l({values:s,typeName:b.ZodNativeEnum},w(e)));class ct extends O{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==y.promise&&t.common.async===!1)return m(t,{code:d.invalid_type,expected:y.promise,received:t.parsedType}),k;const r=t.parsedType===y.promise?t.data:Promise.resolve(t.data);return ee(r.then(n=>this._def.type.parseAsync(n,{path:t.path,errorMap:t.common.contextualErrorMap})))}}ct.create=(s,e)=>new ct(l({type:s,typeName:b.ZodPromise},w(e)));class oe extends O{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===b.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,a={addIssue:i=>{m(r,i),i.fatal?t.abort():t.dirty()},get path(){return r.path}};if(a.addIssue=a.addIssue.bind(a),n.type==="preprocess"){const i=n.transform(r.data,a);if(r.common.async)return Promise.resolve(i).then(o=>T(this,null,function*(){if(t.value==="aborted")return k;const c=yield this._def.schema._parseAsync({data:o,path:r.path,parent:r});return c.status==="aborted"?k:c.status==="dirty"||t.value==="dirty"?Ue(c.value):c}));{if(t.value==="aborted")return k;const o=this._def.schema._parseSync({data:i,path:r.path,parent:r});return o.status==="aborted"?k:o.status==="dirty"||t.value==="dirty"?Ue(o.value):o}}if(n.type==="refinement"){const i=o=>{const c=n.refinement(o,a);if(r.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(r.common.async===!1){const o=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return o.status==="aborted"?k:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(o=>o.status==="aborted"?k:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(n.type==="transform")if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!Ee(i))return k;const o=n.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>Ee(i)?Promise.resolve(n.transform(i.value,a)).then(o=>({status:t.value,value:o})):k);F.assertNever(n)}}oe.create=(s,e,t)=>new oe(l({schema:s,typeName:b.ZodEffects,effect:e},w(t)));oe.createWithPreprocess=(s,e,t)=>new oe(l({schema:e,effect:{type:"preprocess",transform:s},typeName:b.ZodEffects},w(t)));class fe extends O{_parse(e){return this._getType(e)===y.undefined?ee(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}fe.create=(s,e)=>new fe(l({innerType:s,typeName:b.ZodOptional},w(e)));class je extends O{_parse(e){return this._getType(e)===y.null?ee(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}je.create=(s,e)=>new je(l({innerType:s,typeName:b.ZodNullable},w(e)));class Ze extends O{_parse(e){const{ctx:t}=this._processInputParams(e);let r=t.data;return t.parsedType===y.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Ze.create=(s,e)=>new Ze(l({innerType:s,typeName:b.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default},w(e)));class Ct extends O{_parse(e){const{ctx:t}=this._processInputParams(e),r=x(l({},t),{common:x(l({},t.common),{issues:[]})}),n=this._def.innerType._parse({data:r.data,path:r.path,parent:l({},r)});return it(n)?n.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new he(r.common.issues)},input:r.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new he(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}Ct.create=(s,e)=>new Ct(l({innerType:s,typeName:b.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch},w(e)));class as extends O{_parse(e){if(this._getType(e)!==y.nan){const r=this._getOrReturnCtx(e);return m(r,{code:d.invalid_type,expected:y.nan,received:r.parsedType}),k}return{status:"valid",value:e.data}}}as.create=s=>new as(l({typeName:b.ZodNaN},w(s)));class fn extends O{_parse(e){const{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class Nt extends O{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return T(this,null,function*(){const a=yield this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return a.status==="aborted"?k:a.status==="dirty"?(t.dirty(),Ue(a.value)):this._def.out._parseAsync({data:a.value,path:r.path,parent:r})});{const n=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return n.status==="aborted"?k:n.status==="dirty"?(t.dirty(),{status:"dirty",value:n.value}):this._def.out._parseSync({data:n.value,path:r.path,parent:r})}}static create(e,t){return new Nt({in:e,out:t,typeName:b.ZodPipeline})}}class St extends O{_parse(e){const t=this._def.innerType._parse(e),r=n=>(Ee(n)&&(n.value=Object.freeze(n.value)),n);return it(t)?t.then(n=>r(n)):r(t)}unwrap(){return this._def.innerType}}St.create=(s,e)=>new St(l({innerType:s,typeName:b.ZodReadonly},w(e)));N.lazycreate;var b;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(b||(b={}));const zn=ae.create,Un=ot.create;ke.create;ie.create;const hn=N.create;N.strictCreate;Ie.create;Te.create;xe.create;Re.create;$e.create;ct.create;oe.create;fe.create;je.create;oe.createWithPreprocess;const Ce=(s,e)=>s.constructor.name===e.name,z=new Map;z.set(ot.name,()=>!1),z.set(Oe.name,()=>0),z.set(ae.name,()=>""),z.set(ie.name,()=>[]),z.set(Re.name,()=>({})),z.set(Ze.name,s=>s._def.defaultValue()),z.set(oe.name,s=>qe(s._def.schema)),z.set(fe.name,s=>Ce(s._def.innerType,Ze)?s._def.innerType._def.defaultValue():void 0),z.set(xe.name,s=>{const e=[];for(const t of s._def.items)e.push(qe(t));return e}),z.set(oe.name,s=>qe(s._def.schema)),z.set(Ie.name,s=>qe(s._def.options[0])),z.set(N.name,s=>_e(s)),z.set(Re.name,s=>_e(s)),z.set(Te.name,s=>_e(s));function qe(s){const e=s.constructor.name;if(!z.has(e)){console.warn("getSchemaDefaultForField: Unhandled type",s.constructor.name);return}return z.get(e)(s)}function _e(s){if(Ce(s,Re))return{};if(Ce(s,oe))return _e(s._def.schema);if(Ce(s,Te))return l(l({},_e(s._def.left)),_e(s._def.right));if(Ce(s,Ie)){for(const e of s._def.options)if(Ce(e,N))return _e(e);return console.warn("getSchemaDefaultObject: No object found in union, returning empty object"),{}}return Ce(s,N)?Object.fromEntries(Object.entries(s.shape).map(([e,t])=>[e,qe(t)]).filter(e=>e[1]!==void 0)):(console.warn(`getSchemaDefaultObject: Expected object schema, got ${s.constructor.name}`),{})}function mn(s){return _e(s)}const[pn,yn]=At("VbenFormProps"),[vn,gn]=At("ComponentRefMap");function _n(s){var o;const e=zs(),t=a(),r=ur(l({},(o=Object.keys(t))!=null&&o.length?{initialValues:t}:{})),n=I(()=>{const c=[];for(const u of Object.keys(e))u!=="default"&&c.push(u);return c});function a(){const c={},u={};(f(s).schema||[]).forEach(g=>{if(Reflect.has(g,"defaultValue"))_t(c,g.fieldName,g.defaultValue);else if(g.rules&&!de(g.rules)){const S=i(g.rules);u[g.fieldName]=g.rules,S!==void 0&&(c[g.fieldName]=S)}});const h=mn(hn(u)),_={};for(const g in h)_t(_,g,h[g]);return We(c,_)}function i(c){if(c instanceof ae)return"";if(c instanceof Oe)return null;if(c instanceof N){const u={};for(const[h,_]of Object.entries(c.shape))u[h]=i(_);return u}else if(c instanceof Te){const u=i(c._def.left),h=i(c._def.right);return typeof u=="object"&&typeof h=="object"?l(l({},u),h):u!=null?u:h}else return}return{delegatedSlots:n,form:r}}const bn=G({__name:"form-actions",props:{modelValue:{default:!1},modelModifiers:{}},emits:["update:modelValue"],setup(s,{expose:e}){const{$t:t}=dr(),[r,n]=pn(),a=us(s,"modelValue"),i=I(()=>l({content:`${t.value("reset")}`,show:!0},f(r).resetButtonOptions)),o=I(()=>l({content:`${t.value("submit")}`,show:!0},f(r).submitButtonOptions)),c=I(()=>f(r).actionWrapperClass?{}:{"grid-column":"-2 / -1",marginLeft:"auto"});function u(_){return T(this,null,function*(){var C;_==null||_.preventDefault(),_==null||_.stopPropagation();const g=f(r);if(!g.formApi)return;const{valid:S}=yield g.formApi.validate();if(!S)return;const Z=Se(yield g.formApi.getValues());yield(C=g.handleSubmit)==null?void 0:C.call(g,Z)})}function h(_){return T(this,null,function*(){var Z,C;_==null||_.preventDefault(),_==null||_.stopPropagation();const g=f(r),S=Se(yield(Z=g.formApi)==null?void 0:Z.getValues());J(g.handleReset)?yield(C=g.handleReset)==null?void 0:C.call(g,S):n.resetForm()})}return be(()=>a.value,()=>{f(r).collapseTriggerResize&&Us()}),e({handleReset:h,handleSubmit:u}),(_,g)=>(R(),le("div",{class:Q(f(q)("col-span-full w-full text-right",f(r).compact?"pb-2":"pb-6",f(r).actionWrapperClass)),style:ds(c.value)},[f(r).actionButtonsReverse?(R(),le(bt,{key:0},[j(_.$slots,"submit-before"),o.value.show?(R(),P(He(f(st).PrimaryButton),ne({key:0,class:"ml-3",type:"button",onClick:u},o.value),{default:V(()=>[tt(Le(o.value.content),1)]),_:1},16)):B("",!0)],64)):B("",!0),j(_.$slots,"reset-before"),i.value.show?(R(),P(He(f(st).DefaultButton),ne({key:1,class:"ml-3",type:"button",onClick:h},i.value),{default:V(()=>[tt(Le(i.value.content),1)]),_:1},16)):B("",!0),f(r).actionButtonsReverse?B("",!0):(R(),le(bt,{key:2},[j(_.$slots,"submit-before"),o.value.show?(R(),P(He(f(st).PrimaryButton),ne({key:0,class:"ml-3",type:"button",onClick:u},o.value),{default:V(()=>[tt(Le(o.value.content),1)]),_:1},16)):B("",!0)],64)),j(_.$slots,"expand-before"),f(r).showCollapseButton?(R(),P(f(Nr),{key:3,"model-value":a.value,"onUpdate:modelValue":g[0]||(g[0]=S=>a.value=S),class:"ml-2"},{default:V(()=>[Ge("span",null,Le(a.value?f(t)("expand"):f(t)("collapse")),1)]),_:1},8,["model-value"])):B("",!0),j(_.$slots,"expand-after")],6))}});const is=s=>s!==null&&!!s&&typeof s=="object"&&!Array.isArray(s);function Cs(s){return Number(s)>=0}function kn(s){return typeof s=="object"&&s!==null}function xn(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(s)}function os(s){if(!kn(s)||xn(s)!=="[object Object]")return!1;if(Object.getPrototypeOf(s)===null)return!0;let e=s;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(s)===e}function Ss(s,e){return Object.keys(e).forEach(t=>{if(os(e[t])&&os(s[t])){s[t]||(s[t]={}),Ss(s[t],e[t]);return}s[t]=e[t]}),s}function wn(s){const e=s.split(".");if(!e.length)return"";let t=String(e[0]);for(let r=1;r<e.length;r++){if(Cs(e[r])){t+=`[${e[r]}]`;continue}t+=`.${e[r]}`}return t}function Cn(s,e){return{__type:"VVTypedSchema",parse(n){return T(this,null,function*(){const a=yield s.safeParseAsync(n,e);if(a.success)return{value:a.data,errors:[]};const i={};return Os(a.error.issues,i),{errors:Object.values(i)}})},cast(n){try{return s.parse(n)}catch(a){const i=Ts(s);return is(i)&&is(n)?Ss(i,n):n}},describe(n){try{if(!n)return{required:!s.isOptional(),exists:!0};const a=Sn(n,s);return a?{required:!a.isOptional(),exists:!0}:{required:!1,exists:!1}}catch(a){return{required:!1,exists:!1}}}}}function Os(s,e){s.forEach(t=>{const r=wn(t.path.join("."));t.code==="invalid_union"&&(Os(t.unionErrors.flatMap(n=>n.issues),e),!r)||(e[r]||(e[r]={errors:[],path:r}),e[r].errors.push(t.message))})}function Ts(s){if(s instanceof N)return Object.fromEntries(Object.entries(s.shape).map(([e,t])=>t instanceof Ze?[e,t._def.defaultValue()]:t instanceof N?[e,Ts(t)]:[e,void 0]))}function Sn(s,e){if(!ls(e))return null;if(fr(s))return e.shape[hr(s)];const t=(s||"").split(/\.|\[(\d+)\]/).filter(Boolean);let r=e;for(let n=0;n<=t.length;n++){const a=t[n];if(!a||!r)return r;if(ls(r)){r=r.shape[a]||null;continue}Cs(a)&&On(r)&&(r=r._def.type)}return null}function Rs(s){return s._def.typeName}function On(s){return Rs(s)===b.ZodArray}function ls(s){return Rs(s)===b.ZodObject}const[Vt,Tn]=At("FormRenderProps"),Rn=()=>{const s=Vt(),e=I(()=>s.layout==="vertical"),t=I(()=>s.componentMap);return{componentBindEventMap:I(()=>s.componentBindEventMap),componentMap:t,isVertical:e}};function Fn(s){const e=gs(),r=Vt().form;if(!e)throw new Error("useDependencies should be used within <VbenForm>");const n=ue(!0),a=ue(!1),i=ue(!0),o=ue(!1),c=ue({}),u=ue(),h=I(()=>{var S,Z;return((Z=(S=s())==null?void 0:S.triggerFields)!=null?Z:[]).map(C=>e.value[C])}),_=()=>{a.value=!1,n.value=!0,i.value=!0,o.value=!1,u.value=void 0,c.value={}};return be([h,s],Z=>T(null,[Z],function*([g,S]){var K;if(!S||!((K=S==null?void 0:S.triggerFields)!=null&&K.length))return;_();const{componentProps:C,disabled:A,if:M,required:E,rules:$,show:H,trigger:X}=S,te=e.value;if(J(M)){if(n.value=!!(yield M(te,r)),!n.value)return}else if(yt(M)&&(n.value=M,!n.value))return;if(J(H)){if(i.value=!!(yield H(te,r)),!i.value)return}else if(yt(H)&&(i.value=H,!i.value))return;J(C)&&(c.value=yield C(te,r)),J($)&&(u.value=yield $(te,r)),J(A)?a.value=!!(yield A(te,r)):yt(A)&&(a.value=A),J(E)&&(o.value=!!(yield E(te,r))),J(X)&&(yield X(te,r))}),{deep:!0,immediate:!0}),{dynamicComponentProps:c,dynamicRules:u,isDisabled:a,isIf:n,isRequired:o,isShow:i}}const An={key:0,class:"text-destructive mr-[2px]"},Nn={key:2,class:"ml-[2px]"},Vn=G({__name:"form-label",props:{class:{},colon:{type:Boolean},help:{type:[Function,String]},label:{type:[Function,String]},required:{type:Boolean}},setup(s){const e=s;return(t,r)=>(R(),P(f(Fr),{class:Q(f(q)("flex items-center",e.class))},{default:V(()=>[t.required?(R(),le("span",An,"*")):B("",!0),j(t.$slots,"default"),t.help?(R(),P(f(Ar),{key:1,"trigger-class":"size-3.5 ml-1"},{default:V(()=>[re(f(ze),{content:t.help},null,8,["content"])]),_:1})):B("",!0),t.colon&&t.label?(R(),le("span",Nn,":")):B("",!0)]),_:3},8,["class"]))}});function Ot(s){return!s||de(s)?null:"innerType"in s._def?Ot(s._def.innerType):"schema"in s._def?Ot(s._def.schema):s}function Tt(s){if(!s||de(s))return;const e=s;if(e._def.typeName==="ZodDefault")return e._def.defaultValue();if("innerType"in e._def)return Tt(e._def.innerType);if("schema"in e._def)return Tt(e._def.schema)}function cs(s){return!s||!Rt(s)?!1:Reflect.has(s,"target")&&Reflect.has(s,"stopPropagation")}const En={class:"flex-auto overflow-hidden p-[1px]"},In={key:0,class:"ml-1"},$n=G({__name:"form-field",props:{component:{},componentProps:{type:Function},defaultValue:{},dependencies:{},description:{type:[Function,String]},fieldName:{},help:{type:[Function,String]},label:{type:[Function,String]},renderComponentContent:{type:Function},rules:{},suffix:{type:[Function,String]},colon:{type:Boolean},controlClass:{},disabled:{type:Boolean},disabledOnChangeListener:{type:Boolean},disabledOnInputListener:{type:Boolean},emptyStateValue:{},formFieldProps:{},formItemClass:{},hideLabel:{type:Boolean},hideRequiredMark:{type:Boolean},labelClass:{},labelWidth:{},modelPropName:{},wrapperClass:{},commonComponentProps:{}},setup(s){const{componentBindEventMap:e,componentMap:t,isVertical:r}=Rn(),n=Vt(),a=gs(),i=ys(s.fieldName),o=fs("fieldComponentRef"),c=n.form,u=n.compact,h=I(()=>{var p;return((p=i.value)==null?void 0:p.length)>0}),_=I(()=>{const p=de(s.component)?t.value[s.component]:s.component;return p||console.warn(`Component ${s.component} is not registered`),p}),{dynamicComponentProps:g,dynamicRules:S,isDisabled:Z,isIf:C,isRequired:A,isShow:M}=Fn(()=>s.dependencies),E=I(()=>{var p;return(p=s.labelClass)!=null&&p.includes("w-")||r.value?{}:{width:`${s.labelWidth}px`}}),$=I(()=>S.value||s.rules),H=I(()=>C.value&&M.value),X=I(()=>{var U,L,pe,Pe,De,Be;if(!H.value)return!1;if(!$.value)return A.value;if(A.value)return!0;if(de($.value))return["required","selectRequired"].includes($.value);let p=(L=(U=$==null?void 0:$.value)==null?void 0:U.isOptional)==null?void 0:L.call(U);if(((Pe=(pe=$==null?void 0:$.value)==null?void 0:pe._def)==null?void 0:Pe.typeName)==="ZodDefault"){const se=(De=$==null?void 0:$.value)==null?void 0:De._def.innerType;se&&(p=(Be=se.isOptional)==null?void 0:Be.call(se))}return!p}),te=I(()=>{var U;if(!H.value)return null;let p=$.value;if(!p)return A.value?"required":null;if(de(p))return p;if(!!X.value){const L=(U=p==null?void 0:p.unwrap)==null?void 0:U.call(p);L&&(p=L)}return Cn(p)}),K=I(()=>{const p=J(s.componentProps)?s.componentProps(a.value,c):s.componentProps;return l(l(l({},s.commonComponentProps),p),g.value)});be(()=>{var p;return(p=K.value)==null?void 0:p.autofocus},p=>{p===!0&&nt(()=>{ht()})},{immediate:!0});const Qe=I(()=>{var p;return Z.value||s.disabled||((p=K.value)==null?void 0:p.disabled)}),Xe=I(()=>J(s.renderComponentContent)?s.renderComponentContent(a.value,c):{}),dt=I(()=>Object.keys(Xe.value)),me=I(()=>{const p=te.value;return l(l({keepValue:!0,label:de(s.label)?s.label:""},p?{rules:p}:{}),s.formFieldProps)});function ft(p){var Pe,De,Be;const D=p.componentField.modelValue,U=p.componentField["onUpdate:modelValue"],L=s.modelPropName||(de(s.component)?(Pe=e.value)==null?void 0:Pe[s.component]:null);let pe=D;return D&&Rt(D)&&L&&(pe=cs(D)?(De=D==null?void 0:D.target)==null?void 0:De[L]:(Be=D==null?void 0:D[L])!=null?Be:D),L?l({[`onUpdate:${L}`]:U,[L]:pe===void 0?s.emptyStateValue:pe,onChange:s.disabledOnChangeListener?void 0:se=>{var Et,It,$t;const Fs=cs(se),Fe=(Et=p==null?void 0:p.componentField)==null?void 0:Et.onChange;return Fs?Fe==null?void 0:Fe(($t=(It=se==null?void 0:se.target)==null?void 0:It[L])!=null?$t:se):Fe==null?void 0:Fe(se)}},s.disabledOnInputListener?{onInput:void 0}:{}):l(l({},s.disabledOnInputListener?{onInput:void 0}:{}),s.disabledOnChangeListener?{onChange:void 0}:{})}function Me(p){const D=ft(p);return l(l(l(l(l({},p.componentField),K.value),D),Reflect.has(K.value,"onChange")?{onChange:K.value.onChange}:{}),Reflect.has(K.value,"onInput")?{onInput:K.value.onInput}:{})}function ht(){var p,D;o.value&&J(o.value.focus)&&document.activeElement!==o.value&&((D=(p=o.value)==null?void 0:p.focus)==null||D.call(p))}const we=vn();return be(o,p=>{we==null||we.set(s.fieldName,p)}),qs(()=>{we!=null&&we.has(s.fieldName)&&we.delete(s.fieldName)}),(p,D)=>f(C)?(R(),P(f(mr),ne({key:0},me.value,{name:p.fieldName}),{default:V(U=>[Ws(re(f(Tr),ne({class:[{"form-valid-error":h.value,"form-is-required":X.value,"flex-col":f(r),"flex-row items-center":!f(r),"pb-6":!f(u),"pb-2":f(u)},"relative flex"]},p.$attrs),{default:V(()=>[p.hideLabel?B("",!0):(R(),P(Vn,{key:0,class:Q(f(q)("flex leading-6",{"mr-2 flex-shrink-0 justify-end":!f(r),"mb-1 flex-row":f(r)},p.labelClass)),help:p.help,colon:p.colon,label:p.label,required:X.value&&!p.hideRequiredMark,style:ds(E.value)},{default:V(()=>[p.label?(R(),P(f(ze),{key:0,content:p.label},null,8,["content"])):B("",!0)]),_:1},8,["class","help","colon","label","required","style"])),Ge("div",En,[Ge("div",{class:Q(f(q)("relative flex w-full items-center",p.wrapperClass))},[re(f(Cr),{class:Q(f(q)(p.controlClass))},{default:V(()=>[j(p.$slots,"default",ye(ve(x(l(l({},U),Me(U)),{disabled:Qe.value,isInValid:h.value}))),()=>[(R(),P(He(_.value),ne({ref_key:"fieldComponentRef",ref:o,class:{"border-destructive focus:border-destructive hover:border-destructive/80 focus:shadow-[0_0_0_2px_rgba(255,38,5,0.06)]":h.value}},Me(U),{disabled:Qe.value}),hs({_:2},[Ft(dt.value,L=>({name:L,fn:V(pe=>[re(f(ze),ne({content:Xe.value[L]},x(l({},pe),{formContext:U})),null,16,["content"])])}))]),1040,["class","disabled"])),f(u)&&h.value?(R(),P(f(vs),{key:0,"delay-duration":300,side:"left"},{trigger:V(()=>[j(p.$slots,"trigger",{},()=>[re(f(xr),{class:Q(f(q)("text-foreground/80 hover:text-foreground inline-flex size-5 cursor-pointer"))},null,8,["class"])])]),default:V(()=>[re(f(zt))]),_:3})):B("",!0)])]),_:2},1032,["class"]),p.suffix?(R(),le("div",In,[re(f(ze),{content:p.suffix},null,8,["content"])])):B("",!0),p.description?(R(),P(f(Or),{key:1,class:"ml-1"},{default:V(()=>[re(f(ze),{content:p.description},null,8,["content"])]),_:1})):B("",!0)],2),f(u)?B("",!0):(R(),P(yr,{key:0,name:"slide-up"},{default:V(()=>[re(f(zt),{class:"absolute bottom-1"})]),_:1}))])]),_:2},1040,["class"]),[[pr,f(M)]])]),_:3},16,["name"])):B("",!0)}});function jn(s){const e=fs("wrapperRef"),t=Hs(e),r=ue({}),n=ue(!1),a=Gs(Ys),i=I(()=>{var _,g;const c=(_=s.collapsedRows)!=null?_:1,u=r.value;let h=0;for(let S=1;S<=c;S++)h+=(g=u==null?void 0:u[S])!=null?g:0;return h-1||1});be([()=>s.showCollapseButton,()=>a.active().value,()=>{var c;return(c=s.schema)==null?void 0:c.length},()=>t.value],u=>T(null,[u],function*([c]){c&&(yield nt(),r.value={},n.value=!1,yield o())}));function o(){return T(this,null,function*(){if(!s.showCollapseButton||(yield nt(),!e.value))return;const c=[...e.value.children],u=e.value,_=window.getComputedStyle(u).getPropertyValue("grid-template-rows").split(" "),g=u==null?void 0:u.getBoundingClientRect();c.forEach(S=>{var E,$;const C=S.getBoundingClientRect().top-g.top;let A=0,M=0;for(const[H,X]of _.entries())if(M+=Number.parseFloat(X),C<M){A=H+1;break}A>((E=s==null?void 0:s.collapsedRows)!=null?E:1)||(r.value[A]=(($=r.value[A])!=null?$:0)+1,n.value=!0)})})}return ms(()=>{o()}),{isCalculated:n,keepFormItemIndex:i,wrapperRef:e}}const Zn=G({__name:"form",props:{arrayToStringFields:{},collapsed:{type:Boolean},collapsedRows:{default:1},collapseTriggerResize:{type:Boolean},commonConfig:{default:()=>({})},compact:{type:Boolean},componentBindEventMap:{},componentMap:{},fieldMappingTime:{},form:{},layout:{},schema:{},showCollapseButton:{type:Boolean,default:!1},wrapperClass:{default:"grid-cols-1 sm:grid-cols-2 md:grid-cols-3"},globalCommonConfig:{default:()=>({})}},emits:["submit"],setup(s,{emit:e}){const t=s,r=e;Tn(t);const{isCalculated:n,keepFormItemIndex:a,wrapperRef:i}=jn(t),o=I(()=>{var S;const g=[];return(S=t.schema)==null||S.forEach(Z=>{const{fieldName:C}=Z,A=Z.rules;let M="";A&&!de(A)&&(M=A._def.typeName);const E=Ot(A);g.push({default:Tt(A),fieldName:C,required:!["ZodNullable","ZodOptional"].includes(M),rules:E})}),g}),c=I(()=>t.form?"form":vr),u=I(()=>t.form?{onSubmit:t.form.handleSubmit(g=>r("submit",g))}:{onSubmit:g=>r("submit",g)}),h=I(()=>t.collapsed&&n.value),_=I(()=>{const{colon:g=!1,componentProps:S={},controlClass:Z="",disabled:C,disabledOnChangeListener:A=!0,disabledOnInputListener:M=!0,emptyStateValue:E=void 0,formFieldProps:$={},formItemClass:H="",hideLabel:X=!1,hideRequiredMark:te=!1,labelClass:K="",labelWidth:Qe=100,modelPropName:Xe="",wrapperClass:dt=""}=We(t.commonConfig,t.globalCommonConfig);return(t.schema||[]).map((me,ft)=>{const Me=a.value,ht=t.showCollapseButton&&h.value&&Me?Me<=ft:!1;return x(l({colon:g,disabled:C,disabledOnChangeListener:A,disabledOnInputListener:M,emptyStateValue:E,hideLabel:X,hideRequiredMark:te,labelWidth:Qe,modelPropName:Xe,wrapperClass:dt},me),{commonComponentProps:S,componentProps:me.componentProps,controlClass:q(Z,me.controlClass),formFieldProps:l(l({},$),me.formFieldProps),formItemClass:q("flex-shrink-0",{hidden:ht},H,me.formItemClass),labelClass:q(K,me.labelClass)})})});return(g,S)=>(R(),P(He(c.value),ye(ve(u.value)),{default:V(()=>[Ge("div",{ref_key:"wrapperRef",ref:i,class:Q([g.wrapperClass,"grid"])},[(R(!0),le(bt,null,Ft(_.value,Z=>(R(),P($n,ne({key:Z.fieldName,ref_for:!0},Z,{class:Z.formItemClass,rules:Z.rules}),{default:V(C=>[j(g.$slots,Z.fieldName,ne({ref_for:!0},C))]),_:2},1040,["class","rules"]))),128)),j(g.$slots,"default",{shapes:o.value})],2)]),_:3},16))}}),Mn=G({__name:"vben-use-form",props:{formApi:{},actionButtonsReverse:{type:Boolean},actionWrapperClass:{},arrayToStringFields:{},fieldMappingTime:{},handleReset:{type:Function},handleSubmit:{type:Function},handleValuesChange:{type:Function},resetButtonOptions:{},scrollToFirstError:{type:Boolean},showDefaultActions:{type:Boolean},submitButtonOptions:{},submitOnChange:{type:Boolean},submitOnEnter:{type:Boolean},collapsed:{type:Boolean},collapsedRows:{},collapseTriggerResize:{type:Boolean},commonConfig:{},compact:{type:Boolean},layout:{},schema:{},showCollapseButton:{type:Boolean},wrapperClass:{}},setup(s){var _,g,S,Z;const e=s,t=(g=(_=e.formApi)==null?void 0:_.useStore)==null?void 0:g.call(_),r=gr(e,t),n=new Map,{delegatedSlots:a,form:i}=_n(r);yn([r,i]),gn(n),(Z=(S=e.formApi)==null?void 0:S.mount)==null||Z.call(S,i,n);const o=C=>{var A;(A=e.formApi)==null||A.setState({collapsed:!!C})};function c(C){var A;!t.value.submitOnEnter||!((A=r.value.formApi)!=null&&A.isMounted)||C.target instanceof HTMLTextAreaElement||(C.preventDefault(),r.value.formApi.validateAndSubmitForm())}const u=Xs(()=>T(null,null,function*(){var C;t.value.submitOnChange&&((C=r.value.formApi)==null||C.validateAndSubmitForm())}),300),h={};return ms(()=>T(null,null,function*(){yield nt(),be(()=>i.values,C=>T(null,null,function*(){var A;if(r.value.handleValuesChange){const M=(A=t.value.schema)==null?void 0:A.map(E=>E.fieldName);if(M&&M.length>0){const E=[];M.forEach($=>{const H=Lt(C,$),X=Lt(h,$);Js(H,X)||(E.push($),_t(h,$,H))}),E.length>0&&r.value.handleValuesChange(Qs(yield r.value.formApi.getValues()),E)}}u()}),{deep:!0})})),(C,A)=>(R(),P(f(Zn),ne({onKeydown:kr(c,["enter"])},f(r),{collapsed:f(t).collapsed,"component-bind-event-map":f(br),"component-map":f(st),form:f(i),"global-common-config":f(_r)}),hs({default:V(M=>[j(C.$slots,"default",ye(ve(M)),()=>[f(r).showDefaultActions?(R(),P(bn,{key:0,"model-value":f(t).collapsed,"onUpdate:modelValue":o},{"reset-before":V(E=>[j(C.$slots,"reset-before",ye(ve(E)))]),"submit-before":V(E=>[j(C.$slots,"submit-before",ye(ve(E)))]),"expand-before":V(E=>[j(C.$slots,"expand-before",ye(ve(E)))]),"expand-after":V(E=>[j(C.$slots,"expand-after",ye(ve(E)))]),_:3},8,["model-value"])):B("",!0)])]),_:2},[Ft(f(a),M=>({name:M,fn:V(E=>[j(C.$slots,M,ye(ve(E)))])}))]),1040,["collapsed","component-bind-event-map","component-map","form","global-common-config"]))}});function qn(s){const e=Ks(s),t=new Pr(s),r=t;r.useStore=a=>jr(t.store,a);const n=G((a,{attrs:i,slots:o})=>(er(()=>{t.unmount()}),t.setState(l(l({},a),i)),()=>tr(Mn,x(l(l({},a),i),{formApi:r}),o)),{name:"VbenUseForm",inheritAttrs:!1});return e&&be(()=>s.schema,()=>{t.setState({schema:s.schema})},{immediate:!0}),[n,r]}export{wr as C,kt as S,Ar as _,jr as a,Un as b,zn as s,qn as u};
