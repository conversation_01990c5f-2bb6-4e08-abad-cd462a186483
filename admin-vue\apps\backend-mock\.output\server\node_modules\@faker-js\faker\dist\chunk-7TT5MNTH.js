var a=["6##-###-###","6##.###.###","6## ### ###","6########"];var K={formats:a},e=K;var o=["amarillo","azul","azul marino","beige","blanco","carmes\xED","celeste","cian","crema","dorado","esmeralda","fucsia","granate","gris","gualda","lavanda","lila","magenta","marfil","marr\xF3n","morado","naranja","negro","ocre","plateado","p\xFArpura","rojo","rosa","salm\xF3n","terracota","turquesa","verde","verde lima","verde menta","verde oliva","violeta","\xEDndigo"];var Y={human:o},r=Y;var i=["<PERSON><PERSON>","Bricolaje","Cine","Decoraci\xF3n","Deportes","Electr\xF3nica","Hogar","Inform\xE1tica","Joyer\xEDa","Jugueter\xEDa","Librer\xEDa","Marroquiner\xEDa","Mascotas","Moda","M\xFAsica","Papeler\xEDa","Parafarmacia","Salud","Videojuegos"];var n={adjective:["Artesanal","Ergon\xF3mico","Fant\xE1stico","Gen\xE9rico","Guapa","Guapo","Hecho a mano","Increible","Inteligente","Peque\xF1o","Pr\xE1ctico","Refinado","R\xFAstico","Sabroso","Sorprendente"],material:["Acero","Algod\xF3n","Granito","Hormigon","Ladrillo","Madera","Metal","Pl\xE1stico"],product:["At\xFAn","Bacon","Bicicleta","Camiseta","Coche","Ensalada","Gorro","Guantes","Mesa","Ordenador","Pantalones","Patatas fritas","Pelota","Pescado","Pizza","Pollo","Queso","Raton","Salchichas","Silla","Sopa","Teclado","Toallas","Zapatos"]};var W={department:i,product_name:n},l=W;var t=["Actualizable","Adaptativo","Amigable","Asimilado","Auto proporciona","Automatizado","Avanzado","Cara a cara","Centrado en el negocio","Centrado en el usuario","Centralizado","Clonado","Compartible","Compatible","Configurable","Descentralizado","Digitalizado","Distribuido","Diverso","En red","Enfocado","Enfocado a benficios","Enfocado en la calidad","Equilibrado","Ergon\xF3mico","Exclusivo","Expandido","Extendido","Fundamental","F\xE1cil","Gestionado","Horizontal","Implementado","Ingenier\xEDa inversa","Innovador","Integrado","Intercambiable","Intuitivo","Inverso","Mejorado","Monitorizado","Multi canal","Multi capa","Multi grupo","Multi lateral","Multi plataforma","Obligatorio","Opcional","Open-source","Operativo","Optimizado","Organizado","Org\xE1nico","Orientado a equipos","Orientado a objetos","Or\xEDgenes","Para toda la empresa","Perseverando","Persistente","Polarizado","Pre-emptivo","Proactivo","Profundo","Programable","Progresivo","Public-key","Re-contextualizado","Re-implementado","Reactivo","Realineado","Reducido","Robusto","Seguro","Sincronizado","Total","Totalmente configurable","Universal","Versatil","Virtual","Visionario","en fases"];var s=["24 horas","24/7","3rd generaci\xF3n","4th generaci\xF3n","5th generaci\xF3n","6th generaci\xF3n","acompasada","alto nivel","amplio \xE1banico","analizada","asim\xE9trica","as\xEDncrona","basado en contenido","basado en el contexto","basado en necesidades","bidireccional","bifurcada","cliente servidor","coherente","cohesiva","compuesto","dedicada","defectos cero","didactica","din\xE1mica","direccional","discreta","ejecutiva","escalable","estable","estatica","expl\xEDcita","generada por el cliente","generado por la demanda","global","heur\xEDstica","hibrida","hol\xEDstica","homog\xE9nea","incremental","innovadora","intangible","interactiva","intermedia","local","log\xEDstica","maximizada","met\xF3dica","misi\xF3n cr\xEDtica","modular","monitorizada por red","motivadora","multiestado","multimedia","multitarea","m\xF3bil","nacional","neutral","no-vol\xE1til","nueva generaci\xF3n","optimizada","orientada a soluciones","orientado a objetos","potenciada","radical","rec\xEDproca","regional","secundaria","sensible al contexto","sistem\xE1tica","sist\xE9mica","tangible","terciaria","tiempo real","tolerancia cero","tolerante a fallos","transicional","uniforme","valor a\xF1adido","v\xEDa web","\xF3ptima"];var d=["Hermanos","S.A.","S.L.","e Hijos"];var c=["{{person.last_name.generic}} y {{person.last_name.generic}}","{{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}} {{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}}, {{person.last_name.generic}} y {{person.last_name.generic}} Asociados"];var u=["Interfaz Gr\xE1fica","Interfaz gr\xE1fico de usuario","Soporte","acceso","actitud","adaptador","algoritmo","alianza","analista","aplicaci\xF3n","aprovechar","archivo","arquitectura","arquitectura abierta","array","base de datos","base de trabajo","base del conocimiento","caja de herramientas","capacidad","circuito","codificar","colaboraci\xF3n","complejidad","concepto","conglomeraci\xF3n","conjunto","conjunto de instrucciones","contingencia","data-warehouse","definici\xF3n","desaf\xEDo","emulaci\xF3n","encriptar","enfoque","estandardizaci\xF3n","estrategia","estructura","estructura de precios","extranet","fidelidad","firmware","flexibilidad","focus group","fuerza de trabajo","funcionalidad","funci\xF3n","gesti\xF3n presupuestaria","groupware","habilidad","hardware","implementaci\xF3n","infraestructura","iniciativa","instalaci\xF3n","inteligencia artificial","interfaz","intranet","jerarqu\xEDa","l\xEDnea segura","marco de tiempo","matrices","mediante","medici\xF3n","metodolog\xEDas","middleware","migraci\xF3n","modelo","moderador","monitorizar","n\xFAcleo","orquestar","paradigma","paralelismo","pol\xEDtica","portal","previsi\xF3n","proceso de mejora","productividad","producto","protocolo","proyecci\xF3n","proyecto","red de area local","sinergia","sistema abierto","software","soluci\xF3n","soporte","superestructura","utilizaci\xF3n","website","\xE9xito"];var X={adjective:t,descriptor:s,legal_entity_type:d,name_pattern:c,noun:u},m=X;var p={wide:["abril","agosto","diciembre","enero","febrero","julio","junio","marzo","mayo","noviembre","octubre","septiembre"],abbr:["abr","ago","dic","ene","feb","jul","jun","mar","may","nov","oct","sep"],abbr_context:["abr.","ag.","dic.","en.","febr.","jul.","jun.","my.","mzo.","nov.","oct.","sept."]};var g={wide:["domingo","jueves","lunes","martes","mi\xE9rcoles","s\xE1bado","viernes"],abbr:["dom","jue","lun","mar","mi\xE9","s\xE1b","vie"],abbr_context:["dom.","juev.","lun.","mart.","mi\xE9rc.","s\xE1b.","vier."]};var $={month:p,weekday:g},f=$;var b=["cat","com","com.es","es","eus","info","org"];var C=["gmail.com","hotmail.com","yahoo.com"];var aa={domain_suffix:b,free_email:C},z=aa;var M=[" s/n.",", #",", ##"," #"," ##"];var v=["Parla","Telde","Baracaldo","San Fernando","Torrevieja","Lugo","Santiago de Compostela","Gerona","C\xE1ceres","Lorca","Coslada","Talavera de la Reina","El Puerto de Santa Mar\xEDa","Cornell\xE1 de Llobregat","Avil\xE9s","Palencia","Gecho","Orihuela","Pontevedra","Pozuelo de Alarc\xF3n","Toledo","El Ejido","Guadalajara","Gand\xEDa","Ceuta","Ferrol","Chiclana de la Frontera","Manresa","Roquetas de Mar","Ciudad Real","Rub\xED","Benidorm","San Sebast\xEDan de los Reyes","Ponferrada","Zamora","Alcal\xE1 de Guadaira","Fuengirola","Mijas","Sanl\xFAcar de Barrameda","La L\xEDnea de la Concepci\xF3n","Majadahonda","Sagunto","El Prat de LLobregat","Viladecans","Linares","Alcoy","Ir\xFAn","Estepona","Torremolinos","Rivas-Vaciamadrid","Molina de Segura","Paterna","Granollers","Santa Luc\xEDa de Tirajana","Motril","Cerda\xF1ola del Vall\xE9s","Arrecife","Segovia","Torrelavega","Elda","M\xE9rida","\xC1vila","Valdemoro","Cuenta","Collado Villalba","Benalm\xE1dena","Mollet del Vall\xE9s","Puertollano","Madrid","Barcelona","Valencia","Sevilla","Zaragoza","M\xE1laga","Murcia","Palma de Mallorca","Las Palmas de Gran Canaria","Bilbao","C\xF3rdoba","Alicante","Valladolid","Vigo","Gij\xF3n","Hospitalet de LLobregat","La Coru\xF1a","Granada","Vitoria","Elche","Santa Cruz de Tenerife","Oviedo","Badalona","Cartagena","M\xF3stoles","Jerez de la Frontera","Tarrasa","Sabadell","Alcal\xE1 de Henares","Pamplona","Fuenlabrada","Almer\xEDa","San Sebasti\xE1n","Legan\xE9s","Santander","Burgos","Castell\xF3n de la Plana","Alcorc\xF3n","Albacete","Getafe","Salamanca","Huelva","Logro\xF1o","Badajoz","San Cristr\xF3bal de la Laguna","Le\xF3n","Tarragona","C\xE1diz","L\xE9rida","Marbella","Matar\xF3","Dos Hermanas","Santa Coloma de Gramanet","Ja\xE9n","Algeciras","Torrej\xF3n de Ardoz","Orense","Alcobendas","Reus","Calahorra","Inca","A\xFDna"];var A=["{{location.city_name}}"];var S=["Afganist\xE1n","Albania","Argelia","Andorra","Angola","Argentina","Armenia","Aruba","Australia","Austria","Azerbay\xE1n","Bahamas","Barein","Bangladesh","Barbados","Bielorusia","B\xE9lgica","Belice","Bermuda","But\xE1n","Bolivia","Bosnia Herzegovina","Botswana","Brasil","Bulgaria","Burkina Faso","Burundi","Camboya","Camer\xFAn","Canada","Cabo Verde","Islas Caim\xE1n","Chad","Chile","China","Isla de Navidad","Colombia","Comodos","Congo","Costa Rica","Costa de Marfil","Croacia","Cuba","Chipre","Rep\xFAblica Checa","Dinamarca","Dominica","Rep\xFAblica Dominicana","Ecuador","Egipto","El Salvador","Guinea Ecuatorial","Eritrea","Estonia","Etiop\xEDa","Islas Faro","Fiji","Finlandia","Francia","Gab\xF3n","Gambia","Georgia","Alemania","Ghana","Grecia","Groenlandia","Granada","Guadalupe","Guam","Guatemala","Guinea","Guinea-Bisau","Guayana","Haiti","Honduras","Hong Kong","Hungria","Islandia","India","Indonesia","Iran","Irak","Irlanda","Italia","Jamaica","Jap\xF3n","Jordania","Kazajistan","Kenia","Kiribati","Corea","Kuwait","Letonia","L\xEDbano","Liberia","Liechtenstein","Lituania","Luxemburgo","Macao","Macedonia","Madagascar","Malawi","Malasia","Maldivas","Mali","Malta","Martinica","Mauritania","M\xE9jico","Micronesia","Moldavia","M\xF3naco","Mongolia","Montenegro","Montserrat","Marruecos","Mozambique","Namibia","Nauru","Nepal","Pa\xEDses Bajos","Nueva Zelanda","Nicaragua","Niger","Nigeria","Noruega","Om\xE1n","Pakistan","Panam\xE1","Pap\xFAa Nueva Guinea","Paraguay","Per\xFA","Filipinas","Poland","Portugal","Puerto Rico","Rusia","Ruanda","Samoa","San Marino","Santo Tom\xE9 y Principe","Arabia Saud\xED","Senegal","Serbia","Seychelles","Sierra Leona","Singapur","Eslovaquia","Eslovenia","Somalia","Espa\xF1a","Sri Lanka","Sud\xE1n","Suriname","Suecia","Suiza","Siria","Taiwan","Tajikistan","Tanzania","Tailandia","Timor-Leste","Togo","Tonga","Trinidad y Tobago","Tunez","Turquia","Uganda","Ucrania","Emiratos \xC1rabes Unidos","Reino Unido","Estados Unidos de Am\xE9rica","Uruguay","Uzbekistan","Vanuatu","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"];var P=["\xC1lava","Albacete","Alicante","Almer\xEDa","Asturias","\xC1vila","Badajoz","Barcelona","Burgos","Cantabria","Castell\xF3n","Ciudad Real","Cuenca","C\xE1ceres","C\xE1diz","C\xF3rdoba","Gerona","Granada","Guadalajara","Guip\xFAzcoa","Huelva","Huesca","Islas Baleares","Ja\xE9n","La Coru\xF1a","La Rioja","Las Palmas","Le\xF3n","Lugo","l\xE9rida","Madrid","Murcia","M\xE1laga","Navarra","Orense","Palencia","Pontevedra","Salamanca","Santa Cruz de Tenerife","Segovia","Sevilla","Soria","Tarragona","Teruel","Toledo","Valencia","Valladolid","Vizcaya","Zamora","Zaragoza"];var G=["#####"];var R=["Esc. ###","Puerta ###"];var L=["Andaluc\xEDa","Arag\xF3n","Principado de Asturias","Baleares","Canarias","Cantabria","Castilla-La Mancha","Castilla y Le\xF3n","Catalu\xF1a","Comunidad Valenciana","Extremadura","Galicia","La Rioja","Comunidad de Madrid","Navarra","Pa\xEDs Vasco","Regi\xF3n de Murcia"];var y=["And","Ara","Ast","Bal","Can","Cbr","Man","Leo","Cat","Com","Ext","Gal","Rio","Mad","Nav","Vas","Mur"];var B={normal:"{{location.street}}{{location.buildingNumber}}",full:"{{location.street}}{{location.buildingNumber}} {{location.secondaryAddress}}"};var j=["{{location.street_suffix}} {{person.firstName}}","{{location.street_suffix}} {{person.firstName}} {{person.last_name.generic}}"];var E=["Aldea","Apartamento","Arrabal","Arroyo","Avenida","Bajada","Barranco","Barrio","Bloque","Calle","Calleja","Camino","Carretera","Caserio","Colegio","Colonia","Conjunto","Cuesta","Chalet","Edificio","Entrada","Escalinata","Explanada","Extramuros","Extrarradio","Ferrocarril","Glorieta","Gran Subida","Grupo","Huerta","Jardines","Lado","Lugar","Manzana","Mas\xEDa","Mercado","Monte","Muelle","Municipio","Parcela","Parque","Partida","Pasaje","Paseo","Plaza","Poblado","Pol\xEDgono","Prolongaci\xF3n","Puente","Puerta","Quinta","Ramal","Rambla","Rampa","Riera","Rinc\xF3n","Ronda","Rua","Salida","Sector","Secci\xF3n","Senda","Solar","Subida","Terrenos","Torrente","Traves\xEDa","Urbanizaci\xF3n","V\xEDa","V\xEDa P\xFAblica"];var ea={building_number:M,city_name:v,city_pattern:A,country:S,county:P,postcode:G,secondary_address:R,state:L,state_abbr:y,street_address:B,street_pattern:j,street_suffix:E},x=ea;var oa={title:"Spanish",code:"es",language:"es",endonym:"Espa\xF1ol",dir:"ltr",script:"Latn"},D=oa;var V={female:["Adela","Adriana","Alejandra","Alicia","Amalia","Ana","Ana Luisa","Ana Mar\xEDa","Andrea","Anita","Anni","Antonia","Ariadna","Barbara","Beatriz","Berta","Blanca","Caridad","Carla","Carlota","Carmen","Carolina","Catalina","Cecilia","Clara","Claudia","Concepci\xF3n","Conchita","Cristina","Daniela","Diana","Dolores","Dorotea","D\xE9bora","Elena","Elisa","Eloisa","Elsa","Elvira","Emilia","Esperanza","Estela","Ester","Eva","Florencia","Francisca","Gabriela","Gloria","Graciela","Guadalupe","Guillermina","In\xE9s","Irene","Isabel","Isabela","Jennifer","Josefina","Juana","Julia","Laura","Leonor","Leticia","Lilia","Lola","Lorena","Lourdes","Luc\xEDa","Luisa","Luz","Magdalena","Maica","Manuela","Marcela","Margarita","Mariana","Maricarmen","Maril\xFA","Marisol","Marta","Mar\xEDa","Mar\xEDa Cristina","Mar\xEDa Elena","Mar\xEDa Eugenia","Mar\xEDa Jos\xE9","Mar\xEDa Luisa","Mar\xEDa Soledad","Mar\xEDa Teresa","Mar\xEDa de los \xC1ngeles","Mar\xEDa del Carmen","Matilde","Mayte","Mercedes","Micaela","M\xF3nica","Natalia","Norma","Olivia","Patricia","Pilar","Ramona","Raquel","Rebeca","Reina","Roc\xEDo","Rosa","Rosalia","Rosario","Roser","Sara","Silvia","Sof\xEDa","Soledad","Sonia","Susana","Teresa","Ver\xF3nica","Victoria","Virginia","Yolanda","\xC1ngela"],male:["Ad\xE1n","Agust\xEDn","Alberto","Alejandro","Alfonso","Alfredo","Andr\xE9s","Antonio","Armando","Arturo","Benito","Benjam\xEDn","Bernardo","Carles","Carlos","Claudio","Clemente","Cristi\xE1n","Crist\xF3bal","C\xE9sar","Daniel","David","Diego","Eduardo","Emilio","Enrique","Ernesto","Esteban","Federico","Felipe","Fernando","Francisco","Gabriel","Gerardo","Germ\xE1n","Gilberto","Gonzalo","Gregorio","Guillermo","Gustavo","Hermenegildo","Hern\xE1n","Homero","Horacio","Hugo","Ignacio","Iv\xE1n","Jacobo","Jaime","Javier","Jer\xF3nimo","Jes\xFAs","Joaqu\xEDn","Jordi","Jorge","Jorge Luis","Josep","Jos\xE9","Jos\xE9 Eduardo","Jos\xE9 Emilio","Jos\xE9 Luis","Jos\xE9 Mar\xEDa","Juan","Juan Carlos","Juan Ram\xF3n","Julio","Julio C\xE9sar","Lorenzo","Lucas","Luis","Luis Miguel","Manuel","Marco Antonio","Marcos","Mariano","Mario","Mart\xEDn","Mateo","Miguel","Miguel \xC1ngel","Nicol\xE1s","Octavio","Pablo","Patricio","Pedro","P\xEDo","Rafael","Ramiro","Ram\xF3n","Ra\xFAl","Ricardo","Roberto","Rodrigo","Rub\xE9n","Salvador","Samuel","Sancho","Santiago","Sergi","Sergio","Teodoro","Timoteo","Tom\xE1s","Vicente","V\xEDctor","\xC1ngel","\xD3scar"]};var h=["Soluciones","Programa","Marca","Seguridada","Investigaci\xF3n","Marketing","Normas","Implementaci\xF3n","Integraci\xF3n","Funcionalidad","Respuesta","Paradigma","T\xE1cticas","Identidad","Mercados","Grupo","Divisi\xF3n","Aplicaciones","Optimizaci\xF3n","Operaciones","Infraestructura","Intranet","Comunicaciones","Web","Calidad","Seguro","Mobilidad","Cuentas","Datos","Creativo","Configuraci\xF3n","Contabilidad","Interacciones","Factores","Usabilidad","M\xE9tricas"];var T=["Jefe","Senior","Directo","Corporativo","Din\xE1nmico","Futuro","Producto","Nacional","Regional","Distrito","Central","Global","Cliente","Inversor","International","Heredado","Adelante","Interno","Humano","Gerente","Director"];var I=["{{person.jobType}} de {{person.jobArea}} {{person.jobDescriptor}}"];var _=["Supervisor","Asociado","Ejecutivo","Relacciones","Oficial","Gerente","Ingeniero","Especialista","Director","Coordinador","Administrador","Arquitecto","Analista","Dise\xF1ador","Planificador","T\xE9cnico","Funcionario","Desarrollador","Productor","Consultor","Asistente","Facilitador","Agente","Representante","Estratega"];var J={generic:["Abeyta","Abrego","Abreu","Acevedo","Acosta","Acu\xF1a","Adame","Adorno","Agosto","Aguayo","Aguilar","Aguilera","Aguirre","Alanis","Alan\xEDz","Alarc\xF3n","Alba","Alcal\xE1","Alcaraz","Alc\xE1ntar","Alejandro","Alem\xE1n","Alfaro","Alicea","Almanza","Almar\xE1z","Almonte","Alonso","Alonzo","Altamirano","Alva","Alvarado","Amador","Amaya","Anaya","Anguiano","Angulo","Aparicio","Apodaca","Aponte","Arag\xF3n","Aranda","Ara\xF1a","Arce","Archuleta","Arellano","Arenas","Arevalo","Arg\xFCello","Arias","Armas","Armend\xE1riz","Armenta","Armijo","Arredondo","Arreola","Arriaga","Arroyo","Arteaga","Atencio","Avil\xE9s","Ayala","Baca","Badillo","Baeza","Bahena","Balderas","Ballesteros","Banda","Barajas","Barela","Barrag\xE1n","Barraza","Barrera","Barreto","Barrientos","Barrios","Batista","Ba\xF1uelos","Becerra","Beltr\xE1n","Benavides","Benav\xEDdez","Ben\xEDtez","Berm\xFAdez","Bernal","Berr\xEDos","Betancourt","Blanco","Bonilla","Borrego","Botello","Bravo","Briones","Brise\xF1o","Brito","Bueno","Burgos","Bustamante","Bustos","B\xE1ez","Caballero","Cabrera","Cab\xE1n","Cadena","Caldera","Calder\xF3n","Calvillo","Camacho","Camarillo","Campos","Canales","Candelaria","Cano","Cant\xFA","Caraballo","Carbajal","Cardona","Carmona","Carranza","Carrasco","Carrasquillo","Carrera","Carrero","Carre\xF3n","Carrillo","Carri\xF3n","Carvajal","Casanova","Casares","Casarez","Casas","Casillas","Casta\xF1eda","Castellanos","Castillo","Castro","Cavazos","Cazares","Ceballos","Cedillo","Ceja","Centeno","Cepeda","Cerda","Cervantes","Cerv\xE1ntez","Chac\xF3n","Chapa","Chavarr\xEDa","Ch\xE1vez","Cintr\xF3n","Cisneros","Collado","Collazo","Colunga","Col\xF3n","Concepci\xF3n","Contreras","Cordero","Cornejo","Corona","Coronado","Corral","Corrales","Correa","Cort\xE9s","Cort\xE9z","Cotto","Covarrubias","Crespo","Cruz","Cuellar","Curiel","C\xE1rdenas","C\xF3rdova","Delacr\xFAz","Delafuente","Delagarza","Delao","Delapaz","Delarosa","Delatorre","Dele\xF3n","Delgadillo","Delgado","Delr\xEDo","Delvalle","Dom\xEDnguez","Duarte","Due\xF1as","Dur\xE1n","D\xE1vila","D\xEDaz","Echevarr\xEDa","Elizondo","Enr\xEDquez","Escalante","Escamilla","Escobar","Escobedo","Esparza","Espinal","Espino","Espinosa","Espinosa de los Monteros","Espinoza","Esquibel","Esquivel","Estrada","Est\xE9vez","Fajardo","Far\xEDas","Feliciano","Fern\xE1ndez","Ferrer","Fierro","Figueroa","Flores","Fl\xF3rez","Fonseca","Fr\xEDas","Fuentes","Gait\xE1n","Galarza","Galindo","Gallardo","Gallegos","Galv\xE1n","Gamboa","Gaona","Garay","Garc\xEDa","Garibay","Garica","Garrido","Garza","Gast\xE9lum","Gayt\xE1n","Gil","Gir\xF3n","Godoy","God\xEDnez","Gonz\xE1lez","Gracia","Granado","Granados","Griego","Grijalva","Guajardo","Guardado","Guerra","Guerrero","Guevara","Guill\xE9n","Gurule","Guti\xE9rrez","Guzm\xE1n","G\xE1lvez","G\xE1mez","G\xF3mez","Haro","Henr\xEDquez","Heredia","Hern\xE1ndez","Herrera","Hidalgo","Hinojosa","Holgu\xEDn","Huerta","Hurtado","Ibarra","Iglesias","Irizarry","Jaime","Jaimes","Jaramillo","Jasso","Jim\xE9nez","Jurado","Ju\xE1rez","J\xE1quez","Laboy","Lara","Laureano","Leal","Lebr\xF3n","Ledesma","Leiva","Lemus","Lerma","Leyva","Le\xF3n","Lim\xF3n","Linares","Lira","Llamas","Loera","Lomeli","Longoria","Lovato","Loya","Lozada","Lozano","Lucero","Lucio","Luevano","Lugo","Luna","L\xF3pez","Mac\xEDas","Madera","Madrid","Madrigal","Maestas","Maga\xF1a","Malave","Maldonado","Manzanares","Mares","Marrero","Marroqu\xEDn","Mart\xEDnez","Mar\xEDn","Mascare\xF1as","Mata","Mateo","Matos","Mat\xEDas","Maya","Mayorga","Medina","Medrano","Mej\xEDa","Melgar","Mel\xE9ndez","Mena","Menchaca","Mendoza","Men\xE9ndez","Meraz","Mercado","Merino","Mesa","Meza","Miramontes","Miranda","Mireles","Mojica","Molina","Mondrag\xF3n","Monroy","Montalvo","Monta\xF1ez","Monta\xF1o","Montemayor","Montenegro","Montero","Montes","Montoya","Mont\xE9z","Mora","Morales","Moreno","Mota","Moya","Mungu\xEDa","Murillo","Muro","Mu\xF1iz","Mu\xF1oz","M\xE1rquez","M\xE9ndez","Naranjo","Narv\xE1ez","Nava","Navarrete","Navarro","Nazario","Negrete","Negr\xF3n","Nev\xE1rez","Nieto","Nieves","Ni\xF1o","Noriega","N\xE1jera","N\xFA\xF1ez","Ocampo","Ocasio","Ochoa","Ojeda","Olivares","Olivas","Olivera","Olivo","Oliv\xE1rez","Olmos","Olvera","Ontiveros","Oquendo","Ord\xF3\xF1ez","Orellana","Ornelas","Orosco","Orozco","Orta","Ortega","Ortiz","Osorio","Otero","Ozuna","Pab\xF3n","Pacheco","Padilla","Padr\xF3n","Pagan","Palacios","Palomino","Palomo","Pantoja","Paredes","Parra","Partida","Pati\xF1o","Paz","Pedraza","Pedroza","Pelayo","Perales","Peralta","Perea","Pe\xF1a","Pichardo","Pineda","Pizarro","Pi\xF1a","Polanco","Ponce","Porras","Portillo","Posada","Prado","Preciado","Prieto","Puente","Puga","Pulido","P\xE1ez","P\xE9rez","Quesada","Quezada","Quintana","Quintanilla","Quintero","Quir\xF3z","Qui\xF1ones","Qui\xF1\xF3nez","Rael","Ramos","Ram\xEDrez","Rangel","Rasc\xF3n","Raya","Razo","Regalado","Rend\xF3n","Renter\xEDa","Res\xE9ndez","Reyes","Reyna","Reynoso","Rico","Rinc\xF3n","Riojas","Rivas","Rivera","Rivero","Robledo","Robles","Rocha","Rodarte","Rodr\xEDguez","Rojas","Rojo","Rold\xE1n","Rol\xF3n","Romero","Romo","Roque","Rosado","Rosales","Rosario","Rosas","Roybal","Rubio","Ruelas","Ru\xEDz","R\xEDos","Saavedra","Saiz","Salas","Salazar","Salcedo","Salcido","Salda\xF1a","Saldivar","Salgado","Salinas","Samaniego","Sanabria","Sandoval","Santacruz","Santana","Santiago","Santill\xE1n","Sarabia","Sauceda","Saucedo","Sedillo","Segovia","Segura","Sep\xFAlveda","Serna","Serrano","Serrato","Sevilla","Sierra","Sisneros","Solano","Soliz","Solorio","Solorzano","Sol\xEDs","Soria","Sosa","Sotelo","Soto","Su\xE1rez","S\xE1enz","S\xE1nchez","Tafoya","Tamayo","Tamez","Tapia","Tejada","Tejeda","Tello","Terrazas","Ter\xE1n","Tijerina","Tirado","Toledo","Toro","Torres","Tovar","Trejo","Trevi\xF1o","Trujillo","T\xE9llez","T\xF3rrez","Ulibarri","Ulloa","Urbina","Ure\xF1a","Uribe","Urrutia","Ur\xEDas","Vaca","Valadez","Valdez","Valdivia","Vald\xE9s","Valencia","Valent\xEDn","Valenzuela","Valladares","Valle","Vallejo","Valles","Valverde","Vanegas","Varela","Vargas","Vega","Vela","Velasco","Vel\xE1squez","Vel\xE1zquez","Venegas","Vera","Verdugo","Verduzco","Vergara","Viera","Vigil","Villa","Villag\xF3mez","Villalobos","Villalpando","Villanueva","Villarreal","Villase\xF1or","Villegas","V\xE1zquez","V\xE9lez","V\xE9liz","Ybarra","Y\xE1\xF1ez","Zambrano","Zamora","Zamudio","Zapata","Zaragoza","Zarate","Zavala","Zayas","Zelaya","Zepeda","Z\xFA\xF1iga","de Anda","de Jes\xFAs","\xC1lvarez","\xC1valos","\xC1vila"]};var N={generic:[{value:"{{person.last_name.generic}} {{person.last_name.generic}}",weight:1}]};var O=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:9}];var F={generic:["Sr.","Sra.","Sta."],female:["Sra.","Sta."],male:["Sr."]};var H=["Jr.","Sr.","I","II","III","IV","V","MD","DDS","PhD","DVM"];var ra={first_name:V,job_area:h,job_descriptor:T,job_title_pattern:I,job_type:_,last_name:J,last_name_pattern:N,name:O,prefix:F,suffix:H},q=ra;var w=["9##-###-###","9##.###.###","9## ### ###","9########"];var Z=["+349########"];var U=["9## ## ## ##"];var ia={human:w,international:Z,national:U},Q=ia;var na={format:Q},k=na;var la={cell_phone:e,color:r,commerce:l,company:m,date:f,internet:z,location:x,metadata:D,person:q,phone_number:k},We=la;export{We as a};
