
> @vben/backend-mock@0.0.1 build D:\qianhouduankaifabao\admin-vue\apps\backend-mock
> nitro build

[90m[[90mnitro[90m][39m [32m✔[39m Generated public [36m.output/public[39m
[90m[[90mnitro[90m][39m [36mℹ[39m Building Nitro Server (preset: [36mnode-server[39m, compatibility date: [36m2025-07-16[39m)
[90m[[90mnitro[90m][39m [32m✔[39m Nitro Server built
[90m  ├─ .output/server/chunks/_/cookie-utils.mjs (693 B)[39m[90m (313 B gzip)[39m
[90m  ├─ .output/server/chunks/_/cookie-utils.mjs.map (612 B)[39m[90m (233 B gzip)[39m
[90m  ├─ .output/server/chunks/_/jwt-utils.mjs (10.1 kB)[39m[90m (2.23 kB gzip)[39m
[90m  ├─ .output/server/chunks/_/jwt-utils.mjs.map (9.15 kB)[39m[90m (1.44 kB gzip)[39m
[90m  ├─ .output/server/chunks/_/nitro.mjs (158 kB)[39m[90m (38.7 kB gzip)[39m
[90m  ├─ .output/server/chunks/_/nitro.mjs.map (4.75 kB)[39m[90m (921 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/_..._.mjs (670 B)[39m[90m (330 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/_..._.mjs.map (240 B)[39m[90m (153 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/auth/codes.mjs (789 B)[39m[90m (397 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/auth/codes.mjs.map (518 B)[39m[90m (228 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/auth/login.post.mjs (1.42 kB)[39m[90m (579 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/auth/login.post.mjs.map (827 B)[39m[90m (291 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/auth/logout.post.mjs (687 B)[39m[90m (309 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/auth/logout.post.mjs.map (376 B)[39m[90m (199 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/auth/refresh.post.mjs (1.21 kB)[39m[90m (456 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/auth/refresh.post.mjs.map (719 B)[39m[90m (263 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/demo/bigint.mjs (1.21 kB)[39m[90m (495 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/demo/bigint.mjs.map (501 B)[39m[90m (213 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/menu/all.mjs (789 B)[39m[90m (401 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/menu/all.mjs.map (514 B)[39m[90m (222 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/status.mjs (516 B)[39m[90m (271 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/status.mjs.map (353 B)[39m[90m (175 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/dept/_id_.delete.mjs (679 B)[39m[90m (333 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/dept/_id_.delete.mjs.map (385 B)[39m[90m (206 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/dept/_id_.put.mjs (670 B)[39m[90m (332 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/dept/_id_.put.mjs.map (379 B)[39m[90m (203 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/dept/list.mjs (1.94 kB)[39m[90m (809 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/dept/list.mjs.map (1.83 kB)[39m[90m (443 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/menu/list.mjs (657 B)[39m[90m (326 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/menu/list.mjs.map (340 B)[39m[90m (190 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/menu/name-exists.mjs (1.02 kB)[39m[90m (484 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/menu/name-exists.mjs.map (801 B)[39m[90m (293 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/menu/path-exists.mjs (1.03 kB)[39m[90m (490 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/menu/path-exists.mjs.map (826 B)[39m[90m (298 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/role/list.mjs (2.54 kB)[39m[90m (997 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/system/role/list.mjs.map (2.51 kB)[39m[90m (553 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/table/list.mjs (2.46 kB)[39m[90m (948 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/table/list.mjs.map (2.54 kB)[39m[90m (494 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/test.get.mjs (360 B)[39m[90m (204 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/test.get.mjs.map (178 B)[39m[90m (134 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/test.post.mjs (364 B)[39m[90m (206 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/test.post.mjs.map (180 B)[39m[90m (134 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/upload.mjs (692 B)[39m[90m (353 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/upload.mjs.map (356 B)[39m[90m (192 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/user/info.mjs (618 B)[39m[90m (299 B gzip)[39m
[90m  ├─ .output/server/chunks/routes/api/user/info.mjs.map (330 B)[39m[90m (185 B gzip)[39m
[90m  ├─ .output/server/index.mjs (1.67 kB)[39m[90m (737 B gzip)[39m
[90m  └─ .output/server/package.json (602 B)[39m[90m (312 B gzip)[39m
[36mΣ Total size:[39m 4.3 MB (1.07 MB gzip)
[90m[[90mnitro[90m][39m [32m✔[39m You can preview this build using [36mnode .output/server/index.mjs[39m
