var S=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var x=(s,t)=>{var e={};for(var o in s)F.call(s,o)&&t.indexOf(o)<0&&(e[o]=s[o]);if(s!=null&&S)for(var o of S(s))t.indexOf(o)<0&&I.call(s,o)&&(e[o]=s[o]);return e};var $=(s,t,e)=>new Promise((o,r)=>{var n=d=>{try{u(e.next(d))}catch(h){r(h)}},l=d=>{try{u(e.throw(d))}catch(h){r(h)}},u=d=>d.done?o(d.value):Promise.resolve(d.value).then(n,l);u((e=e.apply(s,t)).next())});import{a4 as f,aa as m,ab as i,a7 as a,af as j,ag as L,ac as c,a8 as _,J as v,x as g,ad as k,aW as M,aF as W,R as H,a_ as O,a$ as N,av as y,F as R,aC as U,aV as D,aq as w,ai as A,ah as G,aj as b,a1 as E,a2 as V,aB as p,n as X}from"../jse/index-index-BAMHRxBA.js";import{aR as B,bv as P,cR as Y,cS as J,cT as K,cU as Q,bs as C,cV as Z,cW as ee,bA as ae,cX as te,cY as oe,cB as se,bn as ne,cD as le,aU as re,az as de,bk as ie,u as ce,aS as z,b5 as ue}from"./bootstrap-BmSDnAET.js";const pe=B("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);const me=B("moon-star",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9",key:"4ay0iu"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}]]);const fe=B("sun-moon",[["path",{d:"M12 8a2.83 2.83 0 0 0 4 4 4 4 0 1 1-4-4",key:"1fu5g2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.9 4.9 1.4 1.4",key:"b9915j"}],["path",{d:"m17.7 17.7 1.4 1.4",key:"qc3ed3"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.3 17.7-1.4 1.4",key:"5gca6"}],["path",{d:"m19.1 4.9-1.4 1.4",key:"wpu9u6"}]]);const he=B("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),ge=f({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(s,{emit:t}){const r=P(s,t);return(n,l)=>(i(),m(a(Y),j(L(a(r))),{default:c(()=>[_(n.$slots,"default")]),_:3},16))}}),ye=f({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(s,{emit:t}){const e=s,o=t,r=v(()=>{const d=e,{class:l}=d;return x(d,["class"])}),n=P(r,o);return(l,u)=>(i(),m(a(J),null,{default:c(()=>[g(a(K),k(a(n),{class:a(M)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",e.class)}),{default:c(()=>[_(l.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),_e=f({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(s){const t=s;return(e,o)=>(i(),m(a(Q),j(L(t)),{default:c(()=>[_(e.$slots,"default")]),_:3},16))}}),ve=f({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(s){const t=s,e=v(()=>{const l=t,{class:r}=l;return x(l,["class"])}),o=C(e);return(r,n)=>(i(),m(a(Z),k(a(o),{class:a(M)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[_(r.$slots,"default")]),_:3},16,["class"]))}}),ke=f({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(s){const e=C(s);return(o,r)=>(i(),m(a(ee),k({class:"outline-none"},a(e)),{default:c(()=>[_(o.$slots,"default")]),_:3},16))}}),xe=ae("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",{defaultVariants:{size:"default",variant:"default"},variants:{size:{default:"h-9 px-3",lg:"h-10 px-3",sm:"h-8 px-2"},variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground"}}}),be=f({__name:"ToggleGroup",props:{rovingFocus:{type:Boolean},disabled:{type:Boolean},orientation:{},dir:{},loop:{type:Boolean},asChild:{type:Boolean},as:{},type:{},modelValue:{},defaultValue:{},class:{},size:{},variant:{}},emits:["update:modelValue"],setup(s,{emit:t}){const e=s,o=t;W("toggleGroup",{size:e.size,variant:e.variant});const r=v(()=>{const d=e,{class:l}=d;return x(d,["class"])}),n=P(r,o);return(l,u)=>(i(),m(a(te),k(a(n),{class:a(M)("flex items-center justify-center gap-1",e.class)}),{default:c(()=>[_(l.$slots,"default")]),_:3},16,["class"]))}}),we=f({__name:"ToggleGroupItem",props:{value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{},size:{},variant:{}},setup(s){const t=s,e=H("toggleGroup"),o=v(()=>{const h=t,{class:n,size:l,variant:u}=h;return x(h,["class","size","variant"])}),r=C(o);return(n,l)=>{var u,d;return i(),m(a(oe),k(a(r),{class:a(M)(a(xe)({variant:((u=a(e))==null?void 0:u.variant)||n.variant,size:((d=a(e))==null?void 0:d.size)||n.size}),t.class)}),{default:c(()=>[_(n.$slots,"default")]),_:3},16,["class"])}}}),Me=f({name:"DropdownRadioMenu",__name:"dropdown-radio-menu",props:O({menus:{}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const t=N(s,"modelValue");function e(o){t.value=o}return(o,r)=>(i(),m(a(ge),null,{default:c(()=>[g(a(ke),{"as-child":"",class:"flex items-center gap-1"},{default:c(()=>[_(o.$slots,"default")]),_:3}),g(a(ye),{align:"start"},{default:c(()=>[g(a(_e),null,{default:c(()=>[(i(!0),y(R,null,U(o.menus,n=>(i(),m(a(ve),{key:n.key,class:D([n.value===t.value?"bg-accent text-accent-foreground":"","data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer"]),onClick:l=>e(n.value)},{default:c(()=>[n.icon?(i(),m(G(n.icon),{key:0,class:"mr-2 size-4"})):w("",!0),n.icon?w("",!0):(i(),y("span",{key:1,class:D([n.value===t.value?"bg-foreground":"","mr-2 size-1.5 rounded-full"])},null,2)),A(" "+b(n.label),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:3}))}}),Be={class:"text-md flex-center"},$e=["href"],ze=["href"],je=f({name:"Copyright",__name:"copyright",props:{companyName:{default:"Vben Admin"},companySiteLink:{default:""},date:{default:"2024"},icp:{default:""},icpLink:{default:""}},setup(s){return(t,e)=>(i(),y("div",Be,[t.icp?(i(),y("a",{key:0,href:t.icpLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},b(t.icp),9,$e)):w("",!0),A(" Copyright © "+b(t.date)+" ",1),t.companyName?(i(),y("a",{key:1,href:t.companySiteLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},b(t.companyName),9,ze)):w("",!0)]))}}),Le=f({name:"LanguageToggle",__name:"language-toggle",setup(s){function t(e){return $(this,null,function*(){if(!e)return;const o=e;V({app:{locale:o}}),yield le(o)})}return(e,o)=>(i(),y("div",null,[g(a(Me),{menus:a(se),"model-value":a(E).app.locale,"onUpdate:modelValue":t},{default:c(()=>[g(a(ne),null,{default:c(()=>[g(a(pe),{class:"text-foreground size-4"})]),_:1})]),_:1},8,["menus","model-value"])]))}}),Ve=f({name:"ThemeToggleButton",__name:"theme-button",props:O({type:{default:"normal"}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const t=s,e=N(s,"modelValue"),o=v(()=>e.value?"light":"dark"),r=v(()=>t.type==="normal"?{variant:"heavy"}:{class:"rounded-full",size:"icon",style:{padding:"7px"},variant:"icon"});function n(l){if(!(document.startViewTransition&&!window.matchMedia("(prefers-reduced-motion: reduce)").matches)||!l){e.value=!e.value;return}const d=l.clientX,h=l.clientY,q=Math.hypot(Math.max(d,innerWidth-d),Math.max(h,innerHeight-h));document.startViewTransition(()=>$(null,null,function*(){e.value=!e.value,yield X()})).ready.then(()=>{const T=[`circle(0px at ${d}px ${h}px)`,`circle(${q}px at ${d}px ${h}px)`];document.documentElement.animate({clipPath:e.value?[...T].reverse():T},{duration:450,easing:"ease-in",pseudoElement:e.value?"::view-transition-old(root)":"::view-transition-new(root)"})})}return(l,u)=>(i(),m(a(re),k({"aria-label":o.value,class:[[`is-${o.value}`],"theme-toggle cursor-pointer border-none bg-none"],"aria-live":"polite"},r.value,{onClick:de(n,["stop"])}),{default:c(()=>u[0]||(u[0]=[p("svg",{"aria-hidden":"true",height:"24",viewBox:"0 0 24 24",width:"24"},[p("mask",{id:"theme-toggle-moon",class:"theme-toggle__moon",fill:"hsl(var(--foreground)/80%)",stroke:"none"},[p("rect",{fill:"white",height:"100%",width:"100%",x:"0",y:"0"}),p("circle",{cx:"40",cy:"8",fill:"black",r:"11"})]),p("circle",{id:"sun",class:"theme-toggle__sun",cx:"12",cy:"12",mask:"url(#theme-toggle-moon)",r:"11"}),p("g",{class:"theme-toggle__sun-beams"},[p("line",{x1:"12",x2:"12",y1:"1",y2:"3"}),p("line",{x1:"12",x2:"12",y1:"21",y2:"23"}),p("line",{x1:"4.22",x2:"5.64",y1:"4.22",y2:"5.64"}),p("line",{x1:"18.36",x2:"19.78",y1:"18.36",y2:"19.78"}),p("line",{x1:"1",x2:"3",y1:"12",y2:"12"}),p("line",{x1:"21",x2:"23",y1:"12",y2:"12"}),p("line",{x1:"4.22",x2:"5.64",y1:"19.78",y2:"18.36"}),p("line",{x1:"18.36",x2:"19.78",y1:"5.64",y2:"4.22"})])],-1)])),_:1,__:[0]},16,["aria-label","class"]))}}),Pe=ie(Ve,[["__scopeId","data-v-bfb8aee9"]]),Oe=f({name:"ThemeToggle",__name:"theme-toggle",props:{shouldOnHover:{type:Boolean,default:!1}},setup(s){function t(r){V({theme:{mode:r?"dark":"light"}})}const{isDark:e}=ce(),o=[{icon:he,name:"light",title:z("preferences.theme.light")},{icon:me,name:"dark",title:z("preferences.theme.dark")},{icon:fe,name:"auto",title:z("preferences.followSystem")}];return(r,n)=>(i(),y("div",null,[g(a(ue),{disabled:!r.shouldOnHover,side:"bottom"},{trigger:c(()=>[g(Pe,{"model-value":a(e),type:"icon","onUpdate:modelValue":t},null,8,["model-value"])]),default:c(()=>[g(a(be),{"model-value":a(E).theme.mode,class:"gap-2",type:"single",variant:"outline","onUpdate:modelValue":n[0]||(n[0]=l=>a(V)({theme:{mode:l}}))},{default:c(()=>[(i(),y(R,null,U(o,l=>g(a(we),{key:l.name,value:l.name},{default:c(()=>[(i(),m(G(l.icon),{class:"size-5"}))]),_:2},1032,["value"])),64))]),_:1},8,["model-value"])]),_:1},8,["disabled"])]))}});export{me as M,he as S,ge as _,ke as a,ye as b,ve as c,_e as d,be as e,we as f,fe as g,Oe as h,Le as i,je as j,Me as k};
