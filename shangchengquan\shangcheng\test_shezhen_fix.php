<?php
/**
 * 舌诊接口修复测试脚本
 * 2025-07-17 测试兼容性修复
 */

// 模拟POST请求数据
$_POST = [
    'image_url' => 'https://kuaifengimg.azheteng.cn/upload/62/20250718/fed0d7bd030b57e056de3dba260e6e36.jpg',
    'use_free' => 1,
    'order_id' => '47'
];

// 模拟input函数
function input($key, $default = null) {
    $keys = explode('.', $key);
    $data = $_POST;
    
    foreach ($keys as $k) {
        if (strpos($k, '/') !== false) {
            $parts = explode('/', $k);
            $k = $parts[0];
            $type = $parts[1] ?? '';
        }
        
        if (!isset($data[$k])) {
            return $default;
        }
        $data = $data[$k];
    }
    
    return $data;
}

echo "<h2>舌诊接口兼容性测试</h2>";

// 测试参数获取
$diagnosisType = input('post.diagnosis_type/d', 1);
$tongueImageUrl = input('post.tongue_image_url');
$faceImageUrl = input('post.face_image_url');
$sublingualImageUrl = input('post.sublingual_image_url');
$useFree = input('post.use_free/d', 0);

// 兼容性处理：支持旧版本的image_url参数
$legacyImageUrl = input('post.image_url');
if ($legacyImageUrl && !$tongueImageUrl) {
    $tongueImageUrl = $legacyImageUrl;
    echo "<p style='color: green;'>✓ 兼容模式：将image_url映射为tongue_image_url</p>";
}

echo "<h3>参数解析结果：</h3>";
echo "<ul>";
echo "<li>诊疗类型 (diagnosis_type): " . $diagnosisType . "</li>";
echo "<li>舌头图片 (tongue_image_url): " . ($tongueImageUrl ?: '未提供') . "</li>";
echo "<li>面部图片 (face_image_url): " . ($faceImageUrl ?: '未提供') . "</li>";
echo "<li>舌下脉络图片 (sublingual_image_url): " . ($sublingualImageUrl ?: '未提供') . "</li>";
echo "<li>使用免费 (use_free): " . $useFree . "</li>";
echo "<li>旧版图片参数 (image_url): " . ($legacyImageUrl ?: '未提供') . "</li>";
echo "</ul>";

// 构建图片数据
$imageData = [];
if ($tongueImageUrl) $imageData['tf_image'] = $tongueImageUrl;
if ($faceImageUrl) $imageData['ff_image'] = $faceImageUrl;
if ($sublingualImageUrl) $imageData['tb_image'] = $sublingualImageUrl;

echo "<h3>构建的图片数据：</h3>";
echo "<pre>" . json_encode($imageData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// 模拟验证逻辑
echo "<h3>验证结果：</h3>";
switch ($diagnosisType) {
    case 1: // 舌诊
        if (empty($imageData['tf_image'])) {
            echo "<p style='color: red;'>✗ 验证失败：舌诊需要提供舌头图片</p>";
        } else {
            echo "<p style='color: green;'>✓ 验证通过：舌诊图片已提供</p>";
        }
        break;
    case 2: // 面诊
        if (empty($imageData['ff_image'])) {
            echo "<p style='color: red;'>✗ 验证失败：面诊需要提供面部图片</p>";
        } else {
            echo "<p style='color: green;'>✓ 验证通过：面诊图片已提供</p>";
        }
        break;
    case 3: // 综合诊疗
        if (empty($imageData['tf_image']) && empty($imageData['ff_image']) && empty($imageData['tb_image'])) {
            echo "<p style='color: red;'>✗ 验证失败：综合诊疗至少需要提供一张图片</p>";
        } else {
            echo "<p style='color: green;'>✓ 验证通过：综合诊疗图片已提供</p>";
        }
        break;
    default:
        echo "<p style='color: red;'>✗ 验证失败：不支持的诊疗类型</p>";
}

echo "<h3>修复总结：</h3>";
echo "<ul>";
echo "<li>✓ 添加了对旧版本 image_url 参数的兼容性支持</li>";
echo "<li>✓ 当 image_url 存在且 tongue_image_url 为空时，自动映射</li>";
echo "<li>✓ 添加了详细的操作日志记录</li>";
echo "<li>✓ 保持了向后兼容性，不影响新版本接口</li>";
echo "</ul>";
?>
