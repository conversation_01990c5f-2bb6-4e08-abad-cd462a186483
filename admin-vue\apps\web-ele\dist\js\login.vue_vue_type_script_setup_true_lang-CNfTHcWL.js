var oe=Object.defineProperty,ae=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var Z=Object.getOwnPropertySymbols;var re=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable;var H=(i,a,s)=>a in i?oe(i,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[a]=s,K=(i,a)=>{for(var s in a||(a={}))re.call(a,s)&&H(i,s,a[s]);if(Z)for(var s of Z(a))le.call(a,s)&&H(i,s,a[s]);return i},U=(i,a)=>ae(i,ie(a));var ee=(i,a,s)=>new Promise((c,r)=>{var u=p=>{try{l(s.next(p))}catch(v){r(v)}},e=p=>{try{l(s.throw(p))}catch(v){r(v)}},l=p=>p.done?c(p.value):Promise.resolve(p.value).then(u,e);l((s=s.apply(i,a)).next())});import{a1 as ce,aB as ue,aC as fe,_ as de,$ as f,aD as O,J as me,f as pe,ao as ge,g as X,t as he,B as ye,a3 as be,K as z,L as ve}from"./bootstrap-CYivmKoJ.js";import{d as B,e as W,f as _,g as m,m as F,l as L,c as V,r as G,n as j,E as g,u as t,D as h,q as A,G as x,t as w,Y as te,Z as we,L as ne,w as _e,V as $e,af as ke,O as se,k as Pe,j as E,o as Se,h as R,ag as Le}from"../jse/index-index-SSqEGcIT.js";import{T as Me}from"./auth-title-Cs6tZ2yh.js";import{a as Te,b as Re,M as xe,c as Ee}from"./index-wKOw6cfx.js";const Be=B({__name:"spine-text",props:{animationDuration:{default:2},animationIterationCount:{default:"infinite"}},setup(i){const a=W(()=>({animation:`shine ${i.animationDuration}s linear ${i.animationIterationCount}`}));return(s,c)=>(m(),_("div",{style:F(a.value),class:"vben-spine-text !bg-clip-text text-transparent"},[L(s.$slots,"default")],4))}}),Ce=B({__name:"slider-captcha-action",props:{actionStyle:{},isPassing:{type:Boolean},toLeft:{type:Boolean}},setup(i,{expose:a}){const s=i,c=V("actionRef"),r=G("0"),u=W(()=>{const{actionStyle:l}=s;return U(K({},l),{left:r.value})}),e=W(()=>Number.parseInt(r.value)>10&&!s.isPassing);return a({getEl:()=>c.value,getStyle:()=>{var l;return(l=c==null?void 0:c.value)==null?void 0:l.style},setLeft:l=>{r.value=l}}),(l,p)=>(m(),_("div",{ref_key:"actionRef",ref:c,class:j([{"transition-width !left-0 duration-300":l.toLeft,"rounded-md":e.value},"bg-background dark:bg-accent absolute left-0 top-0 flex h-full cursor-move items-center justify-center px-3.5 shadow-md"]),style:F(u.value),name:"captcha-action"},[g(t(ce),{"is-passing":l.isPassing,class:"text-foreground/60 size-4"},{default:h(()=>[L(l.$slots,"icon",{},()=>[l.isPassing?(m(),A(t(fe),{key:1})):(m(),A(t(ue),{key:0}))])]),_:3},8,["is-passing"])],6))}}),De=B({__name:"slider-captcha-bar",props:{barStyle:{},toLeft:{type:Boolean}},setup(i,{expose:a}){const s=i,c=V("barRef"),r=G("0"),u=W(()=>{const{barStyle:e}=s;return U(K({},e),{width:r.value})});return a({getEl:()=>c.value,setWidth:e=>{r.value=e}}),(e,l)=>(m(),_("div",{ref_key:"barRef",ref:c,class:j([e.toLeft&&"transition-width !w-0 duration-300","bg-success absolute h-full"]),style:F(u.value)},null,6))}}),Ve=B({__name:"slider-captcha-content",props:{contentStyle:{},isPassing:{type:Boolean},successText:{},text:{}},setup(i,{expose:a}){const s=i,c=V("contentRef"),r=W(()=>{const{contentStyle:u}=s;return K({},u)});return a({getEl:()=>c.value}),(u,e)=>(m(),_("div",{ref_key:"contentRef",ref:c,class:j([{[u.$style.success]:u.isPassing},"absolute top-0 flex size-full select-none items-center justify-center text-xs"]),style:F(r.value)},[L(u.$slots,"text",{},()=>[g(t(Be),{class:"flex h-full items-center"},{default:h(()=>[x(w(u.isPassing?u.successText:u.text),1)]),_:1})])],6))}}),Ae="_success_fwxn1_2",We={success:Ae},Ne={$style:We},Ie=de(Ve,[["__cssModules",Ne]]),Fe=B({__name:"index",props:te({class:{},actionStyle:{default:()=>({})},barStyle:{default:()=>({})},contentStyle:{default:()=>({})},wrapperStyle:{default:()=>({})},isSlot:{type:Boolean,default:!1},successText:{default:""},text:{default:""}},{modelValue:{type:Boolean,default:!1},modelModifiers:{}}),emits:te(["end","move","start","success"],["update:modelValue"]),setup(i,{expose:a,emit:s}){const c=i,r=s,u=we(i,"modelValue"),e=ne({endTime:0,isMoving:!1,isPassing:!1,moveDistance:0,startTime:0,toLeft:!1});a({resume:q});const l=V("wrapperRef"),p=V("barRef"),v=V("contentRef"),$=V("actionRef");_e(()=>e.isPassing,n=>{if(n){const{endTime:y,startTime:b}=e,k=(y-b)/1e3;r("success",{isPassing:n,time:k.toFixed(1)}),u.value=n}}),$e(()=>{e.isPassing=!!u.value});function N(n){return"pageX"in n?n.pageX:"touches"in n&&n.touches[0]?n.touches[0].pageX:0}function C(n){e.isPassing||$.value&&(r("start",n),e.moveDistance=N(n)-Number.parseInt($.value.getStyle().left.replace("px","")||"0",10),e.startTime=Date.now(),e.isMoving=!0)}function o(n){var P,S,D;const y=(S=(P=l.value)==null?void 0:P.offsetWidth)!=null?S:220,b=(D=n==null?void 0:n.offsetWidth)!=null?D:40,k=y-b-6;return{actionWidth:b,offset:k,wrapperWidth:y}}function d(n){const{isMoving:y,moveDistance:b}=e;if(y){const k=t($),P=t(p);if(!k||!P)return;const{actionWidth:S,offset:D,wrapperWidth:I}=o(k.getEl()),T=N(n)-b;r("move",{event:n,moveDistance:b,moveX:T}),T>0&&T<=D?(k.setLeft(`${T}px`),P.setWidth(`${T+S/2}px`)):T>D&&(k.setLeft(`${I-S}px`),P.setWidth(`${I-S/2}px`),c.isSlot||Q())}}function M(n){const{isMoving:y,isPassing:b,moveDistance:k}=e;if(y&&!b){r("end",n);const P=$.value,S=t(p);if(!P||!S)return;const D=N(n)-k,{actionWidth:I,offset:T,wrapperWidth:J}=o(P.getEl());D<T?c.isSlot?setTimeout(()=>{if(u.value){const Y=t(v);Y&&(Y.getEl().style.width=`${Number.parseInt(S.getEl().style.width)}px`)}else q()},0):q():(P.setLeft(`${J-I}px`),S.setWidth(`${J-I/2}px`),Q()),e.isMoving=!1}}function Q(){if(c.isSlot){q();return}e.endTime=Date.now(),e.isPassing=!0,e.isMoving=!1}function q(){e.isMoving=!1,e.isPassing=!1,e.moveDistance=0,e.toLeft=!1,e.startTime=0,e.endTime=0;const n=t($),y=t(p),b=t(v);!n||!y||!b||(b.getEl().style.width="100%",e.toLeft=!0,ke(()=>{e.toLeft=!1,n.setLeft("0"),y.setWidth("0")},300))}return(n,y)=>(m(),_("div",{ref_key:"wrapperRef",ref:l,class:j(t(Pe)("border-border bg-background-deep relative flex h-10 w-full items-center overflow-hidden rounded-md border text-center",c.class)),style:F(n.wrapperStyle),onMouseleave:M,onMousemove:d,onMouseup:M,onTouchend:M,onTouchmove:d},[g(De,{ref_key:"barRef",ref:p,"bar-style":n.barStyle,"to-left":e.toLeft},null,8,["bar-style","to-left"]),g(Ie,{ref_key:"contentRef",ref:v,"content-style":n.contentStyle,"is-passing":e.isPassing,"success-text":n.successText||t(f)("ui.captcha.sliderSuccessText"),text:n.text||t(f)("ui.captcha.sliderDefaultText")},se({_:2},[n.$slots.text?{name:"text",fn:h(()=>[L(n.$slots,"text",{isPassing:e.isPassing})]),key:"0"}:void 0]),1032,["content-style","is-passing","success-text","text"]),g(Ce,{ref_key:"actionRef",ref:$,"action-style":n.actionStyle,"is-passing":e.isPassing,"to-left":e.toLeft,onMousedown:C,onTouchstart:C},se({_:2},[n.$slots.actionIcon?{name:"icon",fn:h(()=>[L(n.$slots,"actionIcon",{isPassing:e.isPassing})]),key:"0"}:void 0]),1032,["action-style","is-passing","to-left"])],38))}}),je={class:"w-full sm:mx-auto md:max-w-md"},qe={class:"mt-4 flex items-center justify-between"},Ke={class:"text-muted-foreground text-center text-xs uppercase"},Oe={class:"mt-4 flex flex-wrap justify-center"},Ue=B({name:"ThirdPartyLogin",__name:"third-party-login",setup(i){return(a,s)=>(m(),_("div",je,[E("div",qe,[s[0]||(s[0]=E("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1)),E("span",Ke,w(t(f)("authentication.thirdPartyLogin")),1),s[1]||(s[1]=E("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1))]),E("div",Oe,[g(t(O),{class:"mb-3"},{default:h(()=>[g(t(Te))]),_:1}),g(t(O),{class:"mb-3"},{default:h(()=>[g(t(Re))]),_:1}),g(t(O),{class:"mb-3"},{default:h(()=>[g(t(xe))]),_:1}),g(t(O),{class:"mb-3"},{default:h(()=>[g(t(Ee))]),_:1})])]))}}),Xe=["onKeydown"],ze={class:"text-muted-foreground"},Ge={key:0,class:"mb-6 flex justify-between"},Qe={class:"flex-center"},Je={key:1,class:"mb-2 mt-4 flex items-center justify-between"},Ye={key:0,class:"mt-3 text-center text-sm"},Ze=B({name:"AuthenticationLogin",__name:"login",props:{formSchema:{default:()=>[]},codeLoginPath:{default:"/auth/code-login"},forgetPasswordPath:{default:"/auth/forget-password"},loading:{type:Boolean,default:!1},qrCodeLoginPath:{default:"/auth/qrcode-login"},registerPath:{default:"/auth/register"},showCodeLogin:{type:Boolean,default:!0},showForgetPassword:{type:Boolean,default:!0},showQrcodeLogin:{type:Boolean,default:!0},showRegister:{type:Boolean,default:!0},showRememberMe:{type:Boolean,default:!0},showThirdPartyLogin:{type:Boolean,default:!0},subTitle:{default:""},title:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(i,{expose:a,emit:s}){const c=i,r=s,[u,e]=me(ne({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:W(()=>c.formSchema),showDefaultActions:!1})),l=pe(),p=`REMEMBER_ME_USERNAME_${location.hostname}`,v=localStorage.getItem(p)||"",$=G(!!v);function N(){return ee(this,null,function*(){const{valid:o}=yield e.validate(),d=yield e.getValues();o&&(localStorage.setItem(p,$.value?d==null?void 0:d.username:""),r("submit",d))})}function C(o){l.push(o)}return Se(()=>{v&&e.setFieldValue("username",v)}),a({getFormApi:()=>e}),(o,d)=>(m(),_("div",{onKeydown:he(ye(N,["prevent"]),["enter"])},[L(o.$slots,"title",{},()=>[g(Me,null,{desc:h(()=>[E("span",ze,[L(o.$slots,"subTitle",{},()=>[x(w(o.subTitle||t(f)("authentication.loginSubtitle")),1)])])]),default:h(()=>[L(o.$slots,"title",{},()=>[x(w(o.title||`${t(f)("authentication.welcomeBack")} 👋🏻`),1)])]),_:3})]),g(t(u)),o.showRememberMe||o.showForgetPassword?(m(),_("div",Ge,[E("div",Qe,[o.showRememberMe?(m(),A(t(ge),{key:0,checked:$.value,"onUpdate:checked":d[0]||(d[0]=M=>$.value=M),name:"rememberMe"},{default:h(()=>[x(w(t(f)("authentication.rememberMe")),1)]),_:1},8,["checked"])):R("",!0)]),o.showForgetPassword?(m(),_("span",{key:0,class:"vben-link text-sm font-normal",onClick:d[1]||(d[1]=M=>C(o.forgetPasswordPath))},w(t(f)("authentication.forgetPassword")),1)):R("",!0)])):R("",!0),g(t(X),{class:j([{"cursor-wait":o.loading},"w-full"]),loading:o.loading,"aria-label":"login",onClick:N},{default:h(()=>[x(w(o.submitButtonText||t(f)("common.login")),1)]),_:1},8,["class","loading"]),o.showCodeLogin||o.showQrcodeLogin?(m(),_("div",Je,[o.showCodeLogin?(m(),A(t(X),{key:0,class:"w-1/2",variant:"outline",onClick:d[2]||(d[2]=M=>C(o.codeLoginPath))},{default:h(()=>[x(w(t(f)("authentication.mobileLogin")),1)]),_:1})):R("",!0),o.showQrcodeLogin?(m(),A(t(X),{key:1,class:"ml-4 w-1/2",variant:"outline",onClick:d[3]||(d[3]=M=>C(o.qrCodeLoginPath))},{default:h(()=>[x(w(t(f)("authentication.qrcodeLogin")),1)]),_:1})):R("",!0)])):R("",!0),L(o.$slots,"third-party-login",{},()=>[o.showThirdPartyLogin?(m(),A(Ue,{key:0})):R("",!0)]),L(o.$slots,"to-register",{},()=>[o.showRegister?(m(),_("div",Ye,[x(w(t(f)("authentication.accountTip"))+" ",1),E("span",{class:"vben-link text-sm font-normal",onClick:d[4]||(d[4]=M=>C(o.registerPath))},w(t(f)("authentication.createAccount")),1)])):R("",!0)])],40,Xe))}}),ot=B({name:"Login",__name:"login",setup(i){const a=be(),s=[{label:"Super",value:"vben"},{label:"Admin",value:"admin"},{label:"User",value:"jack"}],c=W(()=>[{component:"VbenSelect",componentProps:{options:s,placeholder:f("authentication.selectAccount")},fieldName:"selectAccount",label:f("authentication.selectAccount"),rules:z().min(1,{message:f("authentication.selectAccount")}).optional().default("vben")},{component:"VbenInput",componentProps:{placeholder:f("authentication.usernameTip")},dependencies:{trigger(r,u){if(r.selectAccount){const e=s.find(l=>l.value===r.selectAccount);e&&u.setValues({password:"123456",username:e.value})}},triggerFields:["selectAccount"]},fieldName:"username",label:f("authentication.username"),rules:z().min(1,{message:f("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{placeholder:f("authentication.password")},fieldName:"password",label:f("authentication.password"),rules:z().min(1,{message:f("authentication.passwordTip")})},{component:Le(Fe),fieldName:"captcha",rules:ve().refine(r=>r,{message:f("authentication.verifyRequiredTip")})}]);return(r,u)=>(m(),A(t(Ze),{"form-schema":c.value,loading:t(a).loginLoading,onSubmit:t(a).authLogin},null,8,["form-schema","loading","onSubmit"]))}});export{ot as _};
