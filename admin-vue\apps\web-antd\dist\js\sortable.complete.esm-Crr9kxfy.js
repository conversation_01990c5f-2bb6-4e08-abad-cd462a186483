function _e(o,t){var e=Object.keys(o);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(o);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(o,i).enumerable})),e.push.apply(e,n)}return e}function Q(o){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?_e(Object(e),!0).forEach(function(n){Ve(o,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(e)):_e(Object(e)).forEach(function(n){Object.defineProperty(o,n,Object.getOwnPropertyDescriptor(e,n))})}return o}function zt(o){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?zt=function(t){return typeof t}:zt=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zt(o)}function Ve(o,t,e){return t in o?Object.defineProperty(o,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):o[t]=e,o}function $(){return $=Object.assign||function(o){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(o[n]=e[n])}return o},$.apply(this,arguments)}function Ze(o,t){if(o==null)return{};var e={},n=Object.keys(o),i,r;for(r=0;r<n.length;r++)i=n[r],!(t.indexOf(i)>=0)&&(e[i]=o[i]);return e}function Qe(o,t){if(o==null)return{};var e=Ze(o,t),n,i;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(o);for(i=0;i<r.length;i++)n=r[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(o,n)&&(e[n]=o[n])}return e}function Je(o){return tn(o)||en(o)||nn(o)||on()}function tn(o){if(Array.isArray(o))return he(o)}function en(o){if(typeof Symbol!="undefined"&&o[Symbol.iterator]!=null||o["@@iterator"]!=null)return Array.from(o)}function nn(o,t){if(o){if(typeof o=="string")return he(o,t);var e=Object.prototype.toString.call(o).slice(8,-1);if(e==="Object"&&o.constructor&&(e=o.constructor.name),e==="Map"||e==="Set")return Array.from(o);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return he(o,t)}}function he(o,t){(t==null||t>o.length)&&(t=o.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=o[e];return n}function on(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var rn="1.15.6";function et(o){if(typeof window!="undefined"&&window.navigator)return!!navigator.userAgent.match(o)}var nt=et(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Xt=et(/Edge/i),Ce=et(/firefox/i),Pt=et(/safari/i)&&!et(/chrome/i)&&!et(/android/i),be=et(/iP(ad|od|hone)/i),Re=et(/chrome/i)&&et(/android/i),ke={capture:!1,passive:!1};function y(o,t,e){o.addEventListener(t,e,!nt&&ke)}function b(o,t,e){o.removeEventListener(t,e,!nt&&ke)}function Zt(o,t){if(t){if(t[0]===">"&&(t=t.substring(1)),o)try{if(o.matches)return o.matches(t);if(o.msMatchesSelector)return o.msMatchesSelector(t);if(o.webkitMatchesSelector)return o.webkitMatchesSelector(t)}catch(e){return!1}return!1}}function Xe(o){return o.host&&o!==document&&o.host.nodeType?o.host:o.parentNode}function G(o,t,e,n){if(o){e=e||document;do{if(t!=null&&(t[0]===">"?o.parentNode===e&&Zt(o,t):Zt(o,t))||n&&o===e)return o;if(o===e)break}while(o=Xe(o))}return null}var Oe=/\s+/g;function I(o,t,e){if(o&&t)if(o.classList)o.classList[e?"add":"remove"](t);else{var n=(" "+o.className+" ").replace(Oe," ").replace(" "+t+" "," ");o.className=(n+(e?" "+t:"")).replace(Oe," ")}}function h(o,t,e){var n=o&&o.style;if(n){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(o,""):o.currentStyle&&(e=o.currentStyle),t===void 0?e:e[t];!(t in n)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),n[t]=e+(typeof e=="string"?"":"px")}}function ht(o,t){var e="";if(typeof o=="string")e=o;else do{var n=h(o,"transform");n&&n!=="none"&&(e=n+" "+e)}while(!t&&(o=o.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(e)}function Ye(o,t,e){if(o){var n=o.getElementsByTagName(t),i=0,r=n.length;if(e)for(;i<r;i++)e(n[i],i);return n}return[]}function Z(){var o=document.scrollingElement;return o||document.documentElement}function O(o,t,e,n,i){if(!(!o.getBoundingClientRect&&o!==window)){var r,a,l,s,u,d,c;if(o!==window&&o.parentNode&&o!==Z()?(r=o.getBoundingClientRect(),a=r.top,l=r.left,s=r.bottom,u=r.right,d=r.height,c=r.width):(a=0,l=0,s=window.innerHeight,u=window.innerWidth,d=window.innerHeight,c=window.innerWidth),(t||e)&&o!==window&&(i=i||o.parentNode,!nt))do if(i&&i.getBoundingClientRect&&(h(i,"transform")!=="none"||e&&h(i,"position")!=="static")){var p=i.getBoundingClientRect();a-=p.top+parseInt(h(i,"border-top-width")),l-=p.left+parseInt(h(i,"border-left-width")),s=a+r.height,u=l+r.width;break}while(i=i.parentNode);if(n&&o!==window){var D=ht(i||o),w=D&&D.a,S=D&&D.d;D&&(a/=S,l/=w,c/=w,d/=S,s=a+d,u=l+c)}return{top:a,left:l,bottom:s,right:u,width:c,height:d}}}function Te(o,t,e){for(var n=lt(o,!0),i=O(o)[t];n;){var r=O(n)[e],a=void 0;if(a=i>=r,!a)return n;if(n===Z())break;n=lt(n,!1)}return!1}function yt(o,t,e,n){for(var i=0,r=0,a=o.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==g.ghost&&(n||a[r]!==g.dragged)&&G(a[r],e.draggable,o,!1)){if(i===t)return a[r];i++}r++}return null}function ye(o,t){for(var e=o.lastElementChild;e&&(e===g.ghost||h(e,"display")==="none"||t&&!Zt(e,t));)e=e.previousElementSibling;return e||null}function x(o,t){var e=0;if(!o||!o.parentNode)return-1;for(;o=o.previousElementSibling;)o.nodeName.toUpperCase()!=="TEMPLATE"&&o!==g.clone&&(!t||Zt(o,t))&&e++;return e}function Ae(o){var t=0,e=0,n=Z();if(o)do{var i=ht(o),r=i.a,a=i.d;t+=o.scrollLeft*r,e+=o.scrollTop*a}while(o!==n&&(o=o.parentNode));return[t,e]}function an(o,t){for(var e in o)if(o.hasOwnProperty(e)){for(var n in t)if(t.hasOwnProperty(n)&&t[n]===o[e][n])return Number(e)}return-1}function lt(o,t){if(!o||!o.getBoundingClientRect)return Z();var e=o,n=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var i=h(e);if(e.clientWidth<e.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return Z();if(n||t)return e;n=!0}}while(e=e.parentNode);return Z()}function ln(o,t){if(o&&t)for(var e in t)t.hasOwnProperty(e)&&(o[e]=t[e]);return o}function oe(o,t){return Math.round(o.top)===Math.round(t.top)&&Math.round(o.left)===Math.round(t.left)&&Math.round(o.height)===Math.round(t.height)&&Math.round(o.width)===Math.round(t.width)}var Mt;function Be(o,t){return function(){if(!Mt){var e=arguments,n=this;e.length===1?o.call(n,e[0]):o.apply(n,e),Mt=setTimeout(function(){Mt=void 0},t)}}}function sn(){clearTimeout(Mt),Mt=void 0}function He(o,t,e){o.scrollLeft+=t,o.scrollTop+=e}function we(o){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(o).cloneNode(!0):e?e(o).clone(!0)[0]:o.cloneNode(!0)}function Ie(o,t){h(o,"position","absolute"),h(o,"top",t.top),h(o,"left",t.left),h(o,"width",t.width),h(o,"height",t.height)}function ie(o){h(o,"position",""),h(o,"top",""),h(o,"left",""),h(o,"width",""),h(o,"height","")}function Ge(o,t,e){var n={};return Array.from(o.children).forEach(function(i){var r,a,l,s;if(!(!G(i,t.draggable,o,!1)||i.animated||i===e)){var u=O(i);n.left=Math.min((r=n.left)!==null&&r!==void 0?r:1/0,u.left),n.top=Math.min((a=n.top)!==null&&a!==void 0?a:1/0,u.top),n.right=Math.max((l=n.right)!==null&&l!==void 0?l:-1/0,u.right),n.bottom=Math.max((s=n.bottom)!==null&&s!==void 0?s:-1/0,u.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var R="Sortable"+new Date().getTime();function un(){var o=[],t;return{captureAnimationState:function(){if(o=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(i){if(!(h(i,"display")==="none"||i===g.ghost)){o.push({target:i,rect:O(i)});var r=Q({},o[o.length-1].rect);if(i.thisAnimationDuration){var a=ht(i,!0);a&&(r.top-=a.f,r.left-=a.e)}i.fromRect=r}})}},addAnimationState:function(n){o.push(n)},removeAnimationState:function(n){o.splice(an(o,{target:n}),1)},animateAll:function(n){var i=this;if(!this.options.animation){clearTimeout(t),typeof n=="function"&&n();return}var r=!1,a=0;o.forEach(function(l){var s=0,u=l.target,d=u.fromRect,c=O(u),p=u.prevFromRect,D=u.prevToRect,w=l.rect,S=ht(u,!0);S&&(c.top-=S.f,c.left-=S.e),u.toRect=c,u.thisAnimationDuration&&oe(p,c)&&!oe(d,c)&&(w.top-c.top)/(w.left-c.left)===(d.top-c.top)/(d.left-c.left)&&(s=cn(w,p,D,i.options)),oe(c,d)||(u.prevFromRect=d,u.prevToRect=c,s||(s=i.options.animation),i.animate(u,w,c,s)),s&&(r=!0,a=Math.max(a,s),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},s),u.thisAnimationDuration=s)}),clearTimeout(t),r?t=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),o=[]},animate:function(n,i,r,a){if(a){h(n,"transition",""),h(n,"transform","");var l=ht(this.el),s=l&&l.a,u=l&&l.d,d=(i.left-r.left)/(s||1),c=(i.top-r.top)/(u||1);n.animatingX=!!d,n.animatingY=!!c,h(n,"transform","translate3d("+d+"px,"+c+"px,0)"),this.forRepaintDummy=fn(n),h(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}function fn(o){return o.offsetWidth}function cn(o,t,e,n){return Math.sqrt(Math.pow(t.top-o.top,2)+Math.pow(t.left-o.left,2))/Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))*n.animation}var pt=[],re={initializeByDefault:!0},Yt={mount:function(t){for(var e in re)re.hasOwnProperty(e)&&!(e in t)&&(t[e]=re[e]);pt.forEach(function(n){if(n.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),pt.push(t)},pluginEvent:function(t,e,n){var i=this;this.eventCanceled=!1,n.cancel=function(){i.eventCanceled=!0};var r=t+"Global";pt.forEach(function(a){e[a.pluginName]&&(e[a.pluginName][r]&&e[a.pluginName][r](Q({sortable:e},n)),e.options[a.pluginName]&&e[a.pluginName][t]&&e[a.pluginName][t](Q({sortable:e},n)))})},initializePlugins:function(t,e,n,i){pt.forEach(function(l){var s=l.pluginName;if(!(!t.options[s]&&!l.initializeByDefault)){var u=new l(t,e,t.options);u.sortable=t,u.options=t.options,t[s]=u,$(n,u.defaults)}});for(var r in t.options)if(t.options.hasOwnProperty(r)){var a=this.modifyOption(t,r,t.options[r]);typeof a!="undefined"&&(t.options[r]=a)}},getEventProperties:function(t,e){var n={};return pt.forEach(function(i){typeof i.eventProperties=="function"&&$(n,i.eventProperties.call(e[i.pluginName],t))}),n},modifyOption:function(t,e,n){var i;return pt.forEach(function(r){t[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[e]=="function"&&(i=r.optionListeners[e].call(t[r.pluginName],n))}),i}};function At(o){var t=o.sortable,e=o.rootEl,n=o.name,i=o.targetEl,r=o.cloneEl,a=o.toEl,l=o.fromEl,s=o.oldIndex,u=o.newIndex,d=o.oldDraggableIndex,c=o.newDraggableIndex,p=o.originalEvent,D=o.putSortable,w=o.extraEventProperties;if(t=t||e&&e[R],!!t){var S,X=t.options,j="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!nt&&!Xt?S=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(S=document.createEvent("Event"),S.initEvent(n,!0,!0)),S.to=a||e,S.from=l||e,S.item=i||e,S.clone=r,S.oldIndex=s,S.newIndex=u,S.oldDraggableIndex=d,S.newDraggableIndex=c,S.originalEvent=p,S.pullMode=D?D.lastPutMode:void 0;var E=Q(Q({},w),Yt.getEventProperties(n,t));for(var _ in E)S[_]=E[_];e&&e.dispatchEvent(S),X[j]&&X[j].call(t,S)}}var dn=["evt"],B=function(t,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=n.evt,r=Qe(n,dn);Yt.pluginEvent.bind(g)(t,e,Q({dragEl:f,parentEl:N,ghostEl:v,rootEl:T,nextEl:dt,lastDownEl:Ut,cloneEl:A,cloneHidden:at,dragStarted:It,putSortable:F,activeSortable:g.active,originalEvent:i,oldIndex:bt,oldDraggableIndex:Ft,newIndex:L,newDraggableIndex:rt,hideGhostForTarget:je,unhideGhostForTarget:ze,cloneNowHidden:function(){at=!0},cloneNowShown:function(){at=!1},dispatchSortableEvent:function(l){Y({sortable:e,name:l,originalEvent:i})}},r))};function Y(o){At(Q({putSortable:F,cloneEl:A,targetEl:f,rootEl:T,oldIndex:bt,oldDraggableIndex:Ft,newIndex:L,newDraggableIndex:rt},o))}var f,N,v,T,dt,Ut,A,at,bt,L,Ft,rt,Ht,F,vt=!1,Qt=!1,Jt=[],ft,q,ae,le,Ne,xe,It,gt,Rt,kt=!1,Gt=!1,$t,k,se=[],pe=!1,te=[],ne=typeof document!="undefined",Wt=be,Pe=Xt||nt?"cssFloat":"float",hn=ne&&!Re&&!be&&"draggable"in document.createElement("div"),We=function(){if(ne){if(nt)return!1;var o=document.createElement("x");return o.style.cssText="pointer-events:auto",o.style.pointerEvents==="auto"}}(),Ke=function(t,e){var n=h(t),i=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=yt(t,0,e),a=yt(t,1,e),l=r&&h(r),s=a&&h(a),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+O(r).width,d=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+O(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&l.float&&l.float!=="none"){var c=l.float==="left"?"left":"right";return a&&(s.clear==="both"||s.clear===c)?"vertical":"horizontal"}return r&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||u>=i&&n[Pe]==="none"||a&&n[Pe]==="none"&&u+d>i)?"vertical":"horizontal"},pn=function(t,e,n){var i=n?t.left:t.top,r=n?t.right:t.bottom,a=n?t.width:t.height,l=n?e.left:e.top,s=n?e.right:e.bottom,u=n?e.width:e.height;return i===l||r===s||i+a/2===l+u/2},gn=function(t,e){var n;return Jt.some(function(i){var r=i[R].options.emptyInsertThreshold;if(!(!r||ye(i))){var a=O(i),l=t>=a.left-r&&t<=a.right+r,s=e>=a.top-r&&e<=a.bottom+r;if(l&&s)return n=i}}),n},Le=function(t){function e(r,a){return function(l,s,u,d){var c=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(r==null&&(a||c))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return e(r(l,s,u,d),a)(l,s,u,d);var p=(a?l:s).options.group.name;return r===!0||typeof r=="string"&&r===p||r.join&&r.indexOf(p)>-1}}var n={},i=t.group;(!i||zt(i)!="object")&&(i={name:i}),n.name=i.name,n.checkPull=e(i.pull,!0),n.checkPut=e(i.put),n.revertClone=i.revertClone,t.group=n},je=function(){!We&&v&&h(v,"display","none")},ze=function(){!We&&v&&h(v,"display","")};ne&&!Re&&document.addEventListener("click",function(o){if(Qt)return o.preventDefault(),o.stopPropagation&&o.stopPropagation(),o.stopImmediatePropagation&&o.stopImmediatePropagation(),Qt=!1,!1},!0);var ct=function(t){if(f){t=t.touches?t.touches[0]:t;var e=gn(t.clientX,t.clientY);if(e){var n={};for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[R]._onDragOver(n)}}},mn=function(t){f&&f.parentNode[R]._isOutsideThisEl(t.target)};function g(o,t){if(!(o&&o.nodeType&&o.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(o));this.el=o,this.options=t=$({},t),o[R]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(o.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Ke(o,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,l){a.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:g.supportPointer!==!1&&"PointerEvent"in window&&(!Pt||be),emptyInsertThreshold:5};Yt.initializePlugins(this,o,e);for(var n in e)!(n in t)&&(t[n]=e[n]);Le(t);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=t.forceFallback?!1:hn,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?y(o,"pointerdown",this._onTapStart):(y(o,"mousedown",this._onTapStart),y(o,"touchstart",this._onTapStart)),this.nativeDraggable&&(y(o,"dragover",this),y(o,"dragenter",this)),Jt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),$(this,un())}g.prototype={constructor:g,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(gt=null)},_getDirection:function(t,e){return typeof this.options.direction=="function"?this.options.direction.call(this,t,e,f):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,i=this.options,r=i.preventOnFilter,a=t.type,l=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,s=(l||t).target,u=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||s,d=i.filter;if(_n(n),!f&&!(/mousedown|pointerdown/.test(a)&&t.button!==0||i.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Pt&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=G(s,i.draggable,n,!1),!(s&&s.animated)&&Ut!==s)){if(bt=x(s),Ft=x(s,i.draggable),typeof d=="function"){if(d.call(this,t,s,this)){Y({sortable:e,rootEl:u,name:"filter",targetEl:s,toEl:n,fromEl:n}),B("filter",e,{evt:t}),r&&t.preventDefault();return}}else if(d&&(d=d.split(",").some(function(c){if(c=G(u,c.trim(),n,!1),c)return Y({sortable:e,rootEl:c,name:"filter",targetEl:s,fromEl:n,toEl:n}),B("filter",e,{evt:t}),!0}),d)){r&&t.preventDefault();return}i.handle&&!G(u,i.handle,n,!1)||this._prepareDragStart(t,l,s)}}},_prepareDragStart:function(t,e,n){var i=this,r=i.el,a=i.options,l=r.ownerDocument,s;if(n&&!f&&n.parentNode===r){var u=O(n);if(T=r,f=n,N=f.parentNode,dt=f.nextSibling,Ut=n,Ht=a.group,g.dragged=f,ft={target:f,clientX:(e||t).clientX,clientY:(e||t).clientY},Ne=ft.clientX-u.left,xe=ft.clientY-u.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,f.style["will-change"]="all",s=function(){if(B("delayEnded",i,{evt:t}),g.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!Ce&&i.nativeDraggable&&(f.draggable=!0),i._triggerDragStart(t,e),Y({sortable:i,name:"choose",originalEvent:t}),I(f,a.chosenClass,!0)},a.ignore.split(",").forEach(function(d){Ye(f,d.trim(),ue)}),y(l,"dragover",ct),y(l,"mousemove",ct),y(l,"touchmove",ct),a.supportPointer?(y(l,"pointerup",i._onDrop),!this.nativeDraggable&&y(l,"pointercancel",i._onDrop)):(y(l,"mouseup",i._onDrop),y(l,"touchend",i._onDrop),y(l,"touchcancel",i._onDrop)),Ce&&this.nativeDraggable&&(this.options.touchStartThreshold=4,f.draggable=!0),B("delayStart",this,{evt:t}),a.delay&&(!a.delayOnTouchOnly||e)&&(!this.nativeDraggable||!(Xt||nt))){if(g.eventCanceled){this._onDrop();return}a.supportPointer?(y(l,"pointerup",i._disableDelayedDrag),y(l,"pointercancel",i._disableDelayedDrag)):(y(l,"mouseup",i._disableDelayedDrag),y(l,"touchend",i._disableDelayedDrag),y(l,"touchcancel",i._disableDelayedDrag)),y(l,"mousemove",i._delayedDragTouchMoveHandler),y(l,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&y(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(s,a.delay)}else s()}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){f&&ue(f),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;b(t,"mouseup",this._disableDelayedDrag),b(t,"touchend",this._disableDelayedDrag),b(t,"touchcancel",this._disableDelayedDrag),b(t,"pointerup",this._disableDelayedDrag),b(t,"pointercancel",this._disableDelayedDrag),b(t,"mousemove",this._delayedDragTouchMoveHandler),b(t,"touchmove",this._delayedDragTouchMoveHandler),b(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||t.pointerType=="touch"&&t,!this.nativeDraggable||e?this.options.supportPointer?y(document,"pointermove",this._onTouchMove):e?y(document,"touchmove",this._onTouchMove):y(document,"mousemove",this._onTouchMove):(y(f,"dragend",this),y(T,"dragstart",this._onDragStart));try{document.selection?qt(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(n){}},_dragStarted:function(t,e){if(vt=!1,T&&f){B("dragStarted",this,{evt:e}),this.nativeDraggable&&y(document,"dragover",mn);var n=this.options;!t&&I(f,n.dragClass,!1),I(f,n.ghostClass,!0),g.active=this,t&&this._appendGhost(),Y({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(q){this._lastX=q.clientX,this._lastY=q.clientY,je();for(var t=document.elementFromPoint(q.clientX,q.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(q.clientX,q.clientY),t!==e);)e=t;if(f.parentNode[R]._isOutsideThisEl(t),e)do{if(e[R]){var n=void 0;if(n=e[R]._onDragOver({clientX:q.clientX,clientY:q.clientY,target:t,rootEl:e}),n&&!this.options.dragoverBubble)break}t=e}while(e=Xe(e));ze()}},_onTouchMove:function(t){if(ft){var e=this.options,n=e.fallbackTolerance,i=e.fallbackOffset,r=t.touches?t.touches[0]:t,a=v&&ht(v,!0),l=v&&a&&a.a,s=v&&a&&a.d,u=Wt&&k&&Ae(k),d=(r.clientX-ft.clientX+i.x)/(l||1)+(u?u[0]-se[0]:0)/(l||1),c=(r.clientY-ft.clientY+i.y)/(s||1)+(u?u[1]-se[1]:0)/(s||1);if(!g.active&&!vt){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(v){a?(a.e+=d-(ae||0),a.f+=c-(le||0)):a={a:1,b:0,c:0,d:1,e:d,f:c};var p="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(v,"webkitTransform",p),h(v,"mozTransform",p),h(v,"msTransform",p),h(v,"transform",p),ae=d,le=c,q=r}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!v){var t=this.options.fallbackOnBody?document.body:T,e=O(f,!0,Wt,!0,t),n=this.options;if(Wt){for(k=t;h(k,"position")==="static"&&h(k,"transform")==="none"&&k!==document;)k=k.parentNode;k!==document.body&&k!==document.documentElement?(k===document&&(k=Z()),e.top+=k.scrollTop,e.left+=k.scrollLeft):k=Z(),se=Ae(k)}v=f.cloneNode(!0),I(v,n.ghostClass,!1),I(v,n.fallbackClass,!0),I(v,n.dragClass,!0),h(v,"transition",""),h(v,"transform",""),h(v,"box-sizing","border-box"),h(v,"margin",0),h(v,"top",e.top),h(v,"left",e.left),h(v,"width",e.width),h(v,"height",e.height),h(v,"opacity","0.8"),h(v,"position",Wt?"absolute":"fixed"),h(v,"zIndex","100000"),h(v,"pointerEvents","none"),g.ghost=v,t.appendChild(v),h(v,"transform-origin",Ne/parseInt(v.style.width)*100+"% "+xe/parseInt(v.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,i=t.dataTransfer,r=n.options;if(B("dragStart",this,{evt:t}),g.eventCanceled){this._onDrop();return}B("setupClone",this),g.eventCanceled||(A=we(f),A.removeAttribute("id"),A.draggable=!1,A.style["will-change"]="",this._hideClone(),I(A,this.options.chosenClass,!1),g.clone=A),n.cloneId=qt(function(){B("clone",n),!g.eventCanceled&&(n.options.removeCloneOnHide||T.insertBefore(A,f),n._hideClone(),Y({sortable:n,name:"clone"}))}),!e&&I(f,r.dragClass,!0),e?(Qt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(b(document,"mouseup",n._onDrop),b(document,"touchend",n._onDrop),b(document,"touchcancel",n._onDrop),i&&(i.effectAllowed="move",r.setData&&r.setData.call(n,i,f)),y(document,"drop",n),h(f,"transform","translateZ(0)")),vt=!0,n._dragStartId=qt(n._dragStarted.bind(n,e,t)),y(document,"selectstart",n),It=!0,window.getSelection().removeAllRanges(),Pt&&h(document.body,"user-select","none")},_onDragOver:function(t){var e=this.el,n=t.target,i,r,a,l=this.options,s=l.group,u=g.active,d=Ht===s,c=l.sort,p=F||u,D,w=this,S=!1;if(pe)return;function X(_t,$e){B(_t,w,Q({evt:t,isOwner:d,axis:D?"vertical":"horizontal",revert:a,dragRect:i,targetRect:r,canSort:c,fromSortable:p,target:n,completed:E,onMove:function(Se,qe){return Kt(T,e,f,i,Se,O(Se),t,qe)},changed:_},$e))}function j(){X("dragOverAnimationCapture"),w.captureAnimationState(),w!==p&&p.captureAnimationState()}function E(_t){return X("dragOverCompleted",{insertion:_t}),_t&&(d?u._hideClone():u._showClone(w),w!==p&&(I(f,F?F.options.ghostClass:u.options.ghostClass,!1),I(f,l.ghostClass,!0)),F!==w&&w!==g.active?F=w:w===g.active&&F&&(F=null),p===w&&(w._ignoreWhileAnimating=n),w.animateAll(function(){X("dragOverAnimationComplete"),w._ignoreWhileAnimating=null}),w!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(n===f&&!f.animated||n===e&&!n.animated)&&(gt=null),!l.dragoverBubble&&!t.rootEl&&n!==document&&(f.parentNode[R]._isOutsideThisEl(t.target),!_t&&ct(t)),!l.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),S=!0}function _(){L=x(f),rt=x(f,l.draggable),Y({sortable:w,name:"change",toEl:e,newIndex:L,newDraggableIndex:rt,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),n=G(n,l.draggable,e,!0),X("dragOver"),g.eventCanceled)return S;if(f.contains(t.target)||n.animated&&n.animatingX&&n.animatingY||w._ignoreWhileAnimating===n)return E(!1);if(Qt=!1,u&&!l.disabled&&(d?c||(a=N!==T):F===this||(this.lastPutMode=Ht.checkPull(this,u,f,t))&&s.checkPut(this,u,f,t))){if(D=this._getDirection(t,n)==="vertical",i=O(f),X("dragOverValid"),g.eventCanceled)return S;if(a)return N=T,j(),this._hideClone(),X("revert"),g.eventCanceled||(dt?T.insertBefore(f,dt):T.appendChild(f)),E(!0);var M=ye(e,l.draggable);if(!M||wn(t,D,this)&&!M.animated){if(M===f)return E(!1);if(M&&e===t.target&&(n=M),n&&(r=O(n)),Kt(T,e,f,i,n,r,t,!!n)!==!1)return j(),M&&M.nextSibling?e.insertBefore(f,M.nextSibling):e.appendChild(f),N=e,_(),E(!0)}else if(M&&yn(t,D,this)){var J=yt(e,0,l,!0);if(J===f)return E(!1);if(n=J,r=O(n),Kt(T,e,f,i,n,r,t,!1)!==!1)return j(),e.insertBefore(f,J),N=e,_(),E(!0)}else if(n.parentNode===e){r=O(n);var z=0,st,wt=f.parentNode!==e,W=!pn(f.animated&&f.toRect||i,n.animated&&n.toRect||r,D),Et=D?"top":"left",ot=Te(n,"top","top")||Te(f,"top","top"),Dt=ot?ot.scrollTop:void 0;gt!==n&&(st=r[Et],kt=!1,Gt=!W&&l.invertSwap||wt),z=En(t,n,r,D,W?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,Gt,gt===n);var tt;if(z!==0){var ut=x(f);do ut-=z,tt=N.children[ut];while(tt&&(h(tt,"display")==="none"||tt===v))}if(z===0||tt===n)return E(!1);gt=n,Rt=z;var St=n.nextElementSibling,it=!1;it=z===1;var Bt=Kt(T,e,f,i,n,r,t,it);if(Bt!==!1)return(Bt===1||Bt===-1)&&(it=Bt===1),pe=!0,setTimeout(bn,30),j(),it&&!St?e.appendChild(f):n.parentNode.insertBefore(f,it?St:n),ot&&He(ot,0,Dt-ot.scrollTop),N=f.parentNode,st!==void 0&&!Gt&&($t=Math.abs(st-O(n)[Et])),_(),E(!0)}if(e.contains(f))return E(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){b(document,"mousemove",this._onTouchMove),b(document,"touchmove",this._onTouchMove),b(document,"pointermove",this._onTouchMove),b(document,"dragover",ct),b(document,"mousemove",ct),b(document,"touchmove",ct)},_offUpEvents:function(){var t=this.el.ownerDocument;b(t,"mouseup",this._onDrop),b(t,"touchend",this._onDrop),b(t,"pointerup",this._onDrop),b(t,"pointercancel",this._onDrop),b(t,"touchcancel",this._onDrop),b(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;if(L=x(f),rt=x(f,n.draggable),B("drop",this,{evt:t}),N=f&&f.parentNode,L=x(f),rt=x(f,n.draggable),g.eventCanceled){this._nulling();return}vt=!1,Gt=!1,kt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ge(this.cloneId),ge(this._dragStartId),this.nativeDraggable&&(b(document,"drop",this),b(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Pt&&h(document.body,"user-select",""),h(f,"transform",""),t&&(It&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),v&&v.parentNode&&v.parentNode.removeChild(v),(T===N||F&&F.lastPutMode!=="clone")&&A&&A.parentNode&&A.parentNode.removeChild(A),f&&(this.nativeDraggable&&b(f,"dragend",this),ue(f),f.style["will-change"]="",It&&!vt&&I(f,F?F.options.ghostClass:this.options.ghostClass,!1),I(f,this.options.chosenClass,!1),Y({sortable:this,name:"unchoose",toEl:N,newIndex:null,newDraggableIndex:null,originalEvent:t}),T!==N?(L>=0&&(Y({rootEl:N,name:"add",toEl:N,fromEl:T,originalEvent:t}),Y({sortable:this,name:"remove",toEl:N,originalEvent:t}),Y({rootEl:N,name:"sort",toEl:N,fromEl:T,originalEvent:t}),Y({sortable:this,name:"sort",toEl:N,originalEvent:t})),F&&F.save()):L!==bt&&L>=0&&(Y({sortable:this,name:"update",toEl:N,originalEvent:t}),Y({sortable:this,name:"sort",toEl:N,originalEvent:t})),g.active&&((L==null||L===-1)&&(L=bt,rt=Ft),Y({sortable:this,name:"end",toEl:N,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){B("nulling",this),T=f=N=v=dt=A=Ut=at=ft=q=It=L=rt=bt=Ft=gt=Rt=F=Ht=g.dragged=g.ghost=g.clone=g.active=null,te.forEach(function(t){t.checked=!0}),te.length=ae=le=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":f&&(this._onDragOver(t),vn(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],e,n=this.el.children,i=0,r=n.length,a=this.options;i<r;i++)e=n[i],G(e,a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||Sn(e));return t},sort:function(t,e){var n={},i=this.el;this.toArray().forEach(function(r,a){var l=i.children[a];G(l,this.options.draggable,i,!1)&&(n[r]=l)},this),e&&this.captureAnimationState(),t.forEach(function(r){n[r]&&(i.removeChild(n[r]),i.appendChild(n[r]))}),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return G(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(e===void 0)return n[t];var i=Yt.modifyOption(this,t,e);typeof i!="undefined"?n[t]=i:n[t]=e,t==="group"&&Le(n)},destroy:function(){B("destroy",this);var t=this.el;t[R]=null,b(t,"mousedown",this._onTapStart),b(t,"touchstart",this._onTapStart),b(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(b(t,"dragover",this),b(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(e){e.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Jt.splice(Jt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!at){if(B("hideClone",this),g.eventCanceled)return;h(A,"display","none"),this.options.removeCloneOnHide&&A.parentNode&&A.parentNode.removeChild(A),at=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(at){if(B("showClone",this),g.eventCanceled)return;f.parentNode==T&&!this.options.group.revertClone?T.insertBefore(A,f):dt?T.insertBefore(A,dt):T.appendChild(A),this.options.group.revertClone&&this.animate(f,A),h(A,"display",""),at=!1}}};function vn(o){o.dataTransfer&&(o.dataTransfer.dropEffect="move"),o.cancelable&&o.preventDefault()}function Kt(o,t,e,n,i,r,a,l){var s,u=o[R],d=u.options.onMove,c;return window.CustomEvent&&!nt&&!Xt?s=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(s=document.createEvent("Event"),s.initEvent("move",!0,!0)),s.to=t,s.from=o,s.dragged=e,s.draggedRect=n,s.related=i||t,s.relatedRect=r||O(t),s.willInsertAfter=l,s.originalEvent=a,o.dispatchEvent(s),d&&(c=d.call(u,s,a)),c}function ue(o){o.draggable=!1}function bn(){pe=!1}function yn(o,t,e){var n=O(yt(e.el,0,e.options,!0)),i=Ge(e.el,e.options,v),r=10;return t?o.clientX<i.left-r||o.clientY<n.top&&o.clientX<n.right:o.clientY<i.top-r||o.clientY<n.bottom&&o.clientX<n.left}function wn(o,t,e){var n=O(ye(e.el,e.options.draggable)),i=Ge(e.el,e.options,v),r=10;return t?o.clientX>i.right+r||o.clientY>n.bottom&&o.clientX>n.left:o.clientY>i.bottom+r||o.clientX>n.right&&o.clientY>n.top}function En(o,t,e,n,i,r,a,l){var s=n?o.clientY:o.clientX,u=n?e.height:e.width,d=n?e.top:e.left,c=n?e.bottom:e.right,p=!1;if(!a){if(l&&$t<u*i){if(!kt&&(Rt===1?s>d+u*r/2:s<c-u*r/2)&&(kt=!0),kt)p=!0;else if(Rt===1?s<d+$t:s>c-$t)return-Rt}else if(s>d+u*(1-i)/2&&s<c-u*(1-i)/2)return Dn(t)}return p=p||a,p&&(s<d+u*r/2||s>c-u*r/2)?s>d+u/2?1:-1:0}function Dn(o){return x(f)<x(o)?1:-1}function Sn(o){for(var t=o.tagName+o.className+o.src+o.href+o.textContent,e=t.length,n=0;e--;)n+=t.charCodeAt(e);return n.toString(36)}function _n(o){te.length=0;for(var t=o.getElementsByTagName("input"),e=t.length;e--;){var n=t[e];n.checked&&te.push(n)}}function qt(o){return setTimeout(o,0)}function ge(o){return clearTimeout(o)}ne&&y(document,"touchmove",function(o){(g.active||vt)&&o.cancelable&&o.preventDefault()});g.utils={on:y,off:b,css:h,find:Ye,is:function(t,e){return!!G(t,e,t,!1)},extend:ln,throttle:Be,closest:G,toggleClass:I,clone:we,index:x,nextTick:qt,cancelNextTick:ge,detectDirection:Ke,getChild:yt,expando:R};g.get=function(o){return o[R]};g.mount=function(){for(var o=arguments.length,t=new Array(o),e=0;e<o;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(g.utils=Q(Q({},g.utils),n.utils)),Yt.mount(n)})};g.create=function(o,t){return new g(o,t)};g.version=rn;var P=[],Nt,me,ve=!1,fe,ce,ee,xt;function Cn(){function o(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return o.prototype={dragStarted:function(e){var n=e.originalEvent;this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):this.options.supportPointer?y(document,"pointermove",this._handleFallbackAutoScroll):n.touches?y(document,"touchmove",this._handleFallbackAutoScroll):y(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var n=e.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?b(document,"dragover",this._handleAutoScroll):(b(document,"pointermove",this._handleFallbackAutoScroll),b(document,"touchmove",this._handleFallbackAutoScroll),b(document,"mousemove",this._handleFallbackAutoScroll)),Me(),Vt(),sn()},nulling:function(){ee=me=Nt=ve=xt=fe=ce=null,P.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,n){var i=this,r=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,l=document.elementFromPoint(r,a);if(ee=e,n||this.options.forceAutoScrollFallback||Xt||nt||Pt){de(e,this.options,l,n);var s=lt(l,!0);ve&&(!xt||r!==fe||a!==ce)&&(xt&&Me(),xt=setInterval(function(){var u=lt(document.elementFromPoint(r,a),!0);u!==s&&(s=u,Vt()),de(e,i.options,u,n)},10),fe=r,ce=a)}else{if(!this.options.bubbleScroll||lt(l,!0)===Z()){Vt();return}de(e,this.options,lt(l,!1),!1)}}},$(o,{pluginName:"scroll",initializeByDefault:!0})}function Vt(){P.forEach(function(o){clearInterval(o.pid)}),P=[]}function Me(){clearInterval(xt)}var de=Be(function(o,t,e,n){if(t.scroll){var i=(o.touches?o.touches[0]:o).clientX,r=(o.touches?o.touches[0]:o).clientY,a=t.scrollSensitivity,l=t.scrollSpeed,s=Z(),u=!1,d;me!==e&&(me=e,Vt(),Nt=t.scroll,d=t.scrollFn,Nt===!0&&(Nt=lt(e,!0)));var c=0,p=Nt;do{var D=p,w=O(D),S=w.top,X=w.bottom,j=w.left,E=w.right,_=w.width,M=w.height,J=void 0,z=void 0,st=D.scrollWidth,wt=D.scrollHeight,W=h(D),Et=D.scrollLeft,ot=D.scrollTop;D===s?(J=_<st&&(W.overflowX==="auto"||W.overflowX==="scroll"||W.overflowX==="visible"),z=M<wt&&(W.overflowY==="auto"||W.overflowY==="scroll"||W.overflowY==="visible")):(J=_<st&&(W.overflowX==="auto"||W.overflowX==="scroll"),z=M<wt&&(W.overflowY==="auto"||W.overflowY==="scroll"));var Dt=J&&(Math.abs(E-i)<=a&&Et+_<st)-(Math.abs(j-i)<=a&&!!Et),tt=z&&(Math.abs(X-r)<=a&&ot+M<wt)-(Math.abs(S-r)<=a&&!!ot);if(!P[c])for(var ut=0;ut<=c;ut++)P[ut]||(P[ut]={});(P[c].vx!=Dt||P[c].vy!=tt||P[c].el!==D)&&(P[c].el=D,P[c].vx=Dt,P[c].vy=tt,clearInterval(P[c].pid),(Dt!=0||tt!=0)&&(u=!0,P[c].pid=setInterval(function(){n&&this.layer===0&&g.active._onTouchMove(ee);var St=P[this.layer].vy?P[this.layer].vy*l:0,it=P[this.layer].vx?P[this.layer].vx*l:0;typeof d=="function"&&d.call(g.dragged.parentNode[R],it,St,o,ee,P[this.layer].el)!=="continue"||He(P[this.layer].el,it,St)}.bind({layer:c}),24))),c++}while(t.bubbleScroll&&p!==s&&(p=lt(p,!1)));ve=u}},30),Ue=function(t){var e=t.originalEvent,n=t.putSortable,i=t.dragEl,r=t.activeSortable,a=t.dispatchSortableEvent,l=t.hideGhostForTarget,s=t.unhideGhostForTarget;if(e){var u=n||r;l();var d=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,c=document.elementFromPoint(d.clientX,d.clientY);s(),u&&!u.el.contains(c)&&(a("spill"),this.onSpill({dragEl:i,putSortable:n}))}};function Ee(){}Ee.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var i=yt(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(e,i):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:Ue};$(Ee,{pluginName:"revertOnSpill"});function De(){}De.prototype={onSpill:function(t){var e=t.dragEl,n=t.putSortable,i=n||this.sortable;i.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),i.animateAll()},drop:Ue};$(De,{pluginName:"removeOnSpill"});var U;function On(){function o(){this.defaults={swapClass:"sortable-swap-highlight"}}return o.prototype={dragStart:function(e){var n=e.dragEl;U=n},dragOverValid:function(e){var n=e.completed,i=e.target,r=e.onMove,a=e.activeSortable,l=e.changed,s=e.cancel;if(a.options.swap){var u=this.sortable.el,d=this.options;if(i&&i!==u){var c=U;r(i)!==!1?(I(i,d.swapClass,!0),U=i):U=null,c&&c!==U&&I(c,d.swapClass,!1)}l(),n(!0),s()}},drop:function(e){var n=e.activeSortable,i=e.putSortable,r=e.dragEl,a=i||this.sortable,l=this.options;U&&I(U,l.swapClass,!1),U&&(l.swap||i&&i.options.swap)&&r!==U&&(a.captureAnimationState(),a!==n&&n.captureAnimationState(),Tn(r,U),a.animateAll(),a!==n&&n.animateAll())},nulling:function(){U=null}},$(o,{pluginName:"swap",eventProperties:function(){return{swapItem:U}}})}function Tn(o,t){var e=o.parentNode,n=t.parentNode,i,r;!e||!n||e.isEqualNode(t)||n.isEqualNode(o)||(i=x(o),r=x(t),e.isEqualNode(n)&&i<r&&r++,e.insertBefore(t,e.children[i]),n.insertBefore(o,n.children[r]))}var m=[],K=[],Ct,V,Ot=!1,H=!1,mt=!1,C,Tt,Lt;function An(){function o(t){for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this));t.options.avoidImplicitDeselect||(t.options.supportPointer?y(document,"pointerup",this._deselectMultiDrag):(y(document,"mouseup",this._deselectMultiDrag),y(document,"touchend",this._deselectMultiDrag))),y(document,"keydown",this._checkKeyDown),y(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,avoidImplicitDeselect:!1,setData:function(i,r){var a="";m.length&&V===t?m.forEach(function(l,s){a+=(s?", ":"")+l.textContent}):a=r.textContent,i.setData("Text",a)}}}return o.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(e){var n=e.dragEl;C=n},delayEnded:function(){this.isMultiDrag=~m.indexOf(C)},setupClone:function(e){var n=e.sortable,i=e.cancel;if(this.isMultiDrag){for(var r=0;r<m.length;r++)K.push(we(m[r])),K[r].sortableIndex=m[r].sortableIndex,K[r].draggable=!1,K[r].style["will-change"]="",I(K[r],this.options.selectedClass,!1),m[r]===C&&I(K[r],this.options.chosenClass,!1);n._hideClone(),i()}},clone:function(e){var n=e.sortable,i=e.rootEl,r=e.dispatchSortableEvent,a=e.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||m.length&&V===n&&(Fe(!0,i),r("clone"),a()))},showClone:function(e){var n=e.cloneNowShown,i=e.rootEl,r=e.cancel;this.isMultiDrag&&(Fe(!1,i),K.forEach(function(a){h(a,"display","")}),n(),Lt=!1,r())},hideClone:function(e){var n=this;e.sortable;var i=e.cloneNowHidden,r=e.cancel;this.isMultiDrag&&(K.forEach(function(a){h(a,"display","none"),n.options.removeCloneOnHide&&a.parentNode&&a.parentNode.removeChild(a)}),i(),Lt=!0,r())},dragStartGlobal:function(e){e.sortable,!this.isMultiDrag&&V&&V.multiDrag._deselectMultiDrag(),m.forEach(function(n){n.sortableIndex=x(n)}),m=m.sort(function(n,i){return n.sortableIndex-i.sortableIndex}),mt=!0},dragStarted:function(e){var n=this,i=e.sortable;if(this.isMultiDrag){if(this.options.sort&&(i.captureAnimationState(),this.options.animation)){m.forEach(function(a){a!==C&&h(a,"position","absolute")});var r=O(C,!1,!0,!0);m.forEach(function(a){a!==C&&Ie(a,r)}),H=!0,Ot=!0}i.animateAll(function(){H=!1,Ot=!1,n.options.animation&&m.forEach(function(a){ie(a)}),n.options.sort&&jt()})}},dragOver:function(e){var n=e.target,i=e.completed,r=e.cancel;H&&~m.indexOf(n)&&(i(!1),r())},revert:function(e){var n=e.fromSortable,i=e.rootEl,r=e.sortable,a=e.dragRect;m.length>1&&(m.forEach(function(l){r.addAnimationState({target:l,rect:H?O(l):a}),ie(l),l.fromRect=a,n.removeAnimationState(l)}),H=!1,In(!this.options.removeCloneOnHide,i))},dragOverCompleted:function(e){var n=e.sortable,i=e.isOwner,r=e.insertion,a=e.activeSortable,l=e.parentEl,s=e.putSortable,u=this.options;if(r){if(i&&a._hideClone(),Ot=!1,u.animation&&m.length>1&&(H||!i&&!a.options.sort&&!s)){var d=O(C,!1,!0,!0);m.forEach(function(p){p!==C&&(Ie(p,d),l.appendChild(p))}),H=!0}if(!i)if(H||jt(),m.length>1){var c=Lt;a._showClone(n),a.options.animation&&!Lt&&c&&K.forEach(function(p){a.addAnimationState({target:p,rect:Tt}),p.fromRect=Tt,p.thisAnimationDuration=null})}else a._showClone(n)}},dragOverAnimationCapture:function(e){var n=e.dragRect,i=e.isOwner,r=e.activeSortable;if(m.forEach(function(l){l.thisAnimationDuration=null}),r.options.animation&&!i&&r.multiDrag.isMultiDrag){Tt=$({},n);var a=ht(C,!0);Tt.top-=a.f,Tt.left-=a.e}},dragOverAnimationComplete:function(){H&&(H=!1,jt())},drop:function(e){var n=e.originalEvent,i=e.rootEl,r=e.parentEl,a=e.sortable,l=e.dispatchSortableEvent,s=e.oldIndex,u=e.putSortable,d=u||this.sortable;if(n){var c=this.options,p=r.children;if(!mt)if(c.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),I(C,c.selectedClass,!~m.indexOf(C)),~m.indexOf(C))m.splice(m.indexOf(C),1),Ct=null,At({sortable:a,rootEl:i,name:"deselect",targetEl:C,originalEvent:n});else{if(m.push(C),At({sortable:a,rootEl:i,name:"select",targetEl:C,originalEvent:n}),n.shiftKey&&Ct&&a.el.contains(Ct)){var D=x(Ct),w=x(C);~D&&~w&&D!==w&&function(){var E,_;w>D?(_=D,E=w):(_=w,E=D+1);for(var M=c.filter;_<E;_++)if(!~m.indexOf(p[_])&&G(p[_],c.draggable,r,!1)){var J=M&&(typeof M=="function"?M.call(a,n,p[_],a):M.split(",").some(function(z){return G(p[_],z.trim(),r,!1)}));J||(I(p[_],c.selectedClass,!0),m.push(p[_]),At({sortable:a,rootEl:i,name:"select",targetEl:p[_],originalEvent:n}))}}()}else Ct=C;V=d}if(mt&&this.isMultiDrag){if(H=!1,(r[R].options.sort||r!==i)&&m.length>1){var S=O(C),X=x(C,":not(."+this.options.selectedClass+")");if(!Ot&&c.animation&&(C.thisAnimationDuration=null),d.captureAnimationState(),!Ot&&(c.animation&&(C.fromRect=S,m.forEach(function(E){if(E.thisAnimationDuration=null,E!==C){var _=H?O(E):S;E.fromRect=_,d.addAnimationState({target:E,rect:_})}})),jt(),m.forEach(function(E){p[X]?r.insertBefore(E,p[X]):r.appendChild(E),X++}),s===x(C))){var j=!1;m.forEach(function(E){if(E.sortableIndex!==x(E)){j=!0;return}}),j&&(l("update"),l("sort"))}m.forEach(function(E){ie(E)}),d.animateAll()}V=d}(i===r||u&&u.lastPutMode!=="clone")&&K.forEach(function(E){E.parentNode&&E.parentNode.removeChild(E)})}},nullingGlobal:function(){this.isMultiDrag=mt=!1,K.length=0},destroyGlobal:function(){this._deselectMultiDrag(),b(document,"pointerup",this._deselectMultiDrag),b(document,"mouseup",this._deselectMultiDrag),b(document,"touchend",this._deselectMultiDrag),b(document,"keydown",this._checkKeyDown),b(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(e){if(!(typeof mt!="undefined"&&mt)&&V===this.sortable&&!(e&&G(e.target,this.options.draggable,this.sortable.el,!1))&&!(e&&e.button!==0))for(;m.length;){var n=m[0];I(n,this.options.selectedClass,!1),m.shift(),At({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:n,originalEvent:e})}},_checkKeyDown:function(e){e.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(e){e.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},$(o,{pluginName:"multiDrag",utils:{select:function(e){var n=e.parentNode[R];!n||!n.options.multiDrag||~m.indexOf(e)||(V&&V!==n&&(V.multiDrag._deselectMultiDrag(),V=n),I(e,n.options.selectedClass,!0),m.push(e))},deselect:function(e){var n=e.parentNode[R],i=m.indexOf(e);!n||!n.options.multiDrag||!~i||(I(e,n.options.selectedClass,!1),m.splice(i,1))}},eventProperties:function(){var e=this,n=[],i=[];return m.forEach(function(r){n.push({multiDragElement:r,index:r.sortableIndex});var a;H&&r!==C?a=-1:H?a=x(r,":not(."+e.options.selectedClass+")"):a=x(r),i.push({multiDragElement:r,index:a})}),{items:Je(m),clones:[].concat(K),oldIndicies:n,newIndicies:i}},optionListeners:{multiDragKey:function(e){return e=e.toLowerCase(),e==="ctrl"?e="Control":e.length>1&&(e=e.charAt(0).toUpperCase()+e.substr(1)),e}}})}function In(o,t){m.forEach(function(e,n){var i=t.children[e.sortableIndex+(o?Number(n):0)];i?t.insertBefore(e,i):t.appendChild(e)})}function Fe(o,t){K.forEach(function(e,n){var i=t.children[e.sortableIndex+(o?Number(n):0)];i?t.insertBefore(e,i):t.appendChild(e)})}function jt(){m.forEach(function(o){o!==C&&o.parentNode&&o.parentNode.removeChild(o)})}g.mount(new Cn);g.mount(De,Ee);g.mount(new On);g.mount(new An);export{g as default};
