import type {
  ComponentRecordType,
  GenerateMenuAndRoutesOptions,
  RouteRecordStringComponent,
} from '@vben/types';

import { generateAccessible } from '@vben/access';
import { preferences } from '@vben/preferences';

import { message } from 'ant-design-vue';

import { getAllMenusApi } from '#/api';
import { BasicLayout, IFrameView } from '#/layouts';
import { $t } from '#/locales';

const forbiddenComponent = () => import('#/views/_core/fallback/forbidden.vue');

async function generateAccess(options: GenerateMenuAndRoutesOptions) {
  const pageMap: ComponentRecordType = import.meta.glob('../views/**/*.vue');

  const layoutMap: ComponentRecordType = {
    BasicLayout,
    IFrameView,
  };

  const result = await generateAccessible(preferences.app.accessMode, {
    ...options,
    fetchMenuListAsync: async () => {
      message.loading({
        content: `${$t('common.loadingMenu')}...`,
        duration: 1.5,
      });
      return await getAllMenusApi();
    },
    // 可以指定没有权限跳转403页面
    forbiddenComponent,
    // 如果 route.meta.menuVisibleWithForbidden = true
    layoutMap,
    pageMap,
  });

  // 手动添加 /dashboard/analysis 路由
  const analysisRoute: RouteRecordStringComponent = {
    path: '/dashboard',
    name: 'Dashboard',
    component: 'BasicLayout', // 使用 BasicLayout 作为父组件
    meta: {
      title: $t('page.dashboard.title'),
      icon: 'lucide:layout-dashboard',
      order: -1,
    },
    children: [
      {
        path: '/dashboard/analysis',
        name: 'Analysis',
        component: 'dashboard/analytics/index', // 对应 views/dashboard/analytics/index.vue
        meta: {
          title: $t('page.dashboard.analysis'),
          affixTab: true,
          icon: 'lucide:area-chart',
        },
      },
    ],
  };

  // 检查是否已经存在 dashboard 路由，如果存在则合并，否则直接添加
  const existingDashboardIndex = result.accessibleRoutes.findIndex(
    (route) => route.name === 'Dashboard',
  );

  if (existingDashboardIndex !== -1) {
    // 如果已经存在 Dashboard 路由，则将 analysisRoute 的 children 合并到现有路由的 children 中
    const existingDashboardRoute = result.accessibleRoutes[existingDashboardIndex];
    if (existingDashboardRoute.children) {
      const existingAnalysisIndex = existingDashboardRoute.children.findIndex(
        (child) => child.name === 'Analysis',
      );
      if (existingAnalysisIndex === -1) {
        existingDashboardRoute.children.push(analysisRoute.children[0]);
      }
    } else {
      existingDashboardRoute.children = analysisRoute.children;
    }
  } else {
    // 如果不存在 Dashboard 路由，则直接添加 analysisRoute
    result.accessibleRoutes.push(analysisRoute);
  }

  return result;
}

export { generateAccess };
