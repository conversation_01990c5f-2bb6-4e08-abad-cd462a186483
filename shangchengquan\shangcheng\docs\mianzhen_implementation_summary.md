# 面诊和综合诊疗功能实现总结

## 已完成的工作

### 1. 数据库扩展 ✅
- **文件**: `shangchengquan/shangcheng/docs/mianzhen_upgrade.sql`
- **内容**:
  - 扩展 `ddwx_shezhen_record` 表，增加面部图片、舌下脉络图片字段
  - 增加 `diagnosis_type` 字段区分诊疗类型
  - 扩展配置表支持面诊和综合诊疗设置
  - 创建面诊特征配置表
  - 保持向后兼容

### 2. 后端API扩展 ✅
- **文件**:
  - `shangchengquan/shangcheng/app/common/SheZhen.php`
  - `shangchengquan/shangcheng/app/controller/ApiSheZhen.php`
  - `shangchengquan/shangcheng/app/controller/SheZhen.php`

- **主要功能**:
  - 扩展 `callDetectApi` 方法支持多图片和诊疗类型参数
  - 新增图片验证方法 `validateImagesByDiagnosisType`
  - 扩展配置获取方法 `getDiagnosisConfig`
  - 新增诊疗类型名称获取方法 `getDiagnosisTypeName`
  - 修改 `getConfig` 接口支持诊疗类型参数
  - 修改 `analyze` 接口支持多图片上传
  - 修改 `getRecord` 接口支持面诊和综合诊疗图片字段
  - 新增面诊和综合诊疗记录管理方法

### 3. 前端页面开发 ✅
- **面诊页面**: `tiantianshande/pagesB/diagnosis/face/index.vue`
  - 支持面部图片拍摄和上传
  - 实时价格显示和免费次数查询
  - 现代化UI设计
  - 使用原有系统的接口调用方式（app.post）

- **面诊结果页面**: `tiantianshande/pagesB/diagnosis/face/result.vue`
  - 完整的面诊分析结果展示
  - 面部特征分析
  - 健康状况评估
  - 调理建议
  - 商品推荐

- **综合诊疗页面**: `tiantianshande/pagesB/diagnosis/comprehensive/index.vue`
  - 支持舌头、面部、舌下脉络三种图片上传
  - 智能拍照指导
  - 必选和可选图片区分
  - 使用原有系统的接口调用方式

- **综合诊疗结果页面**: `tiantianshande/pagesB/diagnosis/comprehensive/result.vue`
  - 多图片分析结果展示
  - 综合健康评分
  - 单项分析详情
  - 综合分析报告
  - 调理建议

### 4. 菜单权限配置 ✅
- **文件**: `shangchengquan/shangcheng/app/common/Menu.php`
- **变更**:
  - 将"舌诊模块"重命名为"智能诊疗"
  - 增加面诊记录和综合诊疗记录菜单项
  - 更新权限控制

### 5. 路由配置 ✅
- **文件**: `tiantianshande/pages.json`
- **新增路由**:
  - `/pagesB/diagnosis/face/index` - 面诊分析
  - `/pagesB/diagnosis/face/result` - 面诊结果
  - `/pagesB/diagnosis/comprehensive/index` - 综合诊疗
  - `/pagesB/diagnosis/comprehensive/result` - 综合诊疗结果

## 技术特点

### 1. 向后兼容
- 现有舌诊功能完全保持不变
- 现有数据结构和API接口保持兼容
- 用户无需重新配置即可使用原有功能

### 2. 接口调用一致性
- 前端页面使用与原有系统一致的接口调用方式
- 使用 `app.post()` 和 `app.uploadFile()` 方法
- 保持错误处理和加载提示的一致性

### 3. 多图片支持
- 充分利用阿里云API的多图片分析能力
- 支持舌照、面照、舌下脉络三种图片类型
- 根据诊疗类型智能验证必需图片

### 4. 现代化UI
- 响应式设计，适配不同屏幕尺寸
- 渐变色彩和圆角设计
- 智能拍照指导和实时反馈
- 加载动画和状态提示

## 使用说明

### 1. 数据库升级
```sql
-- 执行数据库升级脚本
source shangchengquan/shangcheng/docs/mianzhen_upgrade.sql;
```

### 2. 后台配置
1. 登录后台管理系统
2. 进入"智能诊疗" -> "诊疗设置"
3. 在"基础配置"标签页中：
   - **开启面诊功能**：将"面诊功能"设置为"开启"
   - **开启综合诊疗功能**：将"综合诊疗功能"设置为"开启"
   - **设置价格**：配置面诊价格（建议12.9元）和综合诊疗价格（建议19.9元）
   - **配置免费等级**：选择可以免费使用各功能的会员等级
   - **设置免费次数**：配置每日免费使用次数

### 3. 解决"面诊功能已关闭"错误
如果前端提示"面诊功能已关闭"，请按以下步骤检查：

1. **检查数据库字段**：确保已执行数据库升级脚本
2. **检查后台设置**：
   - 登录后台 -> 智能诊疗 -> 诊疗设置
   - 确认"面诊功能"开关为"开启"状态
   - 确认"综合诊疗功能"开关为"开启"状态
3. **手动数据库修改**（如果后台界面异常）：
   ```sql
   UPDATE ddwx_shezhen_set SET
       face_diagnosis_enable = 1,
       comprehensive_diagnosis_enable = 1
   WHERE aid = 您的应用ID;
   ```

### 3. 前端访问
- 面诊页面：`/pagesB/diagnosis/face/index`
- 综合诊疗页面：`/pagesB/diagnosis/comprehensive/index`

### 4. API接口
- 获取配置：`ApiSheZhen/getConfig` (支持 diagnosis_type 参数)
- 开始分析：`ApiSheZhen/analyze` (支持多图片参数)
- 获取记录：`ApiSheZhen/getRecord` (支持面诊和综合诊疗字段)

## 诊疗类型说明

- **1 = 舌诊**：通过舌头图片分析体质状况（原有功能）
- **2 = 面诊**：通过面部图片分析健康状况（新增功能）
- **3 = 综合诊疗**：结合多种图片进行综合分析（新增功能）

## 文件结构

```
shangchengquan/shangcheng/
├── docs/
│   ├── mianzhen_upgrade.sql              # 数据库升级脚本
│   └── mianzhen_implementation_guide.md  # 实现指南
├── app/
│   ├── common/
│   │   ├── SheZhen.php                   # 扩展公共类
│   │   └── Menu.php                      # 菜单配置
│   └── controller/
│       ├── ApiSheZhen.php                # 前端API接口
│       └── SheZhen.php                   # 后台管理接口

tiantianshande/
├── pagesB/
│   └── diagnosis/
│       ├── face/
│       │   ├── index.vue                 # 面诊页面
│       │   └── result.vue                # 面诊结果页面
│       └── comprehensive/
│           ├── index.vue                 # 综合诊疗页面
│           └── result.vue                # 综合诊疗结果页面
└── pages.json                            # 路由配置
```

## 下一步建议

1. **功能测试**：全面测试新增功能，确保与现有系统兼容
2. **性能优化**：图片压缩和CDN加速
3. **用户体验**：增加拍照指导动画和语音提示
4. **数据分析**：增加诊疗数据统计和趋势分析
5. **商品推荐**：完善基于诊疗结果的商品推荐算法

## 常见问题排查

### 1. "面诊功能已关闭"错误
**原因**：后台未开启面诊功能
**解决方案**：
1. 登录后台 -> 智能诊疗 -> 诊疗设置
2. 将"面诊功能"设置为"开启"
3. 保存设置

### 2. 后台设置页面没有面诊开关
**原因**：数据库字段未添加或页面未更新
**解决方案**：
1. 执行数据库升级脚本：`source mianzhen_upgrade.sql`
2. 清除后台缓存
3. 检查 `app/home/<USER>/index.html` 是否已更新

### 3. 前端页面无法访问
**原因**：路由未配置或页面文件缺失
**解决方案**：
1. 检查 `pages.json` 中是否包含新路由
2. 确认页面文件存在于 `pagesB/diagnosis/` 目录

### 4. 前端图片上传错误
**错误信息**：`app.uploadFile is not a function` 或 `pre_url is not defined`
**原因**：使用了错误的图片上传方法或缺少全局变量
**解决方案**：
1. 使用正确的 `app.chooseImage` 方法进行图片选择和上传
2. 在 `onLoad()` 中设置 `this.pre_url = app.globalData.pre_url || '';`
3. 参考 `pagesB/shezhen/camera.vue` 的正确实现方式
4. 使用双重检查：优先使用 `app.chooseImage`，失败时回退到 `uni.chooseImage`

### 5. 接口调用失败
**原因**：后端API未更新或参数错误
**解决方案**：
1. 检查 `ApiSheZhen.php` 是否已更新
2. 确认传递的 `diagnosis_type` 参数正确（2=面诊，3=综合诊疗）

## 技术支持

如有问题，请参考：
1. 数据库结构：`mianzhen_upgrade.sql`
2. API文档：各控制器文件注释
3. 前端组件：各页面文件注释
4. 实现总结：本文档