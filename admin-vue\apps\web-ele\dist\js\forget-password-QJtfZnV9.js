var T=(c,l,a)=>new Promise((i,s)=>{var f=e=>{try{u(a.next(e))}catch(m){s(m)}},r=e=>{try{u(a.throw(e))}catch(m){s(m)}},u=e=>e.done?i(e.value):Promise.resolve(e.value).then(f,r);u((a=a.apply(c,l)).next())});import{J as B,f as $,$ as n,g as v,K as P}from"./bootstrap-CYivmKoJ.js";import{T as V}from"./auth-title-Cs6tZ2yh.js";import{d as _,L as C,e as S,f as F,g as k,E as p,j as x,D as g,l as w,G as h,t as b,u as o,n as y,r as L,q as N}from"../jse/index-index-SSqEGcIT.js";const A=_({name:"ForgetPassword",__name:"forget-password",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(c,{expose:l,emit:a}){const i=c,s=a,[f,r]=B(C({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:S(()=>i.formSchema),showDefaultActions:!1})),u=$();function e(){return T(this,null,function*(){const{valid:t}=yield r.validate(),d=yield r.getValues();t&&s("submit",d)})}function m(){u.push(i.loginPath)}return l({getFormApi:()=>r}),(t,d)=>(k(),F("div",null,[p(V,null,{desc:g(()=>[w(t.$slots,"subTitle",{},()=>[h(b(t.subTitle||o(n)("authentication.forgetPasswordSubtitle")),1)])]),default:g(()=>[w(t.$slots,"title",{},()=>[h(b(t.title||o(n)("authentication.forgetPassword"))+" 🤦🏻‍♂️ ",1)])]),_:3}),p(o(f)),x("div",null,[p(o(v),{class:y([{"cursor-wait":t.loading},"mt-2 w-full"]),"aria-label":"submit",onClick:e},{default:g(()=>[w(t.$slots,"submitButtonText",{},()=>[h(b(t.submitButtonText||o(n)("authentication.sendResetLink")),1)])]),_:3},8,["class"]),p(o(v),{class:"mt-4 w-full",variant:"outline",onClick:d[0]||(d[0]=D=>m())},{default:g(()=>[h(b(o(n)("common.back")),1)]),_:1})])]))}}),z=_({name:"ForgetPassword",__name:"forget-password",setup(c){const l=L(!1),a=S(()=>[{component:"VbenInput",componentProps:{placeholder:"<EMAIL>"},fieldName:"email",label:n("authentication.email"),rules:P().min(1,{message:n("authentication.emailTip")}).email(n("authentication.emailValidErrorTip"))}]);function i(s){console.log("reset email:",s)}return(s,f)=>(k(),N(o(A),{"form-schema":a.value,loading:l.value,onSubmit:i},null,8,["form-schema","loading"]))}});export{z as default};
