const e="Preferences",o="Customize Preferences & Preview in Real Time",t="Data has changed, click to reset",n="Reset Preferences",a="Preferences reset successfully",i="Appearance",l="Layout",r="Content",s="Other",c="Wide",d="Fixed",u="Follow System",b="Vertical",h="Side vertical menu mode",m="Horizontal",p="Horizontal menu mode, all menus displayed at the top",g="Two Column",S="Vertical Two Column Menu Mode",T="Header Vertical",C="Header Full Width, Sidebar Navigation Mode",w="Header Two Column",y="Header Navigation & Sidebar Two Column co-exists",f="Mixed Menu",M="Vertical & Horizontal Menu Co-exists",k="Full Content",x="Only display content body, hide all menus",v="Normal",P="Plain",A="Rounded",H="Copy Preferences",B="Copy successful",E="Copy successful, please override in `src/preferences.ts` under app",L="Clear Cache & Logout",N="Mode",F="General",W="Language",z="Dynamic Title",D="Watermark",O="Periodic update check",R={title:"Preferences Postion",header:"Header",auto:"Auto",fixed:"Fixed"},G={buttons:"Show Buttons",buttonFixed:"Fixed",buttonCollapsed:"Collapsed",title:"Sidebar",width:"Width",visible:"Show Sidebar",collapsed:"Collpase Menu",collapsedShowTitle:"Show Menu Title",autoActivateChild:"Auto Activate SubMenu",autoActivateChildTip:"`Enabled` to automatically activate the submenu while click menu.",expandOnHover:"Expand On Hover",expandOnHoverTip:"When the mouse hovers over menu, \n `Enabled` to expand children menus \n `Disabled` to expand whole sidebar."},I={title:"Tabbar",enable:"Enable Tab Bar",icon:"Show Tabbar Icon",showMore:"Show More Button",showMaximize:"Show Maximize Button",persist:"Persist Tabs",maxCount:"Max Count of Tabs",maxCountTip:`When the number of tabs exceeds the maximum,
the oldest tab will be closed.
 Set to 0 to disable count checking.`,draggable:"Enable Draggable Sort",wheelable:"Support Mouse Wheel",middleClickClose:"Close Tab when Mouse Middle Button Click",wheelableTip:"When enabled, the Tabbar area responds to vertical scrolling events of the scroll wheel.",styleType:{title:"Tabs Style",chrome:"Chrome",card:"Card",plain:"Plain",brisk:"Brisk"},contextMenu:{reload:"Reload",close:"Close",pin:"Pin",unpin:"Unpin",closeLeft:"Close Left Tabs",closeRight:"Close Right Tabs",closeOther:"Close Other Tabs",closeAll:"Close All Tabs",openInNewWindow:"Open in New Window",maximize:"Maximize",restoreMaximize:"Restore"}},V={title:"Navigation Menu",style:"Navigation Menu Style",accordion:"Sidebar Accordion Menu",split:"Navigation Menu Separation",splitTip:"When enabled, the sidebar displays the top bar's submenu"},K={title:"Breadcrumb",home:"Show Home Button",enable:"Enable Breadcrumb",icon:"Show Breadcrumb Icon",background:"background",style:"Breadcrumb Style",hideOnlyOne:"Hidden when only one"},U={title:"Animation",loading:"Page Loading",transition:"Page Transition",progress:"Page Progress"},Y={title:"Theme",radius:"Radius",light:"Light",dark:"Dark",darkSidebar:"Semi Dark Sidebar",darkHeader:"Semi Dark Header",weakMode:"Weak Mode",grayMode:"Gray Mode",builtin:{title:"Built-in",default:"Default",violet:"Violet",pink:"Pink",rose:"Rose",skyBlue:"Sky Blue",deepBlue:"Deep Blue",green:"Green",deepGreen:"Deep Green",orange:"Orange",yellow:"Yellow",zinc:"Zinc",neutral:"Neutral",slate:"Slate",gray:"Gray",custom:"Custom"}},Z={title:"Header",visible:"Show Header",modeStatic:"Static",modeFixed:"Fixed",modeAuto:"Auto hide & Show",modeAutoScroll:"Scroll to Hide & Show",menuAlign:"Menu Align",menuAlignStart:"Start",menuAlignEnd:"End",menuAlignCenter:"Center"},j={title:"Footer",visible:"Show Footer",fixed:"Fixed at Bottom"},q={title:"Copyright",enable:"Enable Copyright",companyName:"Company Name",companySiteLink:"Company Site Link",date:"Date",icp:"ICP License Number",icpLink:"ICP Site Link"},J={title:"Shortcut Keys",global:"Global",search:"Global Search",logout:"Logout",preferences:"Preferences"},Q={title:"Widget",globalSearch:"Enable Global Search",fullscreen:"Enable Fullscreen",themeToggle:"Enable Theme Toggle",languageToggle:"Enable Language Toggle",notification:"Enable Notification",sidebarToggle:"Enable Sidebar Toggle",lockScreen:"Enable Lock Screen",refresh:"Enable Refresh"},X={title:e,subtitle:o,resetTip:t,resetTitle:n,resetSuccess:a,appearance:i,layout:l,content:r,other:s,wide:c,compact:d,followSystem:u,vertical:b,verticalTip:h,horizontal:m,horizontalTip:p,twoColumn:g,twoColumnTip:S,headerSidebarNav:T,headerSidebarNavTip:C,headerTwoColumn:w,headerTwoColumnTip:y,mixedMenu:f,mixedMenuTip:M,fullContent:k,fullContentTip:x,normal:v,plain:P,rounded:A,copyPreferences:H,copyPreferencesSuccessTitle:B,copyPreferencesSuccess:E,clearAndLogout:L,mode:N,general:F,language:W,dynamicTitle:z,watermark:D,checkUpdates:O,position:R,sidebar:G,tabbar:I,navigationMenu:V,breadcrumb:K,animation:U,theme:Y,header:Z,footer:j,copyright:q,shortcutKeys:J,widget:Q};export{U as animation,i as appearance,K as breadcrumb,O as checkUpdates,L as clearAndLogout,d as compact,r as content,H as copyPreferences,E as copyPreferencesSuccess,B as copyPreferencesSuccessTitle,q as copyright,X as default,z as dynamicTitle,u as followSystem,j as footer,k as fullContent,x as fullContentTip,F as general,Z as header,T as headerSidebarNav,C as headerSidebarNavTip,w as headerTwoColumn,y as headerTwoColumnTip,m as horizontal,p as horizontalTip,W as language,l as layout,f as mixedMenu,M as mixedMenuTip,N as mode,V as navigationMenu,v as normal,s as other,P as plain,R as position,a as resetSuccess,t as resetTip,n as resetTitle,A as rounded,J as shortcutKeys,G as sidebar,o as subtitle,I as tabbar,Y as theme,e as title,g as twoColumn,S as twoColumnTip,b as vertical,h as verticalTip,D as watermark,c as wide,Q as widget};
