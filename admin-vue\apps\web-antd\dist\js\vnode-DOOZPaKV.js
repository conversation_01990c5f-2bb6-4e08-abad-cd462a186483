import{n as l,_ as a,w as i}from"./bootstrap-BmSDnAET.js";import{al as m,ba as f,ak as p,F as d}from"../jse/index-index-BAMHRxBA.js";function y(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,t=e;if(Array.isArray(e)&&(t=l(e)[0]),!t)return null;const n=m(t,r,s);return n.props=u?a(a({},n.props),r):n.props,i(typeof n.props.class!="object"),n}const o=e=>(e||[]).some(r=>f(r)?!(r.type===p||r.type===d&&!o(r.children)):!0)?e:null;function v(e,r,u,s){var t;const n=(t=e[r])===null||t===void 0?void 0:t.call(e,u);return o(n)?n:s==null?void 0:s()}export{v as a,y as c};
