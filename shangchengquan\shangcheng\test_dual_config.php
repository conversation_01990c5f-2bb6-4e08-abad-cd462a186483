<?php
/**
 * 测试双重配置逻辑
 * 2025-07-17 验证用户自有配置和平台公共配置的处理
 */

// 引入ThinkPHP框架
require_once 'vendor/autoload.php';

// 初始化应用
$app = new think\App();
$app->initialize();

use app\common\SheZhen;
use think\facade\Db;

echo "<h1>舌诊双重配置逻辑测试</h1>";

// 测试不同场景
$testScenarios = [
    [
        'name' => '场景1：用户有自己的阿里云配置',
        'aid' => 62,
        'description' => '用户配置了自己的 aliyun_access_key, aliyun_secret_key, aliyun_endpoint'
    ],
    [
        'name' => '场景2：用户没有自己的阿里云配置',
        'aid' => 1,
        'description' => '用户只有基础配置，使用平台的阿里云配置'
    ],
    [
        'name' => '场景3：只有平台配置',
        'aid' => 0,
        'description' => '没有用户配置，只使用平台配置'
    ]
];

foreach ($testScenarios as $scenario) {
    echo "<h2>{$scenario['name']}</h2>";
    echo "<p><em>{$scenario['description']}</em></p>";
    
    try {
        $config = SheZhen::getConfig($scenario['aid']);
        
        if ($config) {
            echo "<p style='color: green;'>✓ 成功获取配置</p>";
            
            // 显示配置类型
            echo "<h3>配置类型判断：</h3>";
            if (isset($config['use_own_aliyun']) && $config['use_own_aliyun']) {
                echo "<p style='color: blue;'><strong>🔑 使用用户自有阿里云配置</strong></p>";
            } elseif (isset($config['use_platform_api']) && $config['use_platform_api']) {
                echo "<p style='color: orange;'><strong>🌐 使用平台公共API配置</strong></p>";
            }
            
            // 显示关键配置项
            echo "<h3>关键配置项：</h3>";
            echo "<ul>";
            
            $keyFields = [
                'aliyun_access_key' => 'Access Key',
                'aliyun_secret_key' => 'Secret Key', 
                'aliyun_endpoint' => 'API端点',
                'aliyun_app_code' => 'App Code',
                'is_enable' => '是否启用',
                'is_open' => '是否开启',
                'price' => '价格'
            ];
            
            foreach ($keyFields as $field => $label) {
                if (isset($config[$field])) {
                    $value = $config[$field];
                    // 隐藏敏感信息
                    if (in_array($field, ['aliyun_access_key', 'aliyun_secret_key', 'aliyun_app_code'])) {
                        $value = strlen($value) > 8 ? substr($value, 0, 8) . '***' : $value;
                    }
                    echo "<li><strong>$label:</strong> $value</li>";
                } else {
                    echo "<li><strong>$label:</strong> <span style='color: red;'>未配置</span></li>";
                }
            }
            echo "</ul>";
            
            // 配置完整性检查
            echo "<h3>配置完整性检查：</h3>";
            
            if ($config['use_own_aliyun']) {
                // 检查用户自有配置
                $required = ['aliyun_access_key', 'aliyun_secret_key', 'aliyun_endpoint'];
                $complete = true;
                
                foreach ($required as $field) {
                    if (empty($config[$field])) {
                        echo "<p style='color: red;'>✗ 缺少用户配置: $field</p>";
                        $complete = false;
                    }
                }
                
                if ($complete) {
                    echo "<p style='color: green;'>✓ 用户自有配置完整</p>";
                }
            } else {
                // 检查平台配置
                $required = ['aliyun_app_code', 'aliyun_endpoint'];
                $complete = true;
                
                foreach ($required as $field) {
                    if (empty($config[$field])) {
                        echo "<p style='color: red;'>✗ 缺少平台配置: $field</p>";
                        $complete = false;
                    }
                }
                
                if ($complete) {
                    echo "<p style='color: green;'>✓ 平台配置完整</p>";
                    
                    // 检查平台次数
                    if ($scenario['aid'] > 0) {
                        $adminInfo = Db::name('admin')->where('id', $scenario['aid'])->field('mianzhen_num')->find();
                        $remaining = $adminInfo ? intval($adminInfo['mianzhen_num']) : 0;
                        echo "<p><strong>平台剩余次数:</strong> $remaining</p>";
                    }
                }
            }
            
        } else {
            echo "<p style='color: red;'>✗ 未找到配置</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ 配置获取异常: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// 测试实际API调用
echo "<h2>API调用测试</h2>";

$testImageUrl = "https://kuaifengimg.azheteng.cn/upload/62/20250718/fed0d7bd030b57e056de3dba260e6e36.jpg";
$imageData = [
    'tf_image' => $testImageUrl,
    'gender' => '男'
];

echo "<h3>测试数据：</h3>";
echo "<pre>" . json_encode($imageData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

try {
    $result = SheZhen::callDetectApi($imageData, 62);
    
    echo "<h3>API调用结果：</h3>";
    if ($result['status'] == 1) {
        echo "<p style='color: green;'>✓ API调用成功</p>";
    } else {
        echo "<p style='color: red;'>✗ API调用失败: " . $result['msg'] . "</p>";
    }
    
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ API调用异常: " . $e->getMessage() . "</p>";
}

echo "<h2>修复总结</h2>";
echo "<ul>";
echo "<li>✓ 实现了双重配置逻辑：用户自有配置 + 平台公共配置</li>";
echo "<li>✓ 自动判断使用哪种配置方式</li>";
echo "<li>✓ 支持配置合并，平台配置补充用户配置的缺失字段</li>";
echo "<li>✓ 添加了配置类型标记，便于后续处理</li>";
echo "<li>✓ 保持了向后兼容性</li>";
echo "</ul>";
?>
