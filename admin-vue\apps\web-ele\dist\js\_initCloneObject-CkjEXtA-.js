import{af as i,ag as l}from"./bootstrap-CYivmKoJ.js";import{o as p,U as n,a as v}from"./isEqual-racMrmQ-.js";var u=Object.create,b=function(){function e(){}return function(t){if(!i(t))return{};if(u)return u(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}(),d=p(Object.getPrototypeOf,Object),a=typeof exports=="object"&&exports&&!exports.nodeType&&exports,c=a&&typeof module=="object"&&module&&!module.nodeType&&module,g=c&&c.exports===a,s=g?l.Buffer:void 0,f=s?s.allocUnsafe:void 0;function y(e,t){if(t)return e.slice();var r=e.length,o=f?f(r):new e.constructor(r);return e.copy(o),o}function m(e){var t=new e.constructor(e.byteLength);return new n(t).set(new n(e)),t}function O(e,t){var r=t?m(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function w(e){return typeof e.constructor=="function"&&!v(e)?b(d(e)):{}}export{O as a,y as b,m as c,d as g,w as i};
