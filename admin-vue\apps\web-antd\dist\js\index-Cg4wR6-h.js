import{ao as ia,ar as Tt,ad as ra,as as la,ac as Pt,P as A,b as W,j as K,bE as sa,x as He,K as ce,_ as c,d as re,R as X,H as De,bF as Le,I as da,k as pt,m as ft,r as bt,J as gt,V as Kt,F as ke,f as jt,o as mt,i as ca,a6 as ua,Y as je,a as be,a2 as va,e as pa,s as Ge,g as fa,z as ba,a5 as Ye,E as ga,bG as Oe,bH as ma,bI as at,G as nt,B as te,bJ as Ke,bK as ha}from"./bootstrap-BmSDnAET.js";import{a4 as D,P as ne,Y as fe,J as H,x as s,F as $a,a5 as U,az as ht,ao as Fe,a9 as Ve,R as ya,aF as Sa,ba as Bt,aa as xa,ab as Ca,ac as z,a7 as N,ai as ae}from"../jse/index-index-BAMHRxBA.js";import{u as wa,E as _a,M as Ta,a as Pa}from"./index-qN2epJy7.js";import{e as Ba,d as Ra,i as Ia,T as Aa}from"./Trigger-ktfUwTEB.js";import{c as dt,a as Ea}from"./vnode-DOOZPaKV.js";import{R as Rt}from"./index-B-vFSvKB.js";import{u as Ma}from"./useRefs-DXJZUNdS.js";import{a as La,c as Xt,t as Oa,b as Ha,h as Da}from"./hasIn-DsfFRncL.js";import{i as ka}from"./isMobile-8sZ0LT6r.js";import{u as It}from"./useMergedState-oYRiGtT_.js";import{i as At}from"./slide-DoU4o8T0.js";import{i as za}from"./isPlainObject-Bx7wC9wW.js";import ot from"./index-B7d8ajem.js";import{_ as Na}from"./page.vue_vue_type_script_setup_true_lang-BEpByXUN.js";import"./shallowequal-BAoEuXbT.js";import"./Overflow-PjgTLHBy.js";import"./index-DKSwBpyj.js";import"./colors-XnAMBo4s.js";import"./collapseMotion-DF3grfIf.js";import"./collapse-BbEVqHco.js";import"./ResizeObserver.es-CDE7jhPe.js";function Wa(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}var Ga=800,Ka=16,ja=Date.now;function Xa(e){var t=0,a=0;return function(){var n=ja(),o=Ka-(n-a);if(a=n,o>0){if(++t>=Ga)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function Fa(e){return function(){return e}}var qe=function(){try{var e=ia(Object,"defineProperty");return e({},"",{}),e}catch(t){}}(),Va=qe?function(e,t){return qe(e,"toString",{configurable:!0,enumerable:!1,value:Fa(t),writable:!0})}:La,qa=Xa(Va);function Ya(e,t,a){t=="__proto__"&&qe?qe(e,t,{configurable:!0,enumerable:!0,value:a,writable:!0}):e[t]=a}var Ua=Object.prototype,Ja=Ua.hasOwnProperty;function Za(e,t,a){var n=e[t];(!(Ja.call(e,t)&&Ba(n,a))||a===void 0&&!(t in e))&&Ya(e,t,a)}var Et=Math.max;function Qa(e,t,a){return t=Et(t===void 0?e.length-1:t,0),function(){for(var n=arguments,o=-1,r=Et(n.length-t,0),i=Array(r);++o<r;)i[o]=n[t+o];o=-1;for(var l=Array(t+1);++o<t;)l[o]=n[o];return l[t]=a(i),Wa(e,this,l)}}var Mt=Tt?Tt.isConcatSpreadable:void 0;function en(e){return ra(e)||la(e)||!!(Mt&&e&&e[Mt])}function tn(e,t,a,n,o){var r=-1,i=e.length;for(a||(a=en),o||(o=[]);++r<i;){var l=e[r];a(l)?Ra(o,l):o[o.length]=l}return o}function an(e){var t=e==null?0:e.length;return t?tn(e):[]}function nn(e){return qa(Qa(e,void 0,an),e+"")}function on(e,t,a,n){if(!Pt(e))return e;t=Xt(t,e);for(var o=-1,r=t.length,i=r-1,l=e;l!=null&&++o<r;){var v=Oa(t[o]),p=a;if(v==="__proto__"||v==="constructor"||v==="prototype")return e;if(o!=i){var g=l[v];p=void 0,p===void 0&&(p=Pt(g)?g:Ia(t[o+1])?[]:{})}Za(l,v,p),l=l[v]}return e}function rn(e,t,a){for(var n=-1,o=t.length,r={};++n<o;){var i=t[n],l=Ha(e,i);a(l,i)&&on(r,Xt(i,e),l)}return r}function ln(e,t){return rn(e,t,function(a,n){return Da(e,n)})}var Ft=nn(function(e,t){return e==null?{}:ln(e,t)});const Re={adjustX:1,adjustY:1},Ie=[0,0],sn={topLeft:{points:["bl","tl"],overflow:Re,offset:[0,-4],targetOffset:Ie},topCenter:{points:["bc","tc"],overflow:Re,offset:[0,-4],targetOffset:Ie},topRight:{points:["br","tr"],overflow:Re,offset:[0,-4],targetOffset:Ie},bottomLeft:{points:["tl","bl"],overflow:Re,offset:[0,4],targetOffset:Ie},bottomCenter:{points:["tc","bc"],overflow:Re,offset:[0,4],targetOffset:Ie},bottomRight:{points:["tr","br"],overflow:Re,offset:[0,4],targetOffset:Ie}};var dn=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const cn=D({compatConfig:{MODE:3},props:{minOverlayWidthMatchTrigger:{type:Boolean,default:void 0},arrow:{type:Boolean,default:!1},prefixCls:A.string.def("rc-dropdown"),transitionName:String,overlayClassName:A.string.def(""),openClassName:String,animation:A.any,align:A.object,overlayStyle:{type:Object,default:void 0},placement:A.string.def("bottomLeft"),overlay:A.any,trigger:A.oneOfType([A.string,A.arrayOf(A.string)]).def("hover"),alignPoint:{type:Boolean,default:void 0},showAction:A.array,hideAction:A.array,getPopupContainer:Function,visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!1},mouseEnterDelay:A.number.def(.15),mouseLeaveDelay:A.number.def(.1)},emits:["visibleChange","overlayClick"],setup(e,t){let{slots:a,emit:n,expose:o}=t;const r=ne(!!e.visible);fe(()=>e.visible,d=>{d!==void 0&&(r.value=d)});const i=ne();o({triggerRef:i});const l=d=>{e.visible===void 0&&(r.value=!1),n("overlayClick",d)},v=d=>{e.visible===void 0&&(r.value=d),n("visibleChange",d)},p=()=>{var d;const u=(d=a.overlay)===null||d===void 0?void 0:d.call(a),h={prefixCls:`${e.prefixCls}-menu`,onClick:l};return s($a,{key:sa},[e.arrow&&s("div",{class:`${e.prefixCls}-arrow`},null),dt(u,h,!1)])},g=H(()=>{const{minOverlayWidthMatchTrigger:d=!e.alignPoint}=e;return d}),y=()=>{var d;const u=(d=a.default)===null||d===void 0?void 0:d.call(a);return r.value&&u?dt(u[0],{class:e.openClassName||`${e.prefixCls}-open`},!1):u},m=H(()=>!e.hideAction&&e.trigger.indexOf("contextmenu")!==-1?["click"]:e.hideAction);return()=>{const{prefixCls:d,arrow:u,showAction:h,overlayStyle:w,trigger:x,placement:R,align:L,getPopupContainer:O,transitionName:b,animation:S,overlayClassName:f}=e,_=dn(e,["prefixCls","arrow","showAction","overlayStyle","trigger","placement","align","getPopupContainer","transitionName","animation","overlayClassName"]);return s(Aa,W(W({},_),{},{prefixCls:d,ref:i,popupClassName:K(f,{[`${d}-show-arrow`]:u}),popupStyle:w,builtinPlacements:sn,action:x,showAction:h,hideAction:m.value||[],popupPlacement:R,popupAlign:L,popupTransitionName:b,popupAnimation:S,popupVisible:r.value,stretch:g.value?"minWidth":"",onPopupVisibleChange:v,getPopupContainer:O}),{popup:p,default:y})}}});function un(e){const t=U(),a=U(!1);function n(){for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];a.value||(He.cancel(t.value),t.value=He(()=>{e(...r)}))}return ht(()=>{a.value=!0,He.cancel(t.value)}),n}function vn(e){const t=U([]),a=U(typeof e=="function"?e():e),n=un(()=>{let r=a.value;t.value.forEach(i=>{r=i(r)}),t.value=[],a.value=r});function o(r){t.value.push(r),n()}return[a,o]}const pn=D({compatConfig:{MODE:3},name:"TabNode",props:{id:{type:String},prefixCls:{type:String},tab:{type:Object},active:{type:Boolean},closable:{type:Boolean},editable:{type:Object},onClick:{type:Function},onResize:{type:Function},renderWrapper:{type:Function},removeAriaLabel:{type:String},onFocus:{type:Function}},emits:["click","resize","remove","focus"],setup(e,t){let{expose:a,attrs:n}=t;const o=ne();function r(v){var p;!((p=e.tab)===null||p===void 0)&&p.disabled||e.onClick(v)}a({domRef:o});function i(v){var p;v.preventDefault(),v.stopPropagation(),e.editable.onEdit("remove",{key:(p=e.tab)===null||p===void 0?void 0:p.key,event:v})}const l=H(()=>{var v;return e.editable&&e.closable!==!1&&!(!((v=e.tab)===null||v===void 0)&&v.disabled)});return()=>{var v;const{prefixCls:p,id:g,active:y,tab:{key:m,tab:d,disabled:u,closeIcon:h},renderWrapper:w,removeAriaLabel:x,editable:R,onFocus:L}=e,O=`${p}-tab`,b=s("div",{key:m,ref:o,class:K(O,{[`${O}-with-remove`]:l.value,[`${O}-active`]:y,[`${O}-disabled`]:u}),style:n.style,onClick:r},[s("div",{role:"tab","aria-selected":y,id:g&&`${g}-tab-${m}`,class:`${O}-btn`,"aria-controls":g&&`${g}-panel-${m}`,"aria-disabled":u,tabindex:u?null:0,onClick:S=>{S.stopPropagation(),r(S)},onKeydown:S=>{[ce.SPACE,ce.ENTER].includes(S.which)&&(S.preventDefault(),r(S))},onFocus:L},[typeof d=="function"?d():d]),l.value&&s("button",{type:"button","aria-label":x||"remove",tabindex:0,class:`${O}-remove`,onClick:S=>{S.stopPropagation(),i(S)}},[(h==null?void 0:h())||((v=R.removeIcon)===null||v===void 0?void 0:v.call(R))||"×"])]);return w?w(b):b}}}),Lt={width:0,height:0,left:0,top:0};function fn(e,t){const a=ne(new Map);return Fe(()=>{var n,o;const r=new Map,i=e.value,l=t.value.get((n=i[0])===null||n===void 0?void 0:n.key)||Lt,v=l.left+l.width;for(let p=0;p<i.length;p+=1){const{key:g}=i[p];let y=t.value.get(g);y||(y=t.value.get((o=i[p-1])===null||o===void 0?void 0:o.key)||Lt);const m=r.get(g)||c({},y);m.right=v-m.left-m.width,r.set(g,m)}a.value=new Map(r)}),a}const Vt=D({compatConfig:{MODE:3},name:"AddButton",inheritAttrs:!1,props:{prefixCls:String,editable:{type:Object},locale:{type:Object,default:void 0}},setup(e,t){let{expose:a,attrs:n}=t;const o=ne();return a({domRef:o}),()=>{const{prefixCls:r,editable:i,locale:l}=e;return!i||i.showAdd===!1?null:s("button",{ref:o,type:"button",class:`${r}-nav-add`,style:n.style,"aria-label":(l==null?void 0:l.addAriaLabel)||"Add tab",onClick:v=>{i.onEdit("add",{event:v})}},[i.addIcon?i.addIcon():"+"])}}}),bn={prefixCls:{type:String},id:{type:String},tabs:{type:Object},rtl:{type:Boolean},tabBarGutter:{type:Number},activeKey:{type:[String,Number]},mobile:{type:Boolean},moreIcon:A.any,moreTransitionName:{type:String},editable:{type:Object},locale:{type:Object,default:void 0},removeAriaLabel:String,onTabClick:{type:Function},popupClassName:String,getPopupContainer:re()},gn=D({compatConfig:{MODE:3},name:"OperationNode",inheritAttrs:!1,props:bn,emits:["tabClick"],slots:Object,setup(e,t){let{attrs:a,slots:n}=t;const[o,r]=X(!1),[i,l]=X(null),v=d=>{const u=e.tabs.filter(x=>!x.disabled);let h=u.findIndex(x=>x.key===i.value)||0;const w=u.length;for(let x=0;x<w;x+=1){h=(h+d+w)%w;const R=u[h];if(!R.disabled){l(R.key);return}}},p=d=>{const{which:u}=d;if(!o.value){[ce.DOWN,ce.SPACE,ce.ENTER].includes(u)&&(r(!0),d.preventDefault());return}switch(u){case ce.UP:v(-1),d.preventDefault();break;case ce.DOWN:v(1),d.preventDefault();break;case ce.ESC:r(!1);break;case ce.SPACE:case ce.ENTER:i.value!==null&&e.onTabClick(i.value,d);break}},g=H(()=>`${e.id}-more-popup`),y=H(()=>i.value!==null?`${g.value}-${i.value}`:null),m=(d,u)=>{d.preventDefault(),d.stopPropagation(),e.editable.onEdit("remove",{key:u,event:d})};return Ve(()=>{fe(i,()=>{const d=document.getElementById(y.value);d&&d.scrollIntoView&&d.scrollIntoView(!1)},{flush:"post",immediate:!0})}),fe(o,()=>{o.value||l(null)}),wa({}),()=>{var d;const{prefixCls:u,id:h,tabs:w,locale:x,mobile:R,moreIcon:L=((d=n.moreIcon)===null||d===void 0?void 0:d.call(n))||s(_a,null,null),moreTransitionName:O,editable:b,tabBarGutter:S,rtl:f,onTabClick:_,popupClassName:E}=e;if(!w.length)return null;const I=`${u}-dropdown`,F=x==null?void 0:x.dropdownAriaLabel,oe={[f?"marginRight":"marginLeft"]:S};w.length||(oe.visibility="hidden",oe.order=1);const le=K({[`${I}-rtl`]:f,[`${E}`]:!0}),ue=R?null:s(cn,{prefixCls:I,trigger:["hover"],visible:o.value,transitionName:O,onVisibleChange:r,overlayClassName:le,mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:e.getPopupContainer},{overlay:()=>s(Ta,{onClick:B=>{let{key:V,domEvent:T}=B;_(V,T),r(!1)},id:g.value,tabindex:-1,role:"listbox","aria-activedescendant":y.value,selectedKeys:[i.value],"aria-label":F!==void 0?F:"expanded dropdown"},{default:()=>[w.map(B=>{var V,T;const Z=b&&B.closable!==!1&&!B.disabled;return s(Pa,{key:B.key,id:`${g.value}-${B.key}`,role:"option","aria-controls":h&&`${h}-panel-${B.key}`,disabled:B.disabled},{default:()=>[s("span",null,[typeof B.tab=="function"?B.tab():B.tab]),Z&&s("button",{type:"button","aria-label":e.removeAriaLabel||"remove",tabindex:0,class:`${I}-menu-item-remove`,onClick:Q=>{Q.stopPropagation(),m(Q,B.key)}},[((V=B.closeIcon)===null||V===void 0?void 0:V.call(B))||((T=b.removeIcon)===null||T===void 0?void 0:T.call(b))||"×"])]})})]}),default:()=>s("button",{type:"button",class:`${u}-nav-more`,style:oe,tabindex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":g.value,id:`${h}-more`,"aria-expanded":o.value,onKeydown:p},[L])});return s("div",{class:K(`${u}-nav-operations`,a.class),style:a.style},[ue,s(Vt,{prefixCls:u,locale:x,editable:b},null)])}}}),qt=Symbol("tabsContextKey"),mn=e=>{Sa(qt,e)},Yt=()=>ya(qt,{tabs:ne([]),prefixCls:ne()}),hn=.1,Ot=.01,Xe=20,Ht=Math.pow(.995,Xe);function $n(e,t){const[a,n]=X(),[o,r]=X(0),[i,l]=X(0),[v,p]=X(),g=ne();function y(b){const{screenX:S,screenY:f}=b.touches[0];n({x:S,y:f}),clearInterval(g.value)}function m(b){if(!a.value)return;b.preventDefault();const{screenX:S,screenY:f}=b.touches[0],_=S-a.value.x,E=f-a.value.y;t(_,E),n({x:S,y:f});const I=Date.now();l(I-o.value),r(I),p({x:_,y:E})}function d(){if(!a.value)return;const b=v.value;if(n(null),p(null),b){const S=b.x/i.value,f=b.y/i.value,_=Math.abs(S),E=Math.abs(f);if(Math.max(_,E)<hn)return;let I=S,F=f;g.value=setInterval(()=>{if(Math.abs(I)<Ot&&Math.abs(F)<Ot){clearInterval(g.value);return}I*=Ht,F*=Ht,t(I*Xe,F*Xe)},Xe)}}const u=ne();function h(b){const{deltaX:S,deltaY:f}=b;let _=0;const E=Math.abs(S),I=Math.abs(f);E===I?_=u.value==="x"?S:f:E>I?(_=S,u.value="x"):(_=f,u.value="y"),t(-_,-_)&&b.preventDefault()}const w=ne({onTouchStart:y,onTouchMove:m,onTouchEnd:d,onWheel:h});function x(b){w.value.onTouchStart(b)}function R(b){w.value.onTouchMove(b)}function L(b){w.value.onTouchEnd(b)}function O(b){w.value.onWheel(b)}Ve(()=>{var b,S;document.addEventListener("touchmove",R,{passive:!1}),document.addEventListener("touchend",L,{passive:!1}),(b=e.value)===null||b===void 0||b.addEventListener("touchstart",x,{passive:!1}),(S=e.value)===null||S===void 0||S.addEventListener("wheel",O,{passive:!1})}),ht(()=>{document.removeEventListener("touchmove",R),document.removeEventListener("touchend",L)})}function Dt(e,t){const a=ne(e);function n(o){const r=typeof o=="function"?o(a.value):o;r!==a.value&&t(r,a.value),a.value=r}return[a,n]}const kt={width:0,height:0,left:0,top:0,right:0},yn=()=>({id:{type:String},tabPosition:{type:String},activeKey:{type:[String,Number]},rtl:{type:Boolean},animated:De(),editable:De(),moreIcon:A.any,moreTransitionName:{type:String},mobile:{type:Boolean},tabBarGutter:{type:Number},renderTabBar:{type:Function},locale:De(),popupClassName:String,getPopupContainer:re(),onTabClick:{type:Function},onTabScroll:{type:Function}}),Sn=(e,t)=>{const{offsetWidth:a,offsetHeight:n,offsetTop:o,offsetLeft:r}=e,{width:i,height:l,x:v,y:p}=e.getBoundingClientRect();return Math.abs(i-a)<1?[i,l,v-t.x,p-t.y]:[a,n,r,o]},zt=D({compatConfig:{MODE:3},name:"TabNavList",inheritAttrs:!1,props:yn(),slots:Object,emits:["tabClick","tabScroll"],setup(e,t){let{attrs:a,slots:n}=t;const{tabs:o,prefixCls:r}=Yt(),i=U(),l=U(),v=U(),p=U(),[g,y]=Ma(),m=H(()=>e.tabPosition==="top"||e.tabPosition==="bottom"),[d,u]=Dt(0,(C,$)=>{m.value&&e.onTabScroll&&e.onTabScroll({direction:C>$?"left":"right"})}),[h,w]=Dt(0,(C,$)=>{!m.value&&e.onTabScroll&&e.onTabScroll({direction:C>$?"top":"bottom"})}),[x,R]=X(0),[L,O]=X(0),[b,S]=X(null),[f,_]=X(null),[E,I]=X(0),[F,oe]=X(0),[le,ue]=vn(new Map),B=fn(o,le),V=H(()=>`${r.value}-nav-operations-hidden`),T=U(0),Z=U(0);Fe(()=>{m.value?e.rtl?(T.value=0,Z.value=Math.max(0,x.value-b.value)):(T.value=Math.min(0,b.value-x.value),Z.value=0):(T.value=Math.min(0,f.value-L.value),Z.value=0)});const Q=C=>C<T.value?T.value:C>Z.value?Z.value:C,ge=U(),[q,me]=X(),he=()=>{me(Date.now())},Ce=()=>{clearTimeout(ge.value)},_e=(C,$)=>{C(P=>Q(P+$))};$n(i,(C,$)=>{if(m.value){if(b.value>=x.value)return!1;_e(u,C)}else{if(f.value>=L.value)return!1;_e(w,$)}return Ce(),he(),!0}),fe(q,()=>{Ce(),q.value&&(ge.value=setTimeout(()=>{me(0)},100))});const $e=function(){let C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activeKey;const $=B.value.get(C)||{width:0,height:0,left:0,right:0,top:0};if(m.value){let P=d.value;e.rtl?$.right<d.value?P=$.right:$.right+$.width>d.value+b.value&&(P=$.right+$.width-b.value):$.left<-d.value?P=-$.left:$.left+$.width>-d.value+b.value&&(P=-($.left+$.width-b.value)),w(0),u(Q(P))}else{let P=h.value;$.top<-h.value?P=-$.top:$.top+$.height>-h.value+f.value&&(P=-($.top+$.height-f.value)),u(0),w(Q(P))}},ve=U(0),Te=U(0);Fe(()=>{let C,$,P,M,G,k;const se=B.value;["top","bottom"].includes(e.tabPosition)?(C="width",M=b.value,G=x.value,k=E.value,$=e.rtl?"right":"left",P=Math.abs(d.value)):(C="height",M=f.value,G=x.value,k=F.value,$="top",P=-h.value);let Y=M;G+k>M&&G<M&&(Y=M-k);const ie=o.value;if(!ie.length)return[ve.value,Te.value]=[0,0];const de=ie.length;let Se=de;for(let ee=0;ee<de;ee+=1){const pe=se.get(ie[ee].key)||kt;if(pe[$]+pe[C]>P+Y){Se=ee-1;break}}let j=0;for(let ee=de-1;ee>=0;ee-=1)if((se.get(ie[ee].key)||kt)[$]<P){j=ee+1;break}return[ve.value,Te.value]=[j,Se]});const we=()=>{ue(()=>{var C;const $=new Map,P=(C=l.value)===null||C===void 0?void 0:C.getBoundingClientRect();return o.value.forEach(M=>{let{key:G}=M;const k=y.value.get(G),se=(k==null?void 0:k.$el)||k;if(se){const[Y,ie,de,Se]=Sn(se,P);$.set(G,{width:Y,height:ie,left:de,top:Se})}}),$})};fe(()=>o.value.map(C=>C.key).join("%%"),()=>{we()},{flush:"post"});const Pe=()=>{var C,$,P,M,G;const k=((C=i.value)===null||C===void 0?void 0:C.offsetWidth)||0,se=(($=i.value)===null||$===void 0?void 0:$.offsetHeight)||0,Y=((P=p.value)===null||P===void 0?void 0:P.$el)||{},ie=Y.offsetWidth||0,de=Y.offsetHeight||0;S(k),_(se),I(ie),oe(de);const Se=(((M=l.value)===null||M===void 0?void 0:M.offsetWidth)||0)-ie,j=(((G=l.value)===null||G===void 0?void 0:G.offsetHeight)||0)-de;R(Se),O(j),we()},Be=H(()=>[...o.value.slice(0,ve.value),...o.value.slice(Te.value+1)]),[ta,aa]=X(),ye=H(()=>B.value.get(e.activeKey)),St=U(),xt=()=>{He.cancel(St.value)};fe([ye,m,()=>e.rtl],()=>{const C={};ye.value&&(m.value?(e.rtl?C.right=Le(ye.value.right):C.left=Le(ye.value.left),C.width=Le(ye.value.width)):(C.top=Le(ye.value.top),C.height=Le(ye.value.height))),xt(),St.value=He(()=>{aa(C)})}),fe([()=>e.activeKey,ye,B,m],()=>{$e()},{flush:"post"}),fe([()=>e.rtl,()=>e.tabBarGutter,()=>e.activeKey,()=>o.value],()=>{Pe()},{flush:"post"});const Ze=C=>{let{position:$,prefixCls:P,extra:M}=C;if(!M)return null;const G=M==null?void 0:M({position:$});return G?s("div",{class:`${P}-extra-content`},[G]):null};return ht(()=>{Ce(),xt()}),()=>{const{id:C,animated:$,activeKey:P,rtl:M,editable:G,locale:k,tabPosition:se,tabBarGutter:Y,onTabClick:ie}=e,{class:de,style:Se}=a,j=r.value,ee=!!Be.value.length,pe=`${j}-nav-wrap`;let Qe,et,Ct,wt;m.value?M?(et=d.value>0,Qe=d.value+b.value<x.value):(Qe=d.value<0,et=-d.value+b.value<x.value):(Ct=h.value<0,wt=-h.value+f.value<L.value);const We={};se==="top"||se==="bottom"?We[M?"marginRight":"marginLeft"]=typeof Y=="number"?`${Y}px`:Y:We.marginTop=typeof Y=="number"?`${Y}px`:Y;const _t=o.value.map((tt,na)=>{const{key:Me}=tt;return s(pn,{id:C,prefixCls:j,key:Me,tab:tt,style:na===0?void 0:We,closable:tt.closable,editable:G,active:Me===P,removeAriaLabel:k==null?void 0:k.removeAriaLabel,ref:g(Me),onClick:oa=>{ie(Me,oa)},onFocus:()=>{$e(Me),he(),i.value&&(M||(i.value.scrollLeft=0),i.value.scrollTop=0)}},n)});return s("div",{role:"tablist",class:K(`${j}-nav`,de),style:Se,onKeydown:()=>{he()}},[s(Ze,{position:"left",prefixCls:j,extra:n.leftExtra},null),s(Rt,{onResize:Pe},{default:()=>[s("div",{class:K(pe,{[`${pe}-ping-left`]:Qe,[`${pe}-ping-right`]:et,[`${pe}-ping-top`]:Ct,[`${pe}-ping-bottom`]:wt}),ref:i},[s(Rt,{onResize:Pe},{default:()=>[s("div",{ref:l,class:`${j}-nav-list`,style:{transform:`translate(${d.value}px, ${h.value}px)`,transition:q.value?"none":void 0}},[_t,s(Vt,{ref:p,prefixCls:j,locale:k,editable:G,style:c(c({},_t.length===0?void 0:We),{visibility:ee?"hidden":null})},null),s("div",{class:K(`${j}-ink-bar`,{[`${j}-ink-bar-animated`]:$.inkBar}),style:ta.value},null)])]})])]}),s(gn,W(W({},e),{},{removeAriaLabel:k==null?void 0:k.removeAriaLabel,ref:v,prefixCls:j,tabs:Be.value,class:!ee&&V.value}),Ft(n,["moreIcon"])),s(Ze,{position:"right",prefixCls:j,extra:n.rightExtra},null),s(Ze,{position:"right",prefixCls:j,extra:n.tabBarExtraContent},null)])}}}),xn=D({compatConfig:{MODE:3},name:"TabPanelList",inheritAttrs:!1,props:{activeKey:{type:[String,Number]},id:{type:String},rtl:{type:Boolean},animated:{type:Object,default:void 0},tabPosition:{type:String},destroyInactiveTabPane:{type:Boolean}},setup(e){const{tabs:t,prefixCls:a}=Yt();return()=>{const{id:n,activeKey:o,animated:r,tabPosition:i,rtl:l,destroyInactiveTabPane:v}=e,p=r.tabPane,g=a.value,y=t.value.findIndex(m=>m.key===o);return s("div",{class:`${g}-content-holder`},[s("div",{class:[`${g}-content`,`${g}-content-${i}`,{[`${g}-content-animated`]:p}],style:y&&p?{[l?"marginRight":"marginLeft"]:`-${y}00%`}:null},[t.value.map(m=>dt(m.node,{key:m.key,prefixCls:g,tabKey:m.key,id:n,animated:p,active:m.key===o,destroyInactiveTabPane:v}))])])}}});var Cn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};function Nt(e){for(var t=1;t<arguments.length;t++){var a=arguments[t]!=null?Object(arguments[t]):{},n=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(a).filter(function(o){return Object.getOwnPropertyDescriptor(a,o).enumerable}))),n.forEach(function(o){wn(e,o,a[o])})}return e}function wn(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var $t=function(t,a){var n=Nt({},t,a.attrs);return s(da,Nt({},n,{icon:Cn}),null)};$t.displayName="PlusOutlined";$t.inheritAttrs=!1;const _n=e=>{const{componentCls:t,motionDurationSlow:a}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${a}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${a}`}}}}},[At(e,"slide-up"),At(e,"slide-down")]]},Tn=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeadBackground:n,tabsCardGutter:o,colorSplit:r}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:a,background:n,border:`${e.lineWidth}px ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:e.colorPrimary,background:e.colorBgContainer},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:`${o}px`}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:`${o}px`}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadiusLG}px 0 0 ${e.borderRadiusLG}px`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},Pn=e=>{const{componentCls:t,tabsHoverColor:a,dropdownEdgeChildVerticalPadding:n}=e;return{[`${t}-dropdown`]:c(c({},bt(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${n}px 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":c(c({},gt),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${e.paddingXXS}px ${e.paddingSM}px`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:a}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},Bn=e=>{const{componentCls:t,margin:a,colorSplit:n}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:`0 0 ${a}px 0`,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${e.lineWidth}px ${e.lineType} ${n}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:`${a}px`,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:e.controlHeight*1.25,[`${t}-tab`]:{padding:`${e.paddingXS}px ${e.paddingLG}px`,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:`${e.margin}px 0 0 0`},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:`-${e.lineWidth}px`},borderLeft:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:-e.lineWidth},borderRight:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},Rn=e=>{const{componentCls:t,padding:a}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px 0`,fontSize:e.fontSize}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${a}px 0`,fontSize:e.fontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXXS*1.5}px ${a}px`}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${e.borderRadius}px ${e.borderRadius}px`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${e.borderRadius}px ${e.borderRadius}px 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadius}px ${e.borderRadius}px 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadius}px 0 0 ${e.borderRadius}px`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px ${a}px ${e.paddingXXS*1.5}px`}}}}}},In=e=>{const{componentCls:t,tabsActiveColor:a,tabsHoverColor:n,iconCls:o,tabsHorizontalGutter:r}=e,i=`${t}-tab`;return{[i]:{position:"relative",display:"inline-flex",alignItems:"center",padding:`${e.paddingSM}px 0`,fontSize:`${e.fontSize}px`,background:"transparent",border:0,outline:"none",cursor:"pointer","&-btn, &-remove":c({"&:focus:not(:focus-visible), &:active":{color:a}},Kt(e)),"&-btn":{outline:"none",transition:"all 0.3s"},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:-e.marginXXS},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:n},[`&${i}-active ${i}-btn`]:{color:e.colorPrimary,textShadow:e.tabsActiveTextShadow},[`&${i}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${i}-disabled ${i}-btn, &${i}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${i}-remove ${o}`]:{margin:0},[o]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${i} + ${i}`]:{margin:{_skip_check_:!0,value:`0 0 0 ${r}px`}}}},An=e=>{const{componentCls:t,tabsHorizontalGutter:a,iconCls:n,tabsCardGutter:o}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:`0 0 0 ${a}px`},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[n]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:`${e.marginSM}px`}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:`${e.marginXS}px`},marginLeft:{_skip_check_:!0,value:`-${e.marginXXS}px`},[n]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:`${o}px`},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},En=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeight:n,tabsCardGutter:o,tabsHoverColor:r,tabsActiveColor:i,colorSplit:l}=e;return{[t]:c(c(c(c({},bt(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:a,background:"transparent",border:0,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.controlHeightLG/8,transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:c({minWidth:`${n}px`,marginLeft:{_skip_check_:!0,value:`${o}px`},padding:`0 ${e.paddingXS}px`,background:"transparent",border:`${e.lineWidth}px ${e.lineType} ${l}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},Kt(e))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.colorPrimary,pointerEvents:"none"}}),In(e)),{[`${t}-content`]:{position:"relative",display:"flex",width:"100%","&-animated":{transition:"margin 0.3s"}},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:{outline:"none",flex:"none",width:"100%"}}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping'])`]:{justifyContent:"center"}}}}}},Mn=pt("Tabs",e=>{const t=e.controlHeightLG,a=ft(e,{tabsHoverColor:e.colorPrimaryHover,tabsActiveColor:e.colorPrimaryActive,tabsCardHorizontalPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,tabsCardHeight:t,tabsCardGutter:e.marginXXS/2,tabsHorizontalGutter:32,tabsCardHeadBackground:e.colorFillAlter,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120});return[Rn(a),An(a),Bn(a),Pn(a),Tn(a),En(a),_n(a)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));let Wt=0;const Ut=()=>({prefixCls:{type:String},id:{type:String},popupClassName:String,getPopupContainer:re(),activeKey:{type:[String,Number]},defaultActiveKey:{type:[String,Number]},direction:Ge(),animated:ba([Boolean,Object]),renderTabBar:re(),tabBarGutter:{type:Number},tabBarStyle:De(),tabPosition:Ge(),destroyInactiveTabPane:fa(),hideAdd:Boolean,type:Ge(),size:Ge(),centered:Boolean,onEdit:re(),onChange:re(),onTabClick:re(),onTabScroll:re(),"onUpdate:activeKey":re(),locale:De(),onPrevClick:re(),onNextClick:re(),tabBarExtraContent:A.any});function Ln(e){return e.map(t=>{if(ca(t)){const a=c({},t.props||{});for(const[m,d]of Object.entries(a))delete a[m],a[ua(m)]=d;const n=t.children||{},o=t.key!==void 0?t.key:void 0,{tab:r=n.tab,disabled:i,forceRender:l,closable:v,animated:p,active:g,destroyInactiveTabPane:y}=a;return c(c({key:o},a),{node:t,closeIcon:n.closeIcon,tab:r,disabled:i===""||i,forceRender:l===""||l,closable:v===""||v,animated:p===""||p,active:g===""||g,destroyInactiveTabPane:y===""||y})}return null}).filter(t=>t)}const On=D({compatConfig:{MODE:3},name:"InternalTabs",inheritAttrs:!1,props:c(c({},ke(Ut(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}})),{tabs:pa()}),slots:Object,setup(e,t){let{attrs:a,slots:n}=t;je(e.onPrevClick===void 0&&e.onNextClick===void 0,"Tabs","`onPrevClick / @prevClick` and `onNextClick / @nextClick` has been removed. Please use `onTabScroll / @tabScroll` instead."),je(e.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` prop has been removed. Please use `rightExtra` slot instead."),je(n.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` slot is deprecated. Please use `rightExtra` slot instead.");const{prefixCls:o,direction:r,size:i,rootPrefixCls:l,getPopupContainer:v}=be("tabs",e),[p,g]=Mn(o),y=H(()=>r.value==="rtl"),m=H(()=>{const{animated:f,tabPosition:_}=e;return f===!1||["left","right"].includes(_)?{inkBar:!1,tabPane:!1}:f===!0?{inkBar:!0,tabPane:!0}:c({inkBar:!0,tabPane:!1},typeof f=="object"?f:{})}),[d,u]=X(!1);Ve(()=>{u(ka())});const[h,w]=It(()=>{var f;return(f=e.tabs[0])===null||f===void 0?void 0:f.key},{value:H(()=>e.activeKey),defaultValue:e.defaultActiveKey}),[x,R]=X(()=>e.tabs.findIndex(f=>f.key===h.value));Fe(()=>{var f;let _=e.tabs.findIndex(E=>E.key===h.value);_===-1&&(_=Math.max(0,Math.min(x.value,e.tabs.length-1)),w((f=e.tabs[_])===null||f===void 0?void 0:f.key)),R(_)});const[L,O]=It(null,{value:H(()=>e.id)}),b=H(()=>d.value&&!["left","right"].includes(e.tabPosition)?"top":e.tabPosition);Ve(()=>{e.id||(O(`rc-tabs-${Wt}`),Wt+=1)});const S=(f,_)=>{var E,I;(E=e.onTabClick)===null||E===void 0||E.call(e,f,_);const F=f!==h.value;w(f),F&&((I=e.onChange)===null||I===void 0||I.call(e,f))};return mn({tabs:H(()=>e.tabs),prefixCls:o}),()=>{const{id:f,type:_,tabBarGutter:E,tabBarStyle:I,locale:F,destroyInactiveTabPane:oe,renderTabBar:le=n.renderTabBar,onTabScroll:ue,hideAdd:B,centered:V}=e,T={id:L.value,activeKey:h.value,animated:m.value,tabPosition:b.value,rtl:y.value,mobile:d.value};let Z;_==="editable-card"&&(Z={onEdit:(me,he)=>{let{key:Ce,event:_e}=he;var $e;($e=e.onEdit)===null||$e===void 0||$e.call(e,me==="add"?_e:Ce,me)},removeIcon:()=>s(va,null,null),addIcon:n.addIcon?n.addIcon:()=>s($t,null,null),showAdd:B!==!0});let Q;const ge=c(c({},T),{moreTransitionName:`${l.value}-slide-up`,editable:Z,locale:F,tabBarGutter:E,onTabClick:S,onTabScroll:ue,style:I,getPopupContainer:v.value,popupClassName:K(e.popupClassName,g.value)});le?Q=le(c(c({},ge),{DefaultTabBar:zt})):Q=s(zt,ge,Ft(n,["moreIcon","leftExtra","rightExtra","tabBarExtraContent"]));const q=o.value;return p(s("div",W(W({},a),{},{id:f,class:K(q,`${q}-${b.value}`,{[g.value]:!0,[`${q}-${i.value}`]:i.value,[`${q}-card`]:["card","editable-card"].includes(_),[`${q}-editable-card`]:_==="editable-card",[`${q}-centered`]:V,[`${q}-mobile`]:d.value,[`${q}-editable`]:_==="editable-card",[`${q}-rtl`]:y.value},a.class)}),[Q,s(xn,W(W({destroyInactiveTabPane:oe},T),{},{animated:m.value}),null)]))}}}),Ae=D({compatConfig:{MODE:3},name:"ATabs",inheritAttrs:!1,props:ke(Ut(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}}),slots:Object,setup(e,t){let{attrs:a,slots:n,emit:o}=t;const r=i=>{o("update:activeKey",i),o("change",i)};return()=>{var i;const l=Ln(jt((i=n.default)===null||i===void 0?void 0:i.call(n)));return s(On,W(W(W({},mt(e,["onUpdate:activeKey"])),a),{},{onChange:r,tabs:l}),n)}}}),Hn=()=>({tab:A.any,disabled:{type:Boolean},forceRender:{type:Boolean},closable:{type:Boolean},animated:{type:Boolean},active:{type:Boolean},destroyInactiveTabPane:{type:Boolean},prefixCls:{type:String},tabKey:{type:[String,Number]},id:{type:String}}),ct=D({compatConfig:{MODE:3},name:"ATabPane",inheritAttrs:!1,__ANT_TAB_PANE:!0,props:Hn(),slots:Object,setup(e,t){let{attrs:a,slots:n}=t;const o=ne(e.forceRender);fe([()=>e.active,()=>e.destroyInactiveTabPane],()=>{e.active?o.value=!0:e.destroyInactiveTabPane&&(o.value=!1)},{immediate:!0});const r=H(()=>e.active?{}:e.animated?{visibility:"hidden",height:0,overflowY:"hidden"}:{display:"none"});return()=>{var i;const{prefixCls:l,forceRender:v,id:p,active:g,tabKey:y}=e;return s("div",{id:p&&`${p}-panel-${y}`,role:"tabpanel",tabindex:g?0:-1,"aria-labelledby":p&&`${p}-tab-${y}`,"aria-hidden":!g,style:[r.value,a.style],class:[`${l}-tabpane`,g&&`${l}-tabpane-active`,a.class]},[(g||o.value||v)&&((i=n.default)===null||i===void 0?void 0:i.call(n))])}}});Ae.TabPane=ct;Ae.install=function(e){return e.component(Ae.name,Ae),e.component(ct.name,ct),e};const Dn=e=>{const{antCls:t,componentCls:a,cardHeadHeight:n,cardPaddingBase:o,cardHeadTabsMarginBottom:r}=e;return c(c({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:`0 ${o}px`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,background:"transparent",borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},Ye()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":c(c({display:"inline-block",flex:1},gt),{[`
          > ${a}-typography,
          > ${a}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:r,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorBorderSecondary}`}}})},kn=e=>{const{cardPaddingBase:t,colorBorderSecondary:a,cardShadow:n,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${o}px 0 0 0 ${a},
      0 ${o}px 0 0 ${a},
      ${o}px ${o}px 0 0 ${a},
      ${o}px 0 0 0 ${a} inset,
      0 ${o}px 0 0 ${a} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},zn=e=>{const{componentCls:t,iconCls:a,cardActionsLiMargin:n,cardActionsIconSize:o,colorBorderSecondary:r}=e;return c(c({margin:0,padding:0,listStyle:"none",background:e.colorBgContainer,borderTop:`${e.lineWidth}px ${e.lineType} ${r}`,display:"flex",borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px `},Ye()),{"& > li":{margin:n,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.cardActionsIconSize*2,fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${a}`]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:`${e.fontSize*e.lineHeight}px`,transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${a}`]:{fontSize:o,lineHeight:`${o*e.lineHeight}px`}},"&:not(:last-child)":{borderInlineEnd:`${e.lineWidth}px ${e.lineType} ${r}`}}})},Nn=e=>c(c({margin:`-${e.marginXXS}px 0`,display:"flex"},Ye()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":c({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},gt),"&-description":{color:e.colorTextDescription}}),Wn=e=>{const{componentCls:t,cardPaddingBase:a,colorFillAlter:n}=e;return{[`${t}-head`]:{padding:`0 ${a}px`,background:n,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${e.padding}px ${a}px`}}},Gn=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},Kn=e=>{const{componentCls:t,cardShadow:a,cardHeadPadding:n,colorBorderSecondary:o,boxShadow:r,cardPaddingBase:i}=e;return{[t]:c(c({},bt(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:r},[`${t}-head`]:Dn(e),[`${t}-extra`]:{marginInlineStart:"auto",color:"",fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:c({padding:i,borderRadius:` 0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},Ye()),[`${t}-grid`]:kn(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%"},img:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`}},[`${t}-actions`]:zn(e),[`${t}-meta`]:Nn(e)}),[`${t}-bordered`]:{border:`${e.lineWidth}px ${e.lineType} ${o}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:a}},[`${t}-contain-grid`]:{[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:-e.lineWidth,marginInlineStart:-e.lineWidth,padding:0}},[`${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:n}}},[`${t}-type-inner`]:Wn(e),[`${t}-loading`]:Gn(e),[`${t}-rtl`]:{direction:"rtl"}}},jn=e=>{const{componentCls:t,cardPaddingSM:a,cardHeadHeightSM:n}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:n,padding:`0 ${a}px`,fontSize:e.fontSize,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:a}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{minHeight:n,paddingTop:0,display:"flex",alignItems:"center"}}}}},Xn=pt("Card",e=>{const t=ft(e,{cardShadow:e.boxShadowCard,cardHeadHeight:e.fontSizeLG*e.lineHeightLG+e.padding*2,cardHeadHeightSM:e.fontSize*e.lineHeight+e.paddingXS*2,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardHeadTabsMarginBottom:-e.padding-e.lineWidth,cardActionsLiMargin:`${e.paddingSM}px 0`,cardActionsIconSize:e.fontSize,cardPaddingSM:12});return[Kn(t),jn(t)]}),Fn=()=>({prefixCls:String,width:{type:[Number,String]}}),yt=D({compatConfig:{MODE:3},name:"SkeletonTitle",props:Fn(),setup(e){return()=>{const{prefixCls:t,width:a}=e,n=typeof a=="number"?`${a}px`:a;return s("h3",{class:t,style:{width:n}},null)}}}),Vn=()=>({prefixCls:String,width:{type:[Number,String,Array]},rows:Number}),qn=D({compatConfig:{MODE:3},name:"SkeletonParagraph",props:Vn(),setup(e){const t=a=>{const{width:n,rows:o=2}=e;if(Array.isArray(n))return n[a];if(o-1===a)return n};return()=>{const{prefixCls:a,rows:n}=e,o=[...Array(n)].map((r,i)=>{const l=t(i);return s("li",{key:i,style:{width:typeof l=="number"?`${l}px`:l}},null)});return s("ul",{class:a},[o])}}}),Ue=()=>({prefixCls:String,size:[String,Number],shape:String,active:{type:Boolean,default:void 0}}),ze=e=>{const{prefixCls:t,size:a,shape:n}=e,o=K({[`${t}-lg`]:a==="large",[`${t}-sm`]:a==="small"}),r=K({[`${t}-circle`]:n==="circle",[`${t}-square`]:n==="square",[`${t}-round`]:n==="round"}),i=typeof a=="number"?{width:`${a}px`,height:`${a}px`,lineHeight:`${a}px`}:{};return s("span",{class:K(t,o,r),style:i},null)};ze.displayName="SkeletonElement";const Yn=new ga("ant-skeleton-loading",{"0%":{transform:"translateX(-37.5%)"},"100%":{transform:"translateX(37.5%)"}}),Je=e=>({height:e,lineHeight:`${e}px`}),Ee=e=>c({width:e},Je(e)),Un=e=>({position:"relative",zIndex:0,overflow:"hidden",background:"transparent","&::after":{position:"absolute",top:0,insetInlineEnd:"-150%",bottom:0,insetInlineStart:"-150%",background:e.skeletonLoadingBackground,animationName:Yn,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite",content:'""'}}),it=e=>c({width:e*5,minWidth:e*5},Je(e)),Jn=e=>{const{skeletonAvatarCls:t,color:a,controlHeight:n,controlHeightLG:o,controlHeightSM:r}=e;return{[`${t}`]:c({display:"inline-block",verticalAlign:"top",background:a},Ee(n)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:c({},Ee(o)),[`${t}${t}-sm`]:c({},Ee(r))}},Zn=e=>{const{controlHeight:t,borderRadiusSM:a,skeletonInputCls:n,controlHeightLG:o,controlHeightSM:r,color:i}=e;return{[`${n}`]:c({display:"inline-block",verticalAlign:"top",background:i,borderRadius:a},it(t)),[`${n}-lg`]:c({},it(o)),[`${n}-sm`]:c({},it(r))}},Gt=e=>c({width:e},Je(e)),Qn=e=>{const{skeletonImageCls:t,imageSizeBase:a,color:n,borderRadiusSM:o}=e;return{[`${t}`]:c(c({display:"flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",background:n,borderRadius:o},Gt(a*2)),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:c(c({},Gt(a)),{maxWidth:a*4,maxHeight:a*4}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},rt=(e,t,a)=>{const{skeletonButtonCls:n}=e;return{[`${a}${n}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${a}${n}-round`]:{borderRadius:t}}},lt=e=>c({width:e*2,minWidth:e*2},Je(e)),eo=e=>{const{borderRadiusSM:t,skeletonButtonCls:a,controlHeight:n,controlHeightLG:o,controlHeightSM:r,color:i}=e;return c(c(c(c(c({[`${a}`]:c({display:"inline-block",verticalAlign:"top",background:i,borderRadius:t,width:n*2,minWidth:n*2},lt(n))},rt(e,n,a)),{[`${a}-lg`]:c({},lt(o))}),rt(e,o,`${a}-lg`)),{[`${a}-sm`]:c({},lt(r))}),rt(e,r,`${a}-sm`))},to=e=>{const{componentCls:t,skeletonAvatarCls:a,skeletonTitleCls:n,skeletonParagraphCls:o,skeletonButtonCls:r,skeletonInputCls:i,skeletonImageCls:l,controlHeight:v,controlHeightLG:p,controlHeightSM:g,color:y,padding:m,marginSM:d,borderRadius:u,skeletonTitleHeight:h,skeletonBlockRadius:w,skeletonParagraphLineHeight:x,controlHeightXS:R,skeletonParagraphMarginTop:L}=e;return{[`${t}`]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:m,verticalAlign:"top",[`${a}`]:c({display:"inline-block",verticalAlign:"top",background:y},Ee(v)),[`${a}-circle`]:{borderRadius:"50%"},[`${a}-lg`]:c({},Ee(p)),[`${a}-sm`]:c({},Ee(g))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[`${n}`]:{width:"100%",height:h,background:y,borderRadius:w,[`+ ${o}`]:{marginBlockStart:g}},[`${o}`]:{padding:0,"> li":{width:"100%",height:x,listStyle:"none",background:y,borderRadius:w,"+ li":{marginBlockStart:R}}},[`${o}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${n}, ${o} > li`]:{borderRadius:u}}},[`${t}-with-avatar ${t}-content`]:{[`${n}`]:{marginBlockStart:d,[`+ ${o}`]:{marginBlockStart:L}}},[`${t}${t}-element`]:c(c(c(c({display:"inline-block",width:"auto"},eo(e)),Jn(e)),Zn(e)),Qn(e)),[`${t}${t}-block`]:{width:"100%",[`${r}`]:{width:"100%"},[`${i}`]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${n},
        ${o} > li,
        ${a},
        ${r},
        ${i},
        ${l}
      `]:c({},Un(e))}}},Ne=pt("Skeleton",e=>{const{componentCls:t}=e,a=ft(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:e.controlHeight*1.5,skeletonTitleHeight:e.controlHeight/2,skeletonBlockRadius:e.borderRadiusSM,skeletonParagraphLineHeight:e.controlHeight/2,skeletonParagraphMarginTop:e.marginLG+e.marginXXS,borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.color} 25%, ${e.colorGradientEnd} 37%, ${e.color} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[to(a)]},e=>{const{colorFillContent:t,colorFill:a}=e;return{color:t,colorGradientEnd:a}}),ao=()=>({active:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},prefixCls:String,avatar:{type:[Boolean,Object],default:void 0},title:{type:[Boolean,Object],default:void 0},paragraph:{type:[Boolean,Object],default:void 0},round:{type:Boolean,default:void 0}});function st(e){return e&&typeof e=="object"?e:{}}function no(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function oo(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function io(e,t){const a={};return(!e||!t)&&(a.width="61%"),!e&&t?a.rows=3:a.rows=2,a}const J=D({compatConfig:{MODE:3},name:"ASkeleton",props:ke(ao(),{avatar:!1,title:!0,paragraph:!0}),setup(e,t){let{slots:a}=t;const{prefixCls:n,direction:o}=be("skeleton",e),[r,i]=Ne(n);return()=>{var l;const{loading:v,avatar:p,title:g,paragraph:y,active:m,round:d}=e,u=n.value;if(v||e.loading===void 0){const h=!!p||p==="",w=!!g||g==="",x=!!y||y==="";let R;if(h){const b=c(c({prefixCls:`${u}-avatar`},no(w,x)),st(p));R=s("div",{class:`${u}-header`},[s(ze,b,null)])}let L;if(w||x){let b;if(w){const f=c(c({prefixCls:`${u}-title`},oo(h,x)),st(g));b=s(yt,f,null)}let S;if(x){const f=c(c({prefixCls:`${u}-paragraph`},io(h,w)),st(y));S=s(qn,f,null)}L=s("div",{class:`${u}-content`},[b,S])}const O=K(u,{[`${u}-with-avatar`]:h,[`${u}-active`]:m,[`${u}-rtl`]:o.value==="rtl",[`${u}-round`]:d,[i.value]:!0});return r(s("div",{class:O},[R,L]))}return(l=a.default)===null||l===void 0?void 0:l.call(a)}}}),ro=()=>c(c({},Ue()),{size:String,block:Boolean}),Jt=D({compatConfig:{MODE:3},name:"ASkeletonButton",props:ke(ro(),{size:"default"}),setup(e){const{prefixCls:t}=be("skeleton",e),[a,n]=Ne(t),o=H(()=>K(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},n.value));return()=>a(s("div",{class:o.value},[s(ze,W(W({},e),{},{prefixCls:`${t.value}-button`}),null)]))}}),Zt=D({compatConfig:{MODE:3},name:"ASkeletonInput",props:c(c({},mt(Ue(),["shape"])),{size:String,block:Boolean}),setup(e){const{prefixCls:t}=be("skeleton",e),[a,n]=Ne(t),o=H(()=>K(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},n.value));return()=>a(s("div",{class:o.value},[s(ze,W(W({},e),{},{prefixCls:`${t.value}-input`}),null)]))}}),lo="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",Qt=D({compatConfig:{MODE:3},name:"ASkeletonImage",props:mt(Ue(),["size","shape","active"]),setup(e){const{prefixCls:t}=be("skeleton",e),[a,n]=Ne(t),o=H(()=>K(t.value,`${t.value}-element`,n.value));return()=>a(s("div",{class:o.value},[s("div",{class:`${t.value}-image`},[s("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",class:`${t.value}-image-svg`},[s("path",{d:lo,class:`${t.value}-image-path`},null)])])]))}}),so=()=>c(c({},Ue()),{shape:String}),ea=D({compatConfig:{MODE:3},name:"ASkeletonAvatar",props:ke(so(),{size:"default",shape:"circle"}),setup(e){const{prefixCls:t}=be("skeleton",e),[a,n]=Ne(t),o=H(()=>K(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active},n.value));return()=>a(s("div",{class:o.value},[s(ze,W(W({},e),{},{prefixCls:`${t.value}-avatar`}),null)]))}});J.Button=Jt;J.Avatar=ea;J.Input=Zt;J.Image=Qt;J.Title=yt;J.install=function(e){return e.component(J.name,J),e.component(J.Button.name,Jt),e.component(J.Avatar.name,ea),e.component(J.Input.name,Zt),e.component(J.Image.name,Qt),e.component(J.Title.name,yt),e};const{TabPane:co}=Ae,uo=()=>({prefixCls:String,title:A.any,extra:A.any,bordered:{type:Boolean,default:!0},bodyStyle:{type:Object,default:void 0},headStyle:{type:Object,default:void 0},loading:{type:Boolean,default:!1},hoverable:{type:Boolean,default:!1},type:{type:String},size:{type:String},actions:A.any,tabList:{type:Array},tabBarExtraContent:A.any,activeTabKey:String,defaultActiveTabKey:String,cover:A.any,onTabChange:{type:Function}}),xe=D({compatConfig:{MODE:3},name:"ACard",inheritAttrs:!1,props:uo(),slots:Object,setup(e,t){let{slots:a,attrs:n}=t;const{prefixCls:o,direction:r,size:i}=be("card",e),[l,v]=Xn(o),p=m=>m.map((u,h)=>Bt(u)&&!ma(u)||!Bt(u)?s("li",{style:{width:`${100/m.length}%`},key:`action-${h}`},[s("span",null,[u])]):null),g=m=>{var d;(d=e.onTabChange)===null||d===void 0||d.call(e,m)},y=function(){let m=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],d;return m.forEach(u=>{u&&za(u.type)&&u.type.__ANT_CARD_GRID&&(d=!0)}),d};return()=>{var m,d,u,h,w,x;const{headStyle:R={},bodyStyle:L={},loading:O,bordered:b=!0,type:S,tabList:f,hoverable:_,activeTabKey:E,defaultActiveTabKey:I,tabBarExtraContent:F=Oe((m=a.tabBarExtraContent)===null||m===void 0?void 0:m.call(a)),title:oe=Oe((d=a.title)===null||d===void 0?void 0:d.call(a)),extra:le=Oe((u=a.extra)===null||u===void 0?void 0:u.call(a)),actions:ue=Oe((h=a.actions)===null||h===void 0?void 0:h.call(a)),cover:B=Oe((w=a.cover)===null||w===void 0?void 0:w.call(a))}=e,V=jt((x=a.default)===null||x===void 0?void 0:x.call(a)),T=o.value,Z={[`${T}`]:!0,[v.value]:!0,[`${T}-loading`]:O,[`${T}-bordered`]:b,[`${T}-hoverable`]:!!_,[`${T}-contain-grid`]:y(V),[`${T}-contain-tabs`]:f&&f.length,[`${T}-${i.value}`]:i.value,[`${T}-type-${S}`]:!!S,[`${T}-rtl`]:r.value==="rtl"},Q=s(J,{loading:!0,active:!0,paragraph:{rows:4},title:!1},{default:()=>[V]}),ge=E!==void 0,q={size:"large",[ge?"activeKey":"defaultActiveKey"]:ge?E:I,onChange:g,class:`${T}-head-tabs`};let me;const he=f&&f.length?s(Ae,q,{default:()=>[f.map(ve=>{const{tab:Te,slots:we}=ve,Pe=we==null?void 0:we.tab;je(!we,"Card","tabList slots is deprecated, Please use `customTab` instead.");let Be=Te!==void 0?Te:a[Pe]?a[Pe](ve):null;return Be=Ea(a,"customTab",ve,()=>[Be]),s(co,{tab:Be,key:ve.key,disabled:ve.disabled},null)})],rightExtra:F?()=>F:null}):null;(oe||le||he)&&(me=s("div",{class:`${T}-head`,style:R},[s("div",{class:`${T}-head-wrapper`},[oe&&s("div",{class:`${T}-head-title`},[oe]),le&&s("div",{class:`${T}-extra`},[le])]),he]));const Ce=B?s("div",{class:`${T}-cover`},[B]):null,_e=s("div",{class:`${T}-body`,style:L},[O?Q:V]),$e=ue&&ue.length?s("ul",{class:`${T}-actions`},[p(ue)]):null;return l(s("div",W(W({ref:"cardContainerRef"},n),{},{class:[Z,n.class]}),[me,Ce,V&&V.length?_e:null,$e]))}}}),vo=()=>({prefixCls:String,title:at(),description:at(),avatar:at()}),ut=D({compatConfig:{MODE:3},name:"ACardMeta",props:vo(),slots:Object,setup(e,t){let{slots:a}=t;const{prefixCls:n}=be("card",e);return()=>{const o={[`${n.value}-meta`]:!0},r=nt(a,e,"avatar"),i=nt(a,e,"title"),l=nt(a,e,"description"),v=r?s("div",{class:`${n.value}-meta-avatar`},[r]):null,p=i?s("div",{class:`${n.value}-meta-title`},[i]):null,g=l?s("div",{class:`${n.value}-meta-description`},[l]):null,y=p||g?s("div",{class:`${n.value}-meta-detail`},[p,g]):null;return s("div",{class:o},[v,y])}}}),po=()=>({prefixCls:String,hoverable:{type:Boolean,default:!0}}),vt=D({compatConfig:{MODE:3},name:"ACardGrid",__ANT_CARD_GRID:!0,props:po(),setup(e,t){let{slots:a}=t;const{prefixCls:n}=be("card",e),o=H(()=>({[`${n.value}-grid`]:!0,[`${n.value}-grid-hoverable`]:e.hoverable}));return()=>{var r;return s("div",{class:o.value},[(r=a.default)===null||r===void 0?void 0:r.call(a)])}}});xe.Meta=ut;xe.Grid=vt;xe.install=function(e){return e.component(xe.name,xe),e.component(ut.name,ut),e.component(vt.name,vt),e};const Oo=D({__name:"index",setup(e){function t(){Ke.info("How many roads must a man walk down")}function a(){Ke.error({content:"Once upon a time you dressed so fine",duration:2500})}function n(){Ke.warning("How many roads must a man walk down")}function o(){Ke.success("Cause you walked hand in hand With another man in my place")}function r(i){ha[i]({duration:2500,message:"说点啥呢",type:i})}return(i,l)=>(Ca(),xa(N(Na),{description:"支持多语言，主题功能集成切换等",title:"Ant Design Vue组件使用演示"},{default:z(()=>[s(N(xe),{class:"mb-5",title:"按钮"},{default:z(()=>[s(N(ot),null,{default:z(()=>[s(N(te),null,{default:z(()=>l[4]||(l[4]=[ae("Default")])),_:1,__:[4]}),s(N(te),{type:"primary"},{default:z(()=>l[5]||(l[5]=[ae(" Primary ")])),_:1,__:[5]}),s(N(te),null,{default:z(()=>l[6]||(l[6]=[ae(" Info ")])),_:1,__:[6]}),s(N(te),{danger:""},{default:z(()=>l[7]||(l[7]=[ae(" Error ")])),_:1,__:[7]})]),_:1})]),_:1}),s(N(xe),{class:"mb-5",title:"Message"},{default:z(()=>[s(N(ot),null,{default:z(()=>[s(N(te),{onClick:t},{default:z(()=>l[8]||(l[8]=[ae(" 信息 ")])),_:1,__:[8]}),s(N(te),{danger:"",onClick:a},{default:z(()=>l[9]||(l[9]=[ae(" 错误 ")])),_:1,__:[9]}),s(N(te),{onClick:n},{default:z(()=>l[10]||(l[10]=[ae(" 警告 ")])),_:1,__:[10]}),s(N(te),{onClick:o},{default:z(()=>l[11]||(l[11]=[ae(" 成功 ")])),_:1,__:[11]})]),_:1})]),_:1}),s(N(xe),{class:"mb-5",title:"Notification"},{default:z(()=>[s(N(ot),null,{default:z(()=>[s(N(te),{onClick:l[0]||(l[0]=v=>r("info"))},{default:z(()=>l[12]||(l[12]=[ae(" 信息 ")])),_:1,__:[12]}),s(N(te),{danger:"",onClick:l[1]||(l[1]=v=>r("error"))},{default:z(()=>l[13]||(l[13]=[ae(" 错误 ")])),_:1,__:[13]}),s(N(te),{onClick:l[2]||(l[2]=v=>r("warning"))},{default:z(()=>l[14]||(l[14]=[ae(" 警告 ")])),_:1,__:[14]}),s(N(te),{onClick:l[3]||(l[3]=v=>r("success"))},{default:z(()=>l[15]||(l[15]=[ae(" 成功 ")])),_:1,__:[15]})]),_:1})]),_:1})]),_:1}))}});export{Oo as default};
