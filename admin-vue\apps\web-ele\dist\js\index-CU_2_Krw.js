var M=Object.defineProperty,O=Object.defineProperties;var H=Object.getOwnPropertyDescriptors;var T=Object.getOwnPropertySymbols;var J=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable;var G=(t,n,o)=>n in t?M(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o,N=(t,n)=>{for(var o in n||(n={}))J.call(n,o)&&G(t,o,n[o]);if(T)for(var o of T(n))Q.call(n,o)&&G(t,o,n[o]);return t},V=(t,n)=>O(t,H(n));import{ap as W,k as X,l as Y,U as Z,R as D,q as tt,n as E,m as K,y as F,w as ot,aq as et}from"./bootstrap-CYivmKoJ.js";import{u as at}from"./index-DuhtAOZf.js";import{u as nt}from"./use-form-item-iUVikjOD.js";import{u as lt,b as j}from"./use-form-common-props-DZjBwEkr.js";import{e as d,v as st,r as rt,a1 as it,a7 as ct,a8 as ut,d as B,q as y,g as m,D as $,f as x,h as U,F as dt,l as _,u,n as z,B as w,U as bt,a9 as ft,L as vt,y as q}from"../jse/index-index-SSqEGcIT.js";const A=Symbol("buttonGroupContextKey"),mt=(t,n)=>{at({from:"type.text",replacement:"link",version:"3.0.0",scope:"props",ref:"https://element-plus.org/en-US/component/button.html#button-attributes"},d(()=>t.type==="text"));const o=st(A,void 0),l=W("button"),{form:r}=nt(),s=lt(d(()=>o==null?void 0:o.size)),a=j(),b=rt(),f=it(),p=d(()=>{var e;return t.type||(o==null?void 0:o.type)||((e=l.value)==null?void 0:e.type)||""}),g=d(()=>{var e,i,c;return(c=(i=t.autoInsertSpace)!=null?i:(e=l.value)==null?void 0:e.autoInsertSpace)!=null?c:!1}),h=d(()=>{var e,i,c;return(c=(i=t.plain)!=null?i:(e=l.value)==null?void 0:e.plain)!=null?c:!1}),S=d(()=>{var e,i,c;return(c=(i=t.round)!=null?i:(e=l.value)==null?void 0:e.round)!=null?c:!1}),k=d(()=>t.tag==="button"?{ariaDisabled:a.value||t.loading,disabled:a.value||t.loading,autofocus:t.autofocus,type:t.nativeType}:{}),C=d(()=>{var e;const i=(e=f.default)==null?void 0:e.call(f);if(g.value&&(i==null?void 0:i.length)===1){const c=i[0];if((c==null?void 0:c.type)===ct){const L=c.children;return new RegExp("^\\p{Unified_Ideograph}{2}$","u").test(L.trim())}}return!1});return{_disabled:a,_size:s,_type:p,_ref:b,_props:k,_plain:h,_round:S,shouldAddSpace:C,handleClick:e=>{if(a.value||t.loading){e.stopPropagation();return}t.nativeType==="reset"&&(r==null||r.resetFields()),n("click",e)}}},pt=["default","primary","success","warning","info","danger","text",""],gt=["button","submit","reset"],I=X({size:tt,disabled:Boolean,type:{type:String,values:pt,default:""},icon:{type:D},nativeType:{type:String,values:gt,default:"button"},loading:Boolean,loadingIcon:{type:D,default:()=>Z},plain:{type:Boolean,default:void 0},text:Boolean,link:Boolean,bg:Boolean,autofocus:Boolean,round:{type:Boolean,default:void 0},circle:Boolean,color:String,dark:Boolean,autoInsertSpace:{type:Boolean,default:void 0},tag:{type:Y([String,Object]),default:"button"}}),yt={click:t=>t instanceof MouseEvent};function v(t,n=20){return t.mix("#141414",n).toString()}function kt(t){const n=j(),o=E("button");return d(()=>{let l={},r=t.color;if(r){const s=r.match(/var\((.*?)\)/);s&&(r=window.getComputedStyle(window.document.documentElement).getPropertyValue(s[1]));const a=new ut(r),b=t.dark?a.tint(20).toString():v(a,20);if(t.plain)l=o.cssVarBlock({"bg-color":t.dark?v(a,90):a.tint(90).toString(),"text-color":r,"border-color":t.dark?v(a,50):a.tint(50).toString(),"hover-text-color":`var(${o.cssVarName("color-white")})`,"hover-bg-color":r,"hover-border-color":r,"active-bg-color":b,"active-text-color":`var(${o.cssVarName("color-white")})`,"active-border-color":b}),n.value&&(l[o.cssVarBlockName("disabled-bg-color")]=t.dark?v(a,90):a.tint(90).toString(),l[o.cssVarBlockName("disabled-text-color")]=t.dark?v(a,50):a.tint(50).toString(),l[o.cssVarBlockName("disabled-border-color")]=t.dark?v(a,80):a.tint(80).toString());else{const f=t.dark?v(a,30):a.tint(30).toString(),p=a.isDark()?`var(${o.cssVarName("color-white")})`:`var(${o.cssVarName("color-black")})`;if(l=o.cssVarBlock({"bg-color":r,"text-color":p,"border-color":r,"hover-bg-color":f,"hover-text-color":p,"hover-border-color":f,"active-bg-color":b,"active-border-color":b}),n.value){const g=t.dark?v(a,50):a.tint(50).toString();l[o.cssVarBlockName("disabled-bg-color")]=g,l[o.cssVarBlockName("disabled-text-color")]=t.dark?"rgba(255, 255, 255, 0.5)":`var(${o.cssVarName("color-white")})`,l[o.cssVarBlockName("disabled-border-color")]=g}}}return l})}const _t=B({name:"ElButton"}),Bt=B(V(N({},_t),{props:I,emits:yt,setup(t,{expose:n,emit:o}){const l=t,r=kt(l),s=E("button"),{_ref:a,_size:b,_type:f,_disabled:p,_props:g,_plain:h,_round:S,shouldAddSpace:k,handleClick:C}=mt(l,o),P=d(()=>[s.b(),s.m(f.value),s.m(b.value),s.is("disabled",p.value),s.is("loading",l.loading),s.is("plain",h.value),s.is("round",S.value),s.is("circle",l.circle),s.is("text",l.text),s.is("link",l.link),s.is("has-bg",l.bg)]);return n({ref:a,size:b,type:f,disabled:p,shouldAddSpace:k}),(e,i)=>(m(),y(w(e.tag),bt({ref_key:"_ref",ref:a},u(g),{class:u(P),style:u(r),onClick:u(C)}),{default:$(()=>[e.loading?(m(),x(dt,{key:0},[e.$slots.loading?_(e.$slots,"loading",{key:0}):(m(),y(u(F),{key:1,class:z(u(s).is("loading"))},{default:$(()=>[(m(),y(w(e.loadingIcon)))]),_:1},8,["class"]))],64)):e.icon||e.$slots.icon?(m(),y(u(F),{key:1},{default:$(()=>[e.icon?(m(),y(w(e.icon),{key:0})):_(e.$slots,"icon",{key:1})]),_:3})):U("v-if",!0),e.$slots.default?(m(),x("span",{key:2,class:z({[u(s).em("text","expand")]:u(k)})},[_(e.$slots,"default")],2)):U("v-if",!0)]),_:3},16,["class","style","onClick"]))}}));var ht=K(Bt,[["__file","button.vue"]]);const St={size:I.size,type:I.type},Ct=B({name:"ElButtonGroup"}),Nt=B(V(N({},Ct),{props:St,setup(t){const n=t;ft(A,vt({size:q(n,"size"),type:q(n,"type")}));const o=E("button");return(l,r)=>(m(),x("div",{class:z(u(o).b("group"))},[_(l.$slots,"default")],2))}}));var R=K(Nt,[["__file","button-group.vue"]]);const Et=ot(ht,{ButtonGroup:R}),Pt=et(R);export{Et as ElButton,Pt as ElButtonGroup,yt as buttonEmits,A as buttonGroupContextKey,gt as buttonNativeTypes,I as buttonProps,pt as buttonTypes,Et as default};
