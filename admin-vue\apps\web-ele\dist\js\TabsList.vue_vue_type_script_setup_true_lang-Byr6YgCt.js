var f=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable;var r=(s,a)=>{var t={};for(var e in s)b.call(s,e)&&a.indexOf(e)<0&&(t[e]=s[e]);if(s!=null&&f)for(var e of f(s))a.indexOf(e)<0&&v.call(s,e)&&(t[e]=s[e]);return t};import{S as h,M as B,N as C,O as y}from"./bootstrap-CYivmKoJ.js";import{d as c,q as u,g as d,D as i,l as p,S as P,T as $,u as n,e as m,U as _,k as g}from"../jse/index-index-SSqEGcIT.js";const w=c({__name:"Tabs",props:{defaultValue:{},orientation:{},dir:{},activationMode:{},modelValue:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(s,{emit:a}){const l=h(s,a);return(o,k)=>(d(),u(n(B),P($(n(l))),{default:i(()=>[p(o.$slots,"default")]),_:3},16))}}),M=c({__name:"TabsContent",props:{value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(s){const a=s,t=m(()=>{const o=a,{class:e}=o;return r(o,["class"])});return(e,l)=>(d(),u(n(C),_({class:n(g)("ring-offset-background focus-visible:ring-ring mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",a.class)},t.value),{default:i(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}}),V=c({__name:"TabsList",props:{loop:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(s){const a=s,t=m(()=>{const o=a,{class:e}=o;return r(o,["class"])});return(e,l)=>(d(),u(n(y),_(t.value,{class:n(g)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1",a.class)}),{default:i(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}});export{w as _,V as a,M as b};
