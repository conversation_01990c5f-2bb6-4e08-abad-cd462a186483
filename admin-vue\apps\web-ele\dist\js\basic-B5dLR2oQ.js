import{h as d,j as n,E as f}from"./bootstrap-CYivmKoJ.js";import"./css-BPCLfkxC.js";import{E as C}from"./el-card-mWNTD4rl.js";import"./css-Dvul7bza.js";import{u as B}from"./use-drawer-Cga87ueS.js";import{ElButton as r}from"./index-CU_2_Krw.js";import{_ as h}from"./page.vue_vue_type_script_setup_true_lang-B3qu0mwa.js";import{ElCheckbox as k}from"./index-Dlnkk1PI.js";import{d as A,q as x,g as N,D as t,E as a,u as l,G as i,j as p,H as _}from"../jse/index-index-SSqEGcIT.js";import"./index-DuhtAOZf.js";import"./use-form-item-iUVikjOD.js";import"./use-form-common-props-DZjBwEkr.js";import"./index-DIXeP0hR.js";import"./error-CYrjCQ5V.js";import"./isEqual-racMrmQ-.js";const v={class:"flex items-center"},q=A({__name:"basic",setup(P){const[m,c]=d({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",handleSubmit:e=>{f.success(`表单数据：${JSON.stringify(e)}`)},schema:[{component:"IconPicker",fieldName:"icon",label:"IconPicker"},{component:"ApiSelect",componentProps:{afterFetch:e=>e.map(o=>({label:o.name,value:o.path})),api:n},fieldName:"api",label:"ApiSelect"},{component:"ApiTreeSelect",componentProps:{api:n,childrenField:"children",labelField:"name",valueField:"path"},fieldName:"apiTree",label:"ApiTreeSelect"},{component:"Input",fieldName:"string",label:"String"},{component:"InputNumber",fieldName:"number",label:"Number"},{component:"RadioGroup",fieldName:"radio",label:"Radio",componentProps:{options:[{value:"A",label:"A"},{value:"B",label:"B"},{value:"C",label:"C"},{value:"D",label:"D"},{value:"E",label:"E"}]}},{component:"RadioGroup",fieldName:"radioButton",label:"RadioButton",componentProps:{isButton:!0,options:["A","B","C","D","E","F"].map(e=>({value:e,label:`选项${e}`}))}},{component:"CheckboxGroup",fieldName:"checkbox",label:"Checkbox",componentProps:{options:["A","B","C"].map(e=>({value:e,label:`选项${e}`}))}},{component:"CheckboxGroup",fieldName:"checkbox1",label:"Checkbox1",renderComponentContent:()=>({default:()=>["A","B","C","D"].map(e=>_(k,{label:e,value:e}))})},{component:"CheckboxGroup",fieldName:"checkbotton",label:"CheckBotton",componentProps:{isButton:!0,options:[{value:"A",label:"选项A"},{value:"B",label:"选项B"},{value:"C",label:"选项C"}]}},{component:"DatePicker",fieldName:"date",label:"Date"},{component:"Select",fieldName:"select",label:"Select",componentProps:{filterable:!0,options:[{value:"A",label:"选项A"},{value:"B",label:"选项B"},{value:"C",label:"选项C"}]}}]}),[s,u]=B();function b(){c.setValues({string:"string",number:123,radio:"B",radioButton:"C",checkbox:["A","C"],checkbotton:["B","C"],checkbox1:["A","B"],date:new Date,select:"B"})}return(e,o)=>(N(),x(l(h),{description:"我们重新包装了CheckboxGroup、RadioGroup、Select，可以通过options属性传入选项属性数组以自动生成选项",title:"表单演示"},{default:t(()=>[a(l(s),{class:"w-[600px]",title:"基础表单示例"},{default:t(()=>[a(l(m))]),_:1}),a(l(C),null,{header:t(()=>[p("div",v,[o[1]||(o[1]=p("span",{class:"flex-auto"},"基础表单演示",-1)),a(l(r),{type:"primary",onClick:b},{default:t(()=>o[0]||(o[0]=[i("设置表单值")])),_:1,__:[0]})])]),default:t(()=>[a(l(r),{type:"primary",onClick:l(u).open},{default:t(()=>o[2]||(o[2]=[i(" 打开抽屉 ")])),_:1,__:[2]},8,["onClick"])]),_:1})]),_:1}))}});export{q as default};
