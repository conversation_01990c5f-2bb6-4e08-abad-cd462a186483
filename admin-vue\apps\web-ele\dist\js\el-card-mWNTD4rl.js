var C=Object.defineProperty,S=Object.defineProperties;var c=Object.getOwnPropertyDescriptors;var n=Object.getOwnPropertySymbols;var v=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable;var i=(a,s,e)=>s in a?C(a,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[s]=e,f=(a,s)=>{for(var e in s||(s={}))v.call(s,e)&&i(a,e,s[e]);if(n)for(var e of n(s))g.call(s,e)&&i(a,e,s[e]);return a},p=(a,s)=>S(a,c(s));import{k as b,l as w,m as $,n as k,w as E}from"./bootstrap-CYivmKoJ.js";import{d as m,f as t,g as l,h as y,j as N,n as o,u as r,l as d,G as u,t as h,m as B}from"../jse/index-index-SSqEGcIT.js";const P=b({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:w([String,Object,Array]),default:""},headerClass:String,bodyClass:String,footerClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),V=m({name:"ElCard"}),j=m(p(f({},V),{props:P,setup(a){const s=k("card");return(e,T)=>(l(),t("div",{class:o([r(s).b(),r(s).is(`${e.shadow}-shadow`)])},[e.$slots.header||e.header?(l(),t("div",{key:0,class:o([r(s).e("header"),e.headerClass])},[d(e.$slots,"header",{},()=>[u(h(e.header),1)])],2)):y("v-if",!0),N("div",{class:o([r(s).e("body"),e.bodyClass]),style:B(e.bodyStyle)},[d(e.$slots,"default")],6),e.$slots.footer||e.footer?(l(),t("div",{key:1,class:o([r(s).e("footer"),e.footerClass])},[d(e.$slots,"footer",{},()=>[u(h(e.footer),1)])],2)):y("v-if",!0)],2))}}));var z=$(j,[["__file","card.vue"]]);const I=E(z);export{I as E};
