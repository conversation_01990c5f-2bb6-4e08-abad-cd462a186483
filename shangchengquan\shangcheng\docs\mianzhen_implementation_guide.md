# 面诊和综合诊疗功能实现指南

## 概述

本文档描述了在现有舌诊系统基础上扩展面诊和综合诊疗功能的完整实现方案。该扩展充分利用了阿里云API的多图片分析能力，为用户提供更全面的健康分析服务。

## 功能特性

### 1. 诊疗类型支持
- **舌诊（原有功能）**：通过舌头图片分析体质状况
- **面诊（新增功能）**：通过面部图片分析健康状况
- **综合诊疗（新增功能）**：结合舌诊、面诊、舌下脉络多维度分析

### 2. 技术架构
- **前端**：UniApp框架，支持多图片上传和预览
- **后端**：PHP ThinkPHP框架，扩展现有API接口
- **数据库**：MySQL，向后兼容现有数据结构
- **第三方服务**：阿里云智能诊疗API

## 实现内容

### 1. 数据库扩展
**文件位置**：`shangchengquan/shangcheng/docs/mianzhen_upgrade.sql`

**主要变更**：
- 扩展 `ddwx_shezhen_record` 表，增加面部图片、舌下脉络图片字段
- 增加 `diagnosis_type` 字段区分诊疗类型（1=舌诊，2=面诊，3=综合诊疗）
- 扩展配置表支持面诊和综合诊疗的价格、免费次数等设置
- 创建面诊特征配置表，支持自定义分析规则

### 2. 后端API扩展
**文件位置**：
- `shangchengquan/shangcheng/app/common/SheZhen.php`
- `shangchengquan/shangcheng/app/controller/ApiSheZhen.php`
- `shangchengquan/shangcheng/app/controller/SheZhen.php`

**主要功能**：
- 扩展 `callDetectApi` 方法支持多图片和诊疗类型参数
- 新增图片验证方法，根据诊疗类型验证必需图片
- 扩展配置获取方法，支持不同诊疗类型的配置
- 新增面诊和综合诊疗记录管理方法

### 3. 前端页面开发
**文件位置**：
- `tiantianshande/pagesB/diagnosis/face/index.vue`
- `tiantianshande/pagesB/diagnosis/comprehensive/index.vue`

**主要特性**：
- 现代化UI设计，支持多图片上传预览
- 智能拍照指导，提供拍摄建议
- 实时价格显示，支持免费次数查询
- 渐进式加载，优化用户体验

### 4. 菜单权限配置
**文件位置**：`shangchengquan/shangcheng/app/common/Menu.php`

**变更内容**：
- 将"舌诊模块"重命名为"智能诊疗"
- 增加面诊记录和综合诊疗记录菜单项
- 更新权限控制，支持细粒度访问控制

## 使用说明

### 1. 数据库升级
```sql
-- 执行数据库升级脚本
source shangchengquan/shangcheng/docs/mianzhen_upgrade.sql;
```

### 2. 后台配置
1. 登录后台管理系统
2. 进入"智能诊疗" -> "诊疗设置"
3. 配置面诊和综合诊疗的价格、免费次数等参数
4. 设置会员等级免费权限

### 3. 前端访问
- **面诊页面**：`/pagesB/diagnosis/face/index`
- **综合诊疗页面**：`/pagesB/diagnosis/comprehensive/index`

### 4. API接口调用
```javascript
// 获取配置信息
const config = await this.$api.post('/ApiSheZhen/getConfig', {
    diagnosis_type: 2 // 1=舌诊，2=面诊，3=综合诊疗
});

// 开始分析
const result = await this.$api.post('/ApiSheZhen/analyze', {
    diagnosis_type: 2,
    face_image_url: 'https://example.com/face.jpg',
    use_free: 1
});
```

## 兼容性说明

### 1. 向后兼容
- 现有舌诊功能完全保持不变
- 现有数据结构和API接口保持兼容
- 用户无需重新配置即可使用原有功能

### 2. 数据迁移
- 现有舌诊记录自动标记为诊疗类型1（舌诊）
- 现有配置自动应用到舌诊功能
- 无需手动数据迁移

## 扩展建议

### 1. 功能增强
- 增加诊疗结果对比功能
- 支持历史记录趋势分析
- 增加健康建议推送

### 2. 性能优化
- 图片压缩和CDN加速
- API调用缓存机制
- 数据库查询优化

### 3. 用户体验
- 增加拍照指导动画
- 支持多语言界面
- 增加语音播报功能

## 技术支持

如有技术问题，请参考：
1. 阿里云API文档：`shangchengquan/shangcheng/docs/shezhen.md`
2. 数据库结构：`shangchengquan/shangcheng/docs/mianzhen_upgrade.sql`
3. 前端组件文档：各页面注释说明

## 更新日志

**2025-01-17**
- 初始版本发布
- 支持面诊和综合诊疗功能
- 完成数据库扩展和API接口开发
- 完成前端页面开发和菜单配置