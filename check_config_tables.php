<?php
/**
 * 检查舌诊配置表情况
 */

// 数据库配置
$host = 'localhost';
$username = 'root';
$password = 'root123';
$database = 'qixian_zhongheng';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>舌诊配置表检查</h2>";
    
    // 检查 sysset 表中的 mianzhen_set 配置
    echo "<h3>1. 检查 sysset 表中的 mianzhen_set 配置</h3>";
    $stmt = $pdo->prepare("SELECT * FROM ddwx_sysset WHERE name = 'mianzhen_set'");
    $stmt->execute();
    $mianzhenConfig = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($mianzhenConfig) {
        echo "<p style='color: green;'>✓ 找到 mianzhen_set 配置</p>";
        echo "<pre>" . htmlspecialchars($mianzhenConfig['value']) . "</pre>";
        
        $config = json_decode($mianzhenConfig['value'], true);
        if ($config) {
            echo "<h4>配置解析：</h4>";
            echo "<ul>";
            echo "<li>aliyun_app_code: " . ($config['aliyun_app_code'] ?? '未设置') . "</li>";
            echo "<li>aliyun_endpoint: " . ($config['aliyun_endpoint'] ?? '未设置') . "</li>";
            echo "<li>is_enable: " . ($config['is_enable'] ?? '未设置') . "</li>";
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>✗ 未找到 mianzhen_set 配置</p>";
    }
    
    // 检查 shezhen_set 表
    echo "<h3>2. 检查 shezhen_set 表</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'ddwx_shezhen_set'");
    $stmt->execute();
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "<p style='color: green;'>✓ ddwx_shezhen_set 表存在</p>";
        
        // 查询表中的数据
        $stmt = $pdo->prepare("SELECT * FROM ddwx_shezhen_set LIMIT 5");
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($records) {
            echo "<p>表中有 " . count($records) . " 条记录（显示前5条）：</p>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr>";
            foreach (array_keys($records[0]) as $column) {
                echo "<th>$column</th>";
            }
            echo "</tr>";
            
            foreach ($records as $record) {
                echo "<tr>";
                foreach ($record as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠ 表存在但无数据</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ ddwx_shezhen_set 表不存在</p>";
    }
    
    // 检查其他相关表
    echo "<h3>3. 检查其他相关表</h3>";
    $tables = ['ddwx_mianzhen_call_log', 'ddwx_mianzhen_sessions', 'ddwx_mianzhen_recharge'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE '$table'");
        $stmt->execute();
        $exists = $stmt->fetch();
        
        if ($exists) {
            echo "<p style='color: green;'>✓ $table 表存在</p>";
        } else {
            echo "<p style='color: red;'>✗ $table 表不存在</p>";
        }
    }
    
    echo "<h3>4. 建议解决方案</h3>";
    echo "<ol>";
    echo "<li>如果使用原来的配置方式，需要确保 sysset 表中有 mianzhen_set 配置</li>";
    echo "<li>如果使用新的配置方式，需要确保 shezhen_set 表存在并有对应的配置数据</li>";
    echo "<li>建议统一配置获取方式，避免混乱</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>数据库连接失败: " . $e->getMessage() . "</p>";
}
?>
