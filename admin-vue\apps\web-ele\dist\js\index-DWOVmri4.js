import{k as A,n as C,o as h,P as L,l as d,w as O}from"./bootstrap-CYivmKoJ.js";import{d as $,e as g,H as V,l as I,r as w,V as k,i as m,b as E,P as G,E as v,G as R,C as z}from"../jse/index-index-SSqEGcIT.js";import{P as c,i as N,a as Y}from"./vnode-ih70IEYb.js";const _=A({prefixCls:{type:String}}),x=$({name:"ElSpaceItem",props:_,setup(e,{slots:u}){const p=C("space"),i=g(()=>`${e.prefixCls||p.b()}__item`);return()=>V("div",{class:i.value},I(u,"default"))}}),T={small:8,default:12,large:16};function j(e){const u=C("space"),p=g(()=>[u.b(),u.m(e.direction),e.class]),i=w(0),o=w(0),y=g(()=>{const l=e.wrap||e.fill?{flexWrap:"wrap"}:{},a={alignItems:e.alignment},s={rowGap:`${o.value}px`,columnGap:`${i.value}px`};return[l,a,s,e.style]}),f=g(()=>e.fill?{flexGrow:1,minWidth:`${e.fillRatio}%`}:{});return k(()=>{const{size:l="small",wrap:a,direction:s,fill:t}=e;if(m(l)){const[r=0,n=0]=l;i.value=r,o.value=n}else{let r;h(l)?r=l:r=T[l||"small"]||T.small,(a||t)&&s==="horizontal"?i.value=o.value=r:s==="horizontal"?(i.value=r,o.value=0):(o.value=r,i.value=0)}}),{classes:p,containerStyle:y,itemStyle:f}}const B=A({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},class:{type:d([String,Object,Array]),default:""},style:{type:d([String,Array,Object]),default:""},alignment:{type:d(String),default:"center"},prefixCls:{type:String},spacer:{type:d([Object,String,Number,Array]),default:null,validator:e=>E(e)||h(e)||G(e)},wrap:Boolean,fill:Boolean,fillRatio:{type:Number,default:100},size:{type:[String,Array,Number],values:L,validator:e=>h(e)||m(e)&&e.length===2&&e.every(h)}}),F=$({name:"ElSpace",props:B,setup(e,{slots:u}){const{classes:p,containerStyle:i,itemStyle:o}=j(e);function y(f,l="",a=[]){const{prefixCls:s}=e;return f.forEach((t,r)=>{N(t)?m(t.children)&&t.children.forEach((n,S)=>{N(n)&&m(n.children)?y(n.children,`${l+S}-`,a):E(n)&&(n==null?void 0:n.type)===z?a.push(n):a.push(v(x,{style:o.value,prefixCls:s,key:`nested-${l+S}`},{default:()=>[n]},c.PROPS|c.STYLE,["style","prefixCls"]))}):Y(t)?a.push(v(x,{style:o.value,prefixCls:s,key:`LoopKey${l+r}`},{default:()=>[t]},c.PROPS|c.STYLE,["style","prefixCls"])):E(t)&&t.type===z&&a.push(t)}),a}return()=>{var f;const{spacer:l,direction:a}=e,s=I(u,"default",{key:0},()=>[]);if(((f=s.children)!=null?f:[]).length===0)return null;if(m(s.children)){let t=y(s.children);if(l){const r=t.length-1;t=t.reduce((n,S,P)=>{const b=[...n,S];return P!==r&&b.push(v("span",{style:[o.value,a==="vertical"?"width: 100%":null],key:P},[E(l)?l:R(l,c.TEXT)],c.STYLE)),b},[])}return v("div",{class:p.value,style:i.value},t,c.STYLE|c.CLASS)}return s.children}}}),X=O(F);export{X as ElSpace,X as default,_ as spaceItemProps,B as spaceProps,j as useSpace};
