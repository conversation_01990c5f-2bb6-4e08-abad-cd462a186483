import{D as v}from"./bootstrap-CYivmKoJ.js";import{e as n,x as f,r as c,v as r,u as i}from"../jse/index-index-SSqEGcIT.js";const m=Symbol("formContextKey"),b=Symbol("formItemContextKey"),u=s=>{const o=f();return n(()=>{var e,t;return(t=(e=o==null?void 0:o.proxy)==null?void 0:e.$props)==null?void 0:t[s]})},z=(s,o={})=>{const e=c(void 0),t=o.prop?e:u("size"),d=o.global?e:v(),l=o.form?{size:void 0}:r(m,void 0),a=o.formItem?{size:void 0}:r(b,void 0);return n(()=>t.value||i(s)||(a==null?void 0:a.size)||(l==null?void 0:l.size)||d.value||"")},x=s=>{const o=u("disabled"),e=r(m,void 0);return n(()=>o.value||i(s)||(e==null?void 0:e.disabled)||!1)};export{b as a,x as b,m as f,z as u};
