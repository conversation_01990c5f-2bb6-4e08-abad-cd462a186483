var K=Object.defineProperty;var p=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var F=(t,e,s)=>e in t?K(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,E=(t,e)=>{for(var s in e||(e={}))j.call(e,s)&&F(t,s,e[s]);if(p)for(var s of p(e))N.call(e,s)&&F(t,s,e[s]);return t};var I=(t,e)=>{var s={};for(var a in t)j.call(t,a)&&e.indexOf(a)<0&&(s[a]=t[a]);if(t!=null&&p)for(var a of p(t))e.indexOf(a)<0&&N.call(t,a)&&(s[a]=t[a]);return s};import{ah as Q,ai as W,aj as X,ak as Y,al as Z,am as aa,an as ea}from"./bootstrap-CYivmKoJ.js";import{_ as ta}from"./analytics-trends.vue_vue_type_script_setup_true_lang-D3EvFbSI.js";import{_ as sa}from"./analytics-visits-data.vue_vue_type_script_setup_true_lang-DvVWEyyb.js";import{_ as la}from"./analytics-visits-sales.vue_vue_type_script_setup_true_lang-Bq0j3fdE.js";import{_ as na}from"./analytics-visits-source.vue_vue_type_script_setup_true_lang-CwxyV5ho.js";import{_ as oa}from"./analytics-visits.vue_vue_type_script_setup_true_lang-CcJuVryM.js";import{_ as ra,a as ia,b as ca,c as ua,d as h}from"./analysis-chart-card.vue_vue_type_script_setup_true_lang-BJVZfLOK.js";import{d as f,f as d,g as c,n as da,u as l,k as D,l as w,e as V,q as v,D as r,U as fa,r as P,a4 as z,V as _a,w as ma,o as pa,a5 as va,a6 as ga,m as ba,t as g,E as n,F as $,R as y,G as O,j as q}from"../jse/index-index-SSqEGcIT.js";import{_ as xa,a as ha,b as $a}from"./TabsList.vue_vue_type_script_setup_true_lang-Byr6YgCt.js";import"./use-echarts-Bu2i2yR9.js";const ya=f({__name:"CardFooter",props:{class:{}},setup(t){const e=t;return(s,a)=>(c(),d("div",{class:da(l(D)("flex items-center p-6 pt-0",e.class))},[w(s.$slots,"default")],2))}}),wa=f({__name:"TabsTrigger",props:{value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(t){const e=t,s=V(()=>{const m=e,{class:i}=m;return I(m,["class"])}),a=Q(s);return(i,o)=>(c(),v(l(W),fa(l(a),{class:l(D)("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",e.class)}),{default:r(()=>[w(i.$slots,"default")]),_:3},16,["class"]))}}),A=f({name:"CountToAnimator",__name:"count-to-animator",props:{autoplay:{type:Boolean,default:!0},color:{default:""},decimal:{default:"."},decimals:{default:0},duration:{default:1500},endVal:{default:2021},prefix:{default:""},separator:{default:","},startVal:{default:0},suffix:{default:""},transition:{default:"linear"},useEasing:{type:Boolean,default:!0}},emits:["finished","onFinished","onStarted","started"],setup(t,{expose:e,emit:s}){const a=t,i=s,o=P(a.startVal),m=P(!1);let T=z(o);const G=V(()=>M(l(T)));_a(()=>{o.value=a.startVal}),ma([()=>a.startVal,()=>a.endVal],()=>{a.autoplay&&k()}),pa(()=>{a.autoplay&&k()});function k(){S(),o.value=a.endVal}function L(){o.value=a.startVal,S()}function S(){T=z(o,E({disabled:m,duration:a.duration,onFinished:()=>{i("finished"),i("onFinished")},onStarted:()=>{i("started"),i("onStarted")}},a.useEasing?{transition:va[a.transition]}:{}))}function M(u){if(!u&&u!==0)return"";const{decimal:B,decimals:R,prefix:U,separator:b,suffix:H}=a;u=Number(u).toFixed(R),u+="";const x=u.split(".");let _=x[0];const J=x.length>1?B+x[1]:"",C=/(\d+)(\d{3})/;if(b&&!ga(b)&&_)for(;C.test(_);)_=_.replace(C,`$1${b}$2`);return U+_+J+H}return e({reset:L}),(u,B)=>(c(),d("span",{style:ba({color:u.color})},g(G.value),5))}}),Va={class:"card-box w-full px-4 pb-5 pt-3"},Ta=f({name:"AnalysisChartsTabs",__name:"analysis-charts-tabs",props:{tabs:{default:()=>[]}},setup(t){const e=t,s=V(()=>{var a,i;return(i=(a=e.tabs)==null?void 0:a[0])==null?void 0:i.value});return(a,i)=>(c(),d("div",Va,[n(l(xa),{"default-value":s.value},{default:r(()=>[n(l(ha),null,{default:r(()=>[(c(!0),d($,null,y(a.tabs,o=>(c(),v(l(wa),{key:o.label,value:o.value},{default:r(()=>[O(g(o.label),1)]),_:2},1032,["value"]))),128))]),_:1}),(c(!0),d($,null,y(a.tabs,o=>(c(),v(l($a),{key:o.label,value:o.value,class:"pt-4"},{default:r(()=>[w(a.$slots,o.value)]),_:2},1032,["value"]))),128))]),_:3},8,["default-value"])]))}}),ka={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"},Sa=f({name:"AnalysisOverview",__name:"analysis-overview",props:{items:{default:()=>[]}},setup(t){return(e,s)=>(c(),d("div",ka,[(c(!0),d($,null,y(e.items,a=>(c(),v(l(ua),{key:a.title,title:a.title,class:"w-full"},{default:r(()=>[n(l(ra),null,{default:r(()=>[n(l(ia),{class:"text-xl"},{default:r(()=>[O(g(a.title),1)]),_:2},1024)]),_:2},1024),n(l(ca),{class:"flex items-center justify-between"},{default:r(()=>[n(l(A),{"end-val":a.value,"start-val":1,class:"text-xl",prefix:""},null,8,["end-val"]),n(l(X),{icon:a.icon,class:"size-8 flex-shrink-0"},null,8,["icon"])]),_:2},1024),n(l(ya),{class:"justify-between"},{default:r(()=>[q("span",null,g(a.totalTitle),1),n(l(A),{"end-val":a.totalValue,"start-val":1,prefix:""},null,8,["end-val"])]),_:2},1024)]),_:2},1032,["title"]))),128))]))}}),Ba={class:"p-5"},Ca={class:"mt-5 w-full md:flex"},Ga=f({__name:"index",setup(t){const e=[{icon:Y,title:"用户量",totalTitle:"总用户量",totalValue:12e4,value:2e3},{icon:Z,title:"访问量",totalTitle:"总访问量",totalValue:5e5,value:2e4},{icon:aa,title:"下载量",totalTitle:"总下载量",totalValue:12e4,value:8e3},{icon:ea,title:"使用量",totalTitle:"总使用量",totalValue:5e4,value:5e3}],s=[{label:"流量趋势",value:"trends"},{label:"月访问量",value:"visits"}];return(a,i)=>(c(),d("div",Ba,[n(l(Sa),{items:e}),n(l(Ta),{tabs:s,class:"mt-5"},{trends:r(()=>[n(ta)]),visits:r(()=>[n(oa)]),_:1}),q("div",Ca,[n(l(h),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问数量"},{default:r(()=>[n(sa)]),_:1}),n(l(h),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(na)]),_:1}),n(l(h),{class:"mt-5 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(la)]),_:1})])]))}});export{Ga as default};
