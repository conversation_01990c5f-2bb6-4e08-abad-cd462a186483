import{k as ce,m as $e,_ as p,r as k,E as de,F as te,a as pe,b as K,n as Ie,P as E,G as Oe,e as Pe,v as De,o as fe,j as ee,K as z,H as Te,t as Me,J as ze,f as Ee,L as Fe}from"./bootstrap-BmSDnAET.js";import{T as Ne}from"./Trigger-ktfUwTEB.js";import{M as Le,a as J,u as Be}from"./index-qN2epJy7.js";import{a4 as j,a5 as V,Y as me,az as ge,x as m,ba as ne,al as oe,R as Ae,J as A,P as q,T as je,ao as Re,aF as _e,_ as ie,p as He,n as Xe}from"../jse/index-index-BAMHRxBA.js";import{B as Ve}from"./BaseInput-2dRewYGd.js";import{u as Ge,F as Ke}from"./FormItemContext-C6FSbIjX.js";import{g as We,a as ae}from"./statusUtils-D4Q5DWpU.js";import{i as Ue,g as Ye,a as Je,b as qe,e as Qe,d as Ze}from"./index-DGJlXu39.js";import"./vnode-DOOZPaKV.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./shallowequal-BAoEuXbT.js";import"./Overflow-PjgTLHBy.js";import"./index-B-vFSvKB.js";import"./index-DKSwBpyj.js";import"./colors-XnAMBo4s.js";import"./collapseMotion-DF3grfIf.js";import"./slide-DoU4o8T0.js";import"./collapse-BbEVqHco.js";function ke(e,o,i){var t=i||{},n=t.noTrailing,d=n===void 0?!1:n,s=t.noLeading,l=s===void 0?!1:s,c=t.debounceMode,a=c===void 0?void 0:c,f,P=!1,S=0;function g(){f&&clearTimeout(f)}function y(b){var $=b||{},w=$.upcomingOnly,T=w===void 0?!1:w;g(),P=!T}function C(){for(var b=arguments.length,$=new Array(b),w=0;w<b;w++)$[w]=arguments[w];var T=this,x=Date.now()-S;if(P)return;function D(){S=Date.now(),o.apply(T,$)}function I(){f=void 0}!l&&a&&!f&&D(),g(),a===void 0&&x>e?l?(S=Date.now(),d||(f=setTimeout(a?I:D,e))):D():d!==!0&&(f=setTimeout(a?I:D,a===void 0?e-x:e))}return C.cancel=y,C}function et(e,o,i){var t={},n=t.atBegin,d=n===void 0?!1:n;return ke(e,o,{debounceMode:d!==!1})}const tt=new de("antSpinMove",{to:{opacity:1}}),nt=new de("antRotate",{to:{transform:"rotate(405deg)"}}),ot=e=>({[`${e.componentCls}`]:p(p({},k(e)),{position:"absolute",display:"none",color:e.colorPrimary,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"static",display:"inline-block",opacity:1},"&-nested-loading":{position:"relative",[`> div > ${e.componentCls}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${e.componentCls}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:-e.spinDotSize/2},[`${e.componentCls}-text`]:{position:"absolute",top:"50%",width:"100%",paddingTop:(e.spinDotSize-e.fontSize)/2+2,textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSize/2)-10},"&-sm":{[`${e.componentCls}-dot`]:{margin:-e.spinDotSizeSM/2},[`${e.componentCls}-text`]:{paddingTop:(e.spinDotSizeSM-e.fontSize)/2+2},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSizeSM/2)-10}},"&-lg":{[`${e.componentCls}-dot`]:{margin:-(e.spinDotSizeLG/2)},[`${e.componentCls}-text`]:{paddingTop:(e.spinDotSizeLG-e.fontSize)/2+2},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSizeLG/2)-10}}},[`${e.componentCls}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${e.componentCls}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${e.componentCls}-dot`]:{position:"relative",display:"inline-block",fontSize:e.spinDotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:(e.spinDotSize-e.marginXXS/2)/2,height:(e.spinDotSize-e.marginXXS/2)/2,backgroundColor:e.colorPrimary,borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:tt,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:nt,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&-sm ${e.componentCls}-dot`]:{fontSize:e.spinDotSizeSM,i:{width:(e.spinDotSizeSM-e.marginXXS/2)/2,height:(e.spinDotSizeSM-e.marginXXS/2)/2}},[`&-lg ${e.componentCls}-dot`]:{fontSize:e.spinDotSizeLG,i:{width:(e.spinDotSizeLG-e.marginXXS)/2,height:(e.spinDotSizeLG-e.marginXXS)/2}},[`&${e.componentCls}-show-text ${e.componentCls}-text`]:{display:"block"}})}),it=ce("Spin",e=>{const o=$e(e,{spinDotDefault:e.colorTextDescription,spinDotSize:e.controlHeightLG/2,spinDotSizeSM:e.controlHeightLG*.35,spinDotSizeLG:e.controlHeight});return[ot(o)]},{contentHeight:400});var at=function(e,o){var i={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(i[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(i[t[n]]=e[t[n]]);return i};const rt=()=>({prefixCls:String,spinning:{type:Boolean,default:void 0},size:String,wrapperClassName:String,tip:E.any,delay:Number,indicator:E.any});let Y=null;function st(e,o){return!!e&&!!o&&!isNaN(Number(o))}function lt(e){const o=e.indicator;Y=typeof o=="function"?o:()=>m(o,null,null)}const G=j({compatConfig:{MODE:3},name:"ASpin",inheritAttrs:!1,props:te(rt(),{size:"default",spinning:!0,wrapperClassName:""}),setup(e,o){let{attrs:i,slots:t}=o;const{prefixCls:n,size:d,direction:s}=pe("spin",e),[l,c]=it(n),a=V(e.spinning&&!st(e.spinning,e.delay));let f;return me([()=>e.spinning,()=>e.delay],()=>{f==null||f.cancel(),f=et(e.delay,()=>{a.value=e.spinning}),f==null||f()},{immediate:!0,flush:"post"}),ge(()=>{f==null||f.cancel()}),()=>{var P,S;const{class:g}=i,y=at(i,["class"]),{tip:C=(P=t.tip)===null||P===void 0?void 0:P.call(t)}=e,b=(S=t.default)===null||S===void 0?void 0:S.call(t),$={[c.value]:!0,[n.value]:!0,[`${n.value}-sm`]:d.value==="small",[`${n.value}-lg`]:d.value==="large",[`${n.value}-spinning`]:a.value,[`${n.value}-show-text`]:!!C,[`${n.value}-rtl`]:s.value==="rtl",[g]:!!g};function w(x){const D=`${x}-dot`;let I=Oe(t,e,"indicator");return I===null?null:(Array.isArray(I)&&(I=I.length===1?I[0]:I),ne(I)?oe(I,{class:D}):Y&&ne(Y())?oe(Y(),{class:D}):m("span",{class:`${D} ${x}-dot-spin`},[m("i",{class:`${x}-dot-item`},null),m("i",{class:`${x}-dot-item`},null),m("i",{class:`${x}-dot-item`},null),m("i",{class:`${x}-dot-item`},null)]))}const T=m("div",K(K({},y),{},{class:$,"aria-live":"polite","aria-busy":a.value}),[w(n.value),C?m("div",{class:`${n.value}-text`},[C]):null]);if(b&&Ie(b).length){const x={[`${n.value}-container`]:!0,[`${n.value}-blur`]:a.value};return l(m("div",{class:[`${n.value}-nested-loading`,e.wrapperClassName,c.value]},[a.value&&m("div",{key:"loading"},[T]),m("div",{class:x,key:"container"},[b])]))}return l(T)}}});G.setDefaultIndicator=lt;G.install=function(e){return e.component(G.name,G),e};function ut(e){const{selectionStart:o}=e;return e.value.slice(0,o)}function ct(e){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return(Array.isArray(o)?o:[o]).reduce((t,n)=>{const d=e.lastIndexOf(n);return d>t.location?{location:d,prefix:n}:t},{location:-1,prefix:""})}function re(e){return(e||"").toLowerCase()}function dt(e,o,i){const t=e[0];if(!t||t===i)return e;let n=e;const d=o.length;for(let s=0;s<d;s+=1)if(re(n[s])!==re(o[s])){n=n.slice(s);break}else s===d-1&&(n=n.slice(d));return n}function pt(e,o){const{measureLocation:i,prefix:t,targetText:n,selectionStart:d,split:s}=o;let l=e.slice(0,i);l[l.length-s.length]===s&&(l=l.slice(0,l.length-s.length)),l&&(l=`${l}${s}`);let c=dt(e.slice(d),n.slice(d-i-t.length),s);c.slice(0,s.length)===s&&(c=c.slice(s.length));const a=`${l}${t}${n}${s}`;return{text:`${a}${c}`,selectionLocation:a.length}}function ft(e,o){e.setSelectionRange(o,o),e.blur(),e.focus()}function mt(e,o){const{split:i}=o;return!i||e.indexOf(i)===-1}function gt(e,o){let{value:i=""}=o;const t=e.toLowerCase();return i.toLowerCase().indexOf(t)!==-1}const ve=Symbol("MentionsContextKey");function vt(){}const ht=j({compatConfig:{MODE:3},name:"DropdownMenu",props:{prefixCls:String,options:{type:Array,default:()=>[]}},setup(e,o){let{slots:i}=o;const{activeIndex:t,setActiveIndex:n,selectOption:d,onFocus:s=vt,loading:l}=Ae(ve,{activeIndex:V(),loading:V(!1)});let c;const a=f=>{clearTimeout(c),c=setTimeout(()=>{s(f)})};return ge(()=>{clearTimeout(c)}),()=>{var f;const{prefixCls:P,options:S}=e,g=S[t.value]||{};return m(Le,{prefixCls:`${P}-menu`,activeKey:g.value,onSelect:y=>{let{key:C}=y;const b=S.find($=>{let{value:w}=$;return w===C});d(b)},onMousedown:a},{default:()=>[!l.value&&S.map((y,C)=>{var b,$;const{value:w,disabled:T,label:x=y.value,class:D,style:I}=y;return m(J,{key:w,disabled:T,onMouseenter:()=>{n(C)},class:D,style:I},{default:()=>[($=(b=i.option)===null||b===void 0?void 0:b.call(i,y))!==null&&$!==void 0?$:typeof x=="function"?x(y):x]})}),!l.value&&S.length===0?m(J,{key:"notFoundContent",disabled:!0},{default:()=>[(f=i.notFoundContent)===null||f===void 0?void 0:f.call(i)]}):null,l.value&&m(J,{key:"loading",disabled:!0},{default:()=>[m(G,{size:"small"},null)]})]})}}}),St={bottomRight:{points:["tl","br"],offset:[0,4],overflow:{adjustX:0,adjustY:1}},bottomLeft:{points:["tr","bl"],offset:[0,4],overflow:{adjustX:0,adjustY:1}},topRight:{points:["bl","tr"],offset:[0,-4],overflow:{adjustX:0,adjustY:1}},topLeft:{points:["br","tl"],offset:[0,-4],overflow:{adjustX:0,adjustY:1}}},bt=j({compatConfig:{MODE:3},name:"KeywordTrigger",props:{loading:{type:Boolean,default:void 0},options:{type:Array,default:()=>[]},prefixCls:String,placement:String,visible:{type:Boolean,default:void 0},transitionName:String,getPopupContainer:Function,direction:String,dropdownClassName:String},setup(e,o){let{slots:i}=o;const t=()=>`${e.prefixCls}-dropdown`,n=()=>{const{options:s}=e;return m(ht,{prefixCls:t(),options:s},{notFoundContent:i.notFoundContent,option:i.option})},d=A(()=>{const{placement:s,direction:l}=e;let c="topRight";return l==="rtl"?c=s==="top"?"topLeft":"bottomLeft":c=s==="top"?"topRight":"bottomRight",c});return()=>{const{visible:s,transitionName:l,getPopupContainer:c}=e;return m(Ne,{prefixCls:t(),popupVisible:s,popup:n(),popupClassName:e.dropdownClassName,popupPlacement:d.value,popupTransitionName:l,builtinPlacements:St,getPopupContainer:c},{default:i.default})}}}),xt=De("top","bottom"),he={autofocus:{type:Boolean,default:void 0},prefix:E.oneOfType([E.string,E.arrayOf(E.string)]),prefixCls:String,value:String,disabled:{type:Boolean,default:void 0},split:String,transitionName:String,placement:E.oneOf(xt),character:E.any,characterRender:Function,filterOption:{type:[Boolean,Function]},validateSearch:Function,getPopupContainer:{type:Function},options:Pe(),loading:{type:Boolean,default:void 0},rows:[Number,String],direction:{type:String}},Se=p(p({},he),{dropdownClassName:String}),be={prefix:"@",split:" ",rows:1,validateSearch:mt,filterOption:()=>gt};te(Se,be);var se=function(e,o){var i={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(i[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(i[t[n]]=e[t[n]]);return i};function le(){}const yt=j({compatConfig:{MODE:3},name:"Mentions",inheritAttrs:!1,props:te(Se,be),emits:["change","select","search","focus","blur","pressenter"],setup(e,o){let{emit:i,attrs:t,expose:n,slots:d}=o;const s=q(null),l=q(null),c=q(),a=je({value:e.value||"",measuring:!1,measureLocation:0,measureText:null,measurePrefix:"",activeIndex:0,isFocus:!1});Re(()=>{a.value=e.value});const f=u=>{i("change",u)},P=u=>{let{target:{value:r}}=u;f(r)},S=(u,r,v)=>{p(a,{measuring:!0,measureText:u,measurePrefix:r,measureLocation:v,activeIndex:0})},g=u=>{p(a,{measuring:!1,measureLocation:0,measureText:null}),u==null||u()},y=u=>{const{which:r}=u;if(a.measuring){if(r===z.UP||r===z.DOWN){const v=R.value.length,h=r===z.UP?-1:1,O=(a.activeIndex+h+v)%v;a.activeIndex=O,u.preventDefault()}else if(r===z.ESC)g();else if(r===z.ENTER){if(u.preventDefault(),!R.value.length){g();return}const v=R.value[a.activeIndex];D(v)}}},C=u=>{const{key:r,which:v}=u,{measureText:h,measuring:O}=a,{prefix:W,validateSearch:N}=e,_=u.target;if(_.composing)return;const L=ut(_),{location:B,prefix:F}=ct(L,W);if([z.ESC,z.UP,z.DOWN,z.ENTER].indexOf(v)===-1)if(B!==-1){const M=L.slice(B+F.length),H=N(M,e),X=!!U(M).length;H?(r===F||r==="Shift"||O||M!==h&&X)&&S(M,F,B):O&&g(),H&&i("search",M,F)}else O&&g()},b=u=>{a.measuring||i("pressenter",u)},$=u=>{T(u)},w=u=>{x(u)},T=u=>{clearTimeout(c.value);const{isFocus:r}=a;!r&&u&&i("focus",u),a.isFocus=!0},x=u=>{c.value=setTimeout(()=>{a.isFocus=!1,g(),i("blur",u)},100)},D=u=>{const{split:r}=e,{value:v=""}=u,{text:h,selectionLocation:O}=pt(a.value,{measureLocation:a.measureLocation,targetText:v,prefix:a.measurePrefix,selectionStart:l.value.getSelectionStart(),split:r});f(h),g(()=>{ft(l.value.input,O)}),i("select",u,a.measurePrefix)},I=u=>{a.activeIndex=u},U=u=>{const r=u||a.measureText||"",{filterOption:v}=e;return e.options.filter(O=>v?v(r,O):!0)},R=A(()=>U());return n({blur:()=>{l.value.blur()},focus:()=>{l.value.focus()}}),_e(ve,{activeIndex:ie(a,"activeIndex"),setActiveIndex:I,selectOption:D,onFocus:T,onBlur:x,loading:ie(e,"loading")}),He(()=>{Xe(()=>{a.measuring&&(s.value.scrollTop=l.value.getScrollTop())})}),()=>{const{measureLocation:u,measurePrefix:r,measuring:v}=a,{prefixCls:h,placement:O,transitionName:W,getPopupContainer:N,direction:_}=e,L=se(e,["prefixCls","placement","transitionName","getPopupContainer","direction"]),{class:B,style:F}=t,M=se(t,["class","style"]),H=fe(L,["value","prefix","split","validateSearch","filterOption","options","loading"]),X=p(p(p({},H),M),{onChange:le,onSelect:le,value:a.value,onInput:P,onBlur:w,onKeydown:y,onKeyup:C,onFocus:$,onPressenter:b});return m("div",{class:ee(h,B),style:F},[m(Ve,K(K({},X),{},{ref:l,tag:"textarea"}),null),v&&m("div",{ref:s,class:`${h}-measure`},[a.value.slice(0,u),m(bt,{prefixCls:h,transitionName:W,dropdownClassName:e.dropdownClassName,placement:O,options:v?R.value:[],visible:!0,direction:_,getPopupContainer:N},{default:()=>[m("span",null,[r])],notFoundContent:d.notFoundContent,option:d.option}),a.value.slice(u+r.length)])])}}}),Ct={value:String,disabled:Boolean,payload:Te()},xe=p(p({},Ct),{label:Me([])}),ye={name:"Option",props:xe,render(e,o){let{slots:i}=o;var t;return(t=i.default)===null||t===void 0?void 0:t.call(i)}};j(p({compatConfig:{MODE:3}},ye));const wt=e=>{const{componentCls:o,colorTextDisabled:i,controlItemBgHover:t,controlPaddingHorizontal:n,colorText:d,motionDurationSlow:s,lineHeight:l,controlHeight:c,inputPaddingHorizontal:a,inputPaddingVertical:f,fontSize:P,colorBgElevated:S,borderRadiusLG:g,boxShadowSecondary:y}=e,C=Math.round((e.controlHeight-e.fontSize*e.lineHeight)/2);return{[o]:p(p(p(p(p({},k(e)),Ye(e)),{position:"relative",display:"inline-block",height:"auto",padding:0,overflow:"hidden",lineHeight:l,whiteSpace:"pre-wrap",verticalAlign:"bottom"}),Je(e,o)),{"&-disabled":{"> textarea":p({},Ze(e))},"&-focused":p({},Qe(e)),[`&-affix-wrapper ${o}-suffix`]:{position:"absolute",top:0,insetInlineEnd:a,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto"},[`> textarea, ${o}-measure`]:{color:d,boxSizing:"border-box",minHeight:c-2,margin:0,padding:`${f}px ${a}px`,overflow:"inherit",overflowX:"hidden",overflowY:"auto",fontWeight:"inherit",fontSize:"inherit",fontFamily:"inherit",fontStyle:"inherit",fontVariant:"inherit",fontSizeAdjust:"inherit",fontStretch:"inherit",lineHeight:"inherit",direction:"inherit",letterSpacing:"inherit",whiteSpace:"inherit",textAlign:"inherit",verticalAlign:"top",wordWrap:"break-word",wordBreak:"inherit",tabSize:"inherit"},"> textarea":p({width:"100%",border:"none",outline:"none",resize:"none",backgroundColor:"inherit"},qe(e.colorTextPlaceholder)),[`${o}-measure`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:-1,color:"transparent",pointerEvents:"none","> span":{display:"inline-block",minHeight:"1em"}},"&-dropdown":p(p({},k(e)),{position:"absolute",top:-9999,insetInlineStart:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",fontSize:P,fontVariant:"initial",backgroundColor:S,borderRadius:g,outline:"none",boxShadow:y,"&-hidden":{display:"none"},[`${o}-dropdown-menu`]:{maxHeight:e.dropdownHeight,marginBottom:0,paddingInlineStart:0,overflow:"auto",listStyle:"none",outline:"none","&-item":p(p({},ze),{position:"relative",display:"block",minWidth:e.controlItemWidth,padding:`${C}px ${n}px`,color:d,fontWeight:"normal",lineHeight:l,cursor:"pointer",transition:`background ${s} ease`,"&:hover":{backgroundColor:t},"&:first-child":{borderStartStartRadius:g,borderStartEndRadius:g,borderEndStartRadius:0,borderEndEndRadius:0},"&:last-child":{borderStartStartRadius:0,borderStartEndRadius:0,borderEndStartRadius:g,borderEndEndRadius:g},"&-disabled":{color:i,cursor:"not-allowed","&:hover":{color:i,backgroundColor:t,cursor:"not-allowed"}},"&-selected":{color:d,fontWeight:e.fontWeightStrong,backgroundColor:t},"&-active":{backgroundColor:t}})}})})}},$t=ce("Mentions",e=>{const o=Ue(e);return[wt(o)]},e=>({dropdownHeight:250,controlItemWidth:100,zIndexPopup:e.zIndexPopupBase+50}));var ue=function(e,o){var i={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(i[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(i[t[n]]=e[t[n]]);return i};function It(){return!0}const Ot=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{prefix:i="@",split:t=" "}=o,n=Array.isArray(i)?i:[i];return e.split(t).map(function(){let d=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",s=null;return n.some(l=>d.slice(0,l.length)===l?(s=l,!0):!1),s!==null?{prefix:s,value:d.slice(s.length)}:null}).filter(d=>!!d&&!!d.value)},Pt=()=>p(p({},he),{loading:{type:Boolean,default:void 0},onFocus:{type:Function},onBlur:{type:Function},onSelect:{type:Function},onChange:{type:Function},onPressenter:{type:Function},"onUpdate:value":{type:Function},notFoundContent:E.any,defaultValue:String,id:String,status:String}),Q=j({compatConfig:{MODE:3},name:"AMentions",inheritAttrs:!1,props:Pt(),slots:Object,setup(e,o){let{slots:i,emit:t,attrs:n,expose:d}=o;var s,l;const{prefixCls:c,renderEmpty:a,direction:f}=pe("mentions",e),[P,S]=$t(c),g=V(!1),y=V(null),C=V((l=(s=e.value)!==null&&s!==void 0?s:e.defaultValue)!==null&&l!==void 0?l:""),b=Ge(),$=Ke.useInject(),w=A(()=>We($.status,e.status));Be({prefixCls:A(()=>`${c.value}-menu`),mode:A(()=>"vertical"),selectable:A(()=>!1),onClick:()=>{},validator:r=>{let{mode:v}=r}}),me(()=>e.value,r=>{C.value=r});const T=r=>{g.value=!0,t("focus",r)},x=r=>{g.value=!1,t("blur",r),b.onFieldBlur()},D=function(){for(var r=arguments.length,v=new Array(r),h=0;h<r;h++)v[h]=arguments[h];t("select",...v),g.value=!0},I=r=>{e.value===void 0&&(C.value=r),t("update:value",r),t("change",r),b.onFieldChange()},U=()=>{const r=e.notFoundContent;return r!==void 0?r:i.notFoundContent?i.notFoundContent():a("Select")},R=()=>{var r;return Ee(((r=i.default)===null||r===void 0?void 0:r.call(i))||[]).map(v=>{var h,O;return p(p({},Fe(v)),{label:(O=(h=v.children)===null||h===void 0?void 0:h.default)===null||O===void 0?void 0:O.call(h)})})};d({focus:()=>{y.value.focus()},blur:()=>{y.value.blur()}});const u=A(()=>e.loading?It:e.filterOption);return()=>{const{disabled:r,getPopupContainer:v,rows:h=1,id:O=b.id.value}=e,W=ue(e,["disabled","getPopupContainer","rows","id"]),{hasFeedback:N,feedbackIcon:_}=$,{class:L}=n,B=ue(n,["class"]),F=fe(W,["defaultValue","onUpdate:value","prefixCls"]),M=ee({[`${c.value}-disabled`]:r,[`${c.value}-focused`]:g.value,[`${c.value}-rtl`]:f.value==="rtl"},ae(c.value,w.value),!N&&L,S.value),H=p(p(p(p({prefixCls:c.value},F),{disabled:r,direction:f.value,filterOption:u.value,getPopupContainer:v,options:e.loading?[{value:"ANTDV_SEARCHING",disabled:!0,label:m(G,{size:"small"},null)}]:e.options||R(),class:M}),B),{rows:h,onChange:I,onSelect:D,onFocus:T,onBlur:x,ref:y,value:C.value,id:O}),X=m(yt,K(K({},H),{},{dropdownClassName:S.value}),{notFoundContent:U,option:i.option});return P(N?m("div",{class:ee(`${c.value}-affix-wrapper`,ae(`${c.value}-affix-wrapper`,w.value,N),L,S.value)},[X,m("span",{class:`${c.value}-suffix`},[_])]):X)}}}),Z=j(p(p({compatConfig:{MODE:3}},ye),{name:"AMentionsOption",props:xe})),Wt=p(Q,{Option:Z,getMentions:Ot,install:e=>(e.component(Q.name,Q),e.component(Z.name,Z),e)});export{Z as MentionsOption,Wt as default,Pt as mentionsProps};
