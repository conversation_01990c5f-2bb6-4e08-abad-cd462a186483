var Ve=Object.defineProperty,Xe=Object.defineProperties;var Je=Object.getOwnPropertyDescriptors;var le=Object.getOwnPropertySymbols;var Qe=Object.prototype.hasOwnProperty,Ye=Object.prototype.propertyIsEnumerable;var ie=(e,t,a)=>t in e?Ve(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,M=(e,t)=>{for(var a in t||(t={}))Qe.call(t,a)&&ie(e,a,t[a]);if(le)for(var a of le(t))Ye.call(t,a)&&ie(e,a,t[a]);return e},_=(e,t)=>Xe(e,Je(t));var V=(e,t,a)=>new Promise((l,c)=>{var u=f=>{try{T(a.next(f))}catch(m){c(m)}},p=f=>{try{T(a.throw(f))}catch(m){c(m)}},T=f=>f.done?l(f.value):Promise.resolve(f.value).then(u,p);T((a=a.apply(e,t)).next())});import{b4 as ue,e as Ee,af as Ze,aA as xe,bn as et,k as G,l as h,m as X,n as W,bo as tt,bp as Se,ac as at,bq as Ce,ad as Fe,y as q,w as Pe,p as Ae,a6 as ae,r as st,t as Re,B as z,br as rt,bs as nt,bt as ot,bu as lt,bv as it,bw as ut}from"./bootstrap-CYivmKoJ.js";import{d as N,e as S,a as Le,P as ce,f as E,g as v,h as w,n as b,u as r,j as I,m as K,l as P,t as Q,q as D,D as A,B as ct,i as Ue,a2 as C,r as De,F as dt,R as pt,E as B,v as ft,Q as se,at as de,w as pe,aa as gt,a9 as yt,y as bt,O as fe,U as ge}from"../jse/index-index-SSqEGcIT.js";import{t as ne,d as vt}from"./error-CYrjCQ5V.js";import{b as Z}from"./use-form-common-props-DZjBwEkr.js";import{g as oe,h as Oe,n as Y,j as mt,S as ht,l as $t,c as Tt}from"./isEqual-racMrmQ-.js";import{c as Be,a as kt,b as wt,i as jt}from"./_initCloneObject-CkjEXtA-.js";function Et(e,t){for(var a=-1,l=e==null?0:e.length;++a<l&&t(e[a],a,e)!==!1;);return e}var St=Object.prototype,Ct=St.hasOwnProperty;function Ft(e){var t=e.length,a=new e.constructor(t);return t&&typeof e[0]=="string"&&Ct.call(e,"index")&&(a.index=e.index,a.input=e.input),a}function Pt(e,t){var a=Be(e.buffer);return new e.constructor(a,e.byteOffset,e.byteLength)}var At=/\w*$/;function Rt(e){var t=new e.constructor(e.source,At.exec(e));return t.lastIndex=e.lastIndex,t}var ye=ue?ue.prototype:void 0,be=ye?ye.valueOf:void 0;function Lt(e){return be?Object(be.call(e)):{}}var Ut="[object Boolean]",Dt="[object Date]",Ot="[object Map]",Bt="[object Number]",It="[object RegExp]",Nt="[object Set]",Mt="[object String]",_t="[object Symbol]",qt="[object ArrayBuffer]",Kt="[object DataView]",Ht="[object Float32Array]",Wt="[object Float64Array]",zt="[object Int8Array]",Gt="[object Int16Array]",Vt="[object Int32Array]",Xt="[object Uint8Array]",Jt="[object Uint8ClampedArray]",Qt="[object Uint16Array]",Yt="[object Uint32Array]";function Zt(e,t,a){var l=e.constructor;switch(t){case qt:return Be(e);case Ut:case Dt:return new l(+e);case Kt:return Pt(e);case Ht:case Wt:case zt:case Gt:case Vt:case Xt:case Jt:case Qt:case Yt:return kt(e,a);case Ot:return new l;case Bt:case Mt:return new l(e);case It:return Rt(e);case Nt:return new l;case _t:return Lt(e)}}var xt="[object Map]";function ea(e){return Ee(e)&&oe(e)==xt}var ve=Y&&Y.isMap,ta=ve?Oe(ve):ea,aa="[object Set]";function sa(e){return Ee(e)&&oe(e)==aa}var me=Y&&Y.isSet,ra=me?Oe(me):sa,na=1,Ie="[object Arguments]",oa="[object Array]",la="[object Boolean]",ia="[object Date]",ua="[object Error]",Ne="[object Function]",ca="[object GeneratorFunction]",da="[object Map]",pa="[object Number]",Me="[object Object]",fa="[object RegExp]",ga="[object Set]",ya="[object String]",ba="[object Symbol]",va="[object WeakMap]",ma="[object ArrayBuffer]",ha="[object DataView]",$a="[object Float32Array]",Ta="[object Float64Array]",ka="[object Int8Array]",wa="[object Int16Array]",ja="[object Int32Array]",Ea="[object Uint8Array]",Sa="[object Uint8ClampedArray]",Ca="[object Uint16Array]",Fa="[object Uint32Array]",$={};$[Ie]=$[oa]=$[ma]=$[ha]=$[la]=$[ia]=$[$a]=$[Ta]=$[ka]=$[wa]=$[ja]=$[da]=$[pa]=$[Me]=$[fa]=$[ga]=$[ya]=$[ba]=$[Ea]=$[Sa]=$[Ca]=$[Fa]=!0;$[ua]=$[Ne]=$[va]=!1;function J(e,t,a,l,c,u){var p,T=t&na;if(p!==void 0)return p;if(!Ze(e))return e;var f=xe(e);if(f)p=Ft(e);else{var m=oe(e),j=m==Ne||m==ca;if(mt(e))return wt(e,T);if(m==Me||m==Ie||j&&!c)p=j?{}:jt(e);else{if(!$[m])return c?e:{};p=Zt(e,m,T)}}u||(u=new ht);var y=u.get(e);if(y)return y;u.set(e,p),ra(e)?e.forEach(function(i){p.add(J(i,t,a,i,e,u))}):ta(e)&&e.forEach(function(i,s){p.set(s,J(i,t,a,s,e,u))});var R=$t,n=f?void 0:R(e);return Et(n||e,function(i,s){n&&(s=i,i=e[s]),et(p,s,J(i,t,a,s,e,u))}),p}var Pa=1,Aa=4;function he(e){return J(e,Pa|Aa)}const Ra=G({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:e=>e>=0&&e<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:Boolean,duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:h(String),default:"round"},textInside:Boolean,width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:h([String,Array,Function]),default:""},striped:Boolean,stripedFlow:Boolean,format:{type:h(Function),default:e=>`${e}%`}}),La=N({name:"ElProgress"}),Ua=N(_(M({},La),{props:Ra,setup(e){const t=e,a={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},l=W("progress"),c=S(()=>{const o={width:`${t.percentage}%`,animationDuration:`${t.duration}s`},k=F(t.percentage);return k.includes("gradient")?o.background=k:o.backgroundColor=k,o}),u=S(()=>(t.strokeWidth/t.width*100).toFixed(1)),p=S(()=>["circle","dashboard"].includes(t.type)?Number.parseInt(`${50-Number.parseFloat(u.value)/2}`,10):0),T=S(()=>{const o=p.value,k=t.type==="dashboard";return`
          M 50 50
          m 0 ${k?"":"-"}${o}
          a ${o} ${o} 0 1 1 0 ${k?"-":""}${o*2}
          a ${o} ${o} 0 1 1 0 ${k?"":"-"}${o*2}
          `}),f=S(()=>2*Math.PI*p.value),m=S(()=>t.type==="dashboard"?.75:1),j=S(()=>`${-1*f.value*(1-m.value)/2}px`),y=S(()=>({strokeDasharray:`${f.value*m.value}px, ${f.value}px`,strokeDashoffset:j.value})),R=S(()=>({strokeDasharray:`${f.value*m.value*(t.percentage/100)}px, ${f.value}px`,strokeDashoffset:j.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"})),n=S(()=>{let o;return t.color?o=F(t.percentage):o=a[t.status]||a.default,o}),i=S(()=>t.status==="warning"?tt:t.type==="line"?t.status==="success"?Se:at:t.status==="success"?Ce:Fe),s=S(()=>t.type==="line"?12+t.strokeWidth*.4:t.width*.111111+2),g=S(()=>t.format(t.percentage));function d(o){const k=100/o.length;return o.map((L,U)=>ce(L)?{color:L,percentage:(U+1)*k}:L).sort((L,U)=>L.percentage-U.percentage)}const F=o=>{var k;const{color:O}=t;if(Le(O))return O(o);if(ce(O))return O;{const L=d(O);for(const U of L)if(U.percentage>o)return U.color;return(k=L[L.length-1])==null?void 0:k.color}};return(o,k)=>(v(),E("div",{class:b([r(l).b(),r(l).m(o.type),r(l).is(o.status),{[r(l).m("without-text")]:!o.showText,[r(l).m("text-inside")]:o.textInside}]),role:"progressbar","aria-valuenow":o.percentage,"aria-valuemin":"0","aria-valuemax":"100"},[o.type==="line"?(v(),E("div",{key:0,class:b(r(l).b("bar"))},[I("div",{class:b(r(l).be("bar","outer")),style:K({height:`${o.strokeWidth}px`})},[I("div",{class:b([r(l).be("bar","inner"),{[r(l).bem("bar","inner","indeterminate")]:o.indeterminate},{[r(l).bem("bar","inner","striped")]:o.striped},{[r(l).bem("bar","inner","striped-flow")]:o.stripedFlow}]),style:K(r(c))},[(o.showText||o.$slots.default)&&o.textInside?(v(),E("div",{key:0,class:b(r(l).be("bar","innerText"))},[P(o.$slots,"default",{percentage:o.percentage},()=>[I("span",null,Q(r(g)),1)])],2)):w("v-if",!0)],6)],6)],2)):(v(),E("div",{key:1,class:b(r(l).b("circle")),style:K({height:`${o.width}px`,width:`${o.width}px`})},[(v(),E("svg",{viewBox:"0 0 100 100"},[I("path",{class:b(r(l).be("circle","track")),d:r(T),stroke:`var(${r(l).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-linecap":o.strokeLinecap,"stroke-width":r(u),fill:"none",style:K(r(y))},null,14,["d","stroke","stroke-linecap","stroke-width"]),I("path",{class:b(r(l).be("circle","path")),d:r(T),stroke:r(n),fill:"none",opacity:o.percentage?1:0,"stroke-linecap":o.strokeLinecap,"stroke-width":r(u),style:K(r(R))},null,14,["d","stroke","opacity","stroke-linecap","stroke-width"])]))],6)),(o.showText||o.$slots.default)&&!o.textInside?(v(),E("div",{key:2,class:b(r(l).e("text")),style:K({fontSize:`${r(s)}px`})},[P(o.$slots,"default",{percentage:o.percentage},()=>[o.status?(v(),D(r(q),{key:1},{default:A(()=>[(v(),D(ct(r(i))))]),_:1})):(v(),E("span",{key:0},Q(r(g)),1))])],6)):w("v-if",!0)],10,["aria-valuenow"]))}}));var Da=X(Ua,[["__file","progress.vue"]]);const Oa=Pe(Da),_e=Symbol("uploadContextKey"),Ba="ElUpload";class Ia extends Error{constructor(t,a,l,c){super(t),this.name="UploadAjaxError",this.status=a,this.method=l,this.url=c}}function $e(e,t,a){let l;return a.response?l=`${a.response.error||a.response}`:a.responseText?l=`${a.responseText}`:l=`fail to ${t.method} ${e} ${a.status}`,new Ia(l,a.status,t.method,e)}function Na(e){const t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch(a){return t}}const Ma=e=>{typeof XMLHttpRequest=="undefined"&&ne(Ba,"XMLHttpRequest is undefined");const t=new XMLHttpRequest,a=e.action;t.upload&&t.upload.addEventListener("progress",u=>{const p=u;p.percent=u.total>0?u.loaded/u.total*100:0,e.onProgress(p)});const l=new FormData;if(e.data)for(const[u,p]of Object.entries(e.data))Ue(p)&&p.length?l.append(u,...p):l.append(u,p);l.append(e.filename,e.file,e.file.name),t.addEventListener("error",()=>{e.onError($e(a,e,t))}),t.addEventListener("load",()=>{if(t.status<200||t.status>=300)return e.onError($e(a,e,t));e.onSuccess(Na(t))}),t.open(e.method,a,!0),e.withCredentials&&"withCredentials"in t&&(t.withCredentials=!0);const c=e.headers||{};if(c instanceof Headers)c.forEach((u,p)=>t.setRequestHeader(p,u));else for(const[u,p]of Object.entries(c))Ae(p)||t.setRequestHeader(u,String(p));return t.send(l),t},qe=["text","picture","picture-card"];let _a=1;const re=()=>Date.now()+_a++,Ke=G({action:{type:String,default:"#"},headers:{type:h(Object)},method:{type:String,default:"post"},data:{type:h([Object,Function,Promise]),default:()=>ae({})},multiple:Boolean,name:{type:String,default:"file"},drag:Boolean,withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:{type:String,default:""},fileList:{type:h(Array),default:()=>ae([])},autoUpload:{type:Boolean,default:!0},listType:{type:String,values:qe,default:"text"},httpRequest:{type:h(Function),default:Ma},disabled:Boolean,limit:Number}),qa=G(_(M({},Ke),{beforeUpload:{type:h(Function),default:C},beforeRemove:{type:h(Function)},onRemove:{type:h(Function),default:C},onChange:{type:h(Function),default:C},onPreview:{type:h(Function),default:C},onSuccess:{type:h(Function),default:C},onProgress:{type:h(Function),default:C},onError:{type:h(Function),default:C},onExceed:{type:h(Function),default:C},crossorigin:{type:h(String)}})),Ka=G({files:{type:h(Array),default:()=>ae([])},disabled:{type:Boolean,default:!1},handlePreview:{type:h(Function),default:C},listType:{type:String,values:qe,default:"text"},crossorigin:{type:h(String)}}),Ha={remove:e=>!!e},Wa=N({name:"ElUploadList"}),za=N(_(M({},Wa),{props:Ka,emits:Ha,setup(e,{emit:t}){const a=e,{t:l}=st(),c=W("upload"),u=W("icon"),p=W("list"),T=Z(),f=De(!1),m=S(()=>[c.b("list"),c.bm("list",a.listType),c.is("disabled",a.disabled)]),j=y=>{t("remove",y)};return(y,R)=>(v(),D(lt,{tag:"ul",class:b(r(m)),name:r(p).b()},{default:A(()=>[(v(!0),E(dt,null,pt(y.files,(n,i)=>(v(),E("li",{key:n.uid||n.name,class:b([r(c).be("list","item"),r(c).is(n.status),{focusing:f.value}]),tabindex:"0",onKeydown:Re(s=>!r(T)&&j(n),["delete"]),onFocus:s=>f.value=!0,onBlur:s=>f.value=!1,onClick:s=>f.value=!1},[P(y.$slots,"default",{file:n,index:i},()=>[y.listType==="picture"||n.status!=="uploading"&&y.listType==="picture-card"?(v(),E("img",{key:0,class:b(r(c).be("list","item-thumbnail")),src:n.url,crossorigin:y.crossorigin,alt:""},null,10,["src","crossorigin"])):w("v-if",!0),n.status==="uploading"||y.listType!=="picture-card"?(v(),E("div",{key:1,class:b(r(c).be("list","item-info"))},[I("a",{class:b(r(c).be("list","item-name")),onClick:z(s=>y.handlePreview(n),["prevent"])},[B(r(q),{class:b(r(u).m("document"))},{default:A(()=>[B(r(rt))]),_:1},8,["class"]),I("span",{class:b(r(c).be("list","item-file-name")),title:n.name},Q(n.name),11,["title"])],10,["onClick"]),n.status==="uploading"?(v(),D(r(Oa),{key:0,type:y.listType==="picture-card"?"circle":"line","stroke-width":y.listType==="picture-card"?6:2,percentage:Number(n.percentage),style:K(y.listType==="picture-card"?"":"margin-top: 0.5rem")},null,8,["type","stroke-width","percentage","style"])):w("v-if",!0)],2)):w("v-if",!0),I("label",{class:b(r(c).be("list","item-status-label"))},[y.listType==="text"?(v(),D(r(q),{key:0,class:b([r(u).m("upload-success"),r(u).m("circle-check")])},{default:A(()=>[B(r(Se))]),_:1},8,["class"])):["picture-card","picture"].includes(y.listType)?(v(),D(r(q),{key:1,class:b([r(u).m("upload-success"),r(u).m("check")])},{default:A(()=>[B(r(Ce))]),_:1},8,["class"])):w("v-if",!0)],2),r(T)?w("v-if",!0):(v(),D(r(q),{key:2,class:b(r(u).m("close")),onClick:s=>j(n)},{default:A(()=>[B(r(Fe))]),_:2},1032,["class","onClick"])),w(" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn"),w(" This is a bug which needs to be fixed "),w(" TODO: Fix the incorrect navigation interaction "),r(T)?w("v-if",!0):(v(),E("i",{key:3,class:b(r(u).m("close-tip"))},Q(r(l)("el.upload.deleteTip")),3)),y.listType==="picture-card"?(v(),E("span",{key:4,class:b(r(c).be("list","item-actions"))},[I("span",{class:b(r(c).be("list","item-preview")),onClick:s=>y.handlePreview(n)},[B(r(q),{class:b(r(u).m("zoom-in"))},{default:A(()=>[B(r(nt))]),_:1},8,["class"])],10,["onClick"]),r(T)?w("v-if",!0):(v(),E("span",{key:0,class:b(r(c).be("list","item-delete")),onClick:s=>j(n)},[B(r(q),{class:b(r(u).m("delete"))},{default:A(()=>[B(r(ot))]),_:1},8,["class"])],10,["onClick"]))],2)):w("v-if",!0)])],42,["onKeydown","onFocus","onBlur","onClick"]))),128)),P(y.$slots,"append")]),_:3},8,["class","name"]))}}));var Te=X(za,[["__file","upload-list.vue"]]);const Ga=G({disabled:{type:Boolean,default:!1}}),Va={file:e=>Ue(e)},He="ElUploadDrag",Xa=N({name:He}),Ja=N(_(M({},Xa),{props:Ga,emits:Va,setup(e,{emit:t}){ft(_e)||ne(He,"usage: <el-upload><el-upload-dragger /></el-upload>");const l=W("upload"),c=De(!1),u=Z(),p=f=>{if(u.value)return;c.value=!1,f.stopPropagation();const m=Array.from(f.dataTransfer.files),j=f.dataTransfer.items||[];m.forEach((y,R)=>{var n;const i=j[R],s=(n=i==null?void 0:i.webkitGetAsEntry)==null?void 0:n.call(i);s&&(y.isDirectory=s.isDirectory)}),t("file",m)},T=()=>{u.value||(c.value=!0)};return(f,m)=>(v(),E("div",{class:b([r(l).b("dragger"),r(l).is("dragover",c.value)]),onDrop:z(p,["prevent"]),onDragover:z(T,["prevent"]),onDragleave:z(j=>c.value=!1,["prevent"])},[P(f.$slots,"default")],42,["onDrop","onDragover","onDragleave"]))}}));var Qa=X(Ja,[["__file","upload-dragger.vue"]]);const Ya=G(_(M({},Ke),{beforeUpload:{type:h(Function),default:C},onRemove:{type:h(Function),default:C},onStart:{type:h(Function),default:C},onSuccess:{type:h(Function),default:C},onProgress:{type:h(Function),default:C},onError:{type:h(Function),default:C},onExceed:{type:h(Function),default:C}})),Za=N({name:"ElUploadContent",inheritAttrs:!1}),xa=N(_(M({},Za),{props:Ya,setup(e,{expose:t}){const a=e,l=W("upload"),c=Z(),u=se({}),p=se(),T=s=>{if(s.length===0)return;const{autoUpload:g,limit:d,fileList:F,multiple:o,onStart:k,onExceed:O}=a;if(d&&F.length+s.length>d){O(s,F);return}o||(s=s.slice(0,1));for(const L of s){const U=L;U.uid=re(),k(U),g&&f(U)}},f=s=>V(null,null,function*(){if(p.value.value="",!a.beforeUpload)return j(s);let g,d={};try{const o=a.data,k=a.beforeUpload(s);d=de(a.data)?he(a.data):a.data,g=yield k,de(a.data)&&Tt(o,d)&&(d=he(a.data))}catch(o){g=!1}if(g===!1){a.onRemove(s);return}let F=s;g instanceof Blob&&(g instanceof File?F=g:F=new File([g],s.name,{type:s.type})),j(Object.assign(F,{uid:s.uid}),d)}),m=(s,g)=>V(null,null,function*(){return Le(s)?s(g):s}),j=(s,g)=>V(null,null,function*(){const{headers:d,data:F,method:o,withCredentials:k,name:O,action:L,onProgress:U,onSuccess:We,onError:ze,httpRequest:Ge}=a;try{g=yield m(g!=null?g:F,s)}catch(H){a.onRemove(s);return}const{uid:x}=s,ee={headers:d||{},withCredentials:k,file:s,data:g,method:o,filename:O,action:L,onProgress:H=>{U(H,s)},onSuccess:H=>{We(H,s),delete u.value[x]},onError:H=>{ze(H,s),delete u.value[x]}},te=Ge(ee);u.value[x]=te,te instanceof Promise&&te.then(ee.onSuccess,ee.onError)}),y=s=>{const g=s.target.files;g&&T(Array.from(g))},R=()=>{c.value||(p.value.value="",p.value.click())},n=()=>{R()};return t({abort:s=>{it(u.value).filter(s?([d])=>String(s.uid)===d:()=>!0).forEach(([d,F])=>{F instanceof XMLHttpRequest&&F.abort(),delete u.value[d]})},upload:f}),(s,g)=>(v(),E("div",{class:b([r(l).b(),r(l).m(s.listType),r(l).is("drag",s.drag),r(l).is("disabled",r(c))]),tabindex:r(c)?"-1":"0",onClick:R,onKeydown:Re(z(n,["self"]),["enter","space"])},[s.drag?(v(),D(Qa,{key:0,disabled:r(c),onFile:T},{default:A(()=>[P(s.$slots,"default")]),_:3},8,["disabled"])):P(s.$slots,"default",{key:1}),I("input",{ref_key:"inputRef",ref:p,class:b(r(l).e("input")),name:s.name,disabled:r(c),multiple:s.multiple,accept:s.accept,type:"file",onChange:y,onClick:z(()=>{},["stop"])},null,42,["name","disabled","multiple","accept","onClick"])],42,["tabindex","onKeydown"]))}}));var ke=X(xa,[["__file","upload-content.vue"]]);const we="ElUpload",je=e=>{var t;(t=e.url)!=null&&t.startsWith("blob:")&&URL.revokeObjectURL(e.url)},es=(e,t)=>{const a=ut(e,"fileList",void 0,{passive:!0}),l=n=>a.value.find(i=>i.uid===n.uid);function c(n){var i;(i=t.value)==null||i.abort(n)}function u(n=["ready","uploading","success","fail"]){a.value=a.value.filter(i=>!n.includes(i.status))}function p(n){a.value=a.value.filter(i=>i.uid!==n.uid)}const T=(n,i)=>{const s=l(i);s&&(console.error(n),s.status="fail",p(s),e.onError(n,s,a.value),e.onChange(s,a.value))},f=(n,i)=>{const s=l(i);s&&(e.onProgress(n,s,a.value),s.status="uploading",s.percentage=Math.round(n.percent))},m=(n,i)=>{const s=l(i);s&&(s.status="success",s.response=n,e.onSuccess(n,s,a.value),e.onChange(s,a.value))},j=n=>{Ae(n.uid)&&(n.uid=re());const i={name:n.name,percentage:0,status:"ready",size:n.size,raw:n,uid:n.uid};if(e.listType==="picture-card"||e.listType==="picture")try{i.url=URL.createObjectURL(n)}catch(s){vt(we,s.message),e.onError(s,i,a.value)}a.value=[...a.value,i],e.onChange(i,a.value)},y=n=>V(null,null,function*(){const i=n instanceof File?l(n):n;i||ne(we,"file to be removed not found");const s=g=>{c(g),p(g),e.onRemove(g,a.value),je(g)};e.beforeRemove?(yield e.beforeRemove(i,a.value))!==!1&&s(i):s(i)});function R(){a.value.filter(({status:n})=>n==="ready").forEach(({raw:n})=>{var i;return n&&((i=t.value)==null?void 0:i.upload(n))})}return pe(()=>e.listType,n=>{n!=="picture-card"&&n!=="picture"||(a.value=a.value.map(i=>{const{raw:s,url:g}=i;if(!g&&s)try{i.url=URL.createObjectURL(s)}catch(d){e.onError(d,i,a.value)}return i}))}),pe(a,n=>{for(const i of n)i.uid||(i.uid=re()),i.status||(i.status="success")},{immediate:!0,deep:!0}),{uploadFiles:a,abort:c,clearFiles:u,handleError:T,handleProgress:f,handleStart:j,handleSuccess:m,handleRemove:y,submit:R,revokeFileObjectURL:je}},ts=N({name:"ElUpload"}),as=N(_(M({},ts),{props:qa,setup(e,{expose:t}){const a=e,l=Z(),c=se(),{abort:u,submit:p,clearFiles:T,uploadFiles:f,handleStart:m,handleError:j,handleRemove:y,handleSuccess:R,handleProgress:n,revokeFileObjectURL:i}=es(a,c),s=S(()=>a.listType==="picture-card"),g=S(()=>_(M({},a),{fileList:f.value,onStart:m,onProgress:n,onSuccess:R,onError:j,onRemove:y}));return gt(()=>{f.value.forEach(i)}),yt(_e,{accept:bt(a,"accept")}),t({abort:u,submit:p,clearFiles:T,handleStart:m,handleRemove:y}),(d,F)=>(v(),E("div",null,[r(s)&&d.showFileList?(v(),D(Te,{key:0,disabled:r(l),"list-type":d.listType,files:r(f),crossorigin:d.crossorigin,"handle-preview":d.onPreview,onRemove:r(y)},fe({append:A(()=>[B(ke,ge({ref_key:"uploadRef",ref:c},r(g)),{default:A(()=>[d.$slots.trigger?P(d.$slots,"trigger",{key:0}):w("v-if",!0),!d.$slots.trigger&&d.$slots.default?P(d.$slots,"default",{key:1}):w("v-if",!0)]),_:3},16)]),_:2},[d.$slots.file?{name:"default",fn:A(({file:o,index:k})=>[P(d.$slots,"file",{file:o,index:k})])}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):w("v-if",!0),!r(s)||r(s)&&!d.showFileList?(v(),D(ke,ge({key:1,ref_key:"uploadRef",ref:c},r(g)),{default:A(()=>[d.$slots.trigger?P(d.$slots,"trigger",{key:0}):w("v-if",!0),!d.$slots.trigger&&d.$slots.default?P(d.$slots,"default",{key:1}):w("v-if",!0)]),_:3},16)):w("v-if",!0),d.$slots.trigger?P(d.$slots,"default",{key:2}):w("v-if",!0),P(d.$slots,"tip"),!r(s)&&d.showFileList?(v(),D(Te,{key:3,disabled:r(l),"list-type":d.listType,files:r(f),crossorigin:d.crossorigin,"handle-preview":d.onPreview,onRemove:r(y)},fe({_:2},[d.$slots.file?{name:"default",fn:A(({file:o,index:k})=>[P(d.$slots,"file",{file:o,index:k})])}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):w("v-if",!0)]))}}));var ss=X(as,[["__file","upload.vue"]]);const ds=Pe(ss);export{ds as ElUpload,ds as default,re as genFileId,Ke as uploadBaseProps,Ya as uploadContentProps,_e as uploadContextKey,Va as uploadDraggerEmits,Ga as uploadDraggerProps,Ha as uploadListEmits,Ka as uploadListProps,qe as uploadListTypes,qa as uploadProps};
