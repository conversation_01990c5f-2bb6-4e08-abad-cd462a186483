var Un=Object.defineProperty,Gn=Object.defineProperties;var Qn=Object.getOwnPropertyDescriptors;var Ye=Object.getOwnPropertySymbols;var Rt=Object.prototype.hasOwnProperty,Pt=Object.prototype.propertyIsEnumerable;var At=(e,t,l)=>t in e?Un(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,B=(e,t)=>{for(var l in t||(t={}))Rt.call(t,l)&&At(e,l,t[l]);if(Ye)for(var l of Ye(t))Pt.call(t,l)&&At(e,l,t[l]);return e},Z=(e,t)=>Gn(e,Qn(t));var Ht=(e,t)=>{var l={};for(var s in e)Rt.call(e,s)&&t.indexOf(s)<0&&(l[s]=e[s]);if(e!=null&&Ye)for(var s of Ye(e))t.indexOf(s)<0&&Pt.call(e,s)&&(l[s]=e[s]);return l};var zt=(e,t,l)=>new Promise((s,n)=>{var a=d=>{try{f(l.next(d))}catch(v){n(v)}},o=d=>{try{f(l.throw(d))}catch(v){n(v)}},f=d=>d.done?s(d.value):Promise.resolve(d.value).then(a,o);f((l=l.apply(e,t)).next())});import{aA as xn,a as gt,m as re,n as _,ae as Yn,w as Fe,k as yt,ay as Se,ar as De,B as W,ab as Ee,r as Xt,aI as Jn,a8 as Xn,s as ne,bl as Ft,aF as ce,o as Zn,aH as _n,l as Me,v as el,R as rt,ac as tl,q as nl,t as Ae,bm as ll,y as Zt,bx as ol,aq as _t,P as sl,Q as al,as as Wt,by as it,bz as il,U as rl,bA as dl,F as cl,aG as ul,p as fl}from"./bootstrap-CYivmKoJ.js";import{d as hl,u as jt,E as pl,a as vl}from"./index-owS4PRxE.js";import{s as gl,E as yl}from"./index-pMAz7VMb.js";import{e as en,f as ml,t as qt,E as bl,u as Cl}from"./index-Bw18sI2e.js";import{t as kl,d as Ut}from"./error-CYrjCQ5V.js";import{d as Q,q as U,g as S,D as F,l as H,U as Nl,au as El,u as Pe,v as le,e as k,a3 as ae,ah as Sl,w as L,x as Ce,N as ve,f as V,j as R,t as oe,n as E,ad as We,L as ue,aa as wl,p as j,h as P,m as we,r as T,o as Te,i as G,V as Kl,a as ie,at as Ol,P as mt,aj as J,as as Dl,E as se,F as _e,R as et,G as Gt,B as He,av as Tl,a9 as be,b as Il,M as tn,a0 as Vl,ar as dt,H as ze}from"../jse/index-index-SSqEGcIT.js";import{c as Ke}from"./isEqual-racMrmQ-.js";import{b as nn,u as $l,a as Bl}from"./use-form-item-iUVikjOD.js";import{a as Ll,u as Ml}from"./index-CdkCbLvc.js";import{u as ln,a as Al}from"./use-form-common-props-DZjBwEkr.js";import{U as te,C as on,u as Rl,p as tt}from"./index-DIXeP0hR.js";import{C as Pl}from"./index-DcFMbTQH.js";import{f as Hl}from"./vnode-ih70IEYb.js";import{ElCheckbox as zl}from"./index-Dlnkk1PI.js";import"./aria-DGfENwCE.js";import"./_baseFindIndex-D7XfJLKM.js";import"./_baseIteratee-DIAZWcrk.js";import"./index-DuhtAOZf.js";function pe(){if(!arguments.length)return[];var e=arguments[0];return xn(e)?e:[e]}function Fl(e,t){if(!gt)return;if(!t){e.scrollTop=0;return}const l=[];let s=t.offsetParent;for(;s!==null&&e!==s&&e.contains(s);)l.push(s),s=s.offsetParent;const n=t.offsetTop+l.reduce((d,v)=>d+v.offsetTop,0),a=n+t.offsetHeight,o=e.scrollTop,f=o+e.clientHeight;n<o?e.scrollTop=n:a>f&&(e.scrollTop=a-e.clientHeight)}const Wl=Q({name:"ElCollapseTransition"}),jl=Q(Z(B({},Wl),{setup(e){const t=_("collapse-transition"),l=n=>{n.style.maxHeight="",n.style.overflow=n.dataset.oldOverflow,n.style.paddingTop=n.dataset.oldPaddingTop,n.style.paddingBottom=n.dataset.oldPaddingBottom},s={beforeEnter(n){n.dataset||(n.dataset={}),n.dataset.oldPaddingTop=n.style.paddingTop,n.dataset.oldPaddingBottom=n.style.paddingBottom,n.style.height&&(n.dataset.elExistsHeight=n.style.height),n.style.maxHeight=0,n.style.paddingTop=0,n.style.paddingBottom=0},enter(n){requestAnimationFrame(()=>{n.dataset.oldOverflow=n.style.overflow,n.dataset.elExistsHeight?n.style.maxHeight=n.dataset.elExistsHeight:n.scrollHeight!==0?n.style.maxHeight=`${n.scrollHeight}px`:n.style.maxHeight=0,n.style.paddingTop=n.dataset.oldPaddingTop,n.style.paddingBottom=n.dataset.oldPaddingBottom,n.style.overflow="hidden"})},afterEnter(n){n.style.maxHeight="",n.style.overflow=n.dataset.oldOverflow},enterCancelled(n){l(n)},beforeLeave(n){n.dataset||(n.dataset={}),n.dataset.oldPaddingTop=n.style.paddingTop,n.dataset.oldPaddingBottom=n.style.paddingBottom,n.dataset.oldOverflow=n.style.overflow,n.style.maxHeight=`${n.scrollHeight}px`,n.style.overflow="hidden"},leave(n){n.scrollHeight!==0&&(n.style.maxHeight=0,n.style.paddingTop=0,n.style.paddingBottom=0)},afterLeave(n){l(n)},leaveCancelled(n){l(n)}};return(n,a)=>(S(),U(Yn,Nl({name:Pe(t).b()},El(s)),{default:F(()=>[H(n.$slots,"default")]),_:3},16,["name"]))}}));var ql=re(jl,[["__file","collapse-transition.vue"]]);const Ul=Fe(ql),sn=Symbol("ElSelectGroup"),Ie=Symbol("ElSelect"),ct="ElOption",Gl=yt({value:{type:[String,Number,Boolean,Object],required:!0},label:{type:[String,Number]},created:Boolean,disabled:Boolean});function Ql(e,t){const l=le(Ie);l||kl(ct,"usage: <el-select><el-option /></el-select/>");const s=le(sn,{disabled:!1}),n=k(()=>u(pe(l.props.modelValue),e.value)),a=k(()=>{var c;if(l.props.multiple){const h=pe((c=l.props.modelValue)!=null?c:[]);return!n.value&&h.length>=l.props.multipleLimit&&l.props.multipleLimit>0}else return!1}),o=k(()=>{var c;return(c=e.label)!=null?c:ae(e.value)?"":e.value}),f=k(()=>e.value||e.label||""),d=k(()=>e.disabled||t.groupDisabled||a.value),v=Ce(),u=(c=[],h)=>{if(ae(e.value)){const m=l.props.valueKey;return c&&c.some(C=>Sl(Se(C,m))===Se(h,m))}else return c&&c.includes(h)},p=()=>{!e.disabled&&!s.disabled&&(l.states.hoveringIndex=l.optionsArray.indexOf(v.proxy))},r=c=>{const h=new RegExp(en(c),"i");t.visible=h.test(String(o.value))||e.created};return L(()=>o.value,()=>{!e.created&&!l.props.remote&&l.setSelected()}),L(()=>e.value,(c,h)=>{const{remote:m,valueKey:C}=l.props;if((m?c!==h:!Ke(c,h))&&(l.onOptionDestroy(h,v.proxy),l.onOptionCreate(v.proxy)),!e.created&&!m){if(C&&ae(c)&&ae(h)&&c[C]===h[C])return;l.setSelected()}}),L(()=>s.disabled,()=>{t.groupDisabled=s.disabled},{immediate:!0}),{select:l,currentLabel:o,currentValue:f,itemSelected:n,isDisabled:d,hoverItem:p,updateOption:r}}const xl=Q({name:ct,componentName:ct,props:Gl,setup(e){const t=_("select"),l=nn(),s=k(()=>[t.be("dropdown","item"),t.is("disabled",Pe(f)),t.is("selected",Pe(o)),t.is("hovering",Pe(r))]),n=ue({index:-1,groupDisabled:!1,visible:!0,hover:!1}),{currentLabel:a,itemSelected:o,isDisabled:f,select:d,hoverItem:v,updateOption:u}=Ql(e,n),{visible:p,hover:r}=We(n),c=Ce().proxy;d.onOptionCreate(c),wl(()=>{const m=c.value,{selected:C}=d.states,N=C.some(I=>I.value===c.value);j(()=>{d.states.cachedOptions.get(m)===c&&!N&&d.states.cachedOptions.delete(m)}),d.onOptionDestroy(m,c)});function h(){f.value||d.handleOptionSelect(c)}return{ns:t,id:l,containerKls:s,currentLabel:a,itemSelected:o,isDisabled:f,select:d,visible:p,hover:r,states:n,hoverItem:v,updateOption:u,selectOptionClick:h}}});function Yl(e,t){return ve((S(),V("li",{id:e.id,class:E(e.containerKls),role:"option","aria-disabled":e.isDisabled||void 0,"aria-selected":e.itemSelected,onMousemove:e.hoverItem,onClick:W(e.selectOptionClick,["stop"])},[H(e.$slots,"default",{},()=>[R("span",null,oe(e.currentLabel),1)])],42,["id","aria-disabled","aria-selected","onMousemove","onClick"])),[[De,e.visible]])}var bt=re(xl,[["render",Yl],["__file","option.vue"]]);const Jl=Q({name:"ElSelectDropdown",componentName:"ElSelectDropdown",setup(){const e=le(Ie),t=_("select"),l=k(()=>e.props.popperClass),s=k(()=>e.props.multiple),n=k(()=>e.props.fitInputWidth),a=T("");function o(){var f;a.value=`${(f=e.selectRef)==null?void 0:f.offsetWidth}px`}return Te(()=>{o(),Ee(e.selectRef,o)}),{ns:t,minWidth:a,popperClass:l,isMultiple:s,isFitInputWidth:n}}});function Xl(e,t,l,s,n,a){return S(),V("div",{class:E([e.ns.b("dropdown"),e.ns.is("multiple",e.isMultiple),e.popperClass]),style:we({[e.isFitInputWidth?"width":"minWidth"]:e.minWidth})},[e.$slots.header?(S(),V("div",{key:0,class:E(e.ns.be("dropdown","header"))},[H(e.$slots,"header")],2)):P("v-if",!0),H(e.$slots,"default"),e.$slots.footer?(S(),V("div",{key:1,class:E(e.ns.be("dropdown","footer"))},[H(e.$slots,"footer")],2)):P("v-if",!0)],6)}var Zl=re(Jl,[["render",Xl],["__file","select-dropdown.vue"]]);const _l=(e,t)=>{const{t:l}=Xt(),s=nn(),n=_("select"),a=_("input"),o=ue({inputValue:"",options:new Map,cachedOptions:new Map,optionValues:[],selected:[],selectionWidth:0,collapseItemWidth:0,selectedLabel:"",hoveringIndex:-1,previousQuery:null,inputHovering:!1,menuVisibleOnFocus:!1,isBeforeHide:!1}),f=T(),d=T(),v=T(),u=T(),p=T(),r=T(),c=T(),h=T(),m=T(),C=T(),N=T(),{isComposing:I,handleCompositionStart:$,handleCompositionUpdate:O,handleCompositionEnd:M}=Ll({afterComposition:i=>Tt(i)}),{wrapperRef:de,isFocused:q,handleBlur:fe}=Ml(p,{beforeFocus(){return ge.value},afterFocus(){e.automaticDropdown&&!y.value&&(y.value=!0,o.menuVisibleOnFocus=!0)},beforeBlur(i){var g,w;return((g=v.value)==null?void 0:g.isFocusInsideContent(i))||((w=u.value)==null?void 0:w.isFocusInsideContent(i))},afterBlur(){var i;y.value=!1,o.menuVisibleOnFocus=!1,e.validateEvent&&((i=x==null?void 0:x.validate)==null||i.call(x,"blur").catch(g=>Ut()))}}),y=T(!1),K=T(),{form:z,formItem:x}=$l(),{inputId:nt}=Bl(e,{formItemContext:x}),{valueOnClear:lt,isEmptyValue:ot}=Jn(e),ge=k(()=>e.disabled||(z==null?void 0:z.disabled)),Ve=k(()=>G(e.modelValue)?e.modelValue.length>0:!ot(e.modelValue)),b=k(()=>{var i;return(i=z==null?void 0:z.statusIcon)!=null?i:!1}),D=k(()=>e.clearable&&!ge.value&&o.inputHovering&&Ve.value),ee=k(()=>e.remote&&e.filterable&&!e.remoteShowSuffix?"":e.suffixIcon),ye=k(()=>n.is("reverse",!!(ee.value&&y.value))),st=k(()=>(x==null?void 0:x.validateState)||""),un=k(()=>st.value&&Xn[st.value]),fn=k(()=>e.remote?300:0),hn=k(()=>e.remote&&!o.inputValue&&o.options.size===0),pn=k(()=>e.loading?e.loadingText||l("el.select.loading"):e.filterable&&o.inputValue&&o.options.size>0&&$e.value===0?e.noMatchText||l("el.select.noMatch"):o.options.size===0?e.noDataText||l("el.select.noData"):null),$e=k(()=>Y.value.filter(i=>i.visible).length),Y=k(()=>{const i=Array.from(o.options.values()),g=[];return o.optionValues.forEach(w=>{const A=i.findIndex(X=>X.value===w);A>-1&&g.push(i[A])}),g.length>=i.length?g:i}),vn=k(()=>Array.from(o.cachedOptions.values())),gn=k(()=>{const i=Y.value.filter(g=>!g.created).some(g=>g.currentLabel===o.inputValue);return e.filterable&&e.allowCreate&&o.inputValue!==""&&!i}),Et=()=>{e.filterable&&ie(e.filterMethod)||e.filterable&&e.remote&&ie(e.remoteMethod)||Y.value.forEach(i=>{var g;(g=i.updateOption)==null||g.call(i,o.inputValue)})},St=ln(),yn=k(()=>["small"].includes(St.value)?"small":"default"),mn=k({get(){return y.value&&!hn.value},set(i){y.value=i}}),bn=k(()=>{if(e.multiple&&!ne(e.modelValue))return pe(e.modelValue).length===0&&!o.inputValue;const i=G(e.modelValue)?e.modelValue[0]:e.modelValue;return e.filterable||ne(i)?!o.inputValue:!0}),Cn=k(()=>{var i;const g=(i=e.placeholder)!=null?i:l("el.select.placeholder");return e.multiple||!Ve.value?g:o.selectedLabel}),kn=k(()=>Ft?null:"mouseenter");L(()=>e.modelValue,(i,g)=>{e.multiple&&e.filterable&&!e.reserveKeyword&&(o.inputValue="",je("")),qe(),!Ke(i,g)&&e.validateEvent&&(x==null||x.validate("change").catch(w=>Ut()))},{flush:"post",deep:!0}),L(()=>y.value,i=>{i?je(o.inputValue):(o.inputValue="",o.previousQuery=null,o.isBeforeHide=!0),t("visible-change",i)}),L(()=>o.options.entries(),()=>{gt&&(qe(),e.defaultFirstOption&&(e.filterable||e.remote)&&$e.value&&wt())},{flush:"post"}),L([()=>o.hoveringIndex,Y],([i])=>{Zn(i)&&i>-1?K.value=Y.value[i]||{}:K.value={},Y.value.forEach(g=>{g.hover=K.value===g})}),Kl(()=>{o.isBeforeHide||Et()});const je=i=>{o.previousQuery===i||I.value||(o.previousQuery=i,e.filterable&&ie(e.filterMethod)?e.filterMethod(i):e.filterable&&e.remote&&ie(e.remoteMethod)&&e.remoteMethod(i),e.defaultFirstOption&&(e.filterable||e.remote)&&$e.value?j(wt):j(Nn))},wt=()=>{const i=Y.value.filter(X=>X.visible&&!X.disabled&&!X.states.groupDisabled),g=i.find(X=>X.created),w=i[0],A=Y.value.map(X=>X.value);o.hoveringIndex=Bt(A,g||w)},qe=()=>{if(e.multiple)o.selectedLabel="";else{const g=G(e.modelValue)?e.modelValue[0]:e.modelValue,w=Kt(g);o.selectedLabel=w.currentLabel,o.selected=[w];return}const i=[];ne(e.modelValue)||pe(e.modelValue).forEach(g=>{i.push(Kt(g))}),o.selected=i},Kt=i=>{let g;const w=Ol(i);for(let ke=o.cachedOptions.size-1;ke>=0;ke--){const he=vn.value[ke];if(w?Se(he.value,e.valueKey)===Se(i,e.valueKey):he.value===i){g={value:i,currentLabel:he.currentLabel,get isDisabled(){return he.isDisabled}};break}}if(g)return g;const A=w?i.label:i!=null?i:"";return{value:i,currentLabel:A}},Nn=()=>{o.hoveringIndex=Y.value.findIndex(i=>o.selected.some(g=>Qe(g)===Qe(i)))},En=()=>{o.selectionWidth=Number.parseFloat(window.getComputedStyle(d.value).width)},Sn=()=>{o.collapseItemWidth=C.value.getBoundingClientRect().width},at=()=>{var i,g;(g=(i=v.value)==null?void 0:i.updatePopper)==null||g.call(i)},Ot=()=>{var i,g;(g=(i=u.value)==null?void 0:i.updatePopper)==null||g.call(i)},Dt=()=>{o.inputValue.length>0&&!y.value&&(y.value=!0),je(o.inputValue)},Tt=i=>{if(o.inputValue=i.target.value,e.remote)It();else return Dt()},It=hl(()=>{Dt()},fn.value),Be=i=>{Ke(e.modelValue,i)||t(on,i)},wn=i=>ml(i,g=>{const w=o.cachedOptions.get(g);return w&&!w.disabled&&!w.states.groupDisabled}),Kn=i=>{if(e.multiple&&i.code!==ce.delete&&i.target.value.length<=0){const g=pe(e.modelValue).slice(),w=wn(g);if(w<0)return;const A=g[w];g.splice(w,1),t(te,g),Be(g),t("remove-tag",A)}},On=(i,g)=>{const w=o.selected.indexOf(g);if(w>-1&&!ge.value){const A=pe(e.modelValue).slice();A.splice(w,1),t(te,A),Be(A),t("remove-tag",g.value)}i.stopPropagation(),Ge()},Vt=i=>{i.stopPropagation();const g=e.multiple?[]:lt.value;if(e.multiple)for(const w of o.selected)w.isDisabled&&g.push(w.value);t(te,g),Be(g),o.hoveringIndex=-1,y.value=!1,t("clear"),Ge()},$t=i=>{var g;if(e.multiple){const w=pe((g=e.modelValue)!=null?g:[]).slice(),A=Bt(w,i);A>-1?w.splice(A,1):(e.multipleLimit<=0||w.length<e.multipleLimit)&&w.push(i.value),t(te,w),Be(w),i.created&&je(""),e.filterable&&!e.reserveKeyword&&(o.inputValue="")}else t(te,i.value),Be(i.value),y.value=!1;Ge(),!y.value&&j(()=>{Ue(i)})},Bt=(i,g)=>ne(g)?-1:ae(g.value)?i.findIndex(w=>Ke(Se(w,e.valueKey),Qe(g))):i.indexOf(g.value),Ue=i=>{var g,w,A,X,ke;const he=G(i)?i[0]:i;let xe=null;if(he!=null&&he.value){const Le=Y.value.filter(qn=>qn.value===he.value);Le.length>0&&(xe=Le[0].$el)}if(v.value&&xe){const Le=(X=(A=(w=(g=v.value)==null?void 0:g.popperRef)==null?void 0:w.contentRef)==null?void 0:A.querySelector)==null?void 0:X.call(A,`.${n.be("dropdown","wrap")}`);Le&&Fl(Le,xe)}(ke=N.value)==null||ke.handleScroll()},Dn=i=>{o.options.set(i.value,i),o.cachedOptions.set(i.value,i)},Tn=(i,g)=>{o.options.get(i)===g&&o.options.delete(i)},In=k(()=>{var i,g;return(g=(i=v.value)==null?void 0:i.popperRef)==null?void 0:g.contentRef}),Vn=()=>{o.isBeforeHide=!1,j(()=>{var i;(i=N.value)==null||i.update(),Ue(o.selected)})},Ge=()=>{var i;(i=p.value)==null||i.focus()},$n=()=>{var i;if(y.value){y.value=!1,j(()=>{var g;return(g=p.value)==null?void 0:g.blur()});return}(i=p.value)==null||i.blur()},Bn=i=>{Vt(i)},Ln=i=>{if(y.value=!1,q.value){const g=new FocusEvent("focus",i);j(()=>fe(g))}},Mn=()=>{o.inputValue.length>0?o.inputValue="":y.value=!1},Lt=()=>{ge.value||(Ft&&(o.inputHovering=!0),o.menuVisibleOnFocus?o.menuVisibleOnFocus=!1:y.value=!y.value)},An=()=>{if(!y.value)Lt();else{const i=Y.value[o.hoveringIndex];i&&!i.isDisabled&&$t(i)}},Qe=i=>ae(i.value)?Se(i.value,e.valueKey):i.value,Rn=k(()=>Y.value.filter(i=>i.visible).every(i=>i.isDisabled)),Pn=k(()=>e.multiple?e.collapseTags?o.selected.slice(0,e.maxCollapseTags):o.selected:[]),Hn=k(()=>e.multiple?e.collapseTags?o.selected.slice(e.maxCollapseTags):[]:[]),Mt=i=>{if(!y.value){y.value=!0;return}if(!(o.options.size===0||$e.value===0||I.value)&&!Rn.value){i==="next"?(o.hoveringIndex++,o.hoveringIndex===o.options.size&&(o.hoveringIndex=0)):i==="prev"&&(o.hoveringIndex--,o.hoveringIndex<0&&(o.hoveringIndex=o.options.size-1));const g=Y.value[o.hoveringIndex];(g.isDisabled||!g.visible)&&Mt(i),j(()=>Ue(K.value))}},zn=()=>{if(!d.value)return 0;const i=window.getComputedStyle(d.value);return Number.parseFloat(i.gap||"6px")},Fn=k(()=>{const i=zn();return{maxWidth:`${C.value&&e.maxCollapseTags===1?o.selectionWidth-o.collapseItemWidth-i:o.selectionWidth}px`}}),Wn=k(()=>({maxWidth:`${o.selectionWidth}px`})),jn=i=>{t("popup-scroll",i)};return Ee(d,En),Ee(h,at),Ee(de,at),Ee(m,Ot),Ee(C,Sn),Te(()=>{qe()}),{inputId:nt,contentId:s,nsSelect:n,nsInput:a,states:o,isFocused:q,expanded:y,optionsArray:Y,hoverOption:K,selectSize:St,filteredOptionsCount:$e,updateTooltip:at,updateTagTooltip:Ot,debouncedOnInputChange:It,onInput:Tt,deletePrevTag:Kn,deleteTag:On,deleteSelected:Vt,handleOptionSelect:$t,scrollToOption:Ue,hasModelValue:Ve,shouldShowPlaceholder:bn,currentPlaceholder:Cn,mouseEnterEventName:kn,needStatusIcon:b,showClose:D,iconComponent:ee,iconReverse:ye,validateState:st,validateIcon:un,showNewOption:gn,updateOptions:Et,collapseTagSize:yn,setSelected:qe,selectDisabled:ge,emptyText:pn,handleCompositionStart:$,handleCompositionUpdate:O,handleCompositionEnd:M,onOptionCreate:Dn,onOptionDestroy:Tn,handleMenuEnter:Vn,focus:Ge,blur:$n,handleClearClick:Bn,handleClickOutside:Ln,handleEsc:Mn,toggleMenu:Lt,selectOption:An,getValueKey:Qe,navigateOptions:Mt,dropdownMenuVisible:mn,showTagList:Pn,collapseTagList:Hn,popupScroll:jn,tagStyle:Fn,collapseTagStyle:Wn,popperRef:In,inputRef:p,tooltipRef:v,tagTooltipRef:u,prefixRef:r,suffixRef:c,selectRef:f,wrapperRef:de,selectionRef:d,scrollbarRef:N,menuRef:h,tagMenuRef:m,collapseItemRef:C}};var eo=Q({name:"ElOptions",setup(e,{slots:t}){const l=le(Ie);let s=[];return()=>{var n,a;const o=(n=t.default)==null?void 0:n.call(t),f=[];function d(v){G(v)&&v.forEach(u=>{var p,r,c,h;const m=(p=(u==null?void 0:u.type)||{})==null?void 0:p.name;m==="ElOptionGroup"?d(!mt(u.children)&&!G(u.children)&&ie((r=u.children)==null?void 0:r.default)?(c=u.children)==null?void 0:c.default():u.children):m==="ElOption"?f.push((h=u.props)==null?void 0:h.value):G(u.children)&&d(u.children)})}return o.length&&d((a=o[0])==null?void 0:a.children),Ke(f,s)||(s=f,l&&(l.states.optionValues=f)),o}}});const to=yt(B(B({name:String,id:String,modelValue:{type:Me([Array,String,Number,Boolean,Object]),default:void 0},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:nl,effect:{type:Me(String),default:"light"},disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:{type:String,default:""},popperOptions:{type:Me(Object),default:()=>({})},remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},teleported:jt.teleported,persistent:{type:Boolean,default:!0},clearIcon:{type:rt,default:tl},fitInputWidth:Boolean,suffixIcon:{type:rt,default:el},tagType:Z(B({},qt.type),{default:"info"}),tagEffect:Z(B({},qt.effect),{default:"light"}),validateEvent:{type:Boolean,default:!0},remoteShowSuffix:Boolean,showArrow:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:Me(String),values:pl,default:"bottom-start"},fallbackPlacements:{type:Me(Array),default:["bottom-start","top-start","right","left"]},tabindex:{type:[String,Number],default:0},appendTo:jt.appendTo},_n),Rl(["ariaLabel"])));gl.scroll;const Qt="ElSelect",no=Q({name:Qt,componentName:Qt,components:{ElSelectMenu:Zl,ElOption:bt,ElOptions:eo,ElTag:bl,ElScrollbar:yl,ElTooltip:vl,ElIcon:Zt},directives:{ClickOutside:Pl},props:to,emits:[te,on,"remove-tag","clear","visible-change","focus","blur","popup-scroll"],setup(e,{emit:t,slots:l}){const s=Ce();s.appContext.config.warnHandler=(...r)=>{!r[0]||r[0].includes('Slot "default" invoked outside of the render function')||console.warn(...r)};const n=k(()=>{const{modelValue:r,multiple:c}=e,h=c?[]:void 0;return G(r)?c?r:h:c?h:r}),a=ue(Z(B({},We(e)),{modelValue:n})),o=_l(a,t),{calculatorRef:f,inputStyle:d}=Cl(),v=r=>r.reduce((c,h)=>(c.push(h),h.children&&h.children.length>0&&c.push(...v(h.children)),c),[]),u=r=>{Hl(r||[]).forEach(h=>{var m;if(ae(h)&&(h.type.name==="ElOption"||h.type.name==="ElTree")){const C=h.type.name;if(C==="ElTree"){const N=((m=h.props)==null?void 0:m.data)||[];v(N).forEach($=>{$.currentLabel=$.label||(ae($.value)?"":$.value),o.onOptionCreate($)})}else if(C==="ElOption"){const N=B({},h.props);N.currentLabel=N.label||(ae(N.value)?"":N.value),o.onOptionCreate(N)}}})};L(()=>{var r;return(r=l.default)==null?void 0:r.call(l)},r=>{e.persistent||u(r)},{immediate:!0}),be(Ie,ue({props:a,states:o.states,selectRef:o.selectRef,optionsArray:o.optionsArray,setSelected:o.setSelected,handleOptionSelect:o.handleOptionSelect,onOptionCreate:o.onOptionCreate,onOptionDestroy:o.onOptionDestroy}));const p=k(()=>e.multiple?o.states.selected.map(r=>r.currentLabel):o.states.selectedLabel);return Z(B({},o),{modelValue:n,selectedLabel:p,calculatorRef:f,inputStyle:d})}});function lo(e,t){const l=J("el-tag"),s=J("el-tooltip"),n=J("el-icon"),a=J("el-option"),o=J("el-options"),f=J("el-scrollbar"),d=J("el-select-menu"),v=Dl("click-outside");return ve((S(),V("div",{ref:"selectRef",class:E([e.nsSelect.b(),e.nsSelect.m(e.selectSize)]),[Tl(e.mouseEnterEventName)]:u=>e.states.inputHovering=!0,onMouseleave:u=>e.states.inputHovering=!1},[se(s,{ref:"tooltipRef",visible:e.dropdownMenuVisible,placement:e.placement,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"popper-options":e.popperOptions,"fallback-placements":e.fallbackPlacements,effect:e.effect,pure:"",trigger:"click",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,persistent:e.persistent,"append-to":e.appendTo,"show-arrow":e.showArrow,offset:e.offset,onBeforeShow:e.handleMenuEnter,onHide:u=>e.states.isBeforeHide=!1},{default:F(()=>{var u;return[R("div",{ref:"wrapperRef",class:E([e.nsSelect.e("wrapper"),e.nsSelect.is("focused",e.isFocused),e.nsSelect.is("hovering",e.states.inputHovering),e.nsSelect.is("filterable",e.filterable),e.nsSelect.is("disabled",e.selectDisabled)]),onClick:W(e.toggleMenu,["prevent"])},[e.$slots.prefix?(S(),V("div",{key:0,ref:"prefixRef",class:E(e.nsSelect.e("prefix"))},[H(e.$slots,"prefix")],2)):P("v-if",!0),R("div",{ref:"selectionRef",class:E([e.nsSelect.e("selection"),e.nsSelect.is("near",e.multiple&&!e.$slots.prefix&&!!e.states.selected.length)])},[e.multiple?H(e.$slots,"tag",{key:0},()=>[(S(!0),V(_e,null,et(e.showTagList,p=>(S(),V("div",{key:e.getValueKey(p),class:E(e.nsSelect.e("selected-item"))},[se(l,{closable:!e.selectDisabled&&!p.isDisabled,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:we(e.tagStyle),onClose:r=>e.deleteTag(r,p)},{default:F(()=>[R("span",{class:E(e.nsSelect.e("tags-text"))},[H(e.$slots,"label",{label:p.currentLabel,value:p.value},()=>[Gt(oe(p.currentLabel),1)])],2)]),_:2},1032,["closable","size","type","effect","style","onClose"])],2))),128)),e.collapseTags&&e.states.selected.length>e.maxCollapseTags?(S(),U(s,{key:0,ref:"tagTooltipRef",disabled:e.dropdownMenuVisible||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:F(()=>[R("div",{ref:"collapseItemRef",class:E(e.nsSelect.e("selected-item"))},[se(l,{closable:!1,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:we(e.collapseTagStyle)},{default:F(()=>[R("span",{class:E(e.nsSelect.e("tags-text"))}," + "+oe(e.states.selected.length-e.maxCollapseTags),3)]),_:1},8,["size","type","effect","style"])],2)]),content:F(()=>[R("div",{ref:"tagMenuRef",class:E(e.nsSelect.e("selection"))},[(S(!0),V(_e,null,et(e.collapseTagList,p=>(S(),V("div",{key:e.getValueKey(p),class:E(e.nsSelect.e("selected-item"))},[se(l,{class:"in-tooltip",closable:!e.selectDisabled&&!p.isDisabled,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",onClose:r=>e.deleteTag(r,p)},{default:F(()=>[R("span",{class:E(e.nsSelect.e("tags-text"))},[H(e.$slots,"label",{label:p.currentLabel,value:p.value},()=>[Gt(oe(p.currentLabel),1)])],2)]),_:2},1032,["closable","size","type","effect","onClose"])],2))),128))],2)]),_:3},8,["disabled","effect","teleported"])):P("v-if",!0)]):P("v-if",!0),R("div",{class:E([e.nsSelect.e("selected-item"),e.nsSelect.e("input-wrapper"),e.nsSelect.is("hidden",!e.filterable)])},[ve(R("input",{id:e.inputId,ref:"inputRef","onUpdate:modelValue":p=>e.states.inputValue=p,type:"text",name:e.name,class:E([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,autocomplete:e.autocomplete,style:we(e.inputStyle),tabindex:e.tabindex,role:"combobox",readonly:!e.filterable,spellcheck:"false","aria-activedescendant":((u=e.hoverOption)==null?void 0:u.id)||"","aria-controls":e.contentId,"aria-expanded":e.dropdownMenuVisible,"aria-label":e.ariaLabel,"aria-autocomplete":"none","aria-haspopup":"listbox",onKeydown:[Ae(W(p=>e.navigateOptions("next"),["stop","prevent"]),["down"]),Ae(W(p=>e.navigateOptions("prev"),["stop","prevent"]),["up"]),Ae(W(e.handleEsc,["stop","prevent"]),["esc"]),Ae(W(e.selectOption,["stop","prevent"]),["enter"]),Ae(W(e.deletePrevTag,["stop"]),["delete"])],onCompositionstart:e.handleCompositionStart,onCompositionupdate:e.handleCompositionUpdate,onCompositionend:e.handleCompositionEnd,onInput:e.onInput,onClick:W(e.toggleMenu,["stop"])},null,46,["id","onUpdate:modelValue","name","disabled","autocomplete","tabindex","readonly","aria-activedescendant","aria-controls","aria-expanded","aria-label","onKeydown","onCompositionstart","onCompositionupdate","onCompositionend","onInput","onClick"]),[[ll,e.states.inputValue]]),e.filterable?(S(),V("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:E(e.nsSelect.e("input-calculator")),textContent:oe(e.states.inputValue)},null,10,["textContent"])):P("v-if",!0)],2),e.shouldShowPlaceholder?(S(),V("div",{key:1,class:E([e.nsSelect.e("selected-item"),e.nsSelect.e("placeholder"),e.nsSelect.is("transparent",!e.hasModelValue||e.expanded&&!e.states.inputValue)])},[e.hasModelValue?H(e.$slots,"label",{key:0,label:e.currentPlaceholder,value:e.modelValue},()=>[R("span",null,oe(e.currentPlaceholder),1)]):(S(),V("span",{key:1},oe(e.currentPlaceholder),1))],2)):P("v-if",!0)],2),R("div",{ref:"suffixRef",class:E(e.nsSelect.e("suffix"))},[e.iconComponent&&!e.showClose?(S(),U(n,{key:0,class:E([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.iconReverse])},{default:F(()=>[(S(),U(He(e.iconComponent)))]),_:1},8,["class"])):P("v-if",!0),e.showClose&&e.clearIcon?(S(),U(n,{key:1,class:E([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.nsSelect.e("clear")]),onClick:e.handleClearClick},{default:F(()=>[(S(),U(He(e.clearIcon)))]),_:1},8,["class","onClick"])):P("v-if",!0),e.validateState&&e.validateIcon&&e.needStatusIcon?(S(),U(n,{key:2,class:E([e.nsInput.e("icon"),e.nsInput.e("validateIcon"),e.nsInput.is("loading",e.validateState==="validating")])},{default:F(()=>[(S(),U(He(e.validateIcon)))]),_:1},8,["class"])):P("v-if",!0)],2)],10,["onClick"])]}),content:F(()=>[se(d,{ref:"menuRef"},{default:F(()=>[e.$slots.header?(S(),V("div",{key:0,class:E(e.nsSelect.be("dropdown","header")),onClick:W(()=>{},["stop"])},[H(e.$slots,"header")],10,["onClick"])):P("v-if",!0),ve(se(f,{id:e.contentId,ref:"scrollbarRef",tag:"ul","wrap-class":e.nsSelect.be("dropdown","wrap"),"view-class":e.nsSelect.be("dropdown","list"),class:E([e.nsSelect.is("empty",e.filteredOptionsCount===0)]),role:"listbox","aria-label":e.ariaLabel,"aria-orientation":"vertical",onScroll:e.popupScroll},{default:F(()=>[e.showNewOption?(S(),U(a,{key:0,value:e.states.inputValue,created:!0},null,8,["value"])):P("v-if",!0),se(o,null,{default:F(()=>[H(e.$slots,"default")]),_:3})]),_:3},8,["id","wrap-class","view-class","class","aria-label","onScroll"]),[[De,e.states.options.size>0&&!e.loading]]),e.$slots.loading&&e.loading?(S(),V("div",{key:1,class:E(e.nsSelect.be("dropdown","loading"))},[H(e.$slots,"loading")],2)):e.loading||e.filteredOptionsCount===0?(S(),V("div",{key:2,class:E(e.nsSelect.be("dropdown","empty"))},[H(e.$slots,"empty",{},()=>[R("span",null,oe(e.emptyText),1)])],2)):P("v-if",!0),e.$slots.footer?(S(),V("div",{key:3,class:E(e.nsSelect.be("dropdown","footer")),onClick:W(()=>{},["stop"])},[H(e.$slots,"footer")],10,["onClick"])):P("v-if",!0)]),_:3},512)]),_:3},8,["visible","placement","teleported","popper-class","popper-options","fallback-placements","effect","transition","persistent","append-to","show-arrow","offset","onBeforeShow","onHide"])],16,["onMouseleave"])),[[v,e.handleClickOutside,e.popperRef]])}var oo=re(no,[["render",lo],["__file","select.vue"]]);const so=Q({name:"ElOptionGroup",componentName:"ElOptionGroup",props:{label:String,disabled:Boolean},setup(e){const t=_("select"),l=T(),s=Ce(),n=T([]);be(sn,ue(B({},We(e))));const a=k(()=>n.value.some(v=>v.visible===!0)),o=v=>{var u;return v.type.name==="ElOption"&&!!((u=v.component)!=null&&u.proxy)},f=v=>{const u=pe(v),p=[];return u.forEach(r=>{var c;Il(r)&&(o(r)?p.push(r.component.proxy):G(r.children)&&r.children.length?p.push(...f(r.children)):(c=r.component)!=null&&c.subTree&&p.push(...f(r.component.subTree)))}),p},d=()=>{n.value=f(s.subTree)};return Te(()=>{d()}),ol(l,d,{attributes:!0,subtree:!0,childList:!0}),{groupRef:l,visible:a,ns:t}}});function ao(e,t,l,s,n,a){return ve((S(),V("ul",{ref:"groupRef",class:E(e.ns.be("group","wrap"))},[R("li",{class:E(e.ns.be("group","title"))},oe(e.label),3),R("li",null,[R("ul",{class:E(e.ns.b("group"))},[H(e.$slots,"default")],2)])],2)),[[De,e.visible]])}var an=re(so,[["render",ao],["__file","option-group.vue"]]);const ut=Fe(oo,{Option:bt,OptionGroup:an}),xt=_t(bt);_t(an);const io=yt({type:{type:String,values:["primary","success","info","warning","danger",""],default:""},size:{type:String,values:sl,default:""},truncated:Boolean,lineClamp:{type:[String,Number]},tag:{type:String,default:"span"}}),ro=Q({name:"ElText"}),co=Q(Z(B({},ro),{props:io,setup(e){const t=e,l=T(),s=ln(),n=_("text"),a=k(()=>[n.b(),n.m(t.type),n.m(s.value),n.is("truncated",t.truncated),n.is("line-clamp",!ne(t.lineClamp))]),o=()=>{var f,d,v,u,p,r,c;if(Vl().title)return;let m=!1;const C=((f=l.value)==null?void 0:f.textContent)||"";if(t.truncated){const N=(d=l.value)==null?void 0:d.offsetWidth,I=(v=l.value)==null?void 0:v.scrollWidth;N&&I&&I>N&&(m=!0)}else if(!ne(t.lineClamp)){const N=(u=l.value)==null?void 0:u.offsetHeight,I=(p=l.value)==null?void 0:p.scrollHeight;N&&I&&I>N&&(m=!0)}m?(r=l.value)==null||r.setAttribute("title",C):(c=l.value)==null||c.removeAttribute("title")};return Te(o),tn(o),(f,d)=>(S(),U(He(f.tag),{ref_key:"textRef",ref:l,class:E(Pe(a)),style:we({"-webkit-line-clamp":f.lineClamp})},{default:F(()=>[H(f.$slots,"default")]),_:3},8,["class","style"]))}}));var uo=re(co,[["__file","text.vue"]]);const fo=Fe(uo),Oe="$treeNodeId",Yt=function(e,t){!t||t[Oe]||Object.defineProperty(t,Oe,{value:e.id,enumerable:!1,configurable:!1,writable:!1})},Ct=(e,t)=>t==null?void 0:t[e||Oe],ft=(e,t,l)=>{const s=e.value.currentNode;l();const n=e.value.currentNode;s!==n&&t("current-change",n?n.data:null,n)},ht=e=>{let t=!0,l=!0,s=!0;for(let n=0,a=e.length;n<a;n++){const o=e[n];(o.checked!==!0||o.indeterminate)&&(t=!1,o.disabled||(s=!1)),(o.checked!==!1||o.indeterminate)&&(l=!1)}return{all:t,none:l,allWithoutDisable:s,half:!t&&!l}},Re=function(e){if(e.childNodes.length===0||e.loading)return;const{all:t,none:l,half:s}=ht(e.childNodes);t?(e.checked=!0,e.indeterminate=!1):s?(e.checked=!1,e.indeterminate=!0):l&&(e.checked=!1,e.indeterminate=!1);const n=e.parent;!n||n.level===0||e.store.checkStrictly||Re(n)},Je=function(e,t){const l=e.store.props,s=e.data||{},n=l[t];if(ie(n))return n(s,e);if(mt(n))return s[n];if(ne(n)){const a=s[t];return ne(a)?"":a}};let ho=0;class me{constructor(t){this.id=ho++,this.text=null,this.checked=!1,this.indeterminate=!1,this.data=null,this.expanded=!1,this.parent=null,this.visible=!0,this.isCurrent=!1,this.canFocus=!1;for(const l in t)dt(t,l)&&(this[l]=t[l]);this.level=0,this.loaded=!1,this.childNodes=[],this.loading=!1,this.parent&&(this.level=this.parent.level+1)}initialize(){const t=this.store;if(!t)throw new Error("[Node]store is required!");t.registerNode(this);const l=t.props;if(l&&typeof l.isLeaf!="undefined"){const a=Je(this,"isLeaf");al(a)&&(this.isLeafByUser=a)}if(t.lazy!==!0&&this.data?(this.setData(this.data),t.defaultExpandAll&&(this.expanded=!0,this.canFocus=!0)):this.level>0&&t.lazy&&t.defaultExpandAll&&!this.isLeafByUser&&this.expand(),G(this.data)||Yt(this,this.data),!this.data)return;const s=t.defaultExpandedKeys,n=t.key;n&&s&&s.includes(this.key)&&this.expand(null,t.autoExpandParent),n&&t.currentNodeKey!==void 0&&this.key===t.currentNodeKey&&(t.currentNode=this,t.currentNode.isCurrent=!0),t.lazy&&t._initDefaultCheckedNode(this),this.updateLeafState(),this.parent&&(this.level===1||this.parent.expanded===!0)&&(this.canFocus=!0)}setData(t){G(t)||Yt(this,t),this.data=t,this.childNodes=[];let l;this.level===0&&G(this.data)?l=this.data:l=Je(this,"children")||[];for(let s=0,n=l.length;s<n;s++)this.insertChild({data:l[s]})}get label(){return Je(this,"label")}get key(){const t=this.store.key;return this.data?this.data[t]:null}get disabled(){return Je(this,"disabled")}get nextSibling(){const t=this.parent;if(t){const l=t.childNodes.indexOf(this);if(l>-1)return t.childNodes[l+1]}return null}get previousSibling(){const t=this.parent;if(t){const l=t.childNodes.indexOf(this);if(l>-1)return l>0?t.childNodes[l-1]:null}return null}contains(t,l=!0){return(this.childNodes||[]).some(s=>s===t||l&&s.contains(t))}remove(){const t=this.parent;t&&t.removeChild(this)}insertChild(t,l,s){if(!t)throw new Error("InsertChild error: child is required.");if(!(t instanceof me)){if(!s){const n=this.getChildren(!0);n.includes(t.data)||(ne(l)||l<0?n.push(t.data):n.splice(l,0,t.data))}Object.assign(t,{parent:this,store:this.store}),t=ue(new me(t)),t instanceof me&&t.initialize()}t.level=this.level+1,ne(l)||l<0?this.childNodes.push(t):this.childNodes.splice(l,0,t),this.updateLeafState()}insertBefore(t,l){let s;l&&(s=this.childNodes.indexOf(l)),this.insertChild(t,s)}insertAfter(t,l){let s;l&&(s=this.childNodes.indexOf(l),s!==-1&&(s+=1)),this.insertChild(t,s)}removeChild(t){const l=this.getChildren()||[],s=l.indexOf(t.data);s>-1&&l.splice(s,1);const n=this.childNodes.indexOf(t);n>-1&&(this.store&&this.store.deregisterNode(t),t.parent=null,this.childNodes.splice(n,1)),this.updateLeafState()}removeChildByData(t){let l=null;for(let s=0;s<this.childNodes.length;s++)if(this.childNodes[s].data===t){l=this.childNodes[s];break}l&&this.removeChild(l)}expand(t,l){const s=()=>{if(l){let n=this.parent;for(;n.level>0;)n.expanded=!0,n=n.parent}this.expanded=!0,t&&t(),this.childNodes.forEach(n=>{n.canFocus=!0})};this.shouldLoadData()?this.loadData(n=>{G(n)&&(this.checked?this.setChecked(!0,!0):this.store.checkStrictly||Re(this),s())}):s()}doCreateChildren(t,l={}){t.forEach(s=>{this.insertChild(Object.assign({data:s},l),void 0,!0)})}collapse(){this.expanded=!1,this.childNodes.forEach(t=>{t.canFocus=!1})}shouldLoadData(){return this.store.lazy===!0&&this.store.load&&!this.loaded}updateLeafState(){if(this.store.lazy===!0&&this.loaded!==!0&&typeof this.isLeafByUser!="undefined"){this.isLeaf=this.isLeafByUser;return}const t=this.childNodes;if(!this.store.lazy||this.store.lazy===!0&&this.loaded===!0){this.isLeaf=!t||t.length===0;return}this.isLeaf=!1}setChecked(t,l,s,n){if(this.indeterminate=t==="half",this.checked=t===!0,this.store.checkStrictly)return;if(!(this.shouldLoadData()&&!this.store.checkDescendants)){const{all:o,allWithoutDisable:f}=ht(this.childNodes);!this.isLeaf&&!o&&f&&(this.checked=!1,t=!1);const d=()=>{if(l){const v=this.childNodes;for(let r=0,c=v.length;r<c;r++){const h=v[r];n=n||t!==!1;const m=h.disabled?h.checked:n;h.setChecked(m,l,!0,n)}const{half:u,all:p}=ht(v);p||(this.checked=p,this.indeterminate=u)}};if(this.shouldLoadData()){this.loadData(()=>{d(),Re(this)},{checked:t!==!1});return}else d()}const a=this.parent;!a||a.level===0||s||Re(a)}getChildren(t=!1){if(this.level===0)return this.data;const l=this.data;if(!l)return null;const s=this.store.props;let n="children";return s&&(n=s.children||"children"),ne(l[n])&&(l[n]=null),t&&!l[n]&&(l[n]=[]),l[n]}updateChildren(){const t=this.getChildren()||[],l=this.childNodes.map(a=>a.data),s={},n=[];t.forEach((a,o)=>{const f=a[Oe];!!f&&l.findIndex(v=>v[Oe]===f)>=0?s[f]={index:o,data:a}:n.push({index:o,data:a})}),this.store.lazy||l.forEach(a=>{s[a[Oe]]||this.removeChildByData(a)}),n.forEach(({index:a,data:o})=>{this.insertChild({data:o},a)}),this.updateLeafState()}loadData(t,l={}){if(this.store.lazy===!0&&this.store.load&&!this.loaded&&(!this.loading||Object.keys(l).length)){this.loading=!0;const s=a=>{this.childNodes=[],this.doCreateChildren(a,l),this.loaded=!0,this.loading=!1,this.updateLeafState(),t&&t.call(this,a)},n=()=>{this.loading=!1};this.store.load(this,s,n)}else t&&t.call(this)}eachNode(t){const l=[this];for(;l.length;){const s=l.shift();l.unshift(...s.childNodes),t(s)}}reInitChecked(){this.store.checkStrictly||Re(this)}}class po{constructor(t){this.currentNode=null,this.currentNodeKey=null;for(const l in t)dt(t,l)&&(this[l]=t[l]);this.nodesMap={}}initialize(){if(this.root=new me({data:this.data,store:this}),this.root.initialize(),this.lazy&&this.load){const t=this.load;t(this.root,l=>{this.root.doCreateChildren(l),this._initDefaultCheckedNodes()})}else this._initDefaultCheckedNodes()}filter(t){const l=this.filterNodeMethod,s=this.lazy,n=function(a){return zt(this,null,function*(){const o=a.root?a.root.childNodes:a.childNodes;for(const[f,d]of o.entries())d.visible=l.call(d,t,d.data,d),f%80===0&&f>0&&(yield j()),yield n(d);if(!a.visible&&o.length){let f=!0;f=!o.some(d=>d.visible),a.root?a.root.visible=f===!1:a.visible=f===!1}t&&a.visible&&!a.isLeaf&&(!s||a.loaded)&&a.expand()})};n(this)}setData(t){t!==this.root.data?(this.nodesMap={},this.root.setData(t),this._initDefaultCheckedNodes(),this.setCurrentNodeKey(this.currentNodeKey)):this.root.updateChildren()}getNode(t){if(t instanceof me)return t;const l=ae(t)?Ct(this.key,t):t;return this.nodesMap[l]||null}insertBefore(t,l){const s=this.getNode(l);s.parent.insertBefore({data:t},s)}insertAfter(t,l){const s=this.getNode(l);s.parent.insertAfter({data:t},s)}remove(t){const l=this.getNode(t);l&&l.parent&&(l===this.currentNode&&(this.currentNode=null),l.parent.removeChild(l))}append(t,l){const s=Wt(l)?this.root:this.getNode(l);s&&s.insertChild({data:t})}_initDefaultCheckedNodes(){const t=this.defaultCheckedKeys||[],l=this.nodesMap;t.forEach(s=>{const n=l[s];n&&n.setChecked(!0,!this.checkStrictly)})}_initDefaultCheckedNode(t){(this.defaultCheckedKeys||[]).includes(t.key)&&t.setChecked(!0,!this.checkStrictly)}setDefaultCheckedKey(t){t!==this.defaultCheckedKeys&&(this.defaultCheckedKeys=t,this._initDefaultCheckedNodes())}registerNode(t){const l=this.key;!t||!t.data||(l?t.key!==void 0&&(this.nodesMap[t.key]=t):this.nodesMap[t.id]=t)}deregisterNode(t){!this.key||!t||!t.data||(t.childNodes.forEach(s=>{this.deregisterNode(s)}),delete this.nodesMap[t.key])}getCheckedNodes(t=!1,l=!1){const s=[],n=function(a){(a.root?a.root.childNodes:a.childNodes).forEach(f=>{(f.checked||l&&f.indeterminate)&&(!t||t&&f.isLeaf)&&s.push(f.data),n(f)})};return n(this),s}getCheckedKeys(t=!1){return this.getCheckedNodes(t).map(l=>(l||{})[this.key])}getHalfCheckedNodes(){const t=[],l=function(s){(s.root?s.root.childNodes:s.childNodes).forEach(a=>{a.indeterminate&&t.push(a.data),l(a)})};return l(this),t}getHalfCheckedKeys(){return this.getHalfCheckedNodes().map(t=>(t||{})[this.key])}_getAllNodes(){const t=[],l=this.nodesMap;for(const s in l)dt(l,s)&&t.push(l[s]);return t}updateChildren(t,l){const s=this.nodesMap[t];if(!s)return;const n=s.childNodes;for(let a=n.length-1;a>=0;a--){const o=n[a];this.remove(o.data)}for(let a=0,o=l.length;a<o;a++){const f=l[a];this.append(f,s.data)}}_setCheckedKeys(t,l=!1,s){const n=this._getAllNodes().sort((d,v)=>d.level-v.level),a=Object.create(null),o=Object.keys(s);n.forEach(d=>d.setChecked(!1,!1));const f=d=>{d.childNodes.forEach(v=>{var u;a[v.data[t]]=!0,(u=v.childNodes)!=null&&u.length&&f(v)})};for(let d=0,v=n.length;d<v;d++){const u=n[d],p=u.data[t].toString();if(!o.includes(p)){u.checked&&!a[p]&&u.setChecked(!1,!1);continue}if(u.childNodes.length&&f(u),u.isLeaf||this.checkStrictly){u.setChecked(!0,!1);continue}if(u.setChecked(!0,!0),l){u.setChecked(!1,!1);const c=function(h){h.childNodes.forEach(C=>{C.isLeaf||C.setChecked(!1,!1),c(C)})};c(u)}}}setCheckedNodes(t,l=!1){const s=this.key,n={};t.forEach(a=>{n[(a||{})[s]]=!0}),this._setCheckedKeys(s,l,n)}setCheckedKeys(t,l=!1){this.defaultCheckedKeys=t;const s=this.key,n={};t.forEach(a=>{n[a]=!0}),this._setCheckedKeys(s,l,n)}setDefaultExpandedKeys(t){t=t||[],this.defaultExpandedKeys=t,t.forEach(l=>{const s=this.getNode(l);s&&s.expand(null,this.autoExpandParent)})}setChecked(t,l,s){const n=this.getNode(t);n&&n.setChecked(!!l,s)}getCurrentNode(){return this.currentNode}setCurrentNode(t){const l=this.currentNode;l&&(l.isCurrent=!1),this.currentNode=t,this.currentNode.isCurrent=!0}setUserCurrentNode(t,l=!0){const s=t[this.key],n=this.nodesMap[s];this.setCurrentNode(n),l&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0)}setCurrentNodeKey(t,l=!0){if(this.currentNodeKey=t,Wt(t)){this.currentNode&&(this.currentNode.isCurrent=!1),this.currentNode=null;return}const s=this.getNode(t);s&&(this.setCurrentNode(s),l&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0))}}const kt="RootTree",rn="NodeInstance",Jt="TreeNodeMap",vo=Q({name:"ElTreeNodeContent",props:{node:{type:Object,required:!0},renderContent:Function},setup(e){const t=_("tree"),l=le(rn),s=le(kt);return()=>{const n=e.node,{data:a,store:o}=n;return e.renderContent?e.renderContent(ze,{_self:l,node:n,data:a,store:o}):H(s.ctx.slots,"default",{node:n,data:a},()=>[ze(fo,{tag:"span",truncated:!0,class:t.be("node","label")},()=>[n.label])])}}});var go=re(vo,[["__file","tree-node-content.vue"]]);function dn(e){const t=le(Jt,null),l={treeNodeExpand:s=>{e.node!==s&&e.node.collapse()},children:[]};return t&&t.children.push(l),be(Jt,l),{broadcastExpanded:s=>{if(e.accordion)for(const n of l.children)n.treeNodeExpand(s)}}}const cn=Symbol("dragEvents");function yo({props:e,ctx:t,el$:l,dropIndicator$:s,store:n}){const a=_("tree"),o=T({showDropIndicator:!1,draggingNode:null,dropNode:null,allowDrop:!0,dropType:null});return be(cn,{treeNodeDragStart:({event:u,treeNode:p})=>{if(ie(e.allowDrag)&&!e.allowDrag(p.node))return u.preventDefault(),!1;u.dataTransfer.effectAllowed="move";try{u.dataTransfer.setData("text/plain","")}catch(r){}o.value.draggingNode=p,t.emit("node-drag-start",p.node,u)},treeNodeDragOver:({event:u,treeNode:p})=>{const r=p,c=o.value.dropNode;c&&c.node.id!==r.node.id&&it(c.$el,a.is("drop-inner"));const h=o.value.draggingNode;if(!h||!r)return;let m=!0,C=!0,N=!0,I=!0;ie(e.allowDrop)&&(m=e.allowDrop(h.node,r.node,"prev"),I=C=e.allowDrop(h.node,r.node,"inner"),N=e.allowDrop(h.node,r.node,"next")),u.dataTransfer.dropEffect=C||m||N?"move":"none",(m||C||N)&&(c==null?void 0:c.node.id)!==r.node.id&&(c&&t.emit("node-drag-leave",h.node,c.node,u),t.emit("node-drag-enter",h.node,r.node,u)),m||C||N?o.value.dropNode=r:o.value.dropNode=null,r.node.nextSibling===h.node&&(N=!1),r.node.previousSibling===h.node&&(m=!1),r.node.contains(h.node,!1)&&(C=!1),(h.node===r.node||h.node.contains(r.node))&&(m=!1,C=!1,N=!1);const $=r.$el.querySelector(`.${a.be("node","content")}`).getBoundingClientRect(),O=l.value.getBoundingClientRect();let M;const de=m?C?.25:N?.45:1:-1,q=N?C?.75:m?.55:0:1;let fe=-9999;const y=u.clientY-$.top;y<$.height*de?M="before":y>$.height*q?M="after":C?M="inner":M="none";const K=r.$el.querySelector(`.${a.be("node","expand-icon")}`).getBoundingClientRect(),z=s.value;M==="before"?fe=K.top-O.top:M==="after"&&(fe=K.bottom-O.top),z.style.top=`${fe}px`,z.style.left=`${K.right-O.left}px`,M==="inner"?il(r.$el,a.is("drop-inner")):it(r.$el,a.is("drop-inner")),o.value.showDropIndicator=M==="before"||M==="after",o.value.allowDrop=o.value.showDropIndicator||I,o.value.dropType=M,t.emit("node-drag-over",h.node,r.node,u)},treeNodeDragEnd:u=>{const{draggingNode:p,dropType:r,dropNode:c}=o.value;if(u.preventDefault(),u.dataTransfer&&(u.dataTransfer.dropEffect="move"),p&&c){const h={data:p.node.data};r!=="none"&&p.node.remove(),r==="before"?c.node.parent.insertBefore(h,c.node):r==="after"?c.node.parent.insertAfter(h,c.node):r==="inner"&&c.node.insertChild(h),r!=="none"&&(n.value.registerNode(h),n.value.key&&p.node.eachNode(m=>{var C;(C=n.value.nodesMap[m.data[n.value.key]])==null||C.setChecked(m.checked,!n.value.checkStrictly)})),it(c.$el,a.is("drop-inner")),t.emit("node-drag-end",p.node,c.node,r,u),r!=="none"&&t.emit("node-drop",p.node,c.node,r,u)}p&&!c&&t.emit("node-drag-end",p.node,null,r,u),o.value.showDropIndicator=!1,o.value.draggingNode=null,o.value.dropNode=null,o.value.allowDrop=!0}}),{dragState:o}}const mo=Q({name:"ElTreeNode",components:{ElCollapseTransition:Ul,ElCheckbox:zl,NodeContent:go,ElIcon:Zt,Loading:rl},props:{node:{type:me,default:()=>({})},props:{type:Object,default:()=>({})},accordion:Boolean,renderContent:Function,renderAfterExpand:Boolean,showCheckbox:{type:Boolean,default:!1}},emits:["node-expand"],setup(e,t){const l=_("tree"),{broadcastExpanded:s}=dn(e),n=le(kt),a=T(!1),o=T(!1),f=T(),d=T(),v=T(),u=le(cn),p=Ce();be(rn,p),e.node.expanded&&(a.value=!0,o.value=!0);const r=n.props.props.children||"children";L(()=>{var y;const K=(y=e.node.data)==null?void 0:y[r];return K&&[...K]},()=>{e.node.updateChildren()}),L(()=>e.node.indeterminate,y=>{m(e.node.checked,y)}),L(()=>e.node.checked,y=>{m(y,e.node.indeterminate)}),L(()=>e.node.childNodes.length,()=>e.node.reInitChecked()),L(()=>e.node.expanded,y=>{j(()=>a.value=y),y&&(o.value=!0)});const c=y=>Ct(n.props.nodeKey,y.data),h=y=>{const K=e.props.class;if(!K)return{};let z;if(ie(K)){const{data:x}=y;z=K(x,y)}else z=K;return mt(z)?{[z]:!0}:z},m=(y,K)=>{(f.value!==y||d.value!==K)&&n.ctx.emit("check-change",e.node.data,y,K),f.value=y,d.value=K},C=y=>{ft(n.store,n.ctx.emit,()=>{var K;if((K=n==null?void 0:n.props)==null?void 0:K.nodeKey){const x=c(e.node);n.store.value.setCurrentNodeKey(x)}else n.store.value.setCurrentNode(e.node)}),n.currentNode.value=e.node,n.props.expandOnClickNode&&I(),(n.props.checkOnClickNode||e.node.isLeaf&&n.props.checkOnClickLeaf&&e.showCheckbox)&&!e.node.disabled&&$(!e.node.checked),n.ctx.emit("node-click",e.node.data,e.node,p,y)},N=y=>{var K;(K=n.instance.vnode.props)!=null&&K.onNodeContextmenu&&(y.stopPropagation(),y.preventDefault()),n.ctx.emit("node-contextmenu",y,e.node.data,e.node,p)},I=()=>{e.node.isLeaf||(a.value?(n.ctx.emit("node-collapse",e.node.data,e.node,p),e.node.collapse()):e.node.expand(()=>{t.emit("node-expand",e.node.data,e.node,p)}))},$=y=>{e.node.setChecked(y,!(n!=null&&n.props.checkStrictly)),j(()=>{const K=n.store.value;n.ctx.emit("check",e.node.data,{checkedNodes:K.getCheckedNodes(),checkedKeys:K.getCheckedKeys(),halfCheckedNodes:K.getHalfCheckedNodes(),halfCheckedKeys:K.getHalfCheckedKeys()})})};return{ns:l,node$:v,tree:n,expanded:a,childNodeRendered:o,oldChecked:f,oldIndeterminate:d,getNodeKey:c,getNodeClass:h,handleSelectChange:m,handleClick:C,handleContextMenu:N,handleExpandIconClick:I,handleCheckChange:$,handleChildNodeExpand:(y,K,z)=>{s(K),n.ctx.emit("node-expand",y,K,z)},handleDragStart:y=>{n.props.draggable&&u.treeNodeDragStart({event:y,treeNode:e})},handleDragOver:y=>{y.preventDefault(),n.props.draggable&&u.treeNodeDragOver({event:y,treeNode:{$el:v.value,node:e.node}})},handleDrop:y=>{y.preventDefault()},handleDragEnd:y=>{n.props.draggable&&u.treeNodeDragEnd(y)},CaretRight:dl}}});function bo(e,t,l,s,n,a){const o=J("el-icon"),f=J("el-checkbox"),d=J("loading"),v=J("node-content"),u=J("el-tree-node"),p=J("el-collapse-transition");return ve((S(),V("div",{ref:"node$",class:E([e.ns.b("node"),e.ns.is("expanded",e.expanded),e.ns.is("current",e.node.isCurrent),e.ns.is("hidden",!e.node.visible),e.ns.is("focusable",!e.node.disabled),e.ns.is("checked",!e.node.disabled&&e.node.checked),e.getNodeClass(e.node)]),role:"treeitem",tabindex:"-1","aria-expanded":e.expanded,"aria-disabled":e.node.disabled,"aria-checked":e.node.checked,draggable:e.tree.props.draggable,"data-key":e.getNodeKey(e.node),onClick:W(e.handleClick,["stop"]),onContextmenu:e.handleContextMenu,onDragstart:W(e.handleDragStart,["stop"]),onDragover:W(e.handleDragOver,["stop"]),onDragend:W(e.handleDragEnd,["stop"]),onDrop:W(e.handleDrop,["stop"])},[R("div",{class:E(e.ns.be("node","content")),style:we({paddingLeft:(e.node.level-1)*e.tree.props.indent+"px"})},[e.tree.props.icon||e.CaretRight?(S(),U(o,{key:0,class:E([e.ns.be("node","expand-icon"),e.ns.is("leaf",e.node.isLeaf),{expanded:!e.node.isLeaf&&e.expanded}]),onClick:W(e.handleExpandIconClick,["stop"])},{default:F(()=>[(S(),U(He(e.tree.props.icon||e.CaretRight)))]),_:1},8,["class","onClick"])):P("v-if",!0),e.showCheckbox?(S(),U(f,{key:1,"model-value":e.node.checked,indeterminate:e.node.indeterminate,disabled:!!e.node.disabled,onClick:W(()=>{},["stop"]),onChange:e.handleCheckChange},null,8,["model-value","indeterminate","disabled","onClick","onChange"])):P("v-if",!0),e.node.loading?(S(),U(o,{key:2,class:E([e.ns.be("node","loading-icon"),e.ns.is("loading")])},{default:F(()=>[se(d)]),_:1},8,["class"])):P("v-if",!0),se(v,{node:e.node,"render-content":e.renderContent},null,8,["node","render-content"])],6),se(p,null,{default:F(()=>[!e.renderAfterExpand||e.childNodeRendered?ve((S(),V("div",{key:0,class:E(e.ns.be("node","children")),role:"group","aria-expanded":e.expanded,onClick:W(()=>{},["stop"])},[(S(!0),V(_e,null,et(e.node.childNodes,r=>(S(),U(u,{key:e.getNodeKey(r),"render-content":e.renderContent,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,node:r,accordion:e.accordion,props:e.props,onNodeExpand:e.handleChildNodeExpand},null,8,["render-content","render-after-expand","show-checkbox","node","accordion","props","onNodeExpand"]))),128))],10,["aria-expanded","onClick"])),[[De,e.expanded]]):P("v-if",!0)]),_:1})],42,["aria-expanded","aria-disabled","aria-checked","draggable","data-key","onClick","onContextmenu","onDragstart","onDragover","onDragend","onDrop"])),[[De,e.node.visible]])}var Co=re(mo,[["render",bo],["__file","tree-node.vue"]]);function ko({el$:e},t){const l=_("tree");Te(()=>{n()}),tn(()=>{Array.from(e.value.querySelectorAll("input[type=checkbox]")).forEach(o=>{o.setAttribute("tabindex","-1")})}),cl(e,"keydown",a=>{const o=a.target;if(!o.className.includes(l.b("node")))return;const f=a.code,d=Array.from(e.value.querySelectorAll(`.${l.is("focusable")}[role=treeitem]`)),v=d.indexOf(o);let u;if([ce.up,ce.down].includes(f)){if(a.preventDefault(),f===ce.up){u=v===-1?0:v!==0?v-1:d.length-1;const r=u;for(;!t.value.getNode(d[u].dataset.key).canFocus;){if(u--,u===r){u=-1;break}u<0&&(u=d.length-1)}}else{u=v===-1?0:v<d.length-1?v+1:0;const r=u;for(;!t.value.getNode(d[u].dataset.key).canFocus;){if(u++,u===r){u=-1;break}u>=d.length&&(u=0)}}u!==-1&&d[u].focus()}[ce.left,ce.right].includes(f)&&(a.preventDefault(),o.click());const p=o.querySelector('[type="checkbox"]');[ce.enter,ce.numpadEnter,ce.space].includes(f)&&p&&(a.preventDefault(),p.click())});const n=()=>{var a;const o=Array.from(e.value.querySelectorAll(`.${l.is("focusable")}[role=treeitem]`));Array.from(e.value.querySelectorAll("input[type=checkbox]")).forEach(v=>{v.setAttribute("tabindex","-1")});const d=e.value.querySelectorAll(`.${l.is("checked")}[role=treeitem]`);if(d.length){d[0].setAttribute("tabindex","0");return}(a=o[0])==null||a.setAttribute("tabindex","0")}}const No=Q({name:"ElTree",components:{ElTreeNode:Co},props:{data:{type:Array,default:()=>[]},emptyText:{type:String},renderAfterExpand:{type:Boolean,default:!0},nodeKey:String,checkStrictly:Boolean,defaultExpandAll:Boolean,expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:Boolean,checkOnClickLeaf:{type:Boolean,default:!0},checkDescendants:{type:Boolean,default:!1},autoExpandParent:{type:Boolean,default:!0},defaultCheckedKeys:Array,defaultExpandedKeys:Array,currentNodeKey:[String,Number],renderContent:Function,showCheckbox:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1},allowDrag:Function,allowDrop:Function,props:{type:Object,default:()=>({children:"children",label:"label",disabled:"disabled"})},lazy:{type:Boolean,default:!1},highlightCurrent:Boolean,load:Function,filterNodeMethod:Function,accordion:Boolean,indent:{type:Number,default:18},icon:{type:rt}},emits:["check-change","current-change","node-click","node-contextmenu","node-collapse","node-expand","check","node-drag-start","node-drag-end","node-drop","node-drag-leave","node-drag-enter","node-drag-over"],setup(e,t){const{t:l}=Xt(),s=_("tree"),n=le(Ie,null),a=T(new po({key:e.nodeKey,data:e.data,lazy:e.lazy,props:e.props,load:e.load,currentNodeKey:e.currentNodeKey,checkStrictly:e.checkStrictly,checkDescendants:e.checkDescendants,defaultCheckedKeys:e.defaultCheckedKeys,defaultExpandedKeys:e.defaultExpandedKeys,autoExpandParent:e.autoExpandParent,defaultExpandAll:e.defaultExpandAll,filterNodeMethod:e.filterNodeMethod}));a.value.initialize();const o=T(a.value.root),f=T(null),d=T(null),v=T(null),{broadcastExpanded:u}=dn(e),{dragState:p}=yo({props:e,ctx:t,el$:d,dropIndicator$:v,store:a});ko({el$:d},a);const r=k(()=>{const{childNodes:b}=o.value,D=n?n.hasFilteredOptions!==0:!1;return(!b||b.length===0||b.every(({visible:ee})=>!ee))&&!D});L(()=>e.currentNodeKey,b=>{a.value.setCurrentNodeKey(b)}),L(()=>e.defaultCheckedKeys,b=>{a.value.setDefaultCheckedKey(b)}),L(()=>e.defaultExpandedKeys,b=>{a.value.setDefaultExpandedKeys(b)}),L(()=>e.data,b=>{a.value.setData(b)},{deep:!0}),L(()=>e.checkStrictly,b=>{a.value.checkStrictly=b});const c=b=>{if(!e.filterNodeMethod)throw new Error("[Tree] filterNodeMethod is required when filter");a.value.filter(b)},h=b=>Ct(e.nodeKey,b.data),m=b=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getNodePath");const D=a.value.getNode(b);if(!D)return[];const ee=[D.data];let ye=D.parent;for(;ye&&ye!==o.value;)ee.push(ye.data),ye=ye.parent;return ee.reverse()},C=(b,D)=>a.value.getCheckedNodes(b,D),N=b=>a.value.getCheckedKeys(b),I=()=>{const b=a.value.getCurrentNode();return b?b.data:null},$=()=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getCurrentKey");const b=I();return b?b[e.nodeKey]:null},O=(b,D)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedNodes");a.value.setCheckedNodes(b,D)},M=(b,D)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedKeys");a.value.setCheckedKeys(b,D)},de=(b,D,ee)=>{a.value.setChecked(b,D,ee)},q=()=>a.value.getHalfCheckedNodes(),fe=()=>a.value.getHalfCheckedKeys(),y=(b,D=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentNode");ft(a,t.emit,()=>{u(b),a.value.setUserCurrentNode(b,D)})},K=(b,D=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentKey");ft(a,t.emit,()=>{u(),a.value.setCurrentNodeKey(b,D)})},z=b=>a.value.getNode(b),x=b=>{a.value.remove(b)},nt=(b,D)=>{a.value.append(b,D)},lt=(b,D)=>{a.value.insertBefore(b,D)},ot=(b,D)=>{a.value.insertAfter(b,D)},ge=(b,D,ee)=>{u(D),t.emit("node-expand",b,D,ee)},Ve=(b,D)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in updateKeyChild");a.value.updateChildren(b,D)};return be(kt,{ctx:t,props:e,store:a,root:o,currentNode:f,instance:Ce()}),be(Al,void 0),{ns:s,store:a,root:o,currentNode:f,dragState:p,el$:d,dropIndicator$:v,isEmpty:r,filter:c,getNodeKey:h,getNodePath:m,getCheckedNodes:C,getCheckedKeys:N,getCurrentNode:I,getCurrentKey:$,setCheckedNodes:O,setCheckedKeys:M,setChecked:de,getHalfCheckedNodes:q,getHalfCheckedKeys:fe,setCurrentNode:y,setCurrentKey:K,t:l,getNode:z,remove:x,append:nt,insertBefore:lt,insertAfter:ot,handleNodeExpand:ge,updateKeyChildren:Ve}}});function Eo(e,t,l,s,n,a){const o=J("el-tree-node");return S(),V("div",{ref:"el$",class:E([e.ns.b(),e.ns.is("dragging",!!e.dragState.draggingNode),e.ns.is("drop-not-allow",!e.dragState.allowDrop),e.ns.is("drop-inner",e.dragState.dropType==="inner"),{[e.ns.m("highlight-current")]:e.highlightCurrent}]),role:"tree"},[(S(!0),V(_e,null,et(e.root.childNodes,f=>(S(),U(o,{key:e.getNodeKey(f),node:f,props:e.props,accordion:e.accordion,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,"render-content":e.renderContent,onNodeExpand:e.handleNodeExpand},null,8,["node","props","accordion","render-after-expand","show-checkbox","render-content","onNodeExpand"]))),128)),e.isEmpty?(S(),V("div",{key:0,class:E(e.ns.e("empty-block"))},[H(e.$slots,"empty",{},()=>{var f;return[R("span",{class:E(e.ns.e("empty-text"))},oe((f=e.emptyText)!=null?f:e.t("el.tree.emptyText")),3)]})],2)):P("v-if",!0),ve(R("div",{ref:"dropIndicator$",class:E(e.ns.e("drop-indicator"))},null,2),[[De,e.dragState.showDropIndicator]])],2)}var So=re(No,[["render",Eo],["__file","tree.vue"]]);const pt=Fe(So),wo=(e,{attrs:t,emit:l},{select:s,tree:n,key:a})=>{const o=_("tree-select");return L(()=>e.data,()=>{e.filterable&&j(()=>{var d,v;(v=n.value)==null||v.filter((d=s.value)==null?void 0:d.states.inputValue)})},{flush:"post"}),Z(B(B({},tt(We(e),Object.keys(ut.props))),t),{class:k(()=>t.class),style:k(()=>t.style),"onUpdate:modelValue":d=>l(te,d),valueKey:a,popperClass:k(()=>{const d=[o.e("popper")];return e.popperClass&&d.push(e.popperClass),d.join(" ")}),filterMethod:(d="")=>{var v;e.filterMethod?e.filterMethod(d):e.remoteMethod?e.remoteMethod(d):(v=n.value)==null||v.filter(d)}})},Ko=Q({extends:xt,setup(e,t){const l=xt.setup(e,t);delete l.selectOptionClick;const s=Ce().proxy;return j(()=>{l.select.states.cachedOptions.get(s.value)||l.select.onOptionCreate(s)}),L(()=>t.attrs.visible,n=>{j(()=>{l.states.visible=n})},{immediate:!0}),l},methods:{selectOptionClick(){this.$el.parentElement.click()}}});function vt(e){return e||e===0}function Nt(e){return G(e)&&e.length}function Ne(e){return G(e)?e:vt(e)?[e]:[]}function Xe(e,t,l,s,n){for(let a=0;a<e.length;a++){const o=e[a];if(t(o,a,e,n))return s?s(o,a,e,n):o;{const f=l(o);if(Nt(f)){const d=Xe(f,t,l,s,o);if(d)return d}}}}function Ze(e,t,l,s){for(let n=0;n<e.length;n++){const a=e[n];t(a,n,e,s);const o=l(a);Nt(o)&&Ze(o,t,l,a)}}const Oo=(e,{attrs:t,slots:l,emit:s},{select:n,tree:a,key:o})=>{L([()=>e.modelValue,a],()=>{e.showCheckbox&&j(()=>{const r=a.value;r&&!Ke(r.getCheckedKeys(),Ne(e.modelValue))&&r.setCheckedKeys(Ne(e.modelValue))})},{immediate:!0,deep:!0});const f=k(()=>B({value:o.value,label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf"},e.props)),d=(r,c)=>{var h;const m=f.value[r];return ie(m)?m(c,(h=a.value)==null?void 0:h.getNode(d("value",c))):c[m]},v=Ne(e.modelValue).map(r=>Xe(e.data||[],c=>d("value",c)===r,c=>d("children",c),(c,h,m,C)=>C&&d("value",C))).filter(r=>vt(r)),u=k(()=>{if(!e.renderAfterExpand&&!e.lazy)return[];const r=[];return Ze(e.data.concat(e.cacheData),c=>{const h=d("value",c);r.push({value:h,currentLabel:d("label",c),isDisabled:d("disabled",c)})},c=>d("children",c)),r}),p=()=>{var r;return(r=a.value)==null?void 0:r.getCheckedKeys().filter(c=>{var h;const m=(h=a.value)==null?void 0:h.getNode(c);return!fl(m)&&ul(m.childNodes)})};return Z(B(B({},tt(We(e),Object.keys(pt.props))),t),{nodeKey:o,expandOnClickNode:k(()=>!e.checkStrictly&&e.expandOnClickNode),defaultExpandedKeys:k(()=>e.defaultExpandedKeys?e.defaultExpandedKeys.concat(v):v),renderContent:(r,{node:c,data:h,store:m})=>r(Ko,{value:d("value",h),label:d("label",h),disabled:d("disabled",h),visible:c.visible},e.renderContent?()=>e.renderContent(r,{node:c,data:h,store:m}):l.default?()=>l.default({node:c,data:h,store:m}):void 0),filterNodeMethod:(r,c,h)=>e.filterNodeMethod?e.filterNodeMethod(r,c,h):r?new RegExp(en(r),"i").test(d("label",c)||""):!0,onNodeClick:(r,c,h)=>{var m,C,N,I;if((m=t.onNodeClick)==null||m.call(t,r,c,h),!(e.showCheckbox&&e.checkOnClickNode)){if(!e.showCheckbox&&(e.checkStrictly||c.isLeaf)){if(!d("disabled",r)){const $=(C=n.value)==null?void 0:C.states.options.get(d("value",r));(N=n.value)==null||N.handleOptionSelect($)}}else e.expandOnClickNode&&h.proxy.handleExpandIconClick();(I=n.value)==null||I.focus()}},onCheck:(r,c)=>{var h;if(!e.showCheckbox)return;const m=d("value",r),C={};Ze([a.value.store.root],O=>C[O.key]=O,O=>O.childNodes);const N=c.checkedKeys,I=e.multiple?Ne(e.modelValue).filter(O=>!(O in C)&&!N.includes(O)):[],$=I.concat(N);if(e.checkStrictly)s(te,e.multiple?$:$.includes(m)?m:void 0);else if(e.multiple){const O=p();s(te,I.concat(O))}else{const O=Xe([r],q=>!Nt(d("children",q))&&!d("disabled",q),q=>d("children",q)),M=O?d("value",O):void 0,de=vt(e.modelValue)&&!!Xe([r],q=>d("value",q)===e.modelValue,q=>d("children",q));s(te,M===e.modelValue||de?void 0:M)}j(()=>{var O;const M=Ne(e.modelValue);a.value.setCheckedKeys(M),(O=t.onCheck)==null||O.call(t,r,{checkedKeys:a.value.getCheckedKeys(),checkedNodes:a.value.getCheckedNodes(),halfCheckedKeys:a.value.getHalfCheckedKeys(),halfCheckedNodes:a.value.getHalfCheckedNodes()})}),(h=n.value)==null||h.focus()},onNodeExpand:(r,c,h)=>{var m;(m=t.onNodeExpand)==null||m.call(t,r,c,h),j(()=>{if(!e.checkStrictly&&e.lazy&&e.multiple&&c.checked){const C={},N=a.value.getCheckedKeys();Ze([a.value.store.root],O=>C[O.key]=O,O=>O.childNodes);const I=Ne(e.modelValue).filter(O=>!(O in C)&&!N.includes(O)),$=p();s(te,I.concat($))}})},cacheOptions:u})};var Do=Q({props:{data:{type:Array,default:()=>[]}},setup(e){const t=le(Ie);return L(()=>e.data,()=>{var l;e.data.forEach(n=>{t.states.cachedOptions.has(n.value)||t.states.cachedOptions.set(n.value,n)});const s=((l=t.selectRef)==null?void 0:l.querySelectorAll("input"))||[];gt&&!Array.from(s).includes(document.activeElement)&&t.setSelected()},{flush:"post",immediate:!0}),()=>{}}});const To=Q({name:"ElTreeSelect",inheritAttrs:!1,props:Z(B(B({},ut.props),pt.props),{cacheData:{type:Array,default:()=>[]}}),setup(e,t){const{slots:l,expose:s}=t,n=T(),a=T(),o=k(()=>e.nodeKey||e.valueKey||"value"),f=wo(e,t,{select:n,tree:a,key:o}),p=Oo(e,t,{select:n,tree:a,key:o}),{cacheOptions:d}=p,v=Ht(p,["cacheOptions"]),u=ue({});return s(u),Te(()=>{Object.assign(u,B(B({},tt(a.value,["filter","updateKeyChildren","getCheckedNodes","setCheckedNodes","getCheckedKeys","setCheckedKeys","setChecked","getHalfCheckedNodes","getHalfCheckedKeys","getCurrentKey","getCurrentNode","setCurrentKey","setCurrentNode","getNode","remove","append","insertBefore","insertAfter"])),tt(n.value,["focus","blur","selectedLabel"])))}),()=>ze(ut,ue(Z(B({},f),{ref:r=>n.value=r})),Z(B({},l),{default:()=>[ze(Do,{data:d.value}),ze(pt,ue(Z(B({},v),{ref:r=>a.value=r})))]}))}});var Io=re(To,[["__file","tree-select.vue"]]);const Jo=Fe(Io);export{Jo as ElTreeSelect,Jo as default};
