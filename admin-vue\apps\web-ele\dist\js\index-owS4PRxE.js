var qn=Object.defineProperty,Vn=Object.defineProperties;var Gn=Object.getOwnPropertyDescriptors;var Bt=Object.getOwnPropertySymbols;var Jn=Object.prototype.hasOwnProperty,Xn=Object.prototype.propertyIsEnumerable;var Nt=(e,t,n)=>t in e?qn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,F=(e,t)=>{for(var n in t||(t={}))Jn.call(t,n)&&Nt(e,n,t[n]);if(Bt)for(var n of Bt(t))Xn.call(t,n)&&Nt(e,n,t[n]);return e},W=(e,t)=>Vn(e,Gn(t));var Dt=(e,t,n)=>new Promise((o,r)=>{var a=u=>{try{s(n.next(u))}catch(l){r(l)}},i=u=>{try{s(n.throw(u))}catch(l){r(l)}},s=u=>u.done?o(u.value):Promise.resolve(u.value).then(a,i);s((n=n.apply(e,t)).next())});import{bJ as Zn,af as st,ag as Yn,bK as Qn,k as ee,o as it,m as se,n as Ae,l as I,i as pe,aM as cn,p as ft,a as de,aF as ke,a7 as jt,bL as eo,w as pt,bk as fn,Q as pn,u as to,d as no,ae as oo,ar as ro,aL as ao}from"./bootstrap-CYivmKoJ.js";import{u as d,d as $,r as M,e as A,l as Y,a9 as we,v as ge,aa as ie,f as Xe,g as J,m as so,n as dn,a2 as ut,N as vn,aB as io,a3 as uo,F as lo,a7 as co,C as fo,E as Be,o as he,w as D,q as me,h as Ze,D as re,U as dt,p as $t,P as po,Q as vo,x as mo,a as Ke,i as go,y as Ie,aC as ho,aD as yo,X as bo,ap as Eo,t as wo}from"../jse/index-index-SSqEGcIT.js";import{u as mn}from"./index-DIXeP0hR.js";import{i as lt}from"./aria-DGfENwCE.js";import{c as To,b as Oo}from"./use-form-item-iUVikjOD.js";import{a as Ht}from"./use-form-common-props-DZjBwEkr.js";var Co=/\s/;function Po(e){for(var t=e.length;t--&&Co.test(e.charAt(t)););return t}var Ro=/^\s+/;function Ao(e){return e&&e.slice(0,Po(e)+1).replace(Ro,"")}var Wt=NaN,So=/^[-+]0x[0-9a-f]+$/i,xo=/^0b[01]+$/i,Fo=/^0o[0-7]+$/i,_o=parseInt;function Ut(e){if(typeof e=="number")return e;if(Zn(e))return Wt;if(st(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=st(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Ao(e);var n=xo.test(e);return n||Fo.test(e)?_o(e.slice(2),n?2:8):So.test(e)?Wt:+e}var ot=function(){return Yn.Date.now()},Io="Expected a function",ko=Math.max,Lo=Math.min;function is(e,t,n){var o,r,a,i,s,u,l=0,v=!1,m=!1,h=!0;if(typeof e!="function")throw new TypeError(Io);t=Ut(t)||0,st(n)&&(v=!!n.leading,m="maxWait"in n,a=m?ko(Ut(n.maxWait)||0,t):a,h="trailing"in n?!!n.trailing:h);function g(O){var w=o,P=r;return o=r=void 0,l=O,i=e.apply(P,w),i}function c(O){return l=O,s=setTimeout(b,t),v?g(O):i}function f(O){var w=O-u,P=O-l,S=t-w;return m?Lo(S,a-P):S}function E(O){var w=O-u,P=O-l;return u===void 0||w>=t||w<0||m&&P>=a}function b(){var O=ot();if(E(O))return T(O);s=setTimeout(b,f(O))}function T(O){return s=void 0,h&&o?g(O):(o=r=void 0,i)}function p(){s!==void 0&&clearTimeout(s),l=0,o=u=r=s=void 0}function y(){return s===void 0?i:T(ot())}function R(){var O=ot(),w=E(O);if(o=arguments,r=this,u=O,w){if(s===void 0)return c(u);if(m)return clearTimeout(s),s=setTimeout(b,t),g(u)}return s===void 0&&(s=setTimeout(b,t)),i}return R.cancel=p,R.flush=y,R}function Mo(e){return e===void 0}function Kt(){let e;const t=(o,r)=>{n(),e=window.setTimeout(o,r)},n=()=>window.clearTimeout(e);return Qn(()=>n()),{registerTimeout:t,cancelTimeout:n}}const Bo=ee({showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0}}),No=({showAfter:e,hideAfter:t,autoClose:n,open:o,close:r})=>{const{registerTimeout:a}=Kt(),{registerTimeout:i,cancelTimeout:s}=Kt();return{onOpen:v=>{a(()=>{o(v);const m=d(n);it(m)&&m>0&&i(()=>{r(v)},m)},d(e))},onClose:v=>{s(),a(()=>{r(v)},d(t))}}},us=4,ls={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},cs=({move:e,size:t,bar:n})=>({[n.size]:t,transform:`translate${n.axis}(${e}%)`}),vt=Symbol("popper"),gn=Symbol("popperContent"),Do=["dialog","grid","group","listbox","menu","navigation","tooltip","tree"],hn=ee({role:{type:String,values:Do,default:"tooltip"}}),jo=$({name:"ElPopper",inheritAttrs:!1}),$o=$(W(F({},jo),{props:hn,setup(e,{expose:t}){const n=e,o=M(),r=M(),a=M(),i=M(),s=A(()=>n.role),u={triggerRef:o,popperInstanceRef:r,contentRef:a,referenceRef:i,role:s};return t(u),we(vt,u),(l,v)=>Y(l.$slots,"default")}}));var Ho=se($o,[["__file","popper.vue"]]);const Wo=$({name:"ElPopperArrow",inheritAttrs:!1}),Uo=$(W(F({},Wo),{setup(e,{expose:t}){const n=Ae("popper"),{arrowRef:o,arrowStyle:r}=ge(gn,void 0);return ie(()=>{o.value=void 0}),t({arrowRef:o}),(a,i)=>(J(),Xe("span",{ref_key:"arrowRef",ref:o,class:dn(d(n).e("arrow")),style:so(d(r)),"data-popper-arrow":""},null,6))}}));var Ko=se(Uo,[["__file","arrow.vue"]]);const yn=ee({virtualRef:{type:I(Object)},virtualTriggering:Boolean,onMouseenter:{type:I(Function)},onMouseleave:{type:I(Function)},onClick:{type:I(Function)},onKeydown:{type:I(Function)},onFocus:{type:I(Function)},onBlur:{type:I(Function)},onContextmenu:{type:I(Function)},id:String,open:Boolean}),bn=Symbol("elForwardRef"),zo=e=>{we(bn,{setForwardRef:n=>{e.value=n}})},qo=e=>({mounted(t){e(t)},updated(t){e(t)},unmounted(){e(null)}}),Vo="ElOnlyChild",Go=$({name:Vo,setup(e,{slots:t,attrs:n}){var o;const r=ge(bn),a=qo((o=r==null?void 0:r.setForwardRef)!=null?o:ut);return()=>{var i;const s=(i=t.default)==null?void 0:i.call(t,n);if(!s||s.length>1)return null;const u=En(s);return u?vn(io(u,n),[[a]]):null}}});function En(e){if(!e)return null;const t=e;for(const n of t){if(uo(n))switch(n.type){case fo:continue;case co:case"svg":return zt(n);case lo:return En(n.children);default:return n}return zt(n)}return null}function zt(e){const t=Ae("only-child");return Be("span",{class:t.e("content")},[e])}const Jo=$({name:"ElPopperTrigger",inheritAttrs:!1}),Xo=$(W(F({},Jo),{props:yn,setup(e,{expose:t}){const n=e,{role:o,triggerRef:r}=ge(vt,void 0);zo(r);const a=A(()=>s.value?n.id:void 0),i=A(()=>{if(o&&o.value==="tooltip")return n.open&&n.id?n.id:void 0}),s=A(()=>{if(o&&o.value!=="tooltip")return o.value}),u=A(()=>s.value?`${n.open}`:void 0);let l;const v=["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"];return he(()=>{D(()=>n.virtualRef,m=>{m&&(r.value=cn(m))},{immediate:!0}),D(r,(m,h)=>{l==null||l(),l=void 0,pe(m)&&(v.forEach(g=>{var c;const f=n[g];f&&(m.addEventListener(g.slice(2).toLowerCase(),f),(c=h==null?void 0:h.removeEventListener)==null||c.call(h,g.slice(2).toLowerCase(),f))}),lt(m)&&(l=D([a,i,s,u],g=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((c,f)=>{ft(g[f])?m.removeAttribute(c):m.setAttribute(c,g[f])})},{immediate:!0}))),pe(h)&&lt(h)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(g=>h.removeAttribute(g))},{immediate:!0})}),ie(()=>{if(l==null||l(),l=void 0,r.value&&pe(r.value)){const m=r.value;v.forEach(h=>{const g=n[h];g&&m.removeEventListener(h.slice(2).toLowerCase(),g)}),r.value=void 0}}),t({triggerRef:r}),(m,h)=>m.virtualTriggering?Ze("v-if",!0):(J(),me(d(Go),dt({key:0},m.$attrs,{"aria-controls":d(a),"aria-describedby":d(i),"aria-expanded":d(u),"aria-haspopup":d(s)}),{default:re(()=>[Y(m.$slots,"default")]),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}}));var Zo=se(Xo,[["__file","trigger.vue"]]);const rt="focus-trap.focus-after-trapped",at="focus-trap.focus-after-released",Yo="focus-trap.focusout-prevented",qt={cancelable:!0,bubbles:!1},Qo={cancelable:!0,bubbles:!1},Vt="focusAfterTrapped",Gt="focusAfterReleased",er=Symbol("elFocusTrap"),mt=M(),Qe=M(0),gt=M(0);let ze=0;const wn=e=>{const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0||o===document.activeElement?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t},Jt=(e,t)=>{for(const n of e)if(!tr(n,t))return n},tr=(e,t)=>{if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1},nr=e=>{const t=wn(e),n=Jt(t,e),o=Jt(t.reverse(),e);return[n,o]},or=e=>e instanceof HTMLInputElement&&"select"in e,ne=(e,t)=>{if(e&&e.focus){const n=document.activeElement;let o=!1;pe(e)&&!lt(e)&&!e.getAttribute("tabindex")&&(e.setAttribute("tabindex","-1"),o=!0),e.focus({preventScroll:!0}),gt.value=window.performance.now(),e!==n&&or(e)&&t&&e.select(),pe(e)&&o&&e.removeAttribute("tabindex")}};function Xt(e,t){const n=[...e],o=e.indexOf(t);return o!==-1&&n.splice(o,1),n}const rr=()=>{let e=[];return{push:o=>{const r=e[0];r&&o!==r&&r.pause(),e=Xt(e,o),e.unshift(o)},remove:o=>{var r,a;e=Xt(e,o),(a=(r=e[0])==null?void 0:r.resume)==null||a.call(r)}}},ar=(e,t=!1)=>{const n=document.activeElement;for(const o of e)if(ne(o,t),document.activeElement!==n)return},Zt=rr(),sr=()=>Qe.value>gt.value,qe=()=>{mt.value="pointer",Qe.value=window.performance.now()},Yt=()=>{mt.value="keyboard",Qe.value=window.performance.now()},ir=()=>(he(()=>{ze===0&&(document.addEventListener("mousedown",qe),document.addEventListener("touchstart",qe),document.addEventListener("keydown",Yt)),ze++}),ie(()=>{ze--,ze<=0&&(document.removeEventListener("mousedown",qe),document.removeEventListener("touchstart",qe),document.removeEventListener("keydown",Yt))}),{focusReason:mt,lastUserFocusTimestamp:Qe,lastAutomatedFocusTimestamp:gt}),Ve=e=>new CustomEvent(Yo,W(F({},Qo),{detail:e}));let Ee=[];const Qt=e=>{e.code===ke.esc&&Ee.forEach(t=>t(e))},ur=e=>{he(()=>{Ee.length===0&&document.addEventListener("keydown",Qt),de&&Ee.push(e)}),ie(()=>{Ee=Ee.filter(t=>t!==e),Ee.length===0&&de&&document.removeEventListener("keydown",Qt)})},lr=$({name:"ElFocusTrap",inheritAttrs:!1,props:{loop:Boolean,trapped:Boolean,focusTrapEl:Object,focusStartEl:{type:[Object,String],default:"first"}},emits:[Vt,Gt,"focusin","focusout","focusout-prevented","release-requested"],setup(e,{emit:t}){const n=M();let o,r;const{focusReason:a}=ir();ur(c=>{e.trapped&&!i.paused&&t("release-requested",c)});const i={paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}},s=c=>{if(!e.loop&&!e.trapped||i.paused)return;const{code:f,altKey:E,ctrlKey:b,metaKey:T,currentTarget:p,shiftKey:y}=c,{loop:R}=e,O=f===ke.tab&&!E&&!b&&!T,w=document.activeElement;if(O&&w){const P=p,[S,_]=nr(P);if(S&&_){if(!y&&w===_){const x=Ve({focusReason:a.value});t("focusout-prevented",x),x.defaultPrevented||(c.preventDefault(),R&&ne(S,!0))}else if(y&&[S,P].includes(w)){const x=Ve({focusReason:a.value});t("focusout-prevented",x),x.defaultPrevented||(c.preventDefault(),R&&ne(_,!0))}}else if(w===P){const x=Ve({focusReason:a.value});t("focusout-prevented",x),x.defaultPrevented||c.preventDefault()}}};we(er,{focusTrapRef:n,onKeydown:s}),D(()=>e.focusTrapEl,c=>{c&&(n.value=c)},{immediate:!0}),D([n],([c],[f])=>{c&&(c.addEventListener("keydown",s),c.addEventListener("focusin",v),c.addEventListener("focusout",m)),f&&(f.removeEventListener("keydown",s),f.removeEventListener("focusin",v),f.removeEventListener("focusout",m))});const u=c=>{t(Vt,c)},l=c=>t(Gt,c),v=c=>{const f=d(n);if(!f)return;const E=c.target,b=c.relatedTarget,T=E&&f.contains(E);e.trapped||b&&f.contains(b)||(o=b),T&&t("focusin",c),!i.paused&&e.trapped&&(T?r=E:ne(r,!0))},m=c=>{const f=d(n);if(!(i.paused||!f))if(e.trapped){const E=c.relatedTarget;!ft(E)&&!f.contains(E)&&setTimeout(()=>{if(!i.paused&&e.trapped){const b=Ve({focusReason:a.value});t("focusout-prevented",b),b.defaultPrevented||ne(r,!0)}},0)}else{const E=c.target;E&&f.contains(E)||t("focusout",c)}};function h(){return Dt(this,null,function*(){yield $t();const c=d(n);if(c){Zt.push(i);const f=c.contains(document.activeElement)?o:document.activeElement;if(o=f,!c.contains(f)){const b=new Event(rt,qt);c.addEventListener(rt,u),c.dispatchEvent(b),b.defaultPrevented||$t(()=>{let T=e.focusStartEl;po(T)||(ne(T),document.activeElement!==T&&(T="first")),T==="first"&&ar(wn(c),!0),(document.activeElement===f||T==="container")&&ne(c)})}}})}function g(){const c=d(n);if(c){c.removeEventListener(rt,u);const f=new CustomEvent(at,W(F({},qt),{detail:{focusReason:a.value}}));c.addEventListener(at,l),c.dispatchEvent(f),!f.defaultPrevented&&(a.value=="keyboard"||!sr()||c.contains(document.activeElement))&&ne(o!=null?o:document.body),c.removeEventListener(at,l),Zt.remove(i)}}return he(()=>{e.trapped&&h(),D(()=>e.trapped,c=>{c?h():g()})}),ie(()=>{e.trapped&&g(),n.value&&(n.value.removeEventListener("keydown",s),n.value.removeEventListener("focusin",v),n.value.removeEventListener("focusout",m),n.value=void 0)}),{onKeydown:s}}});function cr(e,t,n,o,r,a){return Y(e.$slots,"default",{handleKeydown:e.onKeydown})}var fr=se(lr,[["render",cr],["__file","focus-trap.vue"]]),U="top",q="bottom",V="right",K="left",ht="auto",je=[U,q,V,K],Te="start",Ne="end",pr="clippingParents",Tn="viewport",_e="popper",dr="reference",en=je.reduce(function(e,t){return e.concat([t+"-"+Te,t+"-"+Ne])},[]),yt=[].concat(je,[ht]).reduce(function(e,t){return e.concat([t,t+"-"+Te,t+"-"+Ne])},[]),vr="beforeRead",mr="read",gr="afterRead",hr="beforeMain",yr="main",br="afterMain",Er="beforeWrite",wr="write",Tr="afterWrite",Or=[vr,mr,gr,hr,yr,br,Er,wr,Tr];function Q(e){return e?(e.nodeName||"").toLowerCase():null}function X(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Oe(e){var t=X(e).Element;return e instanceof t||e instanceof Element}function z(e){var t=X(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function bt(e){if(typeof ShadowRoot=="undefined")return!1;var t=X(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Cr(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},r=t.attributes[n]||{},a=t.elements[n];!z(a)||!Q(a)||(Object.assign(a.style,o),Object.keys(r).forEach(function(i){var s=r[i];s===!1?a.removeAttribute(i):a.setAttribute(i,s===!0?"":s)}))})}function Pr(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var r=t.elements[o],a=t.attributes[o]||{},i=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]),s=i.reduce(function(u,l){return u[l]="",u},{});!z(r)||!Q(r)||(Object.assign(r.style,s),Object.keys(a).forEach(function(u){r.removeAttribute(u)}))})}}var On={name:"applyStyles",enabled:!0,phase:"write",fn:Cr,effect:Pr,requires:["computeStyles"]};function Z(e){return e.split("-")[0]}var ve=Math.max,Ye=Math.min,Ce=Math.round;function Pe(e,t){t===void 0&&(t=!1);var n=e.getBoundingClientRect(),o=1,r=1;if(z(e)&&t){var a=e.offsetHeight,i=e.offsetWidth;i>0&&(o=Ce(n.width)/i||1),a>0&&(r=Ce(n.height)/a||1)}return{width:n.width/o,height:n.height/r,top:n.top/r,right:n.right/o,bottom:n.bottom/r,left:n.left/o,x:n.left/o,y:n.top/r}}function Et(e){var t=Pe(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Cn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&bt(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function ae(e){return X(e).getComputedStyle(e)}function Rr(e){return["table","td","th"].indexOf(Q(e))>=0}function ue(e){return((Oe(e)?e.ownerDocument:e.document)||window.document).documentElement}function et(e){return Q(e)==="html"?e:e.assignedSlot||e.parentNode||(bt(e)?e.host:null)||ue(e)}function tn(e){return!z(e)||ae(e).position==="fixed"?null:e.offsetParent}function Ar(e){var t=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,n=navigator.userAgent.indexOf("Trident")!==-1;if(n&&z(e)){var o=ae(e);if(o.position==="fixed")return null}var r=et(e);for(bt(r)&&(r=r.host);z(r)&&["html","body"].indexOf(Q(r))<0;){var a=ae(r);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return r;r=r.parentNode}return null}function $e(e){for(var t=X(e),n=tn(e);n&&Rr(n)&&ae(n).position==="static";)n=tn(n);return n&&(Q(n)==="html"||Q(n)==="body"&&ae(n).position==="static")?t:n||Ar(e)||t}function wt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Le(e,t,n){return ve(e,Ye(t,n))}function Sr(e,t,n){var o=Le(e,t,n);return o>n?n:o}function Pn(){return{top:0,right:0,bottom:0,left:0}}function Rn(e){return Object.assign({},Pn(),e)}function An(e,t){return t.reduce(function(n,o){return n[o]=e,n},{})}var xr=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,Rn(typeof e!="number"?e:An(e,je))};function Fr(e){var t,n=e.state,o=e.name,r=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,s=Z(n.placement),u=wt(s),l=[K,V].indexOf(s)>=0,v=l?"height":"width";if(!(!a||!i)){var m=xr(r.padding,n),h=Et(a),g=u==="y"?U:K,c=u==="y"?q:V,f=n.rects.reference[v]+n.rects.reference[u]-i[u]-n.rects.popper[v],E=i[u]-n.rects.reference[u],b=$e(a),T=b?u==="y"?b.clientHeight||0:b.clientWidth||0:0,p=f/2-E/2,y=m[g],R=T-h[v]-m[c],O=T/2-h[v]/2+p,w=Le(y,O,R),P=u;n.modifiersData[o]=(t={},t[P]=w,t.centerOffset=w-O,t)}}function _r(e){var t=e.state,n=e.options,o=n.element,r=o===void 0?"[data-popper-arrow]":o;r!=null&&(typeof r=="string"&&(r=t.elements.popper.querySelector(r),!r)||!Cn(t.elements.popper,r)||(t.elements.arrow=r))}var Ir={name:"arrow",enabled:!0,phase:"main",fn:Fr,effect:_r,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Re(e){return e.split("-")[1]}var kr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Lr(e){var t=e.x,n=e.y,o=window,r=o.devicePixelRatio||1;return{x:Ce(t*r)/r||0,y:Ce(n*r)/r||0}}function nn(e){var t,n=e.popper,o=e.popperRect,r=e.placement,a=e.variation,i=e.offsets,s=e.position,u=e.gpuAcceleration,l=e.adaptive,v=e.roundOffsets,m=e.isFixed,h=i.x,g=h===void 0?0:h,c=i.y,f=c===void 0?0:c,E=typeof v=="function"?v({x:g,y:f}):{x:g,y:f};g=E.x,f=E.y;var b=i.hasOwnProperty("x"),T=i.hasOwnProperty("y"),p=K,y=U,R=window;if(l){var O=$e(n),w="clientHeight",P="clientWidth";if(O===X(n)&&(O=ue(n),ae(O).position!=="static"&&s==="absolute"&&(w="scrollHeight",P="scrollWidth")),O=O,r===U||(r===K||r===V)&&a===Ne){y=q;var S=m&&O===R&&R.visualViewport?R.visualViewport.height:O[w];f-=S-o.height,f*=u?1:-1}if(r===K||(r===U||r===q)&&a===Ne){p=V;var _=m&&O===R&&R.visualViewport?R.visualViewport.width:O[P];g-=_-o.width,g*=u?1:-1}}var k=Object.assign({position:s},l&&kr),x=v===!0?Lr({x:g,y:f}):{x:g,y:f};if(g=x.x,f=x.y,u){var B;return Object.assign({},k,(B={},B[y]=T?"0":"",B[p]=b?"0":"",B.transform=(R.devicePixelRatio||1)<=1?"translate("+g+"px, "+f+"px)":"translate3d("+g+"px, "+f+"px, 0)",B))}return Object.assign({},k,(t={},t[y]=T?f+"px":"",t[p]=b?g+"px":"",t.transform="",t))}function Mr(e){var t=e.state,n=e.options,o=n.gpuAcceleration,r=o===void 0?!0:o,a=n.adaptive,i=a===void 0?!0:a,s=n.roundOffsets,u=s===void 0?!0:s,l={placement:Z(t.placement),variation:Re(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,nn(Object.assign({},l,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:u})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,nn(Object.assign({},l,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:u})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var Sn={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Mr,data:{}},Ge={passive:!0};function Br(e){var t=e.state,n=e.instance,o=e.options,r=o.scroll,a=r===void 0?!0:r,i=o.resize,s=i===void 0?!0:i,u=X(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&l.forEach(function(v){v.addEventListener("scroll",n.update,Ge)}),s&&u.addEventListener("resize",n.update,Ge),function(){a&&l.forEach(function(v){v.removeEventListener("scroll",n.update,Ge)}),s&&u.removeEventListener("resize",n.update,Ge)}}var xn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Br,data:{}},Nr={left:"right",right:"left",bottom:"top",top:"bottom"};function Je(e){return e.replace(/left|right|bottom|top/g,function(t){return Nr[t]})}var Dr={start:"end",end:"start"};function on(e){return e.replace(/start|end/g,function(t){return Dr[t]})}function Tt(e){var t=X(e),n=t.pageXOffset,o=t.pageYOffset;return{scrollLeft:n,scrollTop:o}}function Ot(e){return Pe(ue(e)).left+Tt(e).scrollLeft}function jr(e){var t=X(e),n=ue(e),o=t.visualViewport,r=n.clientWidth,a=n.clientHeight,i=0,s=0;return o&&(r=o.width,a=o.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(i=o.offsetLeft,s=o.offsetTop)),{width:r,height:a,x:i+Ot(e),y:s}}function $r(e){var t,n=ue(e),o=Tt(e),r=(t=e.ownerDocument)==null?void 0:t.body,a=ve(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),i=ve(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),s=-o.scrollLeft+Ot(e),u=-o.scrollTop;return ae(r||n).direction==="rtl"&&(s+=ve(n.clientWidth,r?r.clientWidth:0)-a),{width:a,height:i,x:s,y:u}}function Ct(e){var t=ae(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+o)}function Fn(e){return["html","body","#document"].indexOf(Q(e))>=0?e.ownerDocument.body:z(e)&&Ct(e)?e:Fn(et(e))}function Me(e,t){var n;t===void 0&&(t=[]);var o=Fn(e),r=o===((n=e.ownerDocument)==null?void 0:n.body),a=X(o),i=r?[a].concat(a.visualViewport||[],Ct(o)?o:[]):o,s=t.concat(i);return r?s:s.concat(Me(et(i)))}function ct(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Hr(e){var t=Pe(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function rn(e,t){return t===Tn?ct(jr(e)):Oe(t)?Hr(t):ct($r(ue(e)))}function Wr(e){var t=Me(et(e)),n=["absolute","fixed"].indexOf(ae(e).position)>=0,o=n&&z(e)?$e(e):e;return Oe(o)?t.filter(function(r){return Oe(r)&&Cn(r,o)&&Q(r)!=="body"}):[]}function Ur(e,t,n){var o=t==="clippingParents"?Wr(e):[].concat(t),r=[].concat(o,[n]),a=r[0],i=r.reduce(function(s,u){var l=rn(e,u);return s.top=ve(l.top,s.top),s.right=Ye(l.right,s.right),s.bottom=Ye(l.bottom,s.bottom),s.left=ve(l.left,s.left),s},rn(e,a));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function _n(e){var t=e.reference,n=e.element,o=e.placement,r=o?Z(o):null,a=o?Re(o):null,i=t.x+t.width/2-n.width/2,s=t.y+t.height/2-n.height/2,u;switch(r){case U:u={x:i,y:t.y-n.height};break;case q:u={x:i,y:t.y+t.height};break;case V:u={x:t.x+t.width,y:s};break;case K:u={x:t.x-n.width,y:s};break;default:u={x:t.x,y:t.y}}var l=r?wt(r):null;if(l!=null){var v=l==="y"?"height":"width";switch(a){case Te:u[l]=u[l]-(t[v]/2-n[v]/2);break;case Ne:u[l]=u[l]+(t[v]/2-n[v]/2);break}}return u}function De(e,t){t===void 0&&(t={});var n=t,o=n.placement,r=o===void 0?e.placement:o,a=n.boundary,i=a===void 0?pr:a,s=n.rootBoundary,u=s===void 0?Tn:s,l=n.elementContext,v=l===void 0?_e:l,m=n.altBoundary,h=m===void 0?!1:m,g=n.padding,c=g===void 0?0:g,f=Rn(typeof c!="number"?c:An(c,je)),E=v===_e?dr:_e,b=e.rects.popper,T=e.elements[h?E:v],p=Ur(Oe(T)?T:T.contextElement||ue(e.elements.popper),i,u),y=Pe(e.elements.reference),R=_n({reference:y,element:b,placement:r}),O=ct(Object.assign({},b,R)),w=v===_e?O:y,P={top:p.top-w.top+f.top,bottom:w.bottom-p.bottom+f.bottom,left:p.left-w.left+f.left,right:w.right-p.right+f.right},S=e.modifiersData.offset;if(v===_e&&S){var _=S[r];Object.keys(P).forEach(function(k){var x=[V,q].indexOf(k)>=0?1:-1,B=[U,q].indexOf(k)>=0?"y":"x";P[k]+=_[B]*x})}return P}function Kr(e,t){t===void 0&&(t={});var n=t,o=n.placement,r=n.boundary,a=n.rootBoundary,i=n.padding,s=n.flipVariations,u=n.allowedAutoPlacements,l=u===void 0?yt:u,v=Re(o),m=v?s?en:en.filter(function(c){return Re(c)===v}):je,h=m.filter(function(c){return l.indexOf(c)>=0});h.length===0&&(h=m);var g=h.reduce(function(c,f){return c[f]=De(e,{placement:f,boundary:r,rootBoundary:a,padding:i})[Z(f)],c},{});return Object.keys(g).sort(function(c,f){return g[c]-g[f]})}function zr(e){if(Z(e)===ht)return[];var t=Je(e);return[on(e),t,on(t)]}function qr(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var r=n.mainAxis,a=r===void 0?!0:r,i=n.altAxis,s=i===void 0?!0:i,u=n.fallbackPlacements,l=n.padding,v=n.boundary,m=n.rootBoundary,h=n.altBoundary,g=n.flipVariations,c=g===void 0?!0:g,f=n.allowedAutoPlacements,E=t.options.placement,b=Z(E),T=b===E,p=u||(T||!c?[Je(E)]:zr(E)),y=[E].concat(p).reduce(function(ce,te){return ce.concat(Z(te)===ht?Kr(t,{placement:te,boundary:v,rootBoundary:m,padding:l,flipVariations:c,allowedAutoPlacements:f}):te)},[]),R=t.rects.reference,O=t.rects.popper,w=new Map,P=!0,S=y[0],_=0;_<y.length;_++){var k=y[_],x=Z(k),B=Re(k)===Te,H=[U,q].indexOf(x)>=0,G=H?"width":"height",L=De(t,{placement:k,boundary:v,rootBoundary:m,altBoundary:h,padding:l}),j=H?B?V:K:B?q:U;R[G]>O[G]&&(j=Je(j));var C=Je(j),N=[];if(a&&N.push(L[x]<=0),s&&N.push(L[j]<=0,L[C]<=0),N.every(function(ce){return ce})){S=k,P=!1;break}w.set(k,N)}if(P)for(var le=c?3:1,Se=function(ce){var te=y.find(function(We){var Fe=w.get(We);if(Fe)return Fe.slice(0,ce).every(function(ye){return ye})});if(te)return S=te,"break"},xe=le;xe>0;xe--){var He=Se(xe);if(He==="break")break}t.placement!==S&&(t.modifiersData[o]._skip=!0,t.placement=S,t.reset=!0)}}var Vr={name:"flip",enabled:!0,phase:"main",fn:qr,requiresIfExists:["offset"],data:{_skip:!1}};function an(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function sn(e){return[U,V,q,K].some(function(t){return e[t]>=0})}function Gr(e){var t=e.state,n=e.name,o=t.rects.reference,r=t.rects.popper,a=t.modifiersData.preventOverflow,i=De(t,{elementContext:"reference"}),s=De(t,{altBoundary:!0}),u=an(i,o),l=an(s,r,a),v=sn(u),m=sn(l);t.modifiersData[n]={referenceClippingOffsets:u,popperEscapeOffsets:l,isReferenceHidden:v,hasPopperEscaped:m},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":v,"data-popper-escaped":m})}var Jr={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Gr};function Xr(e,t,n){var o=Z(e),r=[K,U].indexOf(o)>=0?-1:1,a=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,i=a[0],s=a[1];return i=i||0,s=(s||0)*r,[K,V].indexOf(o)>=0?{x:s,y:i}:{x:i,y:s}}function Zr(e){var t=e.state,n=e.options,o=e.name,r=n.offset,a=r===void 0?[0,0]:r,i=yt.reduce(function(v,m){return v[m]=Xr(m,t.rects,a),v},{}),s=i[t.placement],u=s.x,l=s.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=u,t.modifiersData.popperOffsets.y+=l),t.modifiersData[o]=i}var Yr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Zr};function Qr(e){var t=e.state,n=e.name;t.modifiersData[n]=_n({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}var In={name:"popperOffsets",enabled:!0,phase:"read",fn:Qr,data:{}};function ea(e){return e==="x"?"y":"x"}function ta(e){var t=e.state,n=e.options,o=e.name,r=n.mainAxis,a=r===void 0?!0:r,i=n.altAxis,s=i===void 0?!1:i,u=n.boundary,l=n.rootBoundary,v=n.altBoundary,m=n.padding,h=n.tether,g=h===void 0?!0:h,c=n.tetherOffset,f=c===void 0?0:c,E=De(t,{boundary:u,rootBoundary:l,padding:m,altBoundary:v}),b=Z(t.placement),T=Re(t.placement),p=!T,y=wt(b),R=ea(y),O=t.modifiersData.popperOffsets,w=t.rects.reference,P=t.rects.popper,S=typeof f=="function"?f(Object.assign({},t.rects,{placement:t.placement})):f,_=typeof S=="number"?{mainAxis:S,altAxis:S}:Object.assign({mainAxis:0,altAxis:0},S),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,x={x:0,y:0};if(O){if(a){var B,H=y==="y"?U:K,G=y==="y"?q:V,L=y==="y"?"height":"width",j=O[y],C=j+E[H],N=j-E[G],le=g?-P[L]/2:0,Se=T===Te?w[L]:P[L],xe=T===Te?-P[L]:-w[L],He=t.elements.arrow,ce=g&&He?Et(He):{width:0,height:0},te=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Pn(),We=te[H],Fe=te[G],ye=Le(0,w[L],ce[L]),jn=p?w[L]/2-le-ye-We-_.mainAxis:Se-ye-We-_.mainAxis,$n=p?-w[L]/2+le+ye+Fe+_.mainAxis:xe+ye+Fe+_.mainAxis,tt=t.elements.arrow&&$e(t.elements.arrow),Hn=tt?y==="y"?tt.clientTop||0:tt.clientLeft||0:0,At=(B=k==null?void 0:k[y])!=null?B:0,Wn=j+jn-At-Hn,Un=j+$n-At,St=Le(g?Ye(C,Wn):C,j,g?ve(N,Un):N);O[y]=St,x[y]=St-j}if(s){var xt,Kn=y==="x"?U:K,zn=y==="x"?q:V,fe=O[R],Ue=R==="y"?"height":"width",Ft=fe+E[Kn],_t=fe-E[zn],nt=[U,K].indexOf(b)!==-1,It=(xt=k==null?void 0:k[R])!=null?xt:0,kt=nt?Ft:fe-w[Ue]-P[Ue]-It+_.altAxis,Lt=nt?fe+w[Ue]+P[Ue]-It-_.altAxis:_t,Mt=g&&nt?Sr(kt,fe,Lt):Le(g?kt:Ft,fe,g?Lt:_t);O[R]=Mt,x[R]=Mt-fe}t.modifiersData[o]=x}}var na={name:"preventOverflow",enabled:!0,phase:"main",fn:ta,requiresIfExists:["offset"]};function oa(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function ra(e){return e===X(e)||!z(e)?Tt(e):oa(e)}function aa(e){var t=e.getBoundingClientRect(),n=Ce(t.width)/e.offsetWidth||1,o=Ce(t.height)/e.offsetHeight||1;return n!==1||o!==1}function sa(e,t,n){n===void 0&&(n=!1);var o=z(t),r=z(t)&&aa(t),a=ue(t),i=Pe(e,r),s={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&((Q(t)!=="body"||Ct(a))&&(s=ra(t)),z(t)?(u=Pe(t,!0),u.x+=t.clientLeft,u.y+=t.clientTop):a&&(u.x=Ot(a))),{x:i.left+s.scrollLeft-u.x,y:i.top+s.scrollTop-u.y,width:i.width,height:i.height}}function ia(e){var t=new Map,n=new Set,o=[];e.forEach(function(a){t.set(a.name,a)});function r(a){n.add(a.name);var i=[].concat(a.requires||[],a.requiresIfExists||[]);i.forEach(function(s){if(!n.has(s)){var u=t.get(s);u&&r(u)}}),o.push(a)}return e.forEach(function(a){n.has(a.name)||r(a)}),o}function ua(e){var t=ia(e);return Or.reduce(function(n,o){return n.concat(t.filter(function(r){return r.phase===o}))},[])}function la(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function ca(e){var t=e.reduce(function(n,o){var r=n[o.name];return n[o.name]=r?Object.assign({},r,o,{options:Object.assign({},r.options,o.options),data:Object.assign({},r.data,o.data)}):o,n},{});return Object.keys(t).map(function(n){return t[n]})}var un={placement:"bottom",modifiers:[],strategy:"absolute"};function ln(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function Pt(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,r=t.defaultOptions,a=r===void 0?un:r;return function(i,s,u){u===void 0&&(u=a);var l={placement:"bottom",orderedModifiers:[],options:Object.assign({},un,a),modifiersData:{},elements:{reference:i,popper:s},attributes:{},styles:{}},v=[],m=!1,h={state:l,setOptions:function(f){var E=typeof f=="function"?f(l.options):f;c(),l.options=Object.assign({},a,l.options,E),l.scrollParents={reference:Oe(i)?Me(i):i.contextElement?Me(i.contextElement):[],popper:Me(s)};var b=ua(ca([].concat(o,l.options.modifiers)));return l.orderedModifiers=b.filter(function(T){return T.enabled}),g(),h.update()},forceUpdate:function(){if(!m){var f=l.elements,E=f.reference,b=f.popper;if(ln(E,b)){l.rects={reference:sa(E,$e(b),l.options.strategy==="fixed"),popper:Et(b)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(P){return l.modifiersData[P.name]=Object.assign({},P.data)});for(var T=0;T<l.orderedModifiers.length;T++){if(l.reset===!0){l.reset=!1,T=-1;continue}var p=l.orderedModifiers[T],y=p.fn,R=p.options,O=R===void 0?{}:R,w=p.name;typeof y=="function"&&(l=y({state:l,options:O,name:w,instance:h})||l)}}}},update:la(function(){return new Promise(function(f){h.forceUpdate(),f(l)})}),destroy:function(){c(),m=!0}};if(!ln(i,s))return h;h.setOptions(u).then(function(f){!m&&u.onFirstUpdate&&u.onFirstUpdate(f)});function g(){l.orderedModifiers.forEach(function(f){var E=f.name,b=f.options,T=b===void 0?{}:b,p=f.effect;if(typeof p=="function"){var y=p({state:l,name:E,instance:h,options:T}),R=function(){};v.push(y||R)}})}function c(){v.forEach(function(f){return f()}),v=[]}return h}}Pt();var fa=[xn,In,Sn,On];Pt({defaultModifiers:fa});var pa=[xn,In,Sn,On,Yr,Vr,na,Ir,Jr],da=Pt({defaultModifiers:pa});const kn=ee({arrowOffset:{type:Number,default:5}}),va=["fixed","absolute"],ma=ee({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:I(Array),default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:yt,default:"bottom"},popperOptions:{type:I(Object),default:()=>({})},strategy:{type:String,values:va,default:"absolute"}}),Ln=ee(F(W(F(F({},ma),kn),{id:String,style:{type:I([String,Array,Object])},className:{type:I([String,Array,Object])},effect:{type:I(String),default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:{type:Boolean,default:!1},trapping:{type:Boolean,default:!1},popperClass:{type:I([String,Array,Object])},popperStyle:{type:I([String,Array,Object])},referenceEl:{type:I(Object)},triggerTargetEl:{type:I(Object)},stopPopperMouseEvent:{type:Boolean,default:!0},virtualTriggering:Boolean,zIndex:Number}),mn(["ariaLabel"]))),ga={mouseenter:e=>e instanceof MouseEvent,mouseleave:e=>e instanceof MouseEvent,focus:()=>!0,blur:()=>!0,close:()=>!0},ha=(e,t)=>{const n=M(!1),o=M();return{focusStartRef:o,trapped:n,onFocusAfterReleased:l=>{var v;((v=l.detail)==null?void 0:v.focusReason)!=="pointer"&&(o.value="first",t("blur"))},onFocusAfterTrapped:()=>{t("focus")},onFocusInTrap:l=>{e.visible&&!n.value&&(l.target&&(o.value=l.target),n.value=!0)},onFocusoutPrevented:l=>{e.trapping||(l.detail.focusReason==="pointer"&&l.preventDefault(),n.value=!1)},onReleaseRequested:()=>{n.value=!1,t("close")}}},ya=(e,t=[])=>{const{placement:n,strategy:o,popperOptions:r}=e,a=W(F({placement:n,strategy:o},r),{modifiers:[...Ea(e),...t]});return wa(a,r==null?void 0:r.modifiers),a},ba=e=>{if(de)return cn(e)};function Ea(e){const{offset:t,gpuAcceleration:n,fallbackPlacements:o}=e;return[{name:"offset",options:{offset:[0,t!=null?t:12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:o}},{name:"computeStyles",options:{gpuAcceleration:n}}]}function wa(e,t){t&&(e.modifiers=[...e.modifiers,...t!=null?t:[]])}const Ta=(e,t,n={})=>{const o={name:"updateState",enabled:!0,phase:"write",fn:({state:u})=>{const l=Oa(u);Object.assign(i.value,l)},requires:["computeStyles"]},r=A(()=>{const{onFirstUpdate:u,placement:l,strategy:v,modifiers:m}=d(n);return{onFirstUpdate:u,placement:l||"bottom",strategy:v||"absolute",modifiers:[...m||[],o,{name:"applyStyles",enabled:!1}]}}),a=vo(),i=M({styles:{popper:{position:d(r).strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),s=()=>{a.value&&(a.value.destroy(),a.value=void 0)};return D(r,u=>{const l=d(a);l&&l.setOptions(u)},{deep:!0}),D([e,t],([u,l])=>{s(),!(!u||!l)&&(a.value=da(u,l,d(r)))}),ie(()=>{s()}),{state:A(()=>{var u;return F({},((u=d(a))==null?void 0:u.state)||{})}),styles:A(()=>d(i).styles),attributes:A(()=>d(i).attributes),update:()=>{var u;return(u=d(a))==null?void 0:u.update()},forceUpdate:()=>{var u;return(u=d(a))==null?void 0:u.forceUpdate()},instanceRef:A(()=>d(a))}};function Oa(e){const t=Object.keys(e.elements),n=jt(t.map(r=>[r,e.styles[r]||{}])),o=jt(t.map(r=>[r,e.attributes[r]]));return{styles:n,attributes:o}}const Ca=0,Pa=e=>{const{popperInstanceRef:t,contentRef:n,triggerRef:o,role:r}=ge(vt,void 0),a=M(),i=A(()=>e.arrowOffset),s=A(()=>({name:"eventListeners",enabled:!!e.visible})),u=A(()=>{var b;const T=d(a),p=(b=d(i))!=null?b:Ca;return{name:"arrow",enabled:!Mo(T),options:{element:T,padding:p}}}),l=A(()=>F({onFirstUpdate:()=>{c()}},ya(e,[d(u),d(s)]))),v=A(()=>ba(e.referenceEl)||d(o)),{attributes:m,state:h,styles:g,update:c,forceUpdate:f,instanceRef:E}=Ta(v,n,l);return D(E,b=>t.value=b,{flush:"sync"}),he(()=>{D(()=>{var b;return(b=d(v))==null?void 0:b.getBoundingClientRect()},()=>{c()})}),{attributes:m,arrowRef:a,contentRef:n,instanceRef:E,state:h,styles:g,role:r,forceUpdate:f,update:c}},Ra=(e,{attributes:t,styles:n,role:o})=>{const{nextZIndex:r}=eo(),a=Ae("popper"),i=A(()=>d(t).popper),s=M(it(e.zIndex)?e.zIndex:r()),u=A(()=>[a.b(),a.is("pure",e.pure),a.is(e.effect),e.popperClass]),l=A(()=>[{zIndex:d(s)},d(n).popper,e.popperStyle||{}]),v=A(()=>o.value==="dialog"?"false":void 0),m=A(()=>d(n).arrow||{});return{ariaModal:v,arrowStyle:m,contentAttrs:i,contentClass:u,contentStyle:l,contentZIndex:s,updateZIndex:()=>{s.value=it(e.zIndex)?e.zIndex:r()}}},Aa=$({name:"ElPopperContent"}),Sa=$(W(F({},Aa),{props:Ln,emits:ga,setup(e,{expose:t,emit:n}){const o=e,{focusStartRef:r,trapped:a,onFocusAfterReleased:i,onFocusAfterTrapped:s,onFocusInTrap:u,onFocusoutPrevented:l,onReleaseRequested:v}=ha(o,n),{attributes:m,arrowRef:h,contentRef:g,styles:c,instanceRef:f,role:E,update:b}=Pa(o),{ariaModal:T,arrowStyle:p,contentAttrs:y,contentClass:R,contentStyle:O,updateZIndex:w}=Ra(o,{styles:c,attributes:m,role:E}),P=ge(Ht,void 0);we(gn,{arrowStyle:p,arrowRef:h}),P&&we(Ht,W(F({},P),{addInputId:ut,removeInputId:ut}));let S;const _=(x=!0)=>{b(),x&&w()},k=()=>{_(!1),o.visible&&o.focusOnShow?a.value=!0:o.visible===!1&&(a.value=!1)};return he(()=>{D(()=>o.triggerTargetEl,(x,B)=>{S==null||S(),S=void 0;const H=d(x||g.value),G=d(B||g.value);pe(H)&&(S=D([E,()=>o.ariaLabel,T,()=>o.id],L=>{["role","aria-label","aria-modal","id"].forEach((j,C)=>{ft(L[C])?H.removeAttribute(j):H.setAttribute(j,L[C])})},{immediate:!0})),G!==H&&pe(G)&&["role","aria-label","aria-modal","id"].forEach(L=>{G.removeAttribute(L)})},{immediate:!0}),D(()=>o.visible,k,{immediate:!0})}),ie(()=>{S==null||S(),S=void 0}),t({popperContentRef:g,popperInstanceRef:f,updatePopper:_,contentStyle:O}),(x,B)=>(J(),Xe("div",dt({ref_key:"contentRef",ref:g},d(y),{style:d(O),class:d(R),tabindex:"-1",onMouseenter:H=>x.$emit("mouseenter",H),onMouseleave:H=>x.$emit("mouseleave",H)}),[Be(d(fr),{trapped:d(a),"trap-on-focus-in":!0,"focus-trap-el":d(g),"focus-start-el":d(r),onFocusAfterTrapped:d(s),onFocusAfterReleased:d(i),onFocusin:d(u),onFocusoutPrevented:d(l),onReleaseRequested:d(v)},{default:re(()=>[Y(x.$slots,"default")]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusin","onFocusoutPrevented","onReleaseRequested"])],16,["onMouseenter","onMouseleave"]))}}));var xa=se(Sa,[["__file","content.vue"]]);const Fa=pt(Ho),Rt=Symbol("elTooltip"),Mn=ee({to:{type:I([String,Object]),required:!0},disabled:Boolean}),Bn=ee(F(W(F(F({},Bo),Ln),{appendTo:{type:Mn.to.type},content:{type:String,default:""},rawContent:Boolean,persistent:Boolean,visible:{type:I(Boolean),default:null},transition:String,teleported:{type:Boolean,default:!0},disabled:Boolean}),mn(["ariaLabel"]))),Nn=ee(W(F({},yn),{disabled:Boolean,trigger:{type:I([String,Array]),default:"hover"},triggerKeys:{type:I(Array),default:()=>[ke.enter,ke.numpadEnter,ke.space]}})),_a=fn({type:I(Boolean),default:null}),Ia=fn({type:I(Function)}),ka=e=>{const t=`update:${e}`,n=`onUpdate:${e}`,o=[t],r={[e]:_a,[n]:Ia};return{useModelToggle:({indicator:i,toggleReason:s,shouldHideWhenRouteChanges:u,shouldProceed:l,onShow:v,onHide:m})=>{const h=mo(),{emit:g}=h,c=h.props,f=A(()=>Ke(c[n])),E=A(()=>c[e]===null),b=w=>{i.value!==!0&&(i.value=!0,s&&(s.value=w),Ke(v)&&v(w))},T=w=>{i.value!==!1&&(i.value=!1,s&&(s.value=w),Ke(m)&&m(w))},p=w=>{if(c.disabled===!0||Ke(l)&&!l())return;const P=f.value&&de;P&&g(t,!0),(E.value||!P)&&b(w)},y=w=>{if(c.disabled===!0||!de)return;const P=f.value&&de;P&&g(t,!1),(E.value||!P)&&T(w)},R=w=>{pn(w)&&(c.disabled&&w?f.value&&g(t,!1):i.value!==w&&(w?b():T()))},O=()=>{i.value?y():p()};return D(()=>c[e],R),u&&h.appContext.config.globalProperties.$route!==void 0&&D(()=>F({},h.proxy.$route),()=>{u.value&&i.value&&y()}),he(()=>{R(c[e])}),{hide:y,show:p,toggle:O,hasUpdateHandler:f}},useModelToggleProps:r,useModelToggleEmits:o}},{useModelToggleProps:La,useModelToggleEmits:Ma,useModelToggle:Ba}=ka("visible"),Na=ee(W(F(F(F(F(F({},hn),La),Bn),Nn),kn),{showArrow:{type:Boolean,default:!0}})),Da=[...Ma,"before-show","before-hide","show","hide","open","close"],ja=(e,t)=>go(e)?e.includes(t):e===t,be=(e,t,n)=>o=>{ja(d(e),t)&&n(o)},oe=(e,t,{checkForDefaultPrevented:n=!0}={})=>r=>{const a=e==null?void 0:e(r);if(n===!1||!a)return t==null?void 0:t(r)},$a=$({name:"ElTooltipTrigger"}),Ha=$(W(F({},$a),{props:Nn,setup(e,{expose:t}){const n=e,o=Ae("tooltip"),{controlled:r,id:a,open:i,onOpen:s,onClose:u,onToggle:l}=ge(Rt,void 0),v=M(null),m=()=>{if(d(r)||n.disabled)return!0},h=Ie(n,"trigger"),g=oe(m,be(h,"hover",s)),c=oe(m,be(h,"hover",u)),f=oe(m,be(h,"click",y=>{y.button===0&&l(y)})),E=oe(m,be(h,"focus",s)),b=oe(m,be(h,"focus",u)),T=oe(m,be(h,"contextmenu",y=>{y.preventDefault(),l(y)})),p=oe(m,y=>{const{code:R}=y;n.triggerKeys.includes(R)&&(y.preventDefault(),l(y))});return t({triggerRef:v}),(y,R)=>(J(),me(d(Zo),{id:d(a),"virtual-ref":y.virtualRef,open:d(i),"virtual-triggering":y.virtualTriggering,class:dn(d(o).e("trigger")),onBlur:d(b),onClick:d(f),onContextmenu:d(T),onFocus:d(E),onMouseenter:d(g),onMouseleave:d(c),onKeydown:d(p)},{default:re(()=>[Y(y.$slots,"default")]),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"]))}}));var Wa=se(Ha,[["__file","trigger.vue"]]);const Ua=$({__name:"teleport",props:Mn,setup(e){return(t,n)=>t.disabled?Y(t.$slots,"default",{key:0}):(J(),me(ho,{key:1,to:t.to},[Y(t.$slots,"default")],8,["to"]))}});var Ka=se(Ua,[["__file","teleport.vue"]]);const za=pt(Ka),Dn=()=>{const e=to(),t=To(),n=A(()=>`${e.value}-popper-container-${t.prefix}`),o=A(()=>`#${n.value}`);return{id:n,selector:o}},qa=e=>{const t=document.createElement("div");return t.id=e,document.body.appendChild(t),t},Va=()=>{const{id:e,selector:t}=Dn();return yo(()=>{de&&(document.body.querySelector(t.value)||qa(e.value))}),{id:e,selector:t}},Ga=$({name:"ElTooltipContent",inheritAttrs:!1}),Ja=$(W(F({},Ga),{props:Bn,setup(e,{expose:t}){const n=e,{selector:o}=Dn(),r=Ae("tooltip"),a=M(),i=no(()=>{var C;return(C=a.value)==null?void 0:C.popperContentRef});let s;const{controlled:u,id:l,open:v,trigger:m,onClose:h,onOpen:g,onShow:c,onHide:f,onBeforeShow:E,onBeforeHide:b}=ge(Rt,void 0),T=A(()=>n.transition||`${r.namespace.value}-fade-in-linear`),p=A(()=>n.persistent);ie(()=>{s==null||s()});const y=A(()=>d(p)?!0:d(v)),R=A(()=>n.disabled?!1:d(v)),O=A(()=>n.appendTo||o.value),w=A(()=>{var C;return(C=n.style)!=null?C:{}}),P=M(!0),S=()=>{f(),j()&&ne(document.body),P.value=!0},_=()=>{if(d(u))return!0},k=oe(_,()=>{n.enterable&&d(m)==="hover"&&g()}),x=oe(_,()=>{d(m)==="hover"&&h()}),B=()=>{var C,N;(N=(C=a.value)==null?void 0:C.updatePopper)==null||N.call(C),E==null||E()},H=()=>{b==null||b()},G=()=>{c()},L=()=>{n.virtualTriggering||h()},j=C=>{var N;const le=(N=a.value)==null?void 0:N.popperContentRef,Se=(C==null?void 0:C.relatedTarget)||document.activeElement;return le==null?void 0:le.contains(Se)};return D(()=>d(v),C=>{C?(P.value=!1,s=ao(i,()=>{if(d(u))return;d(m)!=="hover"&&h()})):s==null||s()},{flush:"post"}),D(()=>n.content,()=>{var C,N;(N=(C=a.value)==null?void 0:C.updatePopper)==null||N.call(C)}),t({contentRef:a,isFocusInsideContent:j}),(C,N)=>(J(),me(d(za),{disabled:!C.teleported,to:d(O)},{default:re(()=>[Be(oo,{name:d(T),onAfterLeave:S,onBeforeEnter:B,onAfterEnter:G,onBeforeLeave:H},{default:re(()=>[d(y)?vn((J(),me(d(xa),dt({key:0,id:d(l),ref_key:"contentRef",ref:a},C.$attrs,{"aria-label":C.ariaLabel,"aria-hidden":P.value,"boundaries-padding":C.boundariesPadding,"fallback-placements":C.fallbackPlacements,"gpu-acceleration":C.gpuAcceleration,offset:C.offset,placement:C.placement,"popper-options":C.popperOptions,"arrow-offset":C.arrowOffset,strategy:C.strategy,effect:C.effect,enterable:C.enterable,pure:C.pure,"popper-class":C.popperClass,"popper-style":[C.popperStyle,d(w)],"reference-el":C.referenceEl,"trigger-target-el":C.triggerTargetEl,visible:d(R),"z-index":C.zIndex,onMouseenter:d(k),onMouseleave:d(x),onBlur:L,onClose:d(h)}),{default:re(()=>[Y(C.$slots,"default")]),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","arrow-offset","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onClose"])),[[ro,d(R)]]):Ze("v-if",!0)]),_:3},8,["name"])]),_:3},8,["disabled","to"]))}}));var Xa=se(Ja,[["__file","content.vue"]]);const Za=$({name:"ElTooltip"}),Ya=$(W(F({},Za),{props:Na,emits:Da,setup(e,{expose:t,emit:n}){const o=e;Va();const r=Ae("tooltip"),a=Oo(),i=M(),s=M(),u=()=>{var p;const y=d(i);y&&((p=y.popperInstanceRef)==null||p.update())},l=M(!1),v=M(),{show:m,hide:h,hasUpdateHandler:g}=Ba({indicator:l,toggleReason:v}),{onOpen:c,onClose:f}=No({showAfter:Ie(o,"showAfter"),hideAfter:Ie(o,"hideAfter"),autoClose:Ie(o,"autoClose"),open:m,close:h}),E=A(()=>pn(o.visible)&&!g.value),b=A(()=>[r.b(),o.popperClass]);we(Rt,{controlled:E,id:a,open:bo(l),trigger:Ie(o,"trigger"),onOpen:p=>{c(p)},onClose:p=>{f(p)},onToggle:p=>{d(l)?f(p):c(p)},onShow:()=>{n("show",v.value)},onHide:()=>{n("hide",v.value)},onBeforeShow:()=>{n("before-show",v.value)},onBeforeHide:()=>{n("before-hide",v.value)},updatePopper:u}),D(()=>o.disabled,p=>{p&&l.value&&(l.value=!1)});const T=p=>{var y;return(y=s.value)==null?void 0:y.isFocusInsideContent(p)};return Eo(()=>l.value&&h()),t({popperRef:i,contentRef:s,isFocusInsideContent:T,updatePopper:u,onOpen:c,onClose:f,hide:h}),(p,y)=>(J(),me(d(Fa),{ref_key:"popperRef",ref:i,role:p.role},{default:re(()=>[Be(Wa,{disabled:p.disabled,trigger:p.trigger,"trigger-keys":p.triggerKeys,"virtual-ref":p.virtualRef,"virtual-triggering":p.virtualTriggering},{default:re(()=>[p.$slots.default?Y(p.$slots,"default",{key:0}):Ze("v-if",!0)]),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),Be(Xa,{ref_key:"contentRef",ref:s,"aria-label":p.ariaLabel,"boundaries-padding":p.boundariesPadding,content:p.content,disabled:p.disabled,effect:p.effect,enterable:p.enterable,"fallback-placements":p.fallbackPlacements,"hide-after":p.hideAfter,"gpu-acceleration":p.gpuAcceleration,offset:p.offset,persistent:p.persistent,"popper-class":d(b),"popper-style":p.popperStyle,placement:p.placement,"popper-options":p.popperOptions,"arrow-offset":p.arrowOffset,pure:p.pure,"raw-content":p.rawContent,"reference-el":p.referenceEl,"trigger-target-el":p.triggerTargetEl,"show-after":p.showAfter,strategy:p.strategy,teleported:p.teleported,transition:p.transition,"virtual-triggering":p.virtualTriggering,"z-index":p.zIndex,"append-to":p.appendTo},{default:re(()=>[Y(p.$slots,"content",{},()=>[p.rawContent?(J(),Xe("span",{key:0,innerHTML:p.content},null,8,["innerHTML"])):(J(),Xe("span",{key:1},wo(p.content),1))]),p.showArrow?(J(),me(d(Ko),{key:0})):Ze("v-if",!0)]),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","arrow-offset","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])]),_:3},8,["role"]))}}));var Qa=se(Ya,[["__file","tooltip.vue"]]);const fs=pt(Qa);export{ls as B,yt as E,us as G,Rt as T,fs as a,is as d,cs as r,Bn as u};
