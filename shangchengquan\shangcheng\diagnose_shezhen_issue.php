<?php
/**
 * 舌诊问题诊断和修复脚本
 * 2025-07-17 分析为什么现在拿不到舌诊分析结果
 */

// 引入ThinkPHP框架
require_once 'vendor/autoload.php';

// 初始化应用
$app = new think\App();
$app->initialize();

use app\common\SheZhen;
use think\facade\Db;

echo "<h1>舌诊问题诊断报告</h1>";

// 1. 检查配置获取
echo "<h2>1. 配置获取诊断</h2>";

$aid = 62; // 使用实际的aid
echo "<h3>测试 AID = $aid</h3>";

try {
    $config = SheZhen::getConfig($aid);
    
    if ($config) {
        echo "<p style='color: green;'>✓ 成功获取配置</p>";
        
        // 检查关键配置项
        $requiredFields = ['aliyun_app_code', 'aliyun_endpoint'];
        $configComplete = true;
        
        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                echo "<p style='color: red;'>✗ 缺少必需配置: $field</p>";
                $configComplete = false;
            } else {
                $value = $field === 'aliyun_app_code' ? substr($config[$field], 0, 8) . '***' : $config[$field];
                echo "<p style='color: green;'>✓ $field: $value</p>";
            }
        }
        
        if (!$configComplete) {
            echo "<p style='color: red;'><strong>问题：配置不完整，无法调用API</strong></p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ 未找到配置</p>";
        echo "<p style='color: red;'><strong>问题：配置获取失败</strong></p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 配置获取异常: " . $e->getMessage() . "</p>";
}

// 2. 检查数据库表结构
echo "<h2>2. 数据库表结构检查</h2>";

$tables = [
    'ddwx_sysset' => 'sysset表（旧配置方式）',
    'ddwx_shezhen_set' => 'shezhen_set表（新配置方式）',
    'ddwx_mianzhen_call_log' => 'API调用日志表',
    'ddwx_mianzhen_sessions' => 'session记录表'
];

foreach ($tables as $table => $description) {
    try {
        $exists = Db::query("SHOW TABLES LIKE '$table'");
        if ($exists) {
            echo "<p style='color: green;'>✓ $description 存在</p>";
            
            // 检查表中的数据
            if ($table === 'ddwx_sysset') {
                $mianzhenConfig = Db::name('sysset')->where('name', 'mianzhen_set')->find();
                if ($mianzhenConfig) {
                    echo "<p style='color: green;'>  ✓ 找到 mianzhen_set 配置</p>";
                } else {
                    echo "<p style='color: orange;'>  ⚠ 未找到 mianzhen_set 配置</p>";
                }
            } elseif ($table === 'ddwx_shezhen_set') {
                $count = Db::name('shezhen_set')->where('aid', $aid)->count();
                if ($count > 0) {
                    echo "<p style='color: green;'>  ✓ 找到 aid=$aid 的配置记录</p>";
                } else {
                    echo "<p style='color: orange;'>  ⚠ 未找到 aid=$aid 的配置记录</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ $description 不存在</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ 检查 $description 时出错: " . $e->getMessage() . "</p>";
    }
}

// 3. 模拟API调用测试
echo "<h2>3. API调用流程测试</h2>";

$testImageUrl = "https://kuaifengimg.azheteng.cn/upload/62/20250718/fed0d7bd030b57e056de3dba260e6e36.jpg";

echo "<h3>3.1 图片验证测试</h3>";
try {
    $imageValidation = SheZhen::validateImageUrl($testImageUrl);
    if ($imageValidation['status'] == 1) {
        echo "<p style='color: green;'>✓ 图片URL验证通过</p>";
    } else {
        echo "<p style='color: red;'>✗ 图片URL验证失败: " . $imageValidation['msg'] . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 图片验证异常: " . $e->getMessage() . "</p>";
}

echo "<h3>3.2 API连接测试</h3>";
try {
    $connectionTest = SheZhen::testApiConnection($aid);
    if ($connectionTest['status'] == 1) {
        echo "<p style='color: green;'>✓ API连接测试成功</p>";
    } else {
        echo "<p style='color: red;'>✗ API连接测试失败: " . $connectionTest['msg'] . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ API连接测试异常: " . $e->getMessage() . "</p>";
}

echo "<h3>3.3 检测API调用测试</h3>";
try {
    $imageData = [
        'tf_image' => $testImageUrl,
        'gender' => '男'
    ];
    
    echo "<p>测试图片数据：</p>";
    echo "<pre>" . json_encode($imageData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    $detectResult = SheZhen::callDetectApi($imageData, $aid);
    
    if ($detectResult['status'] == 1) {
        echo "<p style='color: green;'>✓ 检测API调用成功</p>";
        echo "<p>返回数据：</p>";
        echo "<pre>" . json_encode($detectResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<p style='color: red;'>✗ 检测API调用失败: " . $detectResult['msg'] . "</p>";
        echo "<p>返回数据：</p>";
        echo "<pre>" . json_encode($detectResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 检测API调用异常: " . $e->getMessage() . "</p>";
}

// 4. 问题总结和建议
echo "<h2>4. 问题总结和修复建议</h2>";

echo "<h3>已发现的问题：</h3>";
echo "<ol>";
echo "<li><strong>配置获取方式不一致</strong>：原来使用 sysset 表，现在使用 shezhen_set 表</li>";
echo "<li><strong>参数传递不兼容</strong>：前端传递 image_url，后端期望 tongue_image_url</li>";
echo "<li><strong>配置可能缺失</strong>：API配置信息可能不完整或不存在</li>";
echo "</ol>";

echo "<h3>已实施的修复：</h3>";
echo "<ol>";
echo "<li>✓ 修复了参数兼容性问题（image_url → tongue_image_url）</li>";
echo "<li>✓ 修复了配置获取方法，支持新旧两种方式</li>";
echo "<li>✓ 添加了详细的操作日志记录</li>";
echo "</ol>";

echo "<h3>下一步建议：</h3>";
echo "<ol>";
echo "<li>确保数据库中有正确的API配置信息</li>";
echo "<li>检查 aliyun_app_code 和 aliyun_endpoint 是否有效</li>";
echo "<li>测试实际的前端调用流程</li>";
echo "<li>检查网络连接和防火墙设置</li>";
echo "</ol>";

echo "<p><strong>修复完成时间：</strong>" . date('Y-m-d H:i:s') . "</p>";
?>
