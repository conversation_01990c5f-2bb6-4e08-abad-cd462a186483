import{$ as a}from"./bootstrap-CYivmKoJ.js";import{A as r}from"./authentication-CLX_ohXy.js";import{d as s,e as t,I as o,q as c,g as i,u as e}from"../jse/index-index-SSqEGcIT.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-BWxNdLTa.js";const h=s({__name:"auth",setup(u){const p=t(()=>o.app.name),n=t(()=>o.logo.source);return(m,l)=>(i(),c(e(r),{"app-name":p.value,logo:n.value,"page-description":e(a)("authentication.pageDesc"),"page-title":e(a)("authentication.pageTitle")},null,8,["app-name","logo","page-description","page-title"]))}});export{h as default};
