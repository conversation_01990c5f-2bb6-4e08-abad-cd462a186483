var w=(c,l,t)=>new Promise((n,s)=>{var f=e=>{try{u(t.next(e))}catch(m){s(m)}},r=e=>{try{u(t.throw(e))}catch(m){s(m)}},u=e=>e.done?n(e.value):Promise.resolve(e.value).then(f,r);u((t=t.apply(c,l)).next())});import{aT as k,aS as i,aU as v}from"./bootstrap-BmSDnAET.js";import{T as P}from"./auth-title-Drk9Wf4r.js";import{a4 as S,T as V,J as _,av as $,ab as B,x as p,aB as C,ac as g,a8 as T,ai as h,aj as b,a7 as o,aV as x,P as F,aa as y}from"../jse/index-index-BAMHRxBA.js";import{u as N,s as A}from"./use-vben-form-DKopJKB3.js";import"./render-content.vue_vue_type_script_lang-CTn4O0b5.js";const L=S({name:"ForgetPassword",__name:"forget-password",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(c,{expose:l,emit:t}){const n=c,s=t,[f,r]=N(V({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:_(()=>n.formSchema),showDefaultActions:!1})),u=k();function e(){return w(this,null,function*(){const{valid:a}=yield r.validate(),d=yield r.getValues();a&&s("submit",d)})}function m(){u.push(n.loginPath)}return l({getFormApi:()=>r}),(a,d)=>(B(),$("div",null,[p(P,null,{desc:g(()=>[T(a.$slots,"subTitle",{},()=>[h(b(a.subTitle||o(i)("authentication.forgetPasswordSubtitle")),1)])]),default:g(()=>[T(a.$slots,"title",{},()=>[h(b(a.title||o(i)("authentication.forgetPassword"))+" 🤦🏻‍♂️ ",1)])]),_:3}),p(o(f)),C("div",null,[p(o(v),{class:x([{"cursor-wait":a.loading},"mt-2 w-full"]),"aria-label":"submit",onClick:e},{default:g(()=>[T(a.$slots,"submitButtonText",{},()=>[h(b(a.submitButtonText||o(i)("authentication.sendResetLink")),1)])]),_:3},8,["class"]),p(o(v),{class:"mt-4 w-full",variant:"outline",onClick:d[0]||(d[0]=R=>m())},{default:g(()=>[h(b(o(i)("common.back")),1)]),_:1})])]))}}),J=S({name:"ForgetPassword",__name:"forget-password",setup(c){const l=F(!1),t=_(()=>[{component:"VbenInput",componentProps:{placeholder:"<EMAIL>"},fieldName:"email",label:i("authentication.email"),rules:A().min(1,{message:i("authentication.emailTip")}).email(i("authentication.emailValidErrorTip"))}]);function n(s){console.log("reset email:",s)}return(s,f)=>(B(),y(o(L),{"form-schema":t.value,loading:l.value,onSubmit:n},null,8,["form-schema","loading"]))}});export{J as default};
