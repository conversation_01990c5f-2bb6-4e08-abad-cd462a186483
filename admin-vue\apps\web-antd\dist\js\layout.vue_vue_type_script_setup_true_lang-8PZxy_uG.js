var vl=Object.defineProperty,gl=Object.defineProperties;var yl=Object.getOwnPropertyDescriptors;var Ot=Object.getOwnPropertySymbols;var Ia=Object.prototype.hasOwnProperty,Oa=Object.prototype.propertyIsEnumerable;var Qt=(o,t,a)=>t in o?vl(o,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):o[t]=a,Y=(o,t)=>{for(var a in t||(t={}))Ia.call(t,a)&&Qt(o,a,t[a]);if(Ot)for(var a of Ot(t))Oa.call(t,a)&&Qt(o,a,t[a]);return o},xe=(o,t)=>gl(o,yl(t));var Ce=(o,t)=>{var a={};for(var l in o)Ia.call(o,l)&&t.indexOf(l)<0&&(a[l]=o[l]);if(o!=null&&Ot)for(var l of Ot(o))t.indexOf(l)<0&&Oa.call(o,l)&&(a[l]=o[l]);return a};var tt=(o,t,a)=>Qt(o,typeof t!="symbol"?t+"":t,a);var J=(o,t,a)=>new Promise((l,s)=>{var n=u=>{try{d(a.next(u))}catch(f){s(f)}},r=u=>{try{d(a.throw(u))}catch(f){s(f)}},d=u=>u.done?l(u.value):Promise.resolve(u.value).then(n,r);d((a=a.apply(o,t)).next())});import{bM as wl,aR as ge,bA as oo,bN as xl,bO as da,bv as Ue,bP as lo,bQ as ua,bR as no,a7 as it,bS as so,bT as ro,bs as nt,bU as io,bV as uo,bW as kl,bX as Cl,bY as _l,bZ as Sl,b_ as Ml,b$ as Tl,c0 as $l,c1 as Bl,c2 as Vl,c3 as El,c4 as Ll,c5 as zl,c6 as Al,c7 as Pl,c8 as Il,c9 as Ol,ca as Dl,cb as Ul,cc as Hl,bk as ze,aU as Pe,cd as Rl,bu as Ke,az as Le,aj as Kt,b6 as ca,ce as Wl,cf as Nl,cg as Fl,ch as Kl,ci as jl,cj as Gl,bn as Je,ck as ql,bt as Yl,u as ft,aT as mt,cl as st,cm as pa,b9 as co,cn as fa,co as po,cp as ma,cq as sa,cr as fo,aS as g,a8 as De,cs as Xl,bj as mo,ct as ho,bL as ht,cu as bo,cv as Jl,b5 as bt,cw as Zl,cx as Ql,cy as en,cz as tn,cA as an,cB as on,cC as ln,cD as vo,cE as nn,b2 as sn,bl as rn,cF as dn,cG as un,cH as cn,cI as pn,cJ as fn,cK as go,cL as mn,cM as dt,cN as hn,cO as bn,cP as vn,cQ as gn}from"./bootstrap-BmSDnAET.js";import{bo as yn,bp as wn,bS as yo,a1 as D,r as xn,a4 as V,av as C,ab as i,aV as R,a7 as e,aW as le,a8 as B,aa as y,ac as c,x as b,af as Ne,ag as Ze,R as xt,J as w,P as G,aq as z,aw as fe,ad as he,aj as S,a_ as _e,b4 as kn,a$ as x,Y as ve,F as Q,aC as me,i as wt,a5 as Wt,bT as jt,bt as Cn,a9 as Qe,aB as E,ai as A,ah as Ae,bU as _n,a2 as rt,ax as At,O as Sn,bV as Mn,an as wo,bW as xo,aF as kt,bn as ko,T as ut,k as Nt,n as We,ao as ha,az as ba,bu as ea,bX as Tn,M as Co,bY as Dt,aA as Ie,bZ as $n,aZ as ra,bi as Bn,b_ as _o,b$ as Vt,c0 as Ft,c1 as Vn,c2 as Ut,c3 as En,U as va,as as Ge,b2 as vt,be as Ln,c4 as zn,c5 as An,c6 as Pn,c7 as Da,c8 as In,c9 as Ua,ae as So,bx as Ha,aJ as On,ca as Dn,cb as Un,b6 as yt,cc as Ra,cd as Hn,ba as Wa,q as Gt,aI as ia,_ as Rn,bM as Wn,bR as Nn,aD as Mo,N as Fn,a3 as Na}from"../jse/index-index-BAMHRxBA.js";import{_ as Et}from"./avatar.vue_vue_type_script_setup_true_lang-BBfkvUsj.js";import{_ as To,S as $o,a as Bo,u as Vo,s as Eo,C as Ct}from"./use-vben-form-DKopJKB3.js";import{_ as ga,a as ya,b as wa,c as $t,d as Kn,e as Lo,f as zo,S as jn,M as Gn,g as qn,h as Yn,i as Xn,j as Jn}from"./theme-toggle.vue_vue_type_script_setup_true_lang-XcsohWBu.js";import{a as Zn,b as Qn,_ as es}from"./TabsList.vue_vue_type_script_setup_true_lang-BKUkKJ6M.js";import{_ as ts}from"./render-content.vue_vue_type_script_lang-CTn4O0b5.js";import{R as xa}from"./rotate-cw-Bnbb2AMH.js";import{d as as}from"./index-dhlZeuSA.js";function ka(o,t){for(const a of o){if(a.path===t)return a;const l=a.children&&ka(a.children,t);if(l)return l}return null}function Bt(o,t,a=0){var r;const l=ka(o,t),s=(r=l==null?void 0:l.parents)==null?void 0:r[a],n=s?o.find(d=>d.path===s):void 0;return{findMenu:l,rootMenu:n,rootMenuPath:s}}const _t=wl("core-tabbar",{actions:{_bulkCloseByKeys(o){return J(this,null,function*(){const t=new Set(o);this.tabs=this.tabs.filter(a=>!t.has(Ye(a))),yield this.updateCacheTabs()})},_close(o){if(at(o))return;const t=this.tabs.findIndex(a=>ot(a,o));t!==-1&&this.tabs.splice(t,1)},_goToDefaultTab(o){return J(this,null,function*(){if(this.getTabs.length<=0)return;const t=this.getTabs[0];t&&(yield this._goToTab(t,o))})},_goToTab(o,t){return J(this,null,function*(){const{params:a,path:l,query:s}=o,n={params:a||{},path:l,query:s||{}};yield t.replace(n)})},addTab(o){var l,s;let t=os(o);if(t.key||(t.key=Xe(o)),!ls(t))return t;const a=this.tabs.findIndex(n=>ot(n,t));if(a===-1){const n=D.tabbar.maxCount,r=(s=(l=o==null?void 0:o.meta)==null?void 0:l.maxNumOfOpenTab)!=null?s:-1;if(r>0&&this.tabs.filter(d=>d.name===o.name).length>=r){const d=this.tabs.findIndex(u=>u.name===o.name);d!==-1&&this.tabs.splice(d,1)}else if(n>0&&this.tabs.length>=n){const d=this.tabs.findIndex(u=>!Reflect.has(u.meta,"affixTab")||!u.meta.affixTab);d!==-1&&this.tabs.splice(d,1)}this.tabs.push(t)}else{const n=xn(this.tabs)[a],r=xe(Y(Y({},n),t),{meta:Y(Y({},n==null?void 0:n.meta),t.meta)});if(n){const d=n.meta;Reflect.has(d,"affixTab")&&(r.meta.affixTab=d.affixTab),Reflect.has(d,"newTabTitle")&&(r.meta.newTabTitle=d.newTabTitle)}t=r,this.tabs.splice(a,1,r)}return this.updateCacheTabs(),t},closeAllTabs(o){return J(this,null,function*(){const t=this.tabs.filter(a=>at(a));this.tabs=t.length>0?t:[...this.tabs].splice(0,1),yield this._goToDefaultTab(o),this.updateCacheTabs()})},closeLeftTabs(o){return J(this,null,function*(){const t=this.tabs.findIndex(s=>ot(s,o));if(t<1)return;const a=this.tabs.slice(0,t),l=[];for(const s of a)at(s)||l.push(s.key);yield this._bulkCloseByKeys(l)})},closeOtherTabs(o){return J(this,null,function*(){const t=this.tabs.map(l=>Ye(l)),a=[];for(const l of t)if(l!==Ye(o)){const s=this.tabs.find(n=>Ye(n)===l);if(!s)continue;at(s)||a.push(s.key)}yield this._bulkCloseByKeys(a)})},closeRightTabs(o){return J(this,null,function*(){const t=this.tabs.findIndex(a=>ot(a,o));if(t!==-1&&t<this.tabs.length-1){const a=this.tabs.slice(t+1),l=[];for(const s of a)at(s)||l.push(s.key);yield this._bulkCloseByKeys(l)}})},closeTab(o,t){return J(this,null,function*(){const{currentRoute:a}=t;if(Xe(a.value)!==Ye(o)){this._close(o),this.updateCacheTabs();return}const l=this.getTabs.findIndex(r=>Ye(r)===Xe(a.value)),s=this.getTabs[l-1],n=this.getTabs[l+1];n?(this._close(o),yield this._goToTab(n,t)):s?(this._close(o),yield this._goToTab(s,t)):console.error("Failed to close the tab; only one tab remains open.")})},closeTabByKey(o,t){return J(this,null,function*(){const a=decodeURIComponent(o),l=this.tabs.findIndex(n=>Ye(n)===a);if(l===-1)return;const s=this.tabs[l];s&&(yield this.closeTab(s,t))})},getTabByKey(o){return this.getTabs.find(t=>Ye(t)===o)},openTabInNewWindow(o){return J(this,null,function*(){yo(o.fullPath||o.path)})},pinTab(o){return J(this,null,function*(){var n;const t=this.tabs.findIndex(r=>ot(r,o));if(t===-1)return;const a=this.tabs[t];o.meta.affixTab=!0,o.meta.title=(n=a==null?void 0:a.meta)==null?void 0:n.title,this.tabs.splice(t,1,o);const s=this.tabs.filter(r=>at(r)).findIndex(r=>ot(r,o));yield this.sortTabs(t,s)})},refresh(o){return J(this,null,function*(){if(typeof o=="string")return yield this.refreshByName(o);const{currentRoute:t}=o,{name:a}=t.value;this.excludeCachedTabs.add(a),this.renderRouteView=!1,yn(),yield new Promise(l=>setTimeout(l,200)),this.excludeCachedTabs.delete(a),this.renderRouteView=!0,wn()})},refreshByName(o){return J(this,null,function*(){this.excludeCachedTabs.add(o),yield new Promise(t=>setTimeout(t,200)),this.excludeCachedTabs.delete(o)})},resetTabTitle(o){return J(this,null,function*(){var a;if((a=o==null?void 0:o.meta)!=null&&a.newTabTitle)return;const t=this.tabs.find(l=>ot(l,o));t&&(t.meta.newTabTitle=void 0,yield this.updateCacheTabs())})},setAffixTabs(o){for(const t of o)t.meta.affixTab=!0,this.addTab(ns(t))},setMenuList(o){this.menuList=o},setTabTitle(o,t){return J(this,null,function*(){const a=this.tabs.find(l=>ot(l,o));a&&(a.meta.newTabTitle=t,yield this.updateCacheTabs())})},setUpdateTime(){this.updateTime=Date.now()},sortTabs(o,t){return J(this,null,function*(){const a=this.tabs[o];a&&(this.tabs.splice(o,1),this.tabs.splice(t,0,a),this.dragEndIndex=this.dragEndIndex+1)})},toggleTabPin(o){return J(this,null,function*(){var a,l;yield((l=(a=o==null?void 0:o.meta)==null?void 0:a.affixTab)!=null?l:!1)?this.unpinTab(o):this.pinTab(o)})},unpinTab(o){return J(this,null,function*(){var n;const t=this.tabs.findIndex(r=>ot(r,o));if(t===-1)return;const a=this.tabs[t];o.meta.affixTab=!1,o.meta.title=(n=a==null?void 0:a.meta)==null?void 0:n.title,this.tabs.splice(t,1,o);const s=this.tabs.filter(r=>at(r)).length;yield this.sortTabs(t,s)})},updateCacheTabs(){return J(this,null,function*(){var t;const o=new Set;for(const a of this.tabs){if(!((t=a.meta)==null?void 0:t.keepAlive))continue;(a.matched||[]).forEach((n,r)=>{r>0&&o.add(n.name)});const s=a.name;o.add(s)}this.cachedTabs=o})}},getters:{affixTabs(){return this.tabs.filter(t=>at(t)).sort((t,a)=>{var n,r,d,u;const l=(r=(n=t.meta)==null?void 0:n.affixTabOrder)!=null?r:0,s=(u=(d=a.meta)==null?void 0:d.affixTabOrder)!=null?u:0;return l-s})},getCachedTabs(){return[...this.cachedTabs]},getExcludeCachedTabs(){return[...this.excludeCachedTabs]},getMenuList(){return this.menuList},getTabs(){const o=this.tabs.filter(t=>!at(t));return[...this.affixTabs,...o].filter(Boolean)}},persist:[{pick:["tabs"],storage:sessionStorage}],state:()=>({cachedTabs:new Set,dragEndIndex:0,excludeCachedTabs:new Set,menuList:["close","affix","maximize","reload","open-in-new-window","close-left","close-right","close-other","close-all"],renderRouteView:!0,tabs:[],updateTime:Date.now()})});function os(o){if(!o)return o;const s=o,{matched:t,meta:a}=s,l=Ce(s,["matched","meta"]);return xe(Y({},l),{matched:t?t.map(n=>({meta:n.meta,name:n.name,path:n.path})):void 0,meta:xe(Y({},a),{newTabTitle:a.newTabTitle})})}function at(o){var t,a;return(a=(t=o==null?void 0:o.meta)==null?void 0:t.affixTab)!=null?a:!1}function ls(o){var a;const t=(a=o==null?void 0:o.matched)!=null?a:[];return!o.meta.hideInTab&&t.every(l=>!l.meta.hideInTab)}function Xe(o){const{fullPath:t,path:a,meta:{fullPathKey:l}={},query:s={}}=o,n=Array.isArray(s.pageKey)?s.pageKey[0]:s.pageKey;let r;n?r=n:r=l===!1?a:t!=null?t:a;try{return decodeURIComponent(r)}catch(d){return r}}function Ye(o){var t;return(t=o.key)!=null?t:Xe(o)}function ot(o,t){return Ye(o)===Ye(t)}function ns(o){return{meta:o.meta,name:o.name,path:o.path,key:Xe(o)}}const ss=ge("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);const rs=ge("arrow-left-to-line",[["path",{d:"M3 19V5",key:"rwsyhb"}],["path",{d:"m13 6-6 6 6 6",key:"1yhaz7"}],["path",{d:"M7 12h14",key:"uoisry"}]]);const is=ge("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);const ds=ge("arrow-right-to-line",[["path",{d:"M17 12H3",key:"8awo09"}],["path",{d:"m11 18 6-6-6-6",key:"8c2y43"}],["path",{d:"M21 5v14",key:"nzette"}]]);const us=ge("arrow-up-to-line",[["path",{d:"M5 3h14",key:"7usisc"}],["path",{d:"m18 13-6-6-6 6",key:"1kf1n9"}],["path",{d:"M12 7v14",key:"1akyts"}]]);const cs=ge("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);const ps=ge("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);const fs=ge("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);const ms=ge("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);const hs=ge("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);const bs=ge("corner-down-left",[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]]);const vs=ge("expand",[["path",{d:"m15 15 6 6",key:"1s409w"}],["path",{d:"m15 9 6-6",key:"ko1vev"}],["path",{d:"M21 16v5h-5",key:"1ck2sf"}],["path",{d:"M21 8V3h-5",key:"1qoq8a"}],["path",{d:"M3 16v5h5",key:"1t08am"}],["path",{d:"m3 21 6-6",key:"wwnumi"}],["path",{d:"M3 8V3h5",key:"1ln10m"}],["path",{d:"M9 9 3 3",key:"v551iv"}]]);const gs=ge("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);const ys=ge("fold-horizontal",[["path",{d:"M2 12h6",key:"1wqiqv"}],["path",{d:"M22 12h-6",key:"1eg9hc"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m19 9-3 3 3 3",key:"12ol22"}],["path",{d:"m5 15 3-3-3-3",key:"1kdhjc"}]]);const Ao=ge("fullscreen",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]]);const Po=ge("lock-keyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]);const ws=ge("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);const xs=ge("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);const ks=ge("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);const Io=ge("minimize-2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);const Cs=ge("minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]);const Oo=ge("pin-off",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M15 9.34V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H7.89",key:"znwnzq"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v1.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h11",key:"c9qhm2"}]]);const qt=ge("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);const _s=ge("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);const Ss=ge("search-x",[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);const Fa=ge("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);const Do=ge("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);const Ms=ge("shrink",[["path",{d:"m15 15 6 6m-6-6v4.8m0-4.8h4.8",key:"17vawe"}],["path",{d:"M9 19.8V15m0 0H4.2M9 15l-6 6",key:"chjx8e"}],["path",{d:"M15 4.2V9m0 0h4.8M15 9l6-6",key:"lav6yq"}],["path",{d:"M9 4.2V9m0 0H4.2M9 9 3 3",key:"1pxi2q"}]]);const Ts=ge("user-round-pen",[["path",{d:"M2 21a8 8 0 0 1 10.821-7.487",key:"1c8h7z"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}]]);const pt=ge("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),$s=oo("inline-flex items-center rounded-md border border-border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-accent hover:bg-accent text-primary-foreground shadow",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive-hover",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}}),Bs=V({__name:"Badge",props:{class:{},variant:{}},setup(o){const t=o;return(a,l)=>(i(),C("div",{class:R(e(le)(e($s)({variant:a.variant}),t.class))},[B(a.$slots,"default")],2))}}),Vs=V({__name:"Breadcrumb",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("nav",{class:R(t.class),"aria-label":"breadcrumb",role:"navigation"},[B(a.$slots,"default")],2))}}),Es=V({__name:"BreadcrumbItem",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("li",{class:R(e(le)("hover:text-foreground inline-flex items-center gap-1.5",t.class))},[B(a.$slots,"default")],2))}}),Ls=V({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(o){const t=o;return(a,l)=>(i(),y(e(xl),{as:a.as,"as-child":a.asChild,class:R(e(le)("hover:text-foreground transition-colors",t.class))},{default:c(()=>[B(a.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),zs=V({__name:"BreadcrumbList",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("ol",{class:R(e(le)("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5",t.class))},[B(a.$slots,"default")],2))}}),As=V({__name:"BreadcrumbPage",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("span",{class:R(e(le)("text-foreground font-normal",t.class)),"aria-current":"page","aria-disabled":"true",role:"link"},[B(a.$slots,"default")],2))}}),Ps=V({__name:"BreadcrumbSeparator",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("li",{class:R(e(le)("[&>svg]:size-3.5",t.class)),"aria-hidden":"true",role:"presentation"},[B(a.$slots,"default",{},()=>[b(e(da))])],2))}}),Is=V({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:t}){const s=Ue(o,t);return(n,r)=>(i(),y(e(lo),Ne(Ze(e(s))),{default:c(()=>[B(n.$slots,"default")]),_:3},16))}}),Os=["data-dismissable-modal"],Ds=V({__name:"DialogOverlay",setup(o){ua();const t=xt("DISMISSABLE_MODAL_ID");return(a,l)=>(i(),C("div",{"data-dismissable-modal":e(t),class:"bg-overlay z-popup inset-0"},null,8,Os))}}),Us=V({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},appendTo:{default:"body"},class:{},closeClass:{},closeDisabled:{type:Boolean,default:!1},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},showClose:{type:Boolean,default:!0},zIndex:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(o,{expose:t,emit:a}){const l=o,s=a,n=w(()=>{const U=l,{class:h,modal:m,open:v,showClose:k}=U;return Ce(U,["class","modal","open","showClose"])});function r(){return l.appendTo==="body"||l.appendTo===document.body||!l.appendTo}const d=w(()=>r()?"fixed":"absolute"),u=Ue(n,s),f=G(null);function p(h){var m;h.target===((m=f.value)==null?void 0:m.$el)&&(l.open?s("opened"):s("closed"))}return t({getContentRef:()=>f.value}),(h,m)=>(i(),y(e(no),{to:h.appendTo},{default:c(()=>[b(it,{name:"fade"},{default:c(()=>[h.open&&h.modal?(i(),y(Ds,{key:0,style:fe(xe(Y({},h.zIndex?{zIndex:h.zIndex}:{}),{position:d.value,backdropFilter:h.overlayBlur&&h.overlayBlur>0?`blur(${h.overlayBlur}px)`:"none"})),onClick:m[0]||(m[0]=()=>s("close"))},null,8,["style"])):z("",!0)]),_:1}),b(e(ro),he({ref_key:"contentRef",ref:f,style:xe(Y({},h.zIndex?{zIndex:h.zIndex}:{}),{position:d.value}),onAnimationend:p},e(u),{class:e(le)("z-popup bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] w-full p-6 shadow-lg outline-none sm:rounded-xl",l.class)}),{default:c(()=>[B(h.$slots,"default"),h.showClose?(i(),y(e(so),{key:0,disabled:h.closeDisabled,class:R(e(le)("data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-3 top-3 h-6 w-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none",l.closeClass)),onClick:m[1]||(m[1]=()=>s("close"))},{default:c(()=>[b(e(pt),{class:"h-4 w-4"})]),_:1},8,["disabled","class"])):z("",!0)]),_:3},16,["style","class"])]),_:3},8,["to"]))}}),Ka=V({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const r=t,{class:s}=r;return Ce(r,["class"])}),l=nt(a);return(s,n)=>(i(),y(e(io),he(e(l),{class:e(le)("text-muted-foreground text-sm",t.class)}),{default:c(()=>[B(s.$slots,"default")]),_:3},16,["class"]))}}),Hs=V({__name:"DialogFooter",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("div",{class:R(e(le)("flex flex-row flex-col-reverse justify-end gap-x-2",t.class))},[B(a.$slots,"default")],2))}}),Rs=V({__name:"DialogHeader",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("div",{class:R(e(le)("flex flex-col gap-y-1.5 text-center sm:text-left",t.class))},[B(a.$slots,"default")],2))}}),ja=V({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const r=t,{class:s}=r;return Ce(r,["class"])}),l=nt(a);return(s,n)=>(i(),y(e(uo),he(e(l),{class:e(le)("text-lg font-semibold leading-none tracking-tight",t.class)}),{default:c(()=>[B(s.$slots,"default")]),_:3},16,["class"]))}}),Ws=V({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(o){const t=o,a=w(()=>{const r=t,{class:s}=r;return Ce(r,["class"])}),l=nt(a);return(s,n)=>(i(),y(e(kl),he(e(l),{class:e(le)("px-2 py-1.5 text-sm font-semibold",s.inset&&"pl-8",t.class)}),{default:c(()=>[B(s.$slots,"default")]),_:3},16,["class"]))}}),Ht=V({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const n=t,{class:l}=n;return Ce(n,["class"])});return(l,s)=>(i(),y(e(Cl),he(a.value,{class:e(le)("bg-border -mx-1 my-1 h-px",t.class)}),null,16,["class"]))}}),Ga=V({__name:"DropdownMenuShortcut",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("span",{class:R(e(le)("ml-auto text-xs tracking-widest opacity-60",t.class))},[B(a.$slots,"default")],2))}}),Ns=V({__name:"HoverCard",props:{defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(o,{emit:t}){const s=Ue(o,t);return(n,r)=>(i(),y(e(_l),Ne(Ze(e(s))),{default:c(()=>[B(n.$slots,"default")]),_:3},16))}}),Fs=V({__name:"HoverCardContent",props:{forceMount:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const r=t,{class:s}=r;return Ce(r,["class"])}),l=nt(a);return(s,n)=>(i(),y(e(Sl),null,{default:c(()=>[b(e(Ml),he(e(l),{class:e(le)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup w-64 rounded-md border p-4 shadow-md outline-none",t.class)}),{default:c(()=>[B(s.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Ks=V({__name:"HoverCardTrigger",props:{asChild:{type:Boolean},as:{}},setup(o){const t=o;return(a,l)=>(i(),y(e(Tl),Ne(Ze(t)),{default:c(()=>[B(a.$slots,"default")]),_:3},16))}}),js=V({__name:"NumberField",props:{defaultValue:{},modelValue:{},min:{},max:{},step:{},formatOptions:{},locale:{},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:modelValue"],setup(o,{emit:t}){const a=o,l=t,s=w(()=>{const u=a,{class:r}=u;return Ce(u,["class"])}),n=Ue(s,l);return(r,d)=>(i(),y(e($l),he(e(n),{class:e(le)("grid gap-1.5",a.class)}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"]))}}),Gs=V({__name:"NumberFieldContent",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("div",{class:R(e(le)("relative [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5 [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5",t.class))},[B(a.$slots,"default")],2))}}),qs=V({__name:"NumberFieldDecrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const r=t,{class:s}=r;return Ce(r,["class"])}),l=nt(a);return(s,n)=>(i(),y(e(Vl),he({"data-slot":"decrement"},e(l),{class:e(le)("absolute left-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",t.class)}),{default:c(()=>[B(s.$slots,"default",{},()=>[b(e(Bl),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),Ys=V({__name:"NumberFieldIncrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const r=t,{class:s}=r;return Ce(r,["class"])}),l=nt(a);return(s,n)=>(i(),y(e(El),he({"data-slot":"increment"},e(l),{class:e(le)("absolute right-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",t.class)}),{default:c(()=>[B(s.$slots,"default",{},()=>[b(e(_s),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),Xs=V({__name:"NumberFieldInput",setup(o){return(t,a)=>(i(),y(e(Ll),{class:R(e(le)("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent py-1 text-center text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50")),"data-slot":"input"},null,8,["class"]))}}),Uo=V({__name:"ScrollBar",props:{orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const n=t,{class:l}=n;return Ce(n,["class"])});return(l,s)=>(i(),y(e(zl),he(a.value,{class:e(le)("flex touch-none select-none transition-colors",l.orientation==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-px",l.orientation==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-px",t.class)}),{default:c(()=>[b(e(Al),{class:"bg-border relative flex-1 rounded-full"})]),_:1},16,["class"]))}}),Js=V({__name:"ScrollArea",props:{type:{},dir:{},scrollHideDelay:{},asChild:{type:Boolean},as:{},class:{},onScroll:{type:Function,default:()=>{}},viewportProps:{}},setup(o){const t=o,a=w(()=>{const n=t,{class:l}=n;return Ce(n,["class"])});return(l,s)=>(i(),y(e(Pl),he(a.value,{class:e(le)("relative overflow-hidden",t.class)}),{default:c(()=>[b(e(Il),{"as-child":"",class:"h-full w-full rounded-[inherit] focus:outline-none",onScroll:l.onScroll},{default:c(()=>[B(l.$slots,"default")]),_:3},8,["onScroll"]),b(Uo),b(e(Ol))]),_:3},16,["class"]))}}),Zs=V({__name:"Separator",props:{orientation:{},decorative:{type:Boolean},asChild:{type:Boolean},as:{},class:{},label:{}},setup(o){const t=o,a=w(()=>{const n=t,{class:l}=n;return Ce(n,["class"])});return(l,s)=>(i(),y(e(Dl),he(a.value,{class:e(le)("bg-border relative shrink-0",t.orientation==="vertical"?"h-full w-px":"h-px w-full",t.class)}),{default:c(()=>[t.label?(i(),C("span",{key:0,class:R(e(le)("text-muted-foreground bg-background absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 items-center justify-center text-xs",t.orientation==="vertical"?"w-[1px] px-1 py-2":"h-[1px] px-2 py-1"))},S(t.label),3)):z("",!0)]),_:1},16,["class"]))}}),Qs=oo("bg-background shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 border-border",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t border-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left ",right:"inset-y-0 right-0 w-3/4 border-l  data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),er=V({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:t}){const s=Ue(o,t);return(n,r)=>(i(),y(e(lo),Ne(Ze(e(s))),{default:c(()=>[B(n.$slots,"default")]),_:3},16))}}),qa=V({__name:"SheetClose",props:{asChild:{type:Boolean},as:{}},setup(o){const t=o;return(a,l)=>(i(),y(e(so),Ne(Ze(t)),{default:c(()=>[B(a.$slots,"default")]),_:3},16))}}),tr=["data-dismissable-drawer"],ar=V({__name:"SheetOverlay",setup(o){ua();const t=xt("DISMISSABLE_DRAWER_ID");return(a,l)=>(i(),C("div",{"data-dismissable-drawer":e(t),class:"bg-overlay z-popup inset-0"},null,8,tr))}}),or=V({inheritAttrs:!1,__name:"SheetContent",props:{appendTo:{default:"body"},class:{},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},side:{},zIndex:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(o,{emit:t}){const a=o,l=t,s=w(()=>{const O=a,{class:p,modal:h,open:m,side:v}=O;return Ce(O,["class","modal","open","side"])});function n(){return a.appendTo==="body"||a.appendTo===document.body||!a.appendTo}const r=w(()=>n()?"fixed":"absolute"),d=Ue(s,l),u=G(null);function f(p){var h;p.target===((h=u.value)==null?void 0:h.$el)&&(a.open?l("opened"):l("closed"))}return(p,h)=>(i(),y(e(no),{to:p.appendTo},{default:c(()=>[b(it,{name:"fade"},{default:c(()=>[p.open&&p.modal?(i(),y(ar,{key:0,style:fe(xe(Y({},p.zIndex?{zIndex:p.zIndex}:{}),{position:r.value,backdropFilter:p.overlayBlur&&p.overlayBlur>0?`blur(${p.overlayBlur}px)`:"none"}))},null,8,["style"])):z("",!0)]),_:1}),b(e(ro),he({ref_key:"contentRef",ref:u,class:e(le)("z-popup",e(Qs)({side:p.side}),a.class),style:xe(Y({},p.zIndex?{zIndex:p.zIndex}:{}),{position:r.value}),onAnimationend:f},Y(Y({},e(d)),p.$attrs)),{default:c(()=>[B(p.$slots,"default")]),_:3},16,["class","style"])]),_:3},8,["to"]))}}),ta=V({__name:"SheetDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const n=t,{class:l}=n;return Ce(n,["class"])});return(l,s)=>(i(),y(e(io),he({class:e(le)("text-muted-foreground text-sm",t.class)},a.value),{default:c(()=>[B(l.$slots,"default")]),_:3},16,["class"]))}}),lr=V({__name:"SheetFooter",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("div",{class:R(e(le)("flex flex-row flex-col-reverse justify-end gap-x-2",t.class))},[B(a.$slots,"default")],2))}}),nr=V({__name:"SheetHeader",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("div",{class:R(e(le)("flex flex-col text-center sm:text-left",t.class))},[B(a.$slots,"default")],2))}}),aa=V({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const n=t,{class:l}=n;return Ce(n,["class"])});return(l,s)=>(i(),y(e(uo),he({class:e(le)("text-foreground font-medium",t.class)},a.value),{default:c(()=>[B(l.$slots,"default")]),_:3},16,["class"]))}}),sr=V({__name:"Switch",props:{defaultChecked:{type:Boolean},checked:{type:Boolean},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},value:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:checked"],setup(o,{emit:t}){const a=o,l=t,s=w(()=>{const u=a,{class:r}=u;return Ce(u,["class"])}),n=Ue(s,l);return(r,d)=>(i(),y(e(Hl),he(e(n),{class:e(le)("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-input peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a.class)}),{default:c(()=>[b(e(Ul),{class:R(e(le)("bg-background pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"))},null,8,["class"])]),_:1},16,["class"]))}}),rr=V({name:"VbenButtonGroup",__name:"button-group",props:{border:{type:Boolean,default:!1},gap:{default:0},size:{default:"middle"}},setup(o){return(t,a)=>(i(),C("div",{class:R(e(le)("vben-button-group rounded-md",`size-${t.size}`,t.gap?"with-gap":"no-gap",t.$attrs.class)),style:fe({gap:t.gap?`${t.gap}px`:"0px"})},[B(t.$slots,"default",{},void 0,!0)],6))}}),ir=ze(rr,[["__scopeId","data-v-ba11c217"]]),dr={key:0,class:"icon-wrapper"},ur=V({__name:"check-button-group",props:_e({allowClear:{type:Boolean,default:!1},beforeChange:{},btnClass:{},gap:{default:0},maxCount:{default:0},multiple:{type:Boolean,default:!1},options:{},showIcon:{type:Boolean,default:!0},size:{default:"middle"},disabled:{type:Boolean}},{modelValue:{},modelModifiers:{}}),emits:_e(["btnClick"],["update:modelValue"]),setup(o,{emit:t}){const a=o,l=t,s=w(()=>xe(Y({},kn(a,["options","btnClass","size","disabled"])),{class:le(a.btnClass)})),n=x(o,"modelValue"),r=G([]),d=G([]);ve(()=>a.multiple,f=>{f?n.value=r.value:n.value=r.value.length>0?r.value[0]:void 0}),ve(()=>n.value,f=>{if(Array.isArray(f)){const p=f.filter(h=>h!==void 0);p.length>0?r.value=a.multiple?[...p]:[p[0]]:r.value=[]}else r.value=f===void 0?[]:[f]},{deep:!0,immediate:!0});function u(f){return J(this,null,function*(){if(a.beforeChange&&wt(a.beforeChange))try{if(d.value.push(f),(yield a.beforeChange(f,!r.value.includes(f)))===!1)return}finally{d.value.splice(d.value.indexOf(f),1)}if(a.multiple)r.value.includes(f)?r.value=r.value.filter(p=>p!==f):(a.maxCount>0&&r.value.length>=a.maxCount&&(r.value=r.value.slice(0,a.maxCount-1)),r.value.push(f)),n.value=r.value;else if(a.allowClear&&r.value.includes(f)){r.value=[],n.value=void 0,l("btnClick",void 0);return}else r.value=[f],n.value=f;l("btnClick",f)})}return(f,p)=>(i(),y(ir,{size:a.size,gap:a.gap,class:"vben-check-button-group"},{default:c(()=>[(i(!0),C(Q,null,me(a.options,(h,m)=>(i(),y(Pe,he({key:m,class:e(le)("border",a.btnClass),disabled:a.disabled||d.value.includes(h.value)||!a.multiple&&d.value.length>0},{ref_for:!0},s.value,{variant:r.value.includes(h.value)?"default":"outline",onClick:v=>u(h.value),type:"button"}),{default:c(()=>[a.showIcon?(i(),C("div",dr,[B(f.$slots,"icon",{loading:d.value.includes(h.value),checked:r.value.includes(h.value)},()=>[d.value.includes(h.value)?(i(),y(e(Rl),{key:0,class:"animate-spin"})):r.value.includes(h.value)?(i(),y(e(fs),{key:1})):(i(),y(e(ms),{key:2}))],!0)])):z("",!0),B(f.$slots,"option",{label:h.label,value:h.value,data:h},()=>[b(e(ts),{content:h.label},null,8,["content"])],!0)]),_:2},1040,["class","disabled","variant","onClick"]))),128))]),_:3},8,["size","gap"]))}}),cr=ze(ur,[["__scopeId","data-v-9a0233de"]]),pr=o=>{const t=Wt(),a=Wt(),l=G(!1),s=()=>{var d;t.value&&(l.value=t.value.scrollTop>=((d=o==null?void 0:o.visibilityHeight)!=null?d:0))},n=()=>{var d;(d=t.value)==null||d.scrollTo({behavior:"smooth",top:0})},r=jt(s,300,!0);return Cn(a,"scroll",r),Qe(()=>{var d;if(a.value=document,t.value=document.documentElement,o.target){if(t.value=(d=document.querySelector(o.target))!=null?d:void 0,!t.value)throw new Error(`target does not exist: ${o.target}`);a.value=t.value}s()}),{handleClick:n,visible:l}},fr=V({name:"BackTop",__name:"back-top",props:{bottom:{default:20},isGroup:{type:Boolean,default:!1},right:{default:24},target:{default:""},visibilityHeight:{default:200}},setup(o){const t=o,a=w(()=>({bottom:`${t.bottom}px`,right:`${t.right}px`})),{handleClick:l,visible:s}=pr(t);return(n,r)=>(i(),y(it,{name:"fade-down"},{default:c(()=>[e(s)?(i(),y(e(Pe),{key:0,style:fe(a.value),class:"dark:bg-accent dark:hover:bg-heavy bg-background hover:bg-heavy data shadow-float z-popup fixed bottom-10 size-10 rounded-full duration-500",size:"icon",variant:"icon",onClick:e(l)},{default:c(()=>[b(e(us),{class:"size-4"})]),_:1},8,["style","onClick"])):z("",!0)]),_:1}))}}),mr={class:"flex"},hr=["onClick"],br={class:"flex-center z-10 h-full"},vr=V({name:"Breadcrumb",__name:"breadcrumb-background",props:{breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(o,{emit:t}){const a=t;function l(s,n){!n||s===o.breadcrumbs.length-1||a("select",n)}return(s,n)=>(i(),C("ul",mr,[b(Kt,{name:"breadcrumb-transition"},{default:c(()=>[(i(!0),C(Q,null,me(s.breadcrumbs,(r,d)=>(i(),C("li",{key:`${r.path}-${r.title}-${d}`},[E("a",{href:"javascript:void 0",onClick:Le(u=>l(d,r.path),["stop"])},[E("span",br,[s.showIcon?(i(),y(e(Ke),{key:0,icon:r.icon,class:"mr-1 size-4 flex-shrink-0"},null,8,["icon"])):z("",!0),E("span",{class:R({"text-foreground font-normal":d===s.breadcrumbs.length-1})},S(r.title),3)])],8,hr)]))),128))]),_:1})]))}}),gr=ze(vr,[["__scopeId","data-v-da1498bb"]]),yr={key:0},wr={class:"flex-center"},xr={class:"flex-center"},kr=V({name:"Breadcrumb",__name:"breadcrumb",props:{breadcrumbs:{},showIcon:{type:Boolean,default:!1},styleType:{}},emits:["select"],setup(o,{emit:t}){const a=t;function l(s){s&&a("select",s)}return(s,n)=>(i(),y(e(Vs),null,{default:c(()=>[b(e(zs),null,{default:c(()=>[b(Kt,{name:"breadcrumb-transition"},{default:c(()=>[(i(!0),C(Q,null,me(s.breadcrumbs,(r,d)=>(i(),y(e(Es),{key:`${r.path}-${r.title}-${d}`},{default:c(()=>{var u,f;return[(f=(u=r.items)==null?void 0:u.length)!=null&&f?(i(),C("div",yr,[b(e(ga),null,{default:c(()=>[b(e(ya),{class:"flex items-center gap-1"},{default:c(()=>[s.showIcon?(i(),y(e(Ke),{key:0,icon:r.icon,class:"size-5"},null,8,["icon"])):z("",!0),A(" "+S(r.title)+" ",1),b(e(ca),{class:"size-4"})]),_:2},1024),b(e(wa),{align:"start"},{default:c(()=>[(i(!0),C(Q,null,me(r.items,p=>(i(),y(e($t),{key:`sub-${p.path}`,onClick:Le(h=>l(p.path),["stop"])},{default:c(()=>[A(S(p.title),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)])):d!==s.breadcrumbs.length-1?(i(),y(e(Ls),{key:1,href:"javascript:void 0",onClick:Le(p=>l(r.path),["stop"])},{default:c(()=>[E("div",wr,[s.showIcon?(i(),y(e(Ke),{key:0,class:R([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):z("",!0),A(" "+S(r.title),1)])]),_:2},1032,["onClick"])):(i(),y(e(As),{key:2},{default:c(()=>[E("div",xr,[s.showIcon?(i(),y(e(Ke),{key:0,class:R([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):z("",!0),A(" "+S(r.title),1)])]),_:2},1024)),d<s.breadcrumbs.length-1&&!r.isHome?(i(),y(e(Ps),{key:3})):z("",!0)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}))}}),Cr=V({__name:"breadcrumb-view",props:{class:{},breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(o,{emit:t}){const s=Ue(o,t);return(n,r)=>(i(),C(Q,null,[n.styleType==="normal"?(i(),y(kr,he({key:0},e(s),{class:"vben-breadcrumb"}),null,16)):z("",!0),n.styleType==="background"?(i(),y(gr,he({key:1},e(s),{class:"vben-breadcrumb"}),null,16)):z("",!0)],64))}}),_r=ze(Cr,[["__scopeId","data-v-4cd036dd"]]),Sr=V({__name:"ContextMenu",props:{dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(o,{emit:t}){const s=Ue(o,t);return(n,r)=>(i(),y(e(Wl),Ne(Ze(e(s))),{default:c(()=>[B(n.$slots,"default")]),_:3},16))}}),Mr=V({__name:"ContextMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(o,{emit:t}){const a=o,l=t,s=w(()=>{const u=a,{class:r}=u;return Ce(u,["class"])}),n=Ue(s,l);return(r,d)=>(i(),y(e(Nl),null,{default:c(()=>[b(e(Fl),he(e(n),{class:e(le)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",a.class)}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Tr=V({__name:"ContextMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},emits:["select"],setup(o,{emit:t}){const a=o,l=t,s=w(()=>{const u=a,{class:r}=u;return Ce(u,["class"])}),n=Ue(s,l);return(r,d)=>(i(),y(e(Kl),he(e(n),{class:e(le)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",a.class)}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"]))}}),$r=V({__name:"ContextMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const n=t,{class:l}=n;return Ce(n,["class"])});return(l,s)=>(i(),y(e(jl),he(a.value,{class:e(le)("bg-border -mx-1 my-1 h-px",t.class)}),null,16,["class"]))}}),Br=V({__name:"ContextMenuShortcut",props:{class:{}},setup(o){const t=o;return(a,l)=>(i(),C("span",{class:R(e(le)("text-muted-foreground ml-auto text-xs tracking-widest",t.class))},[B(a.$slots,"default")],2))}}),Vr=V({__name:"ContextMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const a=nt(o);return(l,s)=>(i(),y(e(Gl),Ne(Ze(e(a))),{default:c(()=>[B(l.$slots,"default")]),_:3},16))}}),Ho=V({__name:"context-menu",props:{dir:{},modal:{type:Boolean},class:{},contentClass:{},contentProps:{},handlerData:{},itemClass:{},menus:{type:Function}},emits:["update:open"],setup(o,{emit:t}){const a=o,l=t,s=w(()=>{const v=a,{class:u,contentClass:f,contentProps:p,itemClass:h}=v;return Ce(v,["class","contentClass","contentProps","itemClass"])}),n=Ue(s,l),r=w(()=>{var u;return(u=a.menus)==null?void 0:u.call(a,a.handlerData)});function d(u){var f;u.disabled||(f=u==null?void 0:u.handler)==null||f.call(u,a.handlerData)}return(u,f)=>(i(),y(e(Sr),Ne(Ze(e(n))),{default:c(()=>[b(e(Vr),{"as-child":""},{default:c(()=>[B(u.$slots,"default")]),_:3}),b(e(Mr),he({class:u.contentClass},u.contentProps,{class:"side-content z-popup"}),{default:c(()=>[(i(!0),C(Q,null,me(r.value,p=>(i(),C(Q,{key:p.key},[b(e(Tr),{class:R([u.itemClass,"cursor-pointer"]),disabled:p.disabled,inset:p.inset||!p.icon,onClick:h=>d(p)},{default:c(()=>[p.icon?(i(),y(Ae(p.icon),{key:0,class:"mr-2 size-4 text-lg"})):z("",!0),A(" "+S(p.text)+" ",1),p.shortcut?(i(),y(e(Br),{key:1},{default:c(()=>[A(S(p.shortcut),1)]),_:2},1024)):z("",!0)]),_:2},1032,["class","disabled","inset","onClick"]),p.separator?(i(),y(e($r),{key:0})):z("",!0)],64))),128))]),_:1},16,["class"])]),_:3},16))}}),Er=V({name:"DropdownMenu",__name:"dropdown-menu",props:{menus:{}},setup(o){const t=o;function a(l){var s;l.disabled||(s=l==null?void 0:l.handler)==null||s.call(l,t)}return(l,s)=>(i(),y(e(ga),null,{default:c(()=>[b(e(ya),{class:"flex h-full items-center gap-1"},{default:c(()=>[B(l.$slots,"default")]),_:3}),b(e(wa),{align:"start"},{default:c(()=>[b(e(Kn),null,{default:c(()=>[(i(!0),C(Q,null,me(l.menus,n=>(i(),C(Q,{key:n.value},[b(e($t),{disabled:n.disabled,class:"data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer",onClick:r=>a(n)},{default:c(()=>[n.icon?(i(),y(Ae(n.icon),{key:0,class:"mr-2 size-4"})):z("",!0),A(" "+S(n.label),1)]),_:2},1032,["disabled","onClick"]),n.separator?(i(),y(e(Ht),{key:0,class:"bg-border"})):z("",!0)],64))),128))]),_:1})]),_:1})]),_:3}))}}),Lr=V({name:"FullScreen",__name:"full-screen",setup(o){const{isFullscreen:t,toggle:a}=_n();return t.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),(l,s)=>(i(),y(e(Je),{onClick:e(a)},{default:c(()=>[e(t)?(i(),y(e(Cs),{key:0,class:"text-foreground size-4"})):(i(),y(e(ks),{key:1,class:"text-foreground size-4"}))]),_:1},8,["onClick"]))}}),zr={class:"h-full cursor-pointer"},Ar=V({__name:"hover-card",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(o,{emit:t}){const a=o,l=t,s=w(()=>{const p=a,{class:r,contentClass:d,contentProps:u}=p;return Ce(p,["class","contentClass","contentProps"])}),n=Ue(s,l);return(r,d)=>(i(),y(e(Ns),Ne(Ze(e(n))),{default:c(()=>[b(e(Ks),{"as-child":"",class:"h-full"},{default:c(()=>[E("div",zr,[B(r.$slots,"trigger")])]),_:3}),b(e(Fs),he({class:r.contentClass},r.contentProps,{class:"side-content z-popup"}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"])]),_:3},16))}}),Pr=["href"],Ir={class:"text-foreground truncate text-nowrap font-semibold"},Ya=V({name:"VbenLogo",__name:"logo",props:{collapsed:{type:Boolean,default:!1},fit:{default:"cover"},href:{default:"javascript:void 0"},logoSize:{default:32},src:{default:""},text:{},theme:{default:"light"}},setup(o){return(t,a)=>(i(),C("div",{class:R([t.theme,"flex h-full items-center text-lg"])},[E("a",{class:R([t.$attrs.class,"flex h-full items-center gap-2 overflow-hidden px-3 text-lg leading-normal transition-all duration-500"]),href:t.href},[t.src?(i(),y(e(Et),{key:0,alt:t.text,src:t.src,size:t.logoSize,fit:t.fit,class:"relative rounded-none bg-transparent"},null,8,["alt","src","size","fit"])):z("",!0),t.collapsed?z("",!0):B(t.$slots,"text",{key:1},()=>[E("span",Ir,S(t.text),1)])],10,Pr)],2))}}),Xa=1,Or=V({__name:"scrollbar",props:{class:{default:""},horizontal:{type:Boolean,default:!1},scrollBarClass:{},shadow:{type:Boolean,default:!1},shadowBorder:{type:Boolean,default:!1},shadowBottom:{type:Boolean,default:!0},shadowLeft:{type:Boolean,default:!1},shadowRight:{type:Boolean,default:!1},shadowTop:{type:Boolean,default:!0}},emits:["scrollAt"],setup(o,{emit:t}){const a=o,l=t,s=G(!0),n=G(!1),r=G(!1),d=G(!0),u=w(()=>a.shadow&&a.shadowTop),f=w(()=>a.shadow&&a.shadowBottom),p=w(()=>a.shadow&&a.shadowLeft),h=w(()=>a.shadow&&a.shadowRight),m=w(()=>({"both-shadow":!d.value&&!n.value&&p.value&&h.value,"left-shadow":!d.value&&p.value,"right-shadow":!n.value&&h.value}));function v(k){var K,P,$,N,X,ee;const O=k.target,U=(K=O==null?void 0:O.scrollTop)!=null?K:0,_=(P=O==null?void 0:O.scrollLeft)!=null?P:0,W=($=O==null?void 0:O.clientHeight)!=null?$:0,F=(N=O==null?void 0:O.clientWidth)!=null?N:0,I=(X=O==null?void 0:O.scrollHeight)!=null?X:0,L=(ee=O==null?void 0:O.scrollWidth)!=null?ee:0;s.value=U<=0,d.value=_<=0,r.value=Math.abs(U)+W>=I-Xa,n.value=Math.abs(_)+F>=L-Xa,l("scrollAt",{bottom:r.value,left:d.value,right:n.value,top:s.value})}return(k,O)=>(i(),y(e(Js),{class:R([[e(le)(a.class),m.value],"vben-scrollbar relative"]),"on-scroll":v},{default:c(()=>[u.value?(i(),C("div",{key:0,class:R([{"opacity-100":!s.value,"border-border border-t":k.shadowBorder&&!s.value},"scrollbar-top-shadow pointer-events-none absolute top-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):z("",!0),B(k.$slots,"default",{},void 0,!0),f.value?(i(),C("div",{key:1,class:R([{"opacity-100":!s.value&&!r.value,"border-border border-b":k.shadowBorder&&!s.value&&!r.value},"scrollbar-bottom-shadow pointer-events-none absolute bottom-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):z("",!0),k.horizontal?(i(),y(e(Uo),{key:2,class:R(k.scrollBarClass),orientation:"horizontal"},null,8,["class"])):z("",!0)]),_:3},8,["class"]))}}),Lt=ze(Or,[["__scopeId","data-v-c94474ed"]]),Dr={class:"bg-background text-foreground inline-flex h-full w-full items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},Ur=V({__name:"tabs-indicator",props:{asChild:{type:Boolean},as:{},class:{}},setup(o){const t=o,a=w(()=>{const r=t,{class:s}=r;return Ce(r,["class"])}),l=nt(a);return(s,n)=>(i(),y(e(ql),he(e(l),{class:e(le)("absolute bottom-0 left-0 z-10 h-full w-1/2 translate-x-[--radix-tabs-indicator-position] rounded-full px-0 py-1 pr-1 transition-[width,transform] duration-300",t.class)}),{default:c(()=>[E("div",Dr,[B(s.$slots,"default")])]),_:3},16,["class"]))}}),Hr=V({__name:"segmented",props:_e({defaultValue:{default:""},tabs:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=o,a=x(o,"modelValue"),l=w(()=>{var r;return t.defaultValue||((r=t.tabs[0])==null?void 0:r.value)}),s=w(()=>({"grid-template-columns":`repeat(${t.tabs.length}, minmax(0, 1fr))`})),n=w(()=>({width:`${(100/t.tabs.length).toFixed(0)}%`}));return(r,d)=>(i(),y(e(es),{modelValue:a.value,"onUpdate:modelValue":d[0]||(d[0]=u=>a.value=u),"default-value":l.value},{default:c(()=>[b(e(Zn),{style:fe(s.value),class:"bg-accent relative grid w-full"},{default:c(()=>[b(Ur,{style:fe(n.value)},null,8,["style"]),(i(!0),C(Q,null,me(r.tabs,u=>(i(),y(e(Yl),{key:u.value,value:u.value,class:"z-20 inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"},{default:c(()=>[A(S(u.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["style"]),(i(!0),C(Q,null,me(r.tabs,u=>(i(),y(e(Qn),{key:u.value,value:u.value},{default:c(()=>[B(r.$slots,u.value)]),_:2},1032,["value"]))),128))]),_:3},8,["modelValue","default-value"]))}});function Ro(){const{contentIsMaximize:o}=ft();function t(){const a=o.value;rt({header:{hidden:!a},sidebar:{hidden:!a}})}return{contentIsMaximize:o,toggleMaximize:t}}const oa=500,la=0;function Rr(o,t=oa){const a=typeof t=="number"||wt(t)?{enterDelay:la,leaveDelay:t}:Y({enterDelay:la,leaveDelay:oa},t),l=G(!1),s=G(),n=G(),r=G([]),d=w(()=>{const _=e(o);return _===null?[]:Array.isArray(_)?_:[_]}),u=G([]);function f(){r.value.forEach(_=>_.stop()),r.value=[],u.value=d.value.map(_=>{if(!_)return G(!1);const W=w(()=>{const L=e(_);return L instanceof Element?L:L==null?void 0:L.$el}),F=Sn(),I=F.run(()=>Mn(W))||G(!1);return r.value.push(F),I})}const p=w(()=>{const _=e(o);return _===null?0:Array.isArray(_)?_.length:1});f();const h=ve(p,f,{deep:!1}),m=w(()=>u.value.every(_=>!_.value));function v(){s.value&&(clearTimeout(s.value),s.value=void 0),n.value&&(clearTimeout(n.value),n.value=void 0)}function k(_){var W,F;if(v(),_){const I=(W=a.enterDelay)!=null?W:la,L=wt(I)?I():I;L<=0?l.value=!0:s.value=setTimeout(()=>{l.value=!0,s.value=void 0},L)}else{const I=(F=a.leaveDelay)!=null?F:oa,L=wt(I)?I():I;L<=0?l.value=!1:n.value=setTimeout(()=>{l.value=!1,n.value=void 0},L)}}const O=ve(m,_=>{k(!_)},{immediate:!0}),U={enable(){O.resume()},disable(){O.pause()}};return At(()=>{v(),h(),r.value.forEach(_=>_.stop())}),[l,U]}function Wo(){const o=mt(),t=_t();function a(){return J(this,null,function*(){yield t.refresh(o)})}return{refresh:a}}function No(){const o=mt(),t=st(),a=_t();function l(_){return J(this,null,function*(){yield a.closeLeftTabs(_||t)})}function s(){return J(this,null,function*(){yield a.closeAllTabs(o)})}function n(_){return J(this,null,function*(){yield a.closeRightTabs(_||t)})}function r(_){return J(this,null,function*(){yield a.closeOtherTabs(_||t)})}function d(_){return J(this,null,function*(){yield a.closeTab(_||t,o)})}function u(_){return J(this,null,function*(){yield a.pinTab(_||t)})}function f(_){return J(this,null,function*(){yield a.unpinTab(_||t)})}function p(_){return J(this,null,function*(){yield a.toggleTabPin(_||t)})}function h(_){return J(this,null,function*(){yield a.refresh(_||o)})}function m(_){return J(this,null,function*(){yield a.openTabInNewWindow(_||t)})}function v(_){return J(this,null,function*(){yield a.closeTabByKey(_,o)})}function k(_){return J(this,null,function*(){a.setUpdateTime(),yield a.setTabTitle(t,_)})}function O(){return J(this,null,function*(){a.setUpdateTime(),yield a.resetTabTitle(t)})}function U(_=t){var ie;const W=a.getTabs,F=a.affixTabs,I=W.findIndex(ce=>ce.path===_.path),L=W.length<=1,{meta:K}=_,P=(ie=K==null?void 0:K.affixTab)!=null?ie:!1,$=t.path===_.path,N=I===0||I-F.length<=0||!$,X=!$||I===W.length-1,ee=L||!$||W.length-F.length<=1;return{disabledCloseAll:L,disabledCloseCurrent:!!P||L,disabledCloseLeft:N,disabledCloseOther:ee,disabledCloseRight:X,disabledRefresh:!$}}return{closeAllTabs:s,closeCurrentTab:d,closeLeftTabs:l,closeOtherTabs:r,closeRightTabs:n,closeTabByKey:v,getTabDisableState:U,openTabInNewWindow:m,pinTab:u,refreshTab:h,resetTabTitle:O,setTabTitle:k,toggleTabPin:p,unpinTab:f}}const Wr={class:"flex items-center"},Nr={class:"flex-center"},Fr=V({__name:"drawer",props:{drawerApi:{default:void 0},appendToMain:{type:Boolean,default:!1},cancelText:{},class:{},closable:{type:Boolean},closeIconPlacement:{default:"right"},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},footer:{type:Boolean},footerClass:{},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},placement:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean,default:!1},title:{},titleTooltip:{},zIndex:{default:1e3}},setup(o){var pe,Fe;const t=o,a=pa.getComponents(),l=wo();kt("DISMISSABLE_DRAWER_ID",l);const s=G(),{$t:n}=co(),{isMobile:r}=fa(),d=(Fe=(pe=t.drawerApi)==null?void 0:pe.useStore)==null?void 0:Fe.call(pe),{appendToMain:u,cancelText:f,class:p,closable:h,closeIconPlacement:m,closeOnClickModal:v,closeOnPressEscape:k,confirmLoading:O,confirmText:U,contentClass:_,description:W,destroyOnClose:F,footer:I,footerClass:L,header:K,headerClass:P,loading:$,modal:N,openAutoFocus:X,overlayBlur:ee,placement:ie,showCancelButton:ce,showConfirmButton:q,submitting:Z,title:ye,titleTooltip:Se,zIndex:Be}=po(t,d);xo(()=>{var ae;u.value||(ae=t.drawerApi)==null||ae.close()});function Oe(ae){(!v.value||Z.value)&&ae.preventDefault()}function $e(ae){(!k.value||Z.value)&&ae.preventDefault()}function j(ae){const ke=ae.target,H=ke==null?void 0:ke.dataset.dismissableDrawer;(Z.value||!v.value||H!==l)&&ae.preventDefault()}function re(ae){X.value||ae==null||ae.preventDefault()}function te(ae){ae.preventDefault(),ae.stopPropagation()}const we=w(()=>u.value?`#${ma}>div:not(.absolute)>div`:void 0),Me=G(!1),Ve=G(!0);ve(()=>{var ae;return(ae=d==null?void 0:d.value)==null?void 0:ae.isOpen},ae=>{Ve.value=!1,ae&&!e(Me)&&(Me.value=!0)});function ue(){var ae;Ve.value=!0,(ae=t.drawerApi)==null||ae.onClosed()}const be=w(()=>!e(F)&&e(Me));return(ae,ke)=>{var H;return i(),y(e(er),{modal:!1,open:(H=e(d))==null?void 0:H.isOpen,"onUpdate:open":ke[3]||(ke[3]=()=>{var oe;return(oe=ae.drawerApi)==null?void 0:oe.close()})},{default:c(()=>{var oe;return[b(e(or),{"append-to":we.value,class:R(e(le)("flex w-[520px] flex-col",e(p),{"!w-full":e(r)||e(ie)==="bottom"||e(ie)==="top","max-h-[100vh]":e(ie)==="bottom"||e(ie)==="top",hidden:Ve.value})),modal:e(N),open:(oe=e(d))==null?void 0:oe.isOpen,side:e(ie),"z-index":e(Be),"force-mount":be.value,"overlay-blur":e(ee),onCloseAutoFocus:te,onClosed:ue,onEscapeKeyDown:$e,onFocusOutside:te,onInteractOutside:Oe,onOpenAutoFocus:re,onOpened:ke[2]||(ke[2]=()=>{var de;return(de=ae.drawerApi)==null?void 0:de.onOpened()}),onPointerDownOutside:j},{default:c(()=>[e(K)?(i(),y(e(nr),{key:0,class:R(e(le)("!flex flex-row items-center justify-between border-b px-6 py-5",e(P),{"px-4 py-3":e(h),"pl-2":e(h)&&e(m)==="left"}))},{default:c(()=>[E("div",Wr,[e(h)&&e(m)==="left"?(i(),y(e(qa),{key:0,"as-child":"",disabled:e(Z),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:c(()=>[B(ae.$slots,"close-icon",{},()=>[b(e(Je),null,{default:c(()=>[b(e(pt),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):z("",!0),e(h)&&e(m)==="left"?(i(),y(e(Zs),{key:1,class:"ml-1 mr-2 h-8",decorative:"",orientation:"vertical"})):z("",!0),e(ye)?(i(),y(e(aa),{key:2,class:"text-left"},{default:c(()=>[B(ae.$slots,"title",{},()=>[A(S(e(ye))+" ",1),e(Se)?(i(),y(e(To),{key:0,"trigger-class":"pb-1"},{default:c(()=>[A(S(e(Se)),1)]),_:1})):z("",!0)])]),_:3})):z("",!0),e(W)?(i(),y(e(ta),{key:3,class:"mt-1 text-xs"},{default:c(()=>[B(ae.$slots,"description",{},()=>[A(S(e(W)),1)])]),_:3})):z("",!0)]),!e(ye)||!e(W)?(i(),y(e(sa),{key:0},{default:c(()=>[e(ye)?z("",!0):(i(),y(e(aa),{key:0})),e(W)?z("",!0):(i(),y(e(ta),{key:1}))]),_:1})):z("",!0),E("div",Nr,[B(ae.$slots,"extra"),e(h)&&e(m)==="right"?(i(),y(e(qa),{key:0,"as-child":"",disabled:e(Z),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:c(()=>[B(ae.$slots,"close-icon",{},()=>[b(e(Je),null,{default:c(()=>[b(e(pt),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):z("",!0)])]),_:3},8,["class"])):(i(),y(e(sa),{key:1},{default:c(()=>[b(e(aa)),b(e(ta))]),_:1})),E("div",{ref_key:"wrapperRef",ref:s,class:R(e(le)("relative flex-1 overflow-y-auto p-3",e(_),{"pointer-events-none":e($)||e(Z)}))},[B(ae.$slots,"default")],2),e($)||e(Z)?(i(),y(e(fo),{key:2,spinning:""})):z("",!0),e(I)?(i(),y(e(lr),{key:3,class:R(e(le)("w-full flex-row items-center justify-end border-t p-2 px-3",e(L)))},{default:c(()=>[B(ae.$slots,"prepend-footer"),B(ae.$slots,"footer",{},()=>[e(ce)?(i(),y(Ae(e(a).DefaultButton||e(Pe)),{key:0,variant:"ghost",disabled:e(Z),onClick:ke[0]||(ke[0]=()=>{var de;return(de=ae.drawerApi)==null?void 0:de.onCancel()})},{default:c(()=>[B(ae.$slots,"cancelText",{},()=>[A(S(e(f)||e(n)("cancel")),1)])]),_:3},8,["disabled"])):z("",!0),B(ae.$slots,"center-footer"),e(q)?(i(),y(Ae(e(a).PrimaryButton||e(Pe)),{key:1,loading:e(O)||e(Z),onClick:ke[1]||(ke[1]=()=>{var de;return(de=ae.drawerApi)==null?void 0:de.onConfirm()})},{default:c(()=>[B(ae.$slots,"confirmText",{},()=>[A(S(e(U)||e(n)("confirm")),1)])]),_:3},8,["loading"])):z("",!0)]),B(ae.$slots,"append-footer")]),_:3},8,["class"])):z("",!0)]),_:3},8,["append-to","class","modal","open","side","z-index","force-mount","overlay-blur"])]}),_:3},8,["open"])}}});class Kr{constructor(t={}){tt(this,"sharedData",{payload:{}});tt(this,"store");tt(this,"api");tt(this,"state");const h=t,{connectedComponent:a,onBeforeClose:l,onCancel:s,onClosed:n,onConfirm:r,onOpenChange:d,onOpened:u}=h,f=Ce(h,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),p={class:"",closable:!0,closeIconPlacement:"right",closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",footer:!0,header:!0,isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,placement:"right",showCancelButton:!0,showConfirmButton:!0,submitting:!1,title:""};this.store=new $o(Y(Y({},p),f),{onUpdate:()=>{var v,k,O;const m=this.store.state;(m==null?void 0:m.isOpen)===((v=this.state)==null?void 0:v.isOpen)?this.state=m:(this.state=m,(O=(k=this.api).onOpenChange)==null||O.call(k,!!(m!=null&&m.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:l,onCancel:s,onClosed:n,onConfirm:r,onOpenChange:d,onOpened:u},ko(this)}close(){return J(this,null,function*(){var a,l,s;((s=yield(l=(a=this.api).onBeforeClose)==null?void 0:l.call(a))!=null?s:!0)&&this.store.setState(n=>xe(Y({},n),{isOpen:!1,submitting:!1}))})}getData(){var t,a;return(a=(t=this.sharedData)==null?void 0:t.payload)!=null?a:{}}lock(t=!0){return this.setState({submitting:t})}onCancel(){var t,a;this.api.onCancel?(a=(t=this.api).onCancel)==null||a.call(t):this.close()}onClosed(){var t,a;this.state.isOpen||(a=(t=this.api).onClosed)==null||a.call(t)}onConfirm(){var t,a;(a=(t=this.api).onConfirm)==null||a.call(t)}onOpened(){var t,a;this.state.isOpen&&((a=(t=this.api).onOpened)==null||a.call(t))}open(){this.store.setState(t=>xe(Y({},t),{isOpen:!0}))}setData(t){return this.sharedData.payload=t,this}setState(t){return wt(t)?this.store.setState(t):this.store.setState(a=>Y(Y({},a),t)),this}unlock(){return this.lock(!1)}}const Ja=Symbol("VBEN_DRAWER_INJECT"),jr={};function Fo(o={}){var u;const{connectedComponent:t}=o;if(t){const f=ut({}),p=G(!0);return[V((m,{attrs:v,slots:k})=>(kt(Ja,{extendApi(U){Object.setPrototypeOf(f,U)},options:o,reCreateDrawer(){return J(this,null,function*(){p.value=!1,yield We(),p.value=!0})}}),Gr(f,Y(Y(Y({},m),v),k)),()=>Nt(p.value?t:"div",Y(Y({},m),v),k)),{name:"VbenParentDrawer",inheritAttrs:!1}),f]}const a=xt(Ja,{}),l=Y(Y(Y({},jr),a.options),o);l.onOpenChange=f=>{var p,h,m;(p=o.onOpenChange)==null||p.call(o,f),(m=(h=a.options)==null?void 0:h.onOpenChange)==null||m.call(h,f)};const s=l.onClosed;l.onClosed=()=>{var f;s==null||s(),l.destroyOnClose&&((f=a.reCreateDrawer)==null||f.call(a))};const n=new Kr(l),r=n;r.useStore=f=>Bo(n.store,f);const d=V((f,{attrs:p,slots:h})=>()=>Nt(Fr,xe(Y(Y({},f),p),{drawerApi:r}),h),{name:"VbenDrawer",inheritAttrs:!1});return(u=a.extendApi)==null||u.call(a,r),[d,r]}function Gr(o,t){return J(this,null,function*(){var s;if(!t||Object.keys(t).length===0)return;yield We();const a=(s=o==null?void 0:o.store)==null?void 0:s.state;if(!a)return;const l=new Set(Object.keys(a));for(const n of Object.keys(t))l.has(n)&&!["class"].includes(n)&&console.warn(`[Vben Drawer]: When 'connectedComponent' exists, do not set props or slots '${n}', which will increase complexity. If you need to modify the props of Drawer, please use useVbenDrawer or api.`)})}function qr(o,t,a,l){const s=ut({offsetX:0,offsetY:0}),n=G(!1),r=p=>{const h=p.clientX,m=p.clientY;if(!o.value)return;const v=o.value.getBoundingClientRect(),{offsetX:k,offsetY:O}=s,U=v.left,_=v.top,W=v.width,F=v.height;let I=null;if(l!=null&&l.value){const ee=document.querySelector(l.value);ee&&(I=ee.getBoundingClientRect())}let L,K,P,$;if(I)P=I.left-U+k,L=I.right-U-W+k,$=I.top-_+O,K=I.bottom-_-F+O;else{const ee=document.documentElement,ie=ee.clientWidth,ce=ee.clientHeight;P=-U+k,$=-_+O,L=ie-U-W+k,K=ce-_-F+O}const N=ee=>{let ie=k+ee.clientX-h,ce=O+ee.clientY-m;ie=Math.min(Math.max(ie,P),L),ce=Math.min(Math.max(ce,$),K),s.offsetX=ie,s.offsetY=ce,o.value&&(o.value.style.transform=`translate(${ie}px, ${ce}px)`,n.value=!0)},X=()=>{n.value=!1,document.removeEventListener("mousemove",N),document.removeEventListener("mouseup",X)};document.addEventListener("mousemove",N),document.addEventListener("mouseup",X)},d=()=>{const p=ea(t);p&&o.value&&p.addEventListener("mousedown",r)},u=()=>{const p=ea(t);p&&o.value&&p.removeEventListener("mousedown",r)},f=()=>{s.offsetX=0,s.offsetY=0;const p=ea(o);p&&(p.style.transform="none")};return Qe(()=>{ha(()=>{a.value?d():u()})}),ba(()=>{u()}),{dragging:n,resetPosition:f,transform:s}}const Yr=V({__name:"modal",props:{modalApi:{default:void 0},appendToMain:{type:Boolean,default:!1},bordered:{type:Boolean},cancelText:{},centered:{type:Boolean},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmDisabled:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},draggable:{type:Boolean},footer:{type:Boolean},footerClass:{},fullscreen:{type:Boolean},fullscreenButton:{type:Boolean},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean},title:{},titleTooltip:{},zIndex:{}},setup(o){var ct,Mt;const t=o,a=pa.getComponents(),l=G(),s=G(),n=G(),r=G(),d=G(),u=wo();kt("DISMISSABLE_MODAL_ID",u);const{$t:f}=co(),{isMobile:p}=fa(),h=(Mt=(ct=t.modalApi)==null?void 0:ct.useStore)==null?void 0:Mt.call(ct),{appendToMain:m,bordered:v,cancelText:k,centered:O,class:U,closable:_,closeOnClickModal:W,closeOnPressEscape:F,confirmDisabled:I,confirmLoading:L,confirmText:K,contentClass:P,description:$,destroyOnClose:N,draggable:X,footer:ee,footerClass:ie,fullscreen:ce,fullscreenButton:q,header:Z,headerClass:ye,loading:Se,modal:Be,openAutoFocus:Oe,overlayBlur:$e,showCancelButton:j,showConfirmButton:re,submitting:te,title:we,titleTooltip:Me,zIndex:Ve}=po(t,h),ue=w(()=>ce.value&&Z.value||p.value),be=w(()=>X.value&&!ue.value&&Z.value),pe=w(()=>m.value?`#${ma}>div:not(.absolute)>div`:void 0),{dragging:Fe,transform:ae}=qr(n,r,be,pe),ke=G(!1),H=G(!0);ve(()=>{var ne;return(ne=h==null?void 0:h.value)==null?void 0:ne.isOpen},ne=>J(null,null,function*(){if(ne){if(H.value=!1,ke.value||(ke.value=!0),yield We(),!l.value)return;const Te=l.value.getContentRef();n.value=Te.$el;const{offsetX:et,offsetY:qe}=ae;n.value.style.transform=`translate(${et}px, ${qe}px)`}}),{immediate:!0}),xo(()=>{var ne;m.value||(ne=t.modalApi)==null||ne.close()});function oe(){var ne;(ne=t.modalApi)==null||ne.setState(Te=>xe(Y({},Te),{fullscreen:!ce.value}))}function de(ne){(!W.value||te.value)&&(ne.preventDefault(),ne.stopPropagation())}function He(ne){(!F.value||te.value)&&ne.preventDefault()}function Re(ne){Oe.value||ne==null||ne.preventDefault()}function Xt(ne){const Te=ne.target,et=Te==null?void 0:Te.dataset.dismissableModal;(!W.value||et!==u||te.value)&&(ne.preventDefault(),ne.stopPropagation())}function St(ne){ne.preventDefault(),ne.stopPropagation()}const Pt=w(()=>!e(N)&&e(ke));function It(){var ne;H.value=!0,(ne=t.modalApi)==null||ne.onClosed()}return(ne,Te)=>{var et;return i(),y(e(Is),{modal:!1,open:(et=e(h))==null?void 0:et.isOpen,"onUpdate:open":Te[3]||(Te[3]=()=>{var qe;return e(te)||(qe=ne.modalApi)==null?void 0:qe.close()})},{default:c(()=>{var qe;return[b(e(Us),{ref_key:"contentRef",ref:l,"append-to":pe.value,class:R(e(le)("left-0 right-0 top-[10vh] mx-auto flex max-h-[80%] w-[520px] flex-col p-0",ue.value?"sm:rounded-none":"sm:rounded-[var(--radius)]",e(U),{"border-border border":e(v),"shadow-3xl":!e(v),"left-0 top-0 size-full max-h-full !translate-x-0 !translate-y-0":ue.value,"top-1/2 !-translate-y-1/2":e(O)&&!ue.value,"duration-300":!e(Fe),hidden:H.value})),"force-mount":Pt.value,modal:e(Be),open:(qe=e(h))==null?void 0:qe.isOpen,"show-close":e(_),"z-index":e(Ve),"overlay-blur":e($e),"close-class":"top-3",onCloseAutoFocus:St,onClosed:It,"close-disabled":e(te),onEscapeKeyDown:He,onFocusOutside:St,onInteractOutside:de,onOpenAutoFocus:Re,onOpened:Te[2]||(Te[2]=()=>{var je;return(je=ne.modalApi)==null?void 0:je.onOpened()}),onPointerDownOutside:Xt},{default:c(()=>[b(e(Rs),{ref_key:"headerRef",ref:r,class:R(e(le)("px-5 py-4",{"border-b":e(v),hidden:!e(Z),"cursor-move select-none":be.value},e(ye)))},{default:c(()=>[e(we)?(i(),y(e(ja),{key:0,class:"text-left"},{default:c(()=>[B(ne.$slots,"title",{},()=>[A(S(e(we))+" ",1),e(Me)?B(ne.$slots,"titleTooltip",{key:0},()=>[b(e(To),{"trigger-class":"pb-1"},{default:c(()=>[A(S(e(Me)),1)]),_:1})]):z("",!0)])]),_:3})):z("",!0),e($)?(i(),y(e(Ka),{key:1},{default:c(()=>[B(ne.$slots,"description",{},()=>[A(S(e($)),1)])]),_:3})):z("",!0),!e(we)||!e($)?(i(),y(e(sa),{key:2},{default:c(()=>[e(we)?z("",!0):(i(),y(e(ja),{key:0})),e($)?z("",!0):(i(),y(e(Ka),{key:1}))]),_:1})):z("",!0)]),_:3},8,["class"]),E("div",{ref_key:"wrapperRef",ref:s,class:R(e(le)("relative min-h-40 flex-1 overflow-y-auto p-3",e(P),{"pointer-events-none":e(Se)||e(te)}))},[B(ne.$slots,"default")],2),e(Se)||e(te)?(i(),y(e(fo),{key:0,spinning:""})):z("",!0),e(q)?(i(),y(e(Je),{key:1,class:"hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-10 top-3 hidden size-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none sm:block",onClick:oe},{default:c(()=>[e(ce)?(i(),y(e(Ms),{key:0,class:"size-3.5"})):(i(),y(e(vs),{key:1,class:"size-3.5"}))]),_:1})):z("",!0),e(ee)?(i(),y(e(Hs),{key:2,ref_key:"footerRef",ref:d,class:R(e(le)("flex-row items-center justify-end p-2",{"border-t":e(v)},e(ie)))},{default:c(()=>[B(ne.$slots,"prepend-footer"),B(ne.$slots,"footer",{},()=>[e(j)?(i(),y(Ae(e(a).DefaultButton||e(Pe)),{key:0,variant:"ghost",disabled:e(te),onClick:Te[0]||(Te[0]=()=>{var je;return(je=ne.modalApi)==null?void 0:je.onCancel()})},{default:c(()=>[B(ne.$slots,"cancelText",{},()=>[A(S(e(k)||e(f)("cancel")),1)])]),_:3},8,["disabled"])):z("",!0),B(ne.$slots,"center-footer"),e(re)?(i(),y(Ae(e(a).PrimaryButton||e(Pe)),{key:1,disabled:e(I),loading:e(L)||e(te),onClick:Te[1]||(Te[1]=()=>{var je;return(je=ne.modalApi)==null?void 0:je.onConfirm()})},{default:c(()=>[B(ne.$slots,"confirmText",{},()=>[A(S(e(K)||e(f)("confirm")),1)])]),_:3},8,["disabled","loading"])):z("",!0)]),B(ne.$slots,"append-footer")]),_:3},8,["class"])):z("",!0)]),_:3},8,["append-to","class","force-mount","modal","open","show-close","z-index","overlay-blur","close-disabled"])]}),_:3},8,["open"])}}});class Xr{constructor(t={}){tt(this,"sharedData",{payload:{}});tt(this,"store");tt(this,"api");tt(this,"state");const h=t,{connectedComponent:a,onBeforeClose:l,onCancel:s,onClosed:n,onConfirm:r,onOpenChange:d,onOpened:u}=h,f=Ce(h,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),p={bordered:!0,centered:!1,class:"",closeOnClickModal:!0,closeOnPressEscape:!0,confirmDisabled:!1,confirmLoading:!1,contentClass:"",destroyOnClose:!0,draggable:!1,footer:!0,footerClass:"",fullscreen:!1,fullscreenButton:!0,header:!0,headerClass:"",isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new $o(Y(Y({},p),f),{onUpdate:()=>{var v,k,O;const m=this.store.state;(m==null?void 0:m.isOpen)===((v=this.state)==null?void 0:v.isOpen)?this.state=m:(this.state=m,(O=(k=this.api).onOpenChange)==null||O.call(k,!!(m!=null&&m.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:l,onCancel:s,onClosed:n,onConfirm:r,onOpenChange:d,onOpened:u},ko(this)}close(){return J(this,null,function*(){var a,l,s;((s=yield(l=(a=this.api).onBeforeClose)==null?void 0:l.call(a))!=null?s:!0)&&this.store.setState(n=>xe(Y({},n),{isOpen:!1,submitting:!1}))})}getData(){var t,a;return(a=(t=this.sharedData)==null?void 0:t.payload)!=null?a:{}}lock(t=!0){return this.setState({submitting:t})}onCancel(){var t,a;this.api.onCancel?(a=(t=this.api).onCancel)==null||a.call(t):this.close()}onClosed(){var t,a;this.state.isOpen||(a=(t=this.api).onClosed)==null||a.call(t)}onConfirm(){var t,a;(a=(t=this.api).onConfirm)==null||a.call(t)}onOpened(){var t,a;this.state.isOpen&&((a=(t=this.api).onOpened)==null||a.call(t))}open(){this.store.setState(t=>xe(Y({},t),{isOpen:!0}))}setData(t){return this.sharedData.payload=t,this}setState(t){return wt(t)?this.store.setState(t):this.store.setState(a=>Y(Y({},a),t)),this}unlock(){return this.lock(!1)}}const Za=Symbol("VBEN_MODAL_INJECT"),Jr={};function zt(o={}){var u;const{connectedComponent:t}=o;if(t){const f=ut({}),p=G(!0);return[V((m,{attrs:v,slots:k})=>(kt(Za,{extendApi(U){Object.setPrototypeOf(f,U)},options:o,reCreateModal(){return J(this,null,function*(){p.value=!1,yield We(),p.value=!0})}}),Zr(f,Y(Y(Y({},m),v),k)),()=>Nt(p.value?t:"div",Y(Y({},m),v),k)),{name:"VbenParentModal",inheritAttrs:!1}),f]}const a=xt(Za,{}),l=Y(Y(Y({},Jr),a.options),o);l.onOpenChange=f=>{var p,h,m;(p=o.onOpenChange)==null||p.call(o,f),(m=(h=a.options)==null?void 0:h.onOpenChange)==null||m.call(h,f)};const s=l.onClosed;l.onClosed=()=>{var f;s==null||s(),l.destroyOnClose&&((f=a.reCreateModal)==null||f.call(a))};const n=new Xr(l),r=n;r.useStore=f=>Bo(n.store,f);const d=V((f,{attrs:p,slots:h})=>()=>Nt(Yr,xe(Y(Y({},f),p),{modalApi:r}),h),{name:"VbenModal",inheritAttrs:!1});return(u=a.extendApi)==null||u.call(a,r),[d,r]}function Zr(o,t){return J(this,null,function*(){var s;if(!t||Object.keys(t).length===0)return;yield We();const a=(s=o==null?void 0:o.store)==null?void 0:s.state;if(!a)return;const l=new Set(Object.keys(a));for(const n of Object.keys(t))l.has(n)&&!["class"].includes(n)&&console.warn(`[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${n}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`)})}const Qr=V({__name:"breadcrumb",props:{hideWhenOnlyOne:{type:Boolean},showHome:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1},type:{default:"normal"}},setup(o){const t=o,a=st(),l=mt(),s=w(()=>{const r=a.matched,d=[];for(const u of r){const{meta:f,path:p}=u,{hideChildrenInMenu:h,hideInBreadcrumb:m,icon:v,name:k,title:O}=f||{};m||h||!p||d.push({icon:v,path:p||a.path,title:O?g(O||k):""})}return t.showHome&&d.unshift({icon:"mdi:home-outline",isHome:!0,path:"/"}),t.hideWhenOnlyOne&&d.length===1?[]:d});function n(r){l.push(r)}return(r,d)=>(i(),y(e(_r),{breadcrumbs:s.value,"show-icon":r.showIcon,"style-type":r.type,class:"ml-2",onSelect:n},null,8,["breadcrumbs","show-icon","style-type"]))}}),ei=V({name:"CheckUpdates",__name:"check-updates",props:{checkUpdatesInterval:{default:1},checkUpdateUrl:{default:"/"}},setup(o){const t=o;let a=!1;const l=G(""),s=G(""),n=G(),[r,d]=zt({closable:!1,closeOnPressEscape:!1,closeOnClickModal:!1,onConfirm(){s.value=l.value,window.location.reload()}});function u(){return J(this,null,function*(){try{if(location.hostname==="localhost"||location.hostname==="127.0.0.1")return null;const k=yield fetch(t.checkUpdateUrl,{cache:"no-cache",method:"HEAD",redirect:"manual"});return k.headers.get("etag")||k.headers.get("last-modified")}catch(k){return console.error("Failed to fetch version tag"),null}})}function f(){return J(this,null,function*(){const k=yield u();if(k){if(!s.value){s.value=k;return}s.value!==k&&k&&(clearInterval(n.value),p(k))}})}function p(k){l.value=k,d.open()}function h(){t.checkUpdatesInterval<=0||(n.value=setInterval(f,t.checkUpdatesInterval*60*1e3))}function m(){document.hidden?v():a||(a=!0,f().finally(()=>{a=!1,h()}))}function v(){clearInterval(n.value)}return Qe(()=>{h(),document.addEventListener("visibilitychange",m)}),At(()=>{v(),document.removeEventListener("visibilitychange",m)}),(k,O)=>(i(),y(e(r),{"cancel-text":e(g)("common.cancel"),"confirm-text":e(g)("common.refresh"),"fullscreen-button":!1,title:e(g)("ui.widgets.checkUpdatesTitle"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[A(S(e(g)("ui.widgets.checkUpdatesDescription")),1)]),_:1},8,["cancel-text","confirm-text","title"]))}}),ti={class:"!flex h-full justify-center px-2 sm:max-h-[450px]"},ai={key:0,class:"text-muted-foreground text-center"},oi={class:"mb-10 mt-6 text-xs"},li={class:"text-foreground text-sm font-medium"},ni={key:1,class:"text-muted-foreground text-center"},si={class:"my-10 text-xs"},ri={class:"w-full"},ii={key:0,class:"text-muted-foreground mb-2 text-xs"},di=["data-index","data-search-item"],ui={class:"flex-1"},ci=["onClick"],pi=V({name:"SearchPanel",__name:"search-panel",props:{keyword:{default:""},menus:{default:()=>[]}},emits:["close"],setup(o,{emit:t}){const a=o,l=t,s=mt(),n=Tn(`__search-history-${location.hostname}__`,[]),r=G(-1),d=Wt([]),u=G([]),f=jt(p,200);function p(L){if(L=L.trim(),!L){u.value=[];return}const K=I(L),P=[];Bn(d.value,$=>{var N;K.test((N=$.name)==null?void 0:N.toLowerCase())&&P.push($)}),u.value=P,P.length>0&&(r.value=0),r.value=0}function h(){const L=document.querySelector(`[data-search-item="${r.value}"]`);L&&L.scrollIntoView({block:"nearest"})}function m(){return J(this,null,function*(){if(u.value.length===0)return;const L=u.value,K=r.value;if(L.length===0||K<0)return;const P=L[K];P&&(n.value.push(P),O(),yield We(),ra(P.path)?window.open(P.path,"_blank"):s.push({path:P.path,replace:!0}))})}function v(){u.value.length!==0&&(r.value--,r.value<0&&(r.value=u.value.length-1),h())}function k(){u.value.length!==0&&(r.value++,r.value>u.value.length-1&&(r.value=0),h())}function O(){u.value=[],l("close")}function U(L){var P;const K=(P=L.target)==null?void 0:P.dataset.index;r.value=Number(K)}function _(L){a.keyword?u.value.splice(L,1):n.value.splice(L,1),r.value=Math.max(r.value-1,0),h()}const W=new Set(["$","(",")","*","+",".","?","[","\\","]","^","{","|","}"]);function F(L){return W.has(L)?`\\${L}`:L}function I(L){const K=[...L].map(P=>F(P)).join(".*");return new RegExp(`.*${K}.*`)}return ve(()=>a.keyword,L=>{L?f(L):u.value=[...n.value]}),Qe(()=>{d.value=Co(a.menus,L=>xe(Y({},L),{name:g(L==null?void 0:L.name)})),n.value.length>0&&(u.value=n.value),Dt("Enter",m),Dt("ArrowUp",v),Dt("ArrowDown",k),Dt("Escape",O)}),(L,K)=>(i(),y(e(Lt),null,{default:c(()=>[E("div",ti,[L.keyword&&u.value.length===0?(i(),C("div",ai,[b(e(Ss),{class:"mx-auto mt-4 size-12"}),E("p",oi,[A(S(e(g)("ui.widgets.search.noResults"))+" ",1),E("span",li,' "'+S(L.keyword)+'" ',1)])])):z("",!0),!L.keyword&&u.value.length===0?(i(),C("div",ni,[E("p",si,S(e(g)("ui.widgets.search.noRecent")),1)])):z("",!0),Ie(E("ul",ri,[e(n).length>0&&!L.keyword?(i(),C("li",ii,S(e(g)("ui.widgets.search.recent")),1)):z("",!0),(i(!0),C(Q,null,me(e($n)(u.value,"path"),(P,$)=>(i(),C("li",{key:P.path,class:R([r.value===$?"active bg-primary text-primary-foreground":"","bg-accent flex-center group mb-3 w-full cursor-pointer rounded-lg px-4 py-4"]),"data-index":$,"data-search-item":$,onClick:m,onMouseenter:U},[b(e(Ke),{icon:P.icon,class:"mr-2 size-5 flex-shrink-0",fallback:""},null,8,["icon"]),E("span",ui,S(P.name),1),E("div",{class:"flex-center dark:hover:bg-accent hover:text-primary-foreground rounded-full p-1 hover:scale-110",onClick:Le(N=>_($),["stop"])},[b(e(pt),{class:"size-4"})],8,ci)],42,di))),128))],512),[[De,u.value.length>0]])])]),_:1}))}}),fi={class:"flex items-center"},mi=["placeholder"],hi={class:"flex w-full justify-start text-xs"},bi={class:"mr-2 flex items-center"},vi={class:"mr-2 flex items-center"},gi={class:"flex items-center"},yi={class:"text-muted-foreground group-hover:text-foreground hidden text-xs duration-300 md:block"},wi={key:0,class:"bg-background border-foreground/60 text-muted-foreground group-hover:text-foreground relative hidden rounded-sm rounded-r-xl px-1.5 py-1 text-xs leading-none group-hover:opacity-100 md:block"},xi={key:1},ki=V({name:"GlobalSearch",__name:"global-search",props:{enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]}},setup(o){const t=o,a=G(""),l=G(),[s,n]=zt({onCancel(){n.close()},onOpenChange(v){v||(a.value="")}}),r=n.useStore(v=>v.isOpen);function d(){n.close(),a.value=""}const u=_o(),f=Vt()?u["ctrl+k"]:u["cmd+k"];Ft(f,()=>{t.enableShortcutKey&&n.open()}),Ft(r,()=>{We(()=>{var v;(v=l.value)==null||v.focus()})});const p=v=>{var k;((k=v.key)==null?void 0:k.toLowerCase())==="k"&&(v.metaKey||v.ctrlKey)&&v.preventDefault()},h=()=>{t.enableShortcutKey?window.addEventListener("keydown",p):window.removeEventListener("keydown",p)},m=()=>{r.value?n.close():n.open()};return ve(()=>t.enableShortcutKey,h),Qe(()=>{h(),At(()=>{window.removeEventListener("keydown",p)})}),(v,k)=>(i(),C("div",null,[b(e(s),{"fullscreen-button":!1,class:"w-[600px]","header-class":"py-2 border-b"},{title:c(()=>[E("div",fi,[b(e(Fa),{class:"text-muted-foreground mr-2 size-4"}),Ie(E("input",{ref_key:"searchInputRef",ref:l,"onUpdate:modelValue":k[0]||(k[0]=O=>a.value=O),placeholder:e(g)("ui.widgets.search.searchNavigate"),class:"ring-none placeholder:text-muted-foreground w-[80%] rounded-md border border-none bg-transparent p-2 pl-0 text-sm font-normal outline-none ring-0 ring-offset-transparent focus-visible:ring-transparent"},null,8,mi),[[Xl,a.value]])])]),footer:c(()=>[E("div",hi,[E("div",bi,[b(e(bs),{class:"mr-1 size-3"}),A(" "+S(e(g)("ui.widgets.search.select")),1)]),E("div",vi,[b(e(cs),{class:"mr-1 size-3"}),b(e(ss),{class:"mr-1 size-3"}),A(" "+S(e(g)("ui.widgets.search.navigate")),1)]),E("div",gi,[b(e(as),{class:"mr-1 size-3"}),A(" "+S(e(g)("ui.widgets.search.close")),1)])])]),default:c(()=>[b(pi,{keyword:a.value,menus:v.menus,onClose:d},null,8,["keyword","menus"])]),_:1}),E("div",{class:"md:bg-accent group flex h-8 cursor-pointer items-center gap-3 rounded-2xl border-none bg-none px-2 py-0.5 outline-none",onClick:k[1]||(k[1]=O=>m())},[b(e(Fa),{class:"text-muted-foreground group-hover:text-foreground size-4 group-hover:opacity-100"}),E("span",yi,S(e(g)("ui.widgets.search.title")),1),v.enableShortcutKey?(i(),C("span",wi,[A(S(e(Vt)()?"Ctrl":"⌘")+" ",1),k[2]||(k[2]=E("kbd",null,"K",-1))])):(i(),C("span",xi))])]))}}),Ci=["onKeydown"],_i={class:"w-full"},Si={class:"ml-2 flex w-full flex-col items-center"},Mi={class:"text-foreground my-6 flex items-center font-medium"},Ti=V({name:"LockScreenModal",__name:"lock-screen-modal",props:{avatar:{default:""},text:{default:""}},emits:["submit"],setup(o,{emit:t}){const a=t,[l,{resetForm:s,validate:n,getValues:r}]=Vo(ut({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:g("ui.widgets.lockScreen.placeholder")},fieldName:"lockScreenPassword",formFieldProps:{validateOnBlur:!1},label:g("authentication.password"),rules:Eo().min(1,{message:g("ui.widgets.lockScreen.placeholder")})}]),showDefaultActions:!1})),[d]=zt({onConfirm(){u()},onOpenChange(f){f&&s()}});function u(){return J(this,null,function*(){const{valid:f}=yield n(),p=yield r();f&&a("submit",p==null?void 0:p.lockScreenPassword)})}return(f,p)=>(i(),y(e(d),{footer:!1,"fullscreen-button":!1,title:e(g)("ui.widgets.lockScreen.title")},{default:c(()=>[E("div",{class:"mb-10 flex w-full flex-col items-center px-10",onKeydown:mo(Le(u,["prevent"]),["enter"])},[E("div",_i,[E("div",Si,[b(e(Et),{src:f.avatar,class:"size-20","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["src"]),E("div",Mi,S(f.text),1)]),b(e(l)),b(e(Pe),{class:"mt-1 w-full",onClick:u},{default:c(()=>[A(S(e(g)("ui.widgets.lockScreen.screenButton")),1)]),_:1})])],40,Ci)]),_:1},8,["title"]))}}),$i={class:"bg-background fixed z-[2000] size-full"},Bi={class:"size-full"},Vi={class:"flex h-full w-full items-center justify-center"},Ei={class:"flex w-full justify-center gap-4 px-4 sm:gap-6 md:gap-8"},Li={class:"bg-accent relative flex h-[140px] w-[140px] items-center justify-center rounded-xl text-[36px] sm:h-[160px] sm:w-[160px] sm:text-[42px] md:h-[200px] md:w-[200px] md:text-[72px]"},zi={class:"absolute left-3 top-3 text-xs font-semibold sm:text-sm md:text-xl"},Ai={class:"bg-accent flex h-[140px] w-[140px] items-center justify-center rounded-xl text-[36px] sm:h-[160px] sm:w-[160px] sm:text-[42px] md:h-[200px] md:w-[200px] md:text-[72px]"},Pi=["onKeydown"],Ii={class:"flex-col-center mb-10 w-[90%] max-w-[300px] px-4"},Oi={class:"enter-x mb-2 w-full items-center"},Di={class:"enter-y absolute bottom-5 w-full text-center text-xl md:text-2xl xl:text-xl 2xl:text-3xl"},Ui={key:0,class:"enter-x mb-2 text-2xl md:text-3xl"},Hi={class:"text-base md:text-lg"},Ri={class:"text-xl md:text-3xl"},Xc=V({name:"LockScreen",__name:"lock-screen",props:{avatar:{default:""}},emits:["toLogin"],setup(o){const{locale:t}=ho(),a=ht(),l=Vn(),s=Ut(l,"A"),n=Ut(l,"HH"),r=Ut(l,"mm"),d=Ut(l,"YYYY-MM-DD dddd",{locales:t.value}),u=G(!1),{lockScreenPassword:f}=bo(a),[p,{form:h,validate:m}]=Vo(ut({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:g("ui.widgets.lockScreen.placeholder")},fieldName:"password",label:g("authentication.password"),rules:Eo().min(1,{message:g("authentication.passwordTip")})}]),showDefaultActions:!1})),v=w(()=>{var U;return(f==null?void 0:f.value)===((U=h==null?void 0:h.values)==null?void 0:U.password)});function k(){return J(this,null,function*(){const{valid:U}=yield m();U&&(v.value?a.unlockScreen():h.setFieldError("password",g("authentication.passwordErrorTip")))})}function O(){u.value=!u.value}return ua(),(U,_)=>(i(),C("div",$i,[b(it,{name:"slide-left"},{default:c(()=>[Ie(E("div",Bi,[E("div",{class:"flex-col-center text-foreground/80 hover:text-foreground group fixed left-1/2 top-6 z-[2001] -translate-x-1/2 cursor-pointer text-xl font-semibold",onClick:O},[b(e(Po),{class:"size-5 transition-all duration-300 group-hover:scale-125"}),E("span",null,S(e(g)("ui.widgets.lockScreen.unlock")),1)]),E("div",Vi,[E("div",Ei,[E("div",Li,[E("span",zi,S(e(s)),1),A(" "+S(e(n)),1)]),E("div",Ai,S(e(r)),1)])])],512),[[De,!u.value]])]),_:1}),b(it,{name:"slide-right"},{default:c(()=>[u.value?(i(),C("div",{key:0,class:"flex-center size-full",onKeydown:mo(Le(k,["prevent"]),["enter"])},[E("div",Ii,[b(e(Et),{src:U.avatar,class:"enter-x mb-6 size-20"},null,8,["src"]),E("div",Oi,[b(e(p))]),b(e(Pe),{class:"enter-x w-full",onClick:k},{default:c(()=>[A(S(e(g)("ui.widgets.lockScreen.entry")),1)]),_:1}),b(e(Pe),{class:"enter-x my-2 w-full",variant:"ghost",onClick:_[0]||(_[0]=W=>U.$emit("toLogin"))},{default:c(()=>[A(S(e(g)("ui.widgets.lockScreen.backToLogin")),1)]),_:1}),b(e(Pe),{class:"enter-x mr-2 w-full",variant:"ghost",onClick:O},{default:c(()=>[A(S(e(g)("common.back")),1)]),_:1})])],40,Pi)):z("",!0)]),_:1}),E("div",Di,[u.value?(i(),C("div",Ui,[A(S(e(n))+":"+S(e(r))+" ",1),E("span",Hi,S(e(s)),1)])):z("",!0),E("div",Ri,S(e(d)),1)])]))}}),Wi={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Ni={class:"relative"},Fi={class:"flex items-center justify-between p-4 py-3"},Ki={class:"text-foreground"},ji={class:"!flex max-h-[360px] w-full flex-col"},Gi=["onClick"],qi={key:0,class:"bg-primary absolute right-2 top-2 h-2 w-2 rounded"},Yi={class:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full"},Xi=["src"],Ji={class:"flex flex-col gap-1 leading-none"},Zi={class:"font-semibold"},Qi={class:"text-muted-foreground my-1 line-clamp-2 text-xs"},ed={class:"text-muted-foreground line-clamp-2 text-xs"},td={key:1,class:"flex-center text-muted-foreground min-h-[150px] w-full"},ad={class:"border-border flex items-center justify-between border-t px-4 py-3"},od=V({name:"NotificationPopup",__name:"notification",props:{dot:{type:Boolean,default:!1},notifications:{default:()=>[]}},emits:["clear","makeAll","read","viewAll"],setup(o,{emit:t}){const a=t,[l,s]=En();function n(){l.value=!1}function r(){a("viewAll"),n()}function d(){a("makeAll")}function u(){a("clear")}function f(p){a("read",p)}return(p,h)=>(i(),y(e(Jl),{open:e(l),"onUpdate:open":h[1]||(h[1]=m=>va(l)?l.value=m:null),"content-class":"relative right-2 w-[360px] p-0"},{trigger:c(()=>[E("div",{class:"flex-center mr-2 h-full",onClick:h[0]||(h[0]=Le(m=>e(s)(),["stop"]))},[b(e(Je),{class:"bell-button text-foreground relative"},{default:c(()=>[p.dot?(i(),C("span",Wi)):z("",!0),b(e(ps),{class:"size-4"})]),_:1})])]),default:c(()=>[E("div",Ni,[E("div",Fi,[E("div",Ki,S(e(g)("ui.widgets.notifications")),1),b(e(Je),{disabled:p.notifications.length<=0,tooltip:e(g)("ui.widgets.markAllAsRead"),onClick:d},{default:c(()=>[b(e(xs),{class:"size-4"})]),_:1},8,["disabled","tooltip"])]),p.notifications.length>0?(i(),y(e(Lt),{key:0},{default:c(()=>[E("ul",ji,[(i(!0),C(Q,null,me(p.notifications,m=>(i(),C("li",{key:m.title,class:"hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3",onClick:v=>f(m)},[m.isRead?z("",!0):(i(),C("span",qi)),E("span",Yi,[E("img",{src:m.avatar,class:"aspect-square h-full w-full object-cover",role:"img"},null,8,Xi)]),E("div",Ji,[E("p",Zi,S(m.title),1),E("p",Qi,S(m.message),1),E("p",ed,S(m.date),1)])],8,Gi))),128))])]),_:1})):(i(),C("div",td,S(e(g)("common.noData")),1)),E("div",ad,[b(e(Pe),{disabled:p.notifications.length<=0,size:"sm",variant:"ghost",onClick:u},{default:c(()=>[A(S(e(g)("ui.widgets.clearNotifications")),1)]),_:1},8,["disabled"]),b(e(Pe),{size:"sm",onClick:r},{default:c(()=>[A(S(e(g)("ui.widgets.viewAll")),1)]),_:1})])])]),_:1},8,["open"]))}}),Jc=ze(od,[["__scopeId","data-v-d7a4acd4"]]),ld={class:"flex flex-col py-4"},nd={class:"mb-3 font-semibold leading-none tracking-tight"},Ee=V({name:"PreferenceBlock",__name:"block",props:{title:{default:""}},setup(o){return(t,a)=>(i(),C("div",ld,[E("h3",nd,S(t.title),1),B(t.$slots,"default")]))}}),sd={class:"flex items-center text-sm"},rd={key:0,class:"ml-auto mr-2 text-xs opacity-60"},se=V({name:"PreferenceSwitchItem",__name:"switch-item",props:_e({disabled:{type:Boolean,default:!1},tip:{default:""}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=x(o,"modelValue"),a=Ge();function l(){t.value=!t.value}return(s,n)=>(i(),C("div",{class:R([{"pointer-events-none opacity-50":s.disabled},"hover:bg-accent my-1 flex w-full items-center justify-between rounded-md px-2 py-2.5"]),onClick:l},[E("span",sd,[B(s.$slots,"default"),e(a).tip||s.tip?(i(),y(e(bt),{key:0,side:"bottom"},{trigger:c(()=>[b(e(Ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(s.$slots,"tip",{},()=>[s.tip?(i(!0),C(Q,{key:0},me(s.tip.split(`
`),(r,d)=>(i(),C("p",{key:d},S(r),1))),128)):z("",!0)])]),_:3})):z("",!0)]),s.$slots.shortcut?(i(),C("span",rd,[B(s.$slots,"shortcut")])):z("",!0),b(e(sr),{checked:t.value,"onUpdate:checked":n[0]||(n[0]=r=>t.value=r),onClick:n[1]||(n[1]=Le(()=>{},["stop"]))},null,8,["checked"])],2))}}),id={key:0,class:"mb-2 mt-3 flex justify-between gap-3 px-2"},dd=["onClick"],ud=V({name:"PreferenceAnimation",__name:"animation",props:{transitionProgress:{type:Boolean,default:!1},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{}},emits:["update:transitionProgress","update:transitionName","update:transitionEnable","update:transitionLoading"],setup(o){const t=x(o,"transitionProgress"),a=x(o,"transitionName"),l=x(o,"transitionEnable"),s=x(o,"transitionLoading"),n=["fade","fade-slide","fade-up","fade-down"];function r(d){a.value=d}return(d,u)=>(i(),C(Q,null,[b(se,{modelValue:t.value,"onUpdate:modelValue":u[0]||(u[0]=f=>t.value=f)},{default:c(()=>[A(S(e(g)("preferences.animation.progress")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:s.value,"onUpdate:modelValue":u[1]||(u[1]=f=>s.value=f)},{default:c(()=>[A(S(e(g)("preferences.animation.loading")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:l.value,"onUpdate:modelValue":u[2]||(u[2]=f=>l.value=f)},{default:c(()=>[A(S(e(g)("preferences.animation.transition")),1)]),_:1},8,["modelValue"]),l.value?(i(),C("div",id,[(i(),C(Q,null,me(n,f=>E("div",{key:f,class:R([{"outline-box-active":a.value===f},"outline-box p-2"]),onClick:p=>r(f)},[E("div",{class:R([`${f}-slow`,"bg-accent h-10 w-12 rounded-md"])},null,2)],10,dd)),64))])):z("",!0)],64))}}),cd={class:"flex items-center text-sm"},Yt=V({name:"PreferenceSelectItem",__name:"select-item",props:_e({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=x(o,"modelValue"),a=Ge();return(l,s)=>(i(),C("div",{class:R([{"hover:bg-accent":!e(a).tip,"pointer-events-none opacity-50":l.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[E("span",cd,[B(l.$slots,"default"),e(a).tip?(i(),y(e(bt),{key:0,side:"bottom"},{trigger:c(()=>[b(e(Ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(l.$slots,"tip")]),_:3})):z("",!0)]),b(e(Zl),{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n)},{default:c(()=>[b(e(Ql),{class:"h-8 w-[165px]"},{default:c(()=>[b(e(en),{placeholder:l.placeholder},null,8,["placeholder"])]),_:1}),b(e(tn),null,{default:c(()=>[(i(!0),C(Q,null,me(l.items,n=>(i(),y(e(an),{key:n.value,value:n.value},{default:c(()=>[A(S(n.label),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])],2))}}),pd=V({name:"PreferenceGeneralConfig",__name:"general",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{}},emits:["update:appLocale","update:appDynamicTitle","update:appWatermark","update:appEnableCheckUpdates"],setup(o){const t=x(o,"appLocale"),a=x(o,"appDynamicTitle"),l=x(o,"appWatermark"),s=x(o,"appEnableCheckUpdates");return(n,r)=>(i(),C(Q,null,[b(Yt,{modelValue:t.value,"onUpdate:modelValue":r[0]||(r[0]=d=>t.value=d),items:e(on)},{default:c(()=>[A(S(e(g)("preferences.language")),1)]),_:1},8,["modelValue","items"]),b(se,{modelValue:a.value,"onUpdate:modelValue":r[1]||(r[1]=d=>a.value=d)},{default:c(()=>[A(S(e(g)("preferences.dynamicTitle")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:l.value,"onUpdate:modelValue":r[2]||(r[2]=d=>l.value=d)},{default:c(()=>[A(S(e(g)("preferences.watermark")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:s.value,"onUpdate:modelValue":r[3]||(r[3]=d=>s.value=d)},{default:c(()=>[A(S(e(g)("preferences.checkUpdates")),1)]),_:1},8,["modelValue"])],64))}}),fd={class:"text-sm"},Ca=V({name:"PreferenceToggleItem",__name:"toggle-item",props:_e({disabled:{type:Boolean,default:!1},items:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=x(o,"modelValue");return(a,l)=>(i(),C("div",{class:R([{"pointer-events-none opacity-50":a.disabled},"hover:bg-accent flex w-full items-center justify-between rounded-md px-2 py-2"]),disabled:""},[E("span",fd,[B(a.$slots,"default")]),b(e(Lo),{modelValue:t.value,"onUpdate:modelValue":l[0]||(l[0]=s=>t.value=s),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(i(!0),C(Q,null,me(a.items,s=>(i(),y(e(zo),{key:s.value,value:s.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 rounded-sm"},{default:c(()=>[A(S(s.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])],2))}}),md=V({name:"PreferenceBreadcrumbConfig",__name:"breadcrumb",props:_e({disabled:{type:Boolean}},{breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{}}),emits:["update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbStyleType","update:breadcrumbShowHome","update:breadcrumbHideOnlyOne"],setup(o){const t=o,a=x(o,"breadcrumbEnable"),l=x(o,"breadcrumbShowIcon"),s=x(o,"breadcrumbStyleType"),n=x(o,"breadcrumbShowHome"),r=x(o,"breadcrumbHideOnlyOne"),d=[{label:g("preferences.normal"),value:"normal"},{label:g("preferences.breadcrumb.background"),value:"background"}],u=w(()=>!a.value||t.disabled);return(f,p)=>(i(),C(Q,null,[b(se,{modelValue:a.value,"onUpdate:modelValue":p[0]||(p[0]=h=>a.value=h),disabled:f.disabled},{default:c(()=>[A(S(e(g)("preferences.breadcrumb.enable")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:r.value,"onUpdate:modelValue":p[1]||(p[1]=h=>r.value=h),disabled:u.value},{default:c(()=>[A(S(e(g)("preferences.breadcrumb.hideOnlyOne")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:l.value,"onUpdate:modelValue":p[2]||(p[2]=h=>l.value=h),disabled:u.value},{default:c(()=>[A(S(e(g)("preferences.breadcrumb.icon")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:n.value,"onUpdate:modelValue":p[3]||(p[3]=h=>n.value=h),disabled:u.value||!l.value},{default:c(()=>[A(S(e(g)("preferences.breadcrumb.home")),1)]),_:1},8,["modelValue","disabled"]),b(Ca,{modelValue:s.value,"onUpdate:modelValue":p[4]||(p[4]=h=>s.value=h),disabled:u.value,items:d},{default:c(()=>[A(S(e(g)("preferences.breadcrumb.style")),1)]),_:1},8,["modelValue","disabled"])],64))}}),hd={},bd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function vd(o,t){return i(),C("svg",bd,t[0]||(t[0]=[vt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="53.60438" x="43.484" y="13.66705"></rect><path id="svg_14" d="m3.43932,15.53192c0,-1.08676 1.03344,-2 2.26323,-2l30.33036,0c1.22979,0 2.26323,0.91324 2.26323,2l0,17.24865c0,1.08676 -1.03344,2 -2.26323,2l-30.33036,0c-1.22979,0 -2.26323,-0.91324 -2.26323,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="95.02528" x="3.30419" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const Ko=ze(hd,[["render",vd]]),gd={},yd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function wd(o,t){return i(),C("svg",yd,t[0]||(t[0]=[vt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="41.98275" x="45.37589" y="13.53192"></rect><path id="svg_14" d="m16.4123,15.53192c0,-1.08676 0.74096,-2 1.62271,-2l21.74653,0c0.88175,0 1.62271,0.91324 1.62271,2l0,17.24865c0,1.08676 -0.74096,2 -1.62271,2l-21.74653,0c-0.88175,0 -1.62271,-0.91324 -1.62271,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="71.10636" x="16.54743" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const xd=ze(gd,[["render",wd]]),kd={},Cd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function _d(o,t){return i(),C("svg",Cd,t[0]||(t[0]=[E("g",null,[E("path",{id:"svg_1",d:"m0.13514,4.13514c0,-2.17352 1.82648,-4 4,-4l96,0c2.17352,0 4,1.82648 4,4l0,58c0,2.17352 -1.82648,4 -4,4l-96,0c-2.17352,0 -4,-1.82648 -4,-4l0,-58z",fill:"currentColor","fill-opacity":"0.02",opacity:"undefined",stroke:"null"}),E("rect",{id:"svg_13",fill:"currentColor","fill-opacity":"0.08",height:"26.57155",rx:"2",stroke:"null",width:"53.18333",x:"45.79979",y:"3.77232"}),E("path",{id:"svg_14",d:"m4.28142,5.96169c0,-1.37748 1.06465,-2.53502 2.33158,-2.53502l31.2463,0c1.26693,0 2.33158,1.15754 2.33158,2.53502l0,21.86282c0,1.37748 -1.06465,2.53502 -2.33158,2.53502l-31.2463,0c-1.26693,0 -2.33158,-1.15754 -2.33158,-2.53502l0,-21.86282z",fill:"currentColor","fill-opacity":"0.08",opacity:"undefined",stroke:"null"}),E("rect",{id:"svg_15",fill:"currentColor","fill-opacity":"0.08",height:"25.02247",rx:"2",stroke:"null",width:"94.39371",x:"4.56735",y:"34.92584"})],-1)]))}const Sd=ze(kd,[["render",_d]]),Md={},Td={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function $d(o,t){return i(),C("svg",Td,t[0]||(t[0]=[vt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="35.14924" y="4.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="47.25735" y="4.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="59.23033" y="4.07319"></rect></g>',1)]))}const Bd=ze(Md,[["render",$d]]),Vd={},Ed={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Ld(o,t){return i(),C("svg",Ed,t[0]||(t[0]=[vt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#b2b2b2" height="1.689" rx="1.395" stroke="null" width="6.52486" x="10.08168" y="3.50832"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="2.89362"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="2.89362"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="5.13843" rx="2" stroke="null" width="5.78397" x="1.5327" y="1.081"></rect><rect id="svg_5" fill="hsl(var(--primary))" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path></g>',1)]))}const zd=ze(Vd,[["render",Ld]]),Ad={},Pd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Id(o,t){return i(),C("svg",Pd,t[0]||(t[0]=[vt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect><rect id="svg_5" fill="currentColor" fill-opacity="0.08" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path></g>',1)]))}const Od=ze(Ad,[["render",Id]]),Dd={},Ud={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Hd(o,t){return i(),C("svg",Ud,t[0]||(t[0]=[vt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect></g>',1)]))}const Rd=ze(Dd,[["render",Hd]]),Wd={},Nd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Fd(o,t){return i(),C("svg",Nd,t[0]||(t[0]=[vt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104"></rect><path id="svg_2" d="m-3.37838,3.61916a4.4919,4.02457 0 0 1 4.4919,-4.02457l26.35848,0l0,66.40541l-26.35848,0a4.4919,4.02457 0 0 1 -4.4919,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" width="17.66" x="4.906" y="23.884"></rect><rect id="svg_4" fill="#ffffff" height="9.706" rx="2" width="9.811" x="8.83" y="5.881"></rect><path id="svg_5" d="m4.906,35.833c0,-0.75801 0.63699,-1.395 1.395,-1.395l14.87,0c0.75801,0 1.395,0.63699 1.395,1.395l0,-0.001c0,0.75801 -0.63699,1.395 -1.395,1.395l-14.87,0c-0.75801,0 -1.395,-0.63699 -1.395,-1.395l0,0.001z" fill="#ffffff" opacity="undefined"></path><rect id="svg_6" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="44.992"></rect><rect id="svg_7" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="55.546"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="73.53879" x="28.97986" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="32.039" y="3.89903"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="3.49362"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="45.63141" x="56.05157" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="22.82978" x="29.38527" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="72.45771" x="28.97986" y="39.48203"></rect></g>',1)]))}const Kd=ze(Wd,[["render",Fd]]),jd=Ko,Gd={class:"flex w-full gap-5"},qd=["onClick"],Yd={class:"text-muted-foreground mt-2 text-center text-xs"},Xd=V({name:"PreferenceLayoutContent",__name:"content",props:{modelValue:{default:"wide"},modelModifiers:{}},emits:["update:modelValue"],setup(o){const t=x(o,"modelValue"),a={compact:xd,wide:jd},l=w(()=>[{name:g("preferences.wide"),type:"wide"},{name:g("preferences.compact"),type:"compact"}]);function s(n){return n===t.value?["outline-box-active"]:[]}return(n,r)=>(i(),C("div",Gd,[(i(!0),C(Q,null,me(l.value,d=>(i(),C("div",{key:d.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:u=>t.value=d.type},[E("div",{class:R([s(d.type),"outline-box flex-center"])},[(i(),y(Ae(a[d.type])))],2),E("div",Yd,S(d.name),1)],8,qd))),128))]))}}),Jd={class:"flex items-center text-sm"},Tt=V({name:"PreferenceSelectItem",__name:"input-item",props:_e({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=x(o,"modelValue"),a=Ge();return(l,s)=>(i(),C("div",{class:R([{"hover:bg-accent":!e(a).tip,"pointer-events-none opacity-50":l.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[E("span",Jd,[B(l.$slots,"default"),e(a).tip?(i(),y(e(bt),{key:0,side:"bottom"},{trigger:c(()=>[b(e(Ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(l.$slots,"tip")]),_:3})):z("",!0)]),b(e(ln),{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n),class:"h-8 w-[165px]"},null,8,["modelValue"])],2))}}),Zd=V({__name:"copyright",props:_e({disabled:{type:Boolean}},{copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{}}),emits:["update:copyrightEnable","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:copyrightCompanyName","update:copyrightCompanySiteLink"],setup(o){const t=o,a=x(o,"copyrightEnable"),l=x(o,"copyrightDate"),s=x(o,"copyrightIcp"),n=x(o,"copyrightIcpLink"),r=x(o,"copyrightCompanyName"),d=x(o,"copyrightCompanySiteLink"),u=w(()=>t.disabled||!a.value);return(f,p)=>(i(),C(Q,null,[b(se,{modelValue:a.value,"onUpdate:modelValue":p[0]||(p[0]=h=>a.value=h),disabled:f.disabled},{default:c(()=>[A(S(e(g)("preferences.copyright.enable")),1)]),_:1},8,["modelValue","disabled"]),b(Tt,{modelValue:r.value,"onUpdate:modelValue":p[1]||(p[1]=h=>r.value=h),disabled:u.value},{default:c(()=>[A(S(e(g)("preferences.copyright.companyName")),1)]),_:1},8,["modelValue","disabled"]),b(Tt,{modelValue:d.value,"onUpdate:modelValue":p[2]||(p[2]=h=>d.value=h),disabled:u.value},{default:c(()=>[A(S(e(g)("preferences.copyright.companySiteLink")),1)]),_:1},8,["modelValue","disabled"]),b(Tt,{modelValue:l.value,"onUpdate:modelValue":p[3]||(p[3]=h=>l.value=h),disabled:u.value},{default:c(()=>[A(S(e(g)("preferences.copyright.date")),1)]),_:1},8,["modelValue","disabled"]),b(Tt,{modelValue:s.value,"onUpdate:modelValue":p[4]||(p[4]=h=>s.value=h),disabled:u.value},{default:c(()=>[A(S(e(g)("preferences.copyright.icp")),1)]),_:1},8,["modelValue","disabled"]),b(Tt,{modelValue:n.value,"onUpdate:modelValue":p[5]||(p[5]=h=>n.value=h),disabled:u.value},{default:c(()=>[A(S(e(g)("preferences.copyright.icpLink")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Qd=V({__name:"footer",props:{footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{}},emits:["update:footerEnable","update:footerFixed"],setup(o){const t=x(o,"footerEnable"),a=x(o,"footerFixed");return(l,s)=>(i(),C(Q,null,[b(se,{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n)},{default:c(()=>[A(S(e(g)("preferences.footer.visible")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:a.value,"onUpdate:modelValue":s[1]||(s[1]=n=>a.value=n),disabled:!t.value},{default:c(()=>[A(S(e(g)("preferences.footer.fixed")),1)]),_:1},8,["modelValue","disabled"])],64))}}),eu=V({__name:"header",props:_e({disabled:{type:Boolean}},{headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{}}),emits:["update:headerEnable","update:headerMode","update:headerMenuAlign"],setup(o){const t=x(o,"headerEnable"),a=x(o,"headerMode"),l=x(o,"headerMenuAlign"),s=[{label:g("preferences.header.modeStatic"),value:"static"},{label:g("preferences.header.modeFixed"),value:"fixed"},{label:g("preferences.header.modeAuto"),value:"auto"},{label:g("preferences.header.modeAutoScroll"),value:"auto-scroll"}],n=[{label:g("preferences.header.menuAlignStart"),value:"start"},{label:g("preferences.header.menuAlignCenter"),value:"center"},{label:g("preferences.header.menuAlignEnd"),value:"end"}];return(r,d)=>(i(),C(Q,null,[b(se,{modelValue:t.value,"onUpdate:modelValue":d[0]||(d[0]=u=>t.value=u),disabled:r.disabled},{default:c(()=>[A(S(e(g)("preferences.header.visible")),1)]),_:1},8,["modelValue","disabled"]),b(Yt,{modelValue:a.value,"onUpdate:modelValue":d[1]||(d[1]=u=>a.value=u),disabled:!t.value,items:s},{default:c(()=>[A(S(e(g)("preferences.mode")),1)]),_:1},8,["modelValue","disabled"]),b(Ca,{modelValue:l.value,"onUpdate:modelValue":d[2]||(d[2]=u=>l.value=u),disabled:!t.value,items:n},{default:c(()=>[A(S(e(g)("preferences.header.menuAlign")),1)]),_:1},8,["modelValue","disabled"])],64))}}),tu={class:"flex w-full flex-wrap gap-5"},au=["onClick"],ou={class:"text-muted-foreground flex-center hover:text-foreground mt-2 text-center text-xs"},lu=V({name:"PreferenceLayout",__name:"layout",props:{modelValue:{default:"sidebar-nav"},modelModifiers:{}},emits:["update:modelValue"],setup(o){const t=x(o,"modelValue"),a={"full-content":Sd,"header-nav":Ko,"mixed-nav":Od,"sidebar-mixed-nav":Rd,"sidebar-nav":Kd,"header-mixed-nav":Bd,"header-sidebar-nav":zd},l=w(()=>[{name:g("preferences.vertical"),tip:g("preferences.verticalTip"),type:"sidebar-nav"},{name:g("preferences.twoColumn"),tip:g("preferences.twoColumnTip"),type:"sidebar-mixed-nav"},{name:g("preferences.horizontal"),tip:g("preferences.horizontalTip"),type:"header-nav"},{name:g("preferences.headerSidebarNav"),tip:g("preferences.headerSidebarNavTip"),type:"header-sidebar-nav"},{name:g("preferences.mixedMenu"),tip:g("preferences.mixedMenuTip"),type:"mixed-nav"},{name:g("preferences.headerTwoColumn"),tip:g("preferences.headerTwoColumnTip"),type:"header-mixed-nav"},{name:g("preferences.fullContent"),tip:g("preferences.fullContentTip"),type:"full-content"}]);function s(n){return n===t.value?["outline-box-active"]:[]}return(n,r)=>(i(),C("div",tu,[(i(!0),C(Q,null,me(l.value,d=>(i(),C("div",{key:d.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:u=>t.value=d.type},[E("div",{class:R([s(d.type),"outline-box flex-center"])},[(i(),y(Ae(a[d.type])))],2),E("div",ou,[A(S(d.name)+" ",1),d.tip?(i(),y(e(bt),{key:0,side:"bottom"},{trigger:c(()=>[b(e(Ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[A(" "+S(d.tip),1)]),_:2},1024)):z("",!0)])],8,au))),128))]))}}),nu=V({name:"PreferenceNavigationConfig",__name:"navigation",props:_e({disabled:{type:Boolean},disabledNavigationSplit:{type:Boolean}},{navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{}}),emits:["update:navigationStyleType","update:navigationSplit","update:navigationAccordion"],setup(o){const t=x(o,"navigationStyleType"),a=x(o,"navigationSplit"),l=x(o,"navigationAccordion"),s=[{label:g("preferences.rounded"),value:"rounded"},{label:g("preferences.plain"),value:"plain"}];return(n,r)=>(i(),C(Q,null,[b(Ca,{modelValue:t.value,"onUpdate:modelValue":r[0]||(r[0]=d=>t.value=d),disabled:n.disabled,items:s},{default:c(()=>[A(S(e(g)("preferences.navigationMenu.style")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:a.value,"onUpdate:modelValue":r[1]||(r[1]=d=>a.value=d),disabled:n.disabledNavigationSplit||n.disabled},{tip:c(()=>[A(S(e(g)("preferences.navigationMenu.splitTip")),1)]),default:c(()=>[A(S(e(g)("preferences.navigationMenu.split"))+" ",1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:l.value,"onUpdate:modelValue":r[2]||(r[2]=d=>l.value=d),disabled:n.disabled},{default:c(()=>[A(S(e(g)("preferences.navigationMenu.accordion")),1)]),_:1},8,["modelValue","disabled"])],64))}}),su={class:"flex items-center text-sm"},ru=V({name:"PreferenceCheckboxItem",__name:"checkbox-item",props:_e({disabled:{type:Boolean,default:!1},items:{default:()=>[]},multiple:{type:Boolean,default:!1},onBtnClick:{type:Function,default:()=>{}},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=x(o,"modelValue"),a=Ge();return(l,s)=>(i(),C("div",{class:R([{"hover:bg-accent":!e(a).tip,"pointer-events-none opacity-50":l.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[E("span",su,[B(l.$slots,"default"),e(a).tip?(i(),y(e(bt),{key:0,side:"bottom"},{trigger:c(()=>[b(e(Ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(l.$slots,"tip")]),_:3})):z("",!0)]),b(e(cr),{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n),class:"h-8 w-[165px]",options:l.items,disabled:l.disabled,multiple:l.multiple,onBtnClick:l.onBtnClick},null,8,["modelValue","options","disabled","multiple","onBtnClick"])],2))}}),iu={class:"flex items-center text-sm"},jo=V({name:"PreferenceSelectItem",__name:"number-field-item",props:_e({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""},tip:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=x(o,"modelValue"),a=Ge();return(l,s)=>(i(),C("div",{class:R([{"hover:bg-accent":!e(a).tip,"pointer-events-none opacity-50":l.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[E("span",iu,[B(l.$slots,"default"),e(a).tip||l.tip?(i(),y(e(bt),{key:0,side:"bottom"},{trigger:c(()=>[b(e(Ct),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(l.$slots,"tip",{},()=>[l.tip?(i(!0),C(Q,{key:0},me(l.tip.split(`
`),(n,r)=>(i(),C("p",{key:r},S(n),1))),128)):z("",!0)])]),_:3})):z("",!0)]),b(e(js),he({modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n)},l.$attrs,{class:"w-[165px]"}),{default:c(()=>[b(e(Gs),null,{default:c(()=>[b(e(qs)),b(e(Xs)),b(e(Ys))]),_:1})]),_:1},16,["modelValue"])],2))}}),du=V({__name:"sidebar",props:_e({currentLayout:{},disabled:{type:Boolean}},{sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarButtons:{default:[]},sidebarButtonsModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{}}),emits:["update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarCollapsed","update:sidebarExpandOnHover","update:sidebarButtons","update:sidebarCollapsedButton","update:sidebarFixedButton"],setup(o){const t=x(o,"sidebarEnable"),a=x(o,"sidebarWidth"),l=x(o,"sidebarCollapsedShowTitle"),s=x(o,"sidebarAutoActivateChild"),n=x(o,"sidebarCollapsed"),r=x(o,"sidebarExpandOnHover"),d=x(o,"sidebarButtons"),u=x(o,"sidebarCollapsedButton"),f=x(o,"sidebarFixedButton");Qe(()=>{u.value&&!d.value.includes("collapsed")&&d.value.push("collapsed"),f.value&&!d.value.includes("fixed")&&d.value.push("fixed")});const p=()=>{u.value=!!d.value.includes("collapsed"),f.value=!!d.value.includes("fixed")};return(h,m)=>(i(),C(Q,null,[b(se,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=v=>t.value=v),disabled:h.disabled},{default:c(()=>[A(S(e(g)("preferences.sidebar.visible")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:n.value,"onUpdate:modelValue":m[1]||(m[1]=v=>n.value=v),disabled:!t.value||h.disabled},{default:c(()=>[A(S(e(g)("preferences.sidebar.collapsed")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:r.value,"onUpdate:modelValue":m[2]||(m[2]=v=>r.value=v),disabled:!t.value||h.disabled||!n.value,tip:e(g)("preferences.sidebar.expandOnHoverTip")},{default:c(()=>[A(S(e(g)("preferences.sidebar.expandOnHover")),1)]),_:1},8,["modelValue","disabled","tip"]),b(se,{modelValue:l.value,"onUpdate:modelValue":m[3]||(m[3]=v=>l.value=v),disabled:!t.value||h.disabled||!n.value},{default:c(()=>[A(S(e(g)("preferences.sidebar.collapsedShowTitle")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:s.value,"onUpdate:modelValue":m[4]||(m[4]=v=>s.value=v),disabled:!t.value||!["sidebar-mixed-nav","mixed-nav","header-mixed-nav"].includes(h.currentLayout)||h.disabled,tip:e(g)("preferences.sidebar.autoActivateChildTip")},{default:c(()=>[A(S(e(g)("preferences.sidebar.autoActivateChild")),1)]),_:1},8,["modelValue","disabled","tip"]),b(ru,{items:[{label:e(g)("preferences.sidebar.buttonCollapsed"),value:"collapsed"},{label:e(g)("preferences.sidebar.buttonFixed"),value:"fixed"}],multiple:"",modelValue:d.value,"onUpdate:modelValue":m[5]||(m[5]=v=>d.value=v),"on-btn-click":p},{default:c(()=>[A(S(e(g)("preferences.sidebar.buttons")),1)]),_:1},8,["items","modelValue"]),b(jo,{modelValue:a.value,"onUpdate:modelValue":m[6]||(m[6]=v=>a.value=v),disabled:!t.value||h.disabled,max:320,min:160,step:10},{default:c(()=>[A(S(e(g)("preferences.sidebar.width")),1)]),_:1},8,["modelValue","disabled"])],64))}}),uu=V({name:"PreferenceTabsConfig",__name:"tabbar",props:_e({disabled:{type:Boolean}},{tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{}}),emits:["update:tabbarEnable","update:tabbarShowIcon","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarMaxCount","update:tabbarMiddleClickToClose"],setup(o){const t=x(o,"tabbarEnable"),a=x(o,"tabbarShowIcon"),l=x(o,"tabbarPersist"),s=x(o,"tabbarDraggable"),n=x(o,"tabbarWheelable"),r=x(o,"tabbarStyleType"),d=x(o,"tabbarShowMore"),u=x(o,"tabbarShowMaximize"),f=x(o,"tabbarMaxCount"),p=x(o,"tabbarMiddleClickToClose"),h=w(()=>[{label:g("preferences.tabbar.styleType.chrome"),value:"chrome"},{label:g("preferences.tabbar.styleType.plain"),value:"plain"},{label:g("preferences.tabbar.styleType.card"),value:"card"},{label:g("preferences.tabbar.styleType.brisk"),value:"brisk"}]);return(m,v)=>(i(),C(Q,null,[b(se,{modelValue:t.value,"onUpdate:modelValue":v[0]||(v[0]=k=>t.value=k),disabled:m.disabled},{default:c(()=>[A(S(e(g)("preferences.tabbar.enable")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:l.value,"onUpdate:modelValue":v[1]||(v[1]=k=>l.value=k),disabled:!t.value},{default:c(()=>[A(S(e(g)("preferences.tabbar.persist")),1)]),_:1},8,["modelValue","disabled"]),b(jo,{modelValue:f.value,"onUpdate:modelValue":v[2]||(v[2]=k=>f.value=k),disabled:!t.value,max:30,min:0,step:5,tip:e(g)("preferences.tabbar.maxCountTip")},{default:c(()=>[A(S(e(g)("preferences.tabbar.maxCount")),1)]),_:1},8,["modelValue","disabled","tip"]),b(se,{modelValue:s.value,"onUpdate:modelValue":v[3]||(v[3]=k=>s.value=k),disabled:!t.value},{default:c(()=>[A(S(e(g)("preferences.tabbar.draggable")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:n.value,"onUpdate:modelValue":v[4]||(v[4]=k=>n.value=k),disabled:!t.value,tip:e(g)("preferences.tabbar.wheelableTip")},{default:c(()=>[A(S(e(g)("preferences.tabbar.wheelable")),1)]),_:1},8,["modelValue","disabled","tip"]),b(se,{modelValue:p.value,"onUpdate:modelValue":v[5]||(v[5]=k=>p.value=k),disabled:!t.value},{default:c(()=>[A(S(e(g)("preferences.tabbar.middleClickClose")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:a.value,"onUpdate:modelValue":v[6]||(v[6]=k=>a.value=k),disabled:!t.value},{default:c(()=>[A(S(e(g)("preferences.tabbar.icon")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:d.value,"onUpdate:modelValue":v[7]||(v[7]=k=>d.value=k),disabled:!t.value},{default:c(()=>[A(S(e(g)("preferences.tabbar.showMore")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:u.value,"onUpdate:modelValue":v[8]||(v[8]=k=>u.value=k),disabled:!t.value},{default:c(()=>[A(S(e(g)("preferences.tabbar.showMaximize")),1)]),_:1},8,["modelValue","disabled"]),b(Yt,{modelValue:r.value,"onUpdate:modelValue":v[9]||(v[9]=k=>r.value=k),items:h.value},{default:c(()=>[A(S(e(g)("preferences.tabbar.styleType.title")),1)]),_:1},8,["modelValue","items"])],64))}}),cu=V({name:"PreferenceInterfaceControl",__name:"widget",props:{widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:["update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:appPreferencesButtonPosition","update:widgetRefresh"],setup(o){const t=x(o,"widgetGlobalSearch"),a=x(o,"widgetFullscreen"),l=x(o,"widgetLanguageToggle"),s=x(o,"widgetNotification"),n=x(o,"widgetThemeToggle"),r=x(o,"widgetSidebarToggle"),d=x(o,"widgetLockScreen"),u=x(o,"appPreferencesButtonPosition"),f=x(o,"widgetRefresh"),p=w(()=>[{label:g("preferences.position.auto"),value:"auto"},{label:g("preferences.position.header"),value:"header"},{label:g("preferences.position.fixed"),value:"fixed"}]);return(h,m)=>(i(),C(Q,null,[b(se,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=v=>t.value=v)},{default:c(()=>[A(S(e(g)("preferences.widget.globalSearch")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:n.value,"onUpdate:modelValue":m[1]||(m[1]=v=>n.value=v)},{default:c(()=>[A(S(e(g)("preferences.widget.themeToggle")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:l.value,"onUpdate:modelValue":m[2]||(m[2]=v=>l.value=v)},{default:c(()=>[A(S(e(g)("preferences.widget.languageToggle")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:a.value,"onUpdate:modelValue":m[3]||(m[3]=v=>a.value=v)},{default:c(()=>[A(S(e(g)("preferences.widget.fullscreen")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:s.value,"onUpdate:modelValue":m[4]||(m[4]=v=>s.value=v)},{default:c(()=>[A(S(e(g)("preferences.widget.notification")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:d.value,"onUpdate:modelValue":m[5]||(m[5]=v=>d.value=v)},{default:c(()=>[A(S(e(g)("preferences.widget.lockScreen")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:r.value,"onUpdate:modelValue":m[6]||(m[6]=v=>r.value=v)},{default:c(()=>[A(S(e(g)("preferences.widget.sidebarToggle")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:f.value,"onUpdate:modelValue":m[7]||(m[7]=v=>f.value=v)},{default:c(()=>[A(S(e(g)("preferences.widget.refresh")),1)]),_:1},8,["modelValue"]),b(Yt,{modelValue:u.value,"onUpdate:modelValue":m[8]||(m[8]=v=>u.value=v),items:p.value},{default:c(()=>[A(S(e(g)("preferences.position.title")),1)]),_:1},8,["modelValue","items"])],64))}}),pu=V({name:"PreferenceGeneralConfig",__name:"global",props:{shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysLogout:{type:Boolean},shortcutKeysLogoutModifiers:{},shortcutKeysLockScreen:{type:Boolean},shortcutKeysLockScreenModifiers:{}},emits:["update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysLogout","update:shortcutKeysLockScreen"],setup(o){const t=x(o,"shortcutKeysEnable"),a=x(o,"shortcutKeysGlobalSearch"),l=x(o,"shortcutKeysLogout"),s=x(o,"shortcutKeysLockScreen"),n=w(()=>Vt()?"Alt":"⌥");return(r,d)=>(i(),C(Q,null,[b(se,{modelValue:t.value,"onUpdate:modelValue":d[0]||(d[0]=u=>t.value=u)},{default:c(()=>[A(S(e(g)("preferences.shortcutKeys.title")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:a.value,"onUpdate:modelValue":d[1]||(d[1]=u=>a.value=u),disabled:!t.value},{shortcut:c(()=>[A(S(e(Vt)()?"Ctrl":"⌘")+" ",1),d[4]||(d[4]=E("kbd",null," K ",-1))]),default:c(()=>[A(S(e(g)("preferences.shortcutKeys.search"))+" ",1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:l.value,"onUpdate:modelValue":d[2]||(d[2]=u=>l.value=u),disabled:!t.value},{shortcut:c(()=>[A(S(n.value)+" Q ",1)]),default:c(()=>[A(S(e(g)("preferences.shortcutKeys.logout"))+" ",1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:s.value,"onUpdate:modelValue":d[3]||(d[3]=u=>s.value=u),disabled:!t.value},{shortcut:c(()=>[A(S(n.value)+" L ",1)]),default:c(()=>[A(S(e(g)("ui.widgets.lockScreen.title"))+" ",1)]),_:1},8,["modelValue","disabled"])],64))}}),fu={class:"flex w-full flex-wrap justify-between"},mu=["onClick"],hu={class:"flex-center relative size-5 rounded-sm"},bu=["value"],vu={class:"text-muted-foreground my-2 text-center text-xs"},gu=V({name:"PreferenceBuiltinTheme",__name:"builtin",props:_e({isDark:{type:Boolean}},{modelValue:{default:"default"},modelModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{}}),emits:["update:modelValue","update:themeColorPrimary"],setup(o){const t=o,a=G(),l=x(o,"modelValue"),s=x(o,"themeColorPrimary"),n=jt(m=>{s.value=m},300,!0,!0),r=w(()=>new Ln(s.value||"").toHexString()),d=w(()=>[...zn]);function u(m){switch(m){case"custom":return g("preferences.theme.builtin.custom");case"deep-blue":return g("preferences.theme.builtin.deepBlue");case"deep-green":return g("preferences.theme.builtin.deepGreen");case"default":return g("preferences.theme.builtin.default");case"gray":return g("preferences.theme.builtin.gray");case"green":return g("preferences.theme.builtin.green");case"neutral":return g("preferences.theme.builtin.neutral");case"orange":return g("preferences.theme.builtin.orange");case"pink":return g("preferences.theme.builtin.pink");case"rose":return g("preferences.theme.builtin.rose");case"sky-blue":return g("preferences.theme.builtin.skyBlue");case"slate":return g("preferences.theme.builtin.slate");case"violet":return g("preferences.theme.builtin.violet");case"yellow":return g("preferences.theme.builtin.yellow");case"zinc":return g("preferences.theme.builtin.zinc")}}function f(m){l.value=m.type}function p(m){const v=m.target;n(An(v.value))}function h(){var m,v,k;(k=(v=(m=a.value)==null?void 0:m[0])==null?void 0:v.click)==null||k.call(v)}return ve(()=>[l.value,t.isDark],([m,v])=>{const k=d.value.find(O=>O.type===m);if(k){const O=v&&k.darkPrimaryColor||k.primaryColor;s.value=O||k.color}}),(m,v)=>(i(),C("div",fu,[(i(!0),C(Q,null,me(d.value,k=>(i(),C("div",{key:k.type,class:"flex cursor-pointer flex-col",onClick:O=>f(k)},[E("div",{class:R([{"outline-box-active":k.type===l.value},"outline-box flex-center group cursor-pointer"])},[k.type!=="custom"?(i(),C("div",{key:0,style:fe({backgroundColor:k.color}),class:"mx-10 my-2 size-5 rounded-md"},null,4)):(i(),C("div",{key:1,class:"size-full px-10 py-2",onClick:Le(h,["stop"])},[E("div",hu,[b(e(Ts),{class:"absolute z-10 size-5 opacity-60 group-hover:opacity-100"}),E("input",{ref_for:!0,ref_key:"colorInput",ref:a,value:r.value,class:"absolute inset-0 opacity-0",type:"color",onInput:p},null,40,bu)])]))],2),E("div",vu,S(u(k.type)),1)],8,mu))),128))]))}}),yu=V({name:"PreferenceColorMode",__name:"color-mode",props:{appColorWeakMode:{type:Boolean,default:!1},appColorWeakModeModifiers:{},appColorGrayMode:{type:Boolean,default:!1},appColorGrayModeModifiers:{}},emits:["update:appColorWeakMode","update:appColorGrayMode"],setup(o){const t=x(o,"appColorWeakMode"),a=x(o,"appColorGrayMode");return(l,s)=>(i(),C(Q,null,[b(se,{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n)},{default:c(()=>[A(S(e(g)("preferences.theme.weakMode")),1)]),_:1},8,["modelValue"]),b(se,{modelValue:a.value,"onUpdate:modelValue":s[1]||(s[1]=n=>a.value=n)},{default:c(()=>[A(S(e(g)("preferences.theme.grayMode")),1)]),_:1},8,["modelValue"])],64))}}),wu=V({name:"PreferenceColorMode",__name:"radius",props:{themeRadius:{default:"0.5"},themeRadiusModifiers:{}},emits:["update:themeRadius"],setup(o){const t=x(o,"themeRadius"),a=[{label:"0",value:"0"},{label:"0.25",value:"0.25"},{label:"0.5",value:"0.5"},{label:"0.75",value:"0.75"},{label:"1",value:"1"}];return(l,s)=>(i(),y(e(Lo),{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(i(),C(Q,null,me(a,n=>b(e(zo),{key:n.value,value:n.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 w-16 rounded-sm"},{default:c(()=>[A(S(n.label),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]))}}),xu={class:"flex w-full flex-wrap justify-between"},ku=["onClick"],Cu={class:"text-muted-foreground mt-2 text-center text-xs"},_u=V({name:"PreferenceTheme",__name:"theme",props:{modelValue:{default:"auto"},modelModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{}},emits:["update:modelValue","update:themeSemiDarkSidebar","update:themeSemiDarkHeader"],setup(o){const t=x(o,"modelValue"),a=x(o,"themeSemiDarkSidebar"),l=x(o,"themeSemiDarkHeader"),s=[{icon:jn,name:"light"},{icon:Gn,name:"dark"},{icon:qn,name:"auto"}];function n(d){return d===t.value?["outline-box-active"]:[]}function r(d){switch(d){case"auto":return g("preferences.followSystem");case"dark":return g("preferences.theme.dark");case"light":return g("preferences.theme.light")}}return(d,u)=>(i(),C("div",xu,[(i(),C(Q,null,me(s,f=>E("div",{key:f.name,class:"flex cursor-pointer flex-col",onClick:p=>t.value=f.name},[E("div",{class:R([n(f.name),"outline-box flex-center py-4"])},[(i(),y(Ae(f.icon),{class:"mx-9 size-5"}))],2),E("div",Cu,S(r(f.name)),1)],8,ku)),64)),b(se,{modelValue:a.value,"onUpdate:modelValue":u[0]||(u[0]=f=>a.value=f),disabled:t.value==="dark",class:"mt-6"},{default:c(()=>[A(S(e(g)("preferences.theme.darkSidebar")),1)]),_:1},8,["modelValue","disabled"]),b(se,{modelValue:l.value,"onUpdate:modelValue":u[1]||(u[1]=f=>l.value=f),disabled:t.value==="dark"},{default:c(()=>[A(S(e(g)("preferences.theme.darkHeader")),1)]),_:1},8,["modelValue","disabled"])]))}}),Su={class:"flex items-center"},Mu={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Tu={class:"p-1"},$u=V({__name:"preferences-drawer",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appLayout:{},appLayoutModifiers:{},appColorGrayMode:{type:Boolean},appColorGrayModeModifiers:{},appColorWeakMode:{type:Boolean},appColorWeakModeModifiers:{},appContentCompact:{},appContentCompactModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},transitionProgress:{type:Boolean},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{},themeBuiltinType:{},themeBuiltinTypeModifiers:{},themeMode:{},themeModeModifiers:{},themeRadius:{},themeRadiusModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{},sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{},headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{},breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{},tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{},navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{},footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{},copyrightSettingShow:{type:Boolean},copyrightSettingShowModifiers:{},copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysGlobalLogout:{type:Boolean},shortcutKeysGlobalLogoutModifiers:{},shortcutKeysGlobalLockScreen:{type:Boolean},shortcutKeysGlobalLockScreenModifiers:{},widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:_e(["clearPreferencesAndLogout"],["update:appLocale","update:appDynamicTitle","update:appLayout","update:appColorGrayMode","update:appColorWeakMode","update:appContentCompact","update:appWatermark","update:appEnableCheckUpdates","update:appPreferencesButtonPosition","update:transitionProgress","update:transitionName","update:transitionLoading","update:transitionEnable","update:themeColorPrimary","update:themeBuiltinType","update:themeMode","update:themeRadius","update:themeSemiDarkSidebar","update:themeSemiDarkHeader","update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsed","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarExpandOnHover","update:sidebarCollapsedButton","update:sidebarFixedButton","update:headerEnable","update:headerMode","update:headerMenuAlign","update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbShowHome","update:breadcrumbStyleType","update:breadcrumbHideOnlyOne","update:tabbarEnable","update:tabbarShowIcon","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarMaxCount","update:tabbarMiddleClickToClose","update:navigationStyleType","update:navigationSplit","update:navigationAccordion","update:footerEnable","update:footerFixed","update:copyrightSettingShow","update:copyrightEnable","update:copyrightCompanyName","update:copyrightCompanySiteLink","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysGlobalLogout","update:shortcutKeysGlobalLockScreen","update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:widgetRefresh"]),setup(o,{emit:t}){const a=t,l=pa.getMessage(),s=x(o,"appLocale"),n=x(o,"appDynamicTitle"),r=x(o,"appLayout"),d=x(o,"appColorGrayMode"),u=x(o,"appColorWeakMode"),f=x(o,"appContentCompact"),p=x(o,"appWatermark"),h=x(o,"appEnableCheckUpdates"),m=x(o,"appPreferencesButtonPosition"),v=x(o,"transitionProgress"),k=x(o,"transitionName"),O=x(o,"transitionLoading"),U=x(o,"transitionEnable"),_=x(o,"themeColorPrimary"),W=x(o,"themeBuiltinType"),F=x(o,"themeMode"),I=x(o,"themeRadius"),L=x(o,"themeSemiDarkSidebar"),K=x(o,"themeSemiDarkHeader"),P=x(o,"sidebarEnable"),$=x(o,"sidebarWidth"),N=x(o,"sidebarCollapsed"),X=x(o,"sidebarCollapsedShowTitle"),ee=x(o,"sidebarAutoActivateChild"),ie=x(o,"sidebarExpandOnHover"),ce=x(o,"sidebarCollapsedButton"),q=x(o,"sidebarFixedButton"),Z=x(o,"headerEnable"),ye=x(o,"headerMode"),Se=x(o,"headerMenuAlign"),Be=x(o,"breadcrumbEnable"),Oe=x(o,"breadcrumbShowIcon"),$e=x(o,"breadcrumbShowHome"),j=x(o,"breadcrumbStyleType"),re=x(o,"breadcrumbHideOnlyOne"),te=x(o,"tabbarEnable"),we=x(o,"tabbarShowIcon"),Me=x(o,"tabbarShowMore"),Ve=x(o,"tabbarShowMaximize"),ue=x(o,"tabbarPersist"),be=x(o,"tabbarDraggable"),pe=x(o,"tabbarWheelable"),Fe=x(o,"tabbarStyleType"),ae=x(o,"tabbarMaxCount"),ke=x(o,"tabbarMiddleClickToClose"),H=x(o,"navigationStyleType"),oe=x(o,"navigationSplit"),de=x(o,"navigationAccordion"),He=x(o,"footerEnable"),Re=x(o,"footerFixed"),Xt=x(o,"copyrightSettingShow"),St=x(o,"copyrightEnable"),Pt=x(o,"copyrightCompanyName"),It=x(o,"copyrightCompanySiteLink"),ct=x(o,"copyrightDate"),Mt=x(o,"copyrightIcp"),ne=x(o,"copyrightIcpLink"),Te=x(o,"shortcutKeysEnable"),et=x(o,"shortcutKeysGlobalSearch"),qe=x(o,"shortcutKeysGlobalLogout"),je=x(o,"shortcutKeysGlobalLockScreen"),Ma=x(o,"widgetGlobalSearch"),Ta=x(o,"widgetFullscreen"),$a=x(o,"widgetLanguageToggle"),Ba=x(o,"widgetNotification"),Va=x(o,"widgetThemeToggle"),Ea=x(o,"widgetSidebarToggle"),La=x(o,"widgetLockScreen"),za=x(o,"widgetRefresh"),{diffPreference:gt,isDark:ll,isFullContent:Jt,isHeaderNav:nl,isHeaderSidebarNav:sl,isMixedNav:Aa,isSideMixedNav:rl,isSideMode:il,isSideNav:dl}=ft(),{copy:ul}=Pn({legacy:!0}),[cl]=Fo(),Pa=G("appearance"),pl=w(()=>[{label:g("preferences.appearance"),value:"appearance"},{label:g("preferences.layout"),value:"layout"},{label:g("preferences.shortcutKeys.title"),value:"shortcutKey"},{label:g("preferences.general"),value:"general"}]),fl=w(()=>!Jt.value&&!Aa.value&&!nl.value&&D.header.enable);function ml(){return J(this,null,function*(){var Zt;yield ul(JSON.stringify(gt.value,null,2)),(Zt=l.copyPreferencesSuccess)==null||Zt.call(l,g("preferences.copyPreferencesSuccessTitle"),g("preferences.copyPreferencesSuccess"))})}function hl(){return J(this,null,function*(){Da(),In(),a("clearPreferencesAndLogout")})}function bl(){return J(this,null,function*(){gt.value&&(Da(),yield vo(D.app.locale))})}return(Zt,M)=>(i(),C("div",null,[b(e(cl),{description:e(g)("preferences.subtitle"),title:e(g)("preferences.title"),class:"sm:max-w-sm"},{extra:c(()=>[E("div",Su,[b(e(Je),{disabled:!e(gt),tooltip:e(g)("preferences.resetTip"),class:"relative"},{default:c(()=>[e(gt)?(i(),C("span",Mu)):z("",!0),b(e(xa),{class:"size-4",onClick:bl})]),_:1},8,["disabled","tooltip"])])]),footer:c(()=>[b(e(Pe),{disabled:!e(gt),class:"mx-4 w-full",size:"sm",variant:"default",onClick:ml},{default:c(()=>[b(e(hs),{class:"mr-2 size-3"}),A(" "+S(e(g)("preferences.copyPreferences")),1)]),_:1},8,["disabled"]),b(e(Pe),{disabled:!e(gt),class:"mr-4 w-full",size:"sm",variant:"ghost",onClick:hl},{default:c(()=>[A(S(e(g)("preferences.clearAndLogout")),1)]),_:1},8,["disabled"])]),default:c(()=>[E("div",Tu,[b(e(Hr),{modelValue:Pa.value,"onUpdate:modelValue":M[68]||(M[68]=T=>Pa.value=T),tabs:pl.value},{general:c(()=>[b(e(Ee),{title:e(g)("preferences.general")},{default:c(()=>[b(e(pd),{"app-dynamic-title":n.value,"onUpdate:appDynamicTitle":M[0]||(M[0]=T=>n.value=T),"app-enable-check-updates":h.value,"onUpdate:appEnableCheckUpdates":M[1]||(M[1]=T=>h.value=T),"app-locale":s.value,"onUpdate:appLocale":M[2]||(M[2]=T=>s.value=T),"app-watermark":p.value,"onUpdate:appWatermark":M[3]||(M[3]=T=>p.value=T)},null,8,["app-dynamic-title","app-enable-check-updates","app-locale","app-watermark"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.animation.title")},{default:c(()=>[b(e(ud),{"transition-enable":U.value,"onUpdate:transitionEnable":M[4]||(M[4]=T=>U.value=T),"transition-loading":O.value,"onUpdate:transitionLoading":M[5]||(M[5]=T=>O.value=T),"transition-name":k.value,"onUpdate:transitionName":M[6]||(M[6]=T=>k.value=T),"transition-progress":v.value,"onUpdate:transitionProgress":M[7]||(M[7]=T=>v.value=T)},null,8,["transition-enable","transition-loading","transition-name","transition-progress"])]),_:1},8,["title"])]),appearance:c(()=>[b(e(Ee),{title:e(g)("preferences.theme.title")},{default:c(()=>[b(e(_u),{modelValue:F.value,"onUpdate:modelValue":M[8]||(M[8]=T=>F.value=T),"theme-semi-dark-header":K.value,"onUpdate:themeSemiDarkHeader":M[9]||(M[9]=T=>K.value=T),"theme-semi-dark-sidebar":L.value,"onUpdate:themeSemiDarkSidebar":M[10]||(M[10]=T=>L.value=T)},null,8,["modelValue","theme-semi-dark-header","theme-semi-dark-sidebar"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.theme.builtin.title")},{default:c(()=>[b(e(gu),{modelValue:W.value,"onUpdate:modelValue":M[11]||(M[11]=T=>W.value=T),"theme-color-primary":_.value,"onUpdate:themeColorPrimary":M[12]||(M[12]=T=>_.value=T),"is-dark":e(ll)},null,8,["modelValue","theme-color-primary","is-dark"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.theme.radius")},{default:c(()=>[b(e(wu),{modelValue:I.value,"onUpdate:modelValue":M[13]||(M[13]=T=>I.value=T)},null,8,["modelValue"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.other")},{default:c(()=>[b(e(yu),{"app-color-gray-mode":d.value,"onUpdate:appColorGrayMode":M[14]||(M[14]=T=>d.value=T),"app-color-weak-mode":u.value,"onUpdate:appColorWeakMode":M[15]||(M[15]=T=>u.value=T)},null,8,["app-color-gray-mode","app-color-weak-mode"])]),_:1},8,["title"])]),layout:c(()=>[b(e(Ee),{title:e(g)("preferences.layout")},{default:c(()=>[b(e(lu),{modelValue:r.value,"onUpdate:modelValue":M[16]||(M[16]=T=>r.value=T)},null,8,["modelValue"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.content")},{default:c(()=>[b(e(Xd),{modelValue:f.value,"onUpdate:modelValue":M[17]||(M[17]=T=>f.value=T)},null,8,["modelValue"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.sidebar.title")},{default:c(()=>[b(e(du),{"sidebar-auto-activate-child":ee.value,"onUpdate:sidebarAutoActivateChild":M[18]||(M[18]=T=>ee.value=T),"sidebar-collapsed":N.value,"onUpdate:sidebarCollapsed":M[19]||(M[19]=T=>N.value=T),"sidebar-collapsed-show-title":X.value,"onUpdate:sidebarCollapsedShowTitle":M[20]||(M[20]=T=>X.value=T),"sidebar-enable":P.value,"onUpdate:sidebarEnable":M[21]||(M[21]=T=>P.value=T),"sidebar-expand-on-hover":ie.value,"onUpdate:sidebarExpandOnHover":M[22]||(M[22]=T=>ie.value=T),"sidebar-width":$.value,"onUpdate:sidebarWidth":M[23]||(M[23]=T=>$.value=T),"sidebar-collapsed-button":ce.value,"onUpdate:sidebarCollapsedButton":M[24]||(M[24]=T=>ce.value=T),"sidebar-fixed-button":q.value,"onUpdate:sidebarFixedButton":M[25]||(M[25]=T=>q.value=T),"current-layout":r.value,disabled:!e(il)},null,8,["sidebar-auto-activate-child","sidebar-collapsed","sidebar-collapsed-show-title","sidebar-enable","sidebar-expand-on-hover","sidebar-width","sidebar-collapsed-button","sidebar-fixed-button","current-layout","disabled"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.header.title")},{default:c(()=>[b(e(eu),{"header-enable":Z.value,"onUpdate:headerEnable":M[26]||(M[26]=T=>Z.value=T),"header-menu-align":Se.value,"onUpdate:headerMenuAlign":M[27]||(M[27]=T=>Se.value=T),"header-mode":ye.value,"onUpdate:headerMode":M[28]||(M[28]=T=>ye.value=T),disabled:e(Jt)},null,8,["header-enable","header-menu-align","header-mode","disabled"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.navigationMenu.title")},{default:c(()=>[b(e(nu),{"navigation-accordion":de.value,"onUpdate:navigationAccordion":M[29]||(M[29]=T=>de.value=T),"navigation-split":oe.value,"onUpdate:navigationSplit":M[30]||(M[30]=T=>oe.value=T),"navigation-style-type":H.value,"onUpdate:navigationStyleType":M[31]||(M[31]=T=>H.value=T),disabled:e(Jt),"disabled-navigation-split":!e(Aa)},null,8,["navigation-accordion","navigation-split","navigation-style-type","disabled","disabled-navigation-split"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.breadcrumb.title")},{default:c(()=>[b(e(md),{"breadcrumb-enable":Be.value,"onUpdate:breadcrumbEnable":M[32]||(M[32]=T=>Be.value=T),"breadcrumb-hide-only-one":re.value,"onUpdate:breadcrumbHideOnlyOne":M[33]||(M[33]=T=>re.value=T),"breadcrumb-show-home":$e.value,"onUpdate:breadcrumbShowHome":M[34]||(M[34]=T=>$e.value=T),"breadcrumb-show-icon":Oe.value,"onUpdate:breadcrumbShowIcon":M[35]||(M[35]=T=>Oe.value=T),"breadcrumb-style-type":j.value,"onUpdate:breadcrumbStyleType":M[36]||(M[36]=T=>j.value=T),disabled:!fl.value||!(e(dl)||e(rl)||e(sl))},null,8,["breadcrumb-enable","breadcrumb-hide-only-one","breadcrumb-show-home","breadcrumb-show-icon","breadcrumb-style-type","disabled"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.tabbar.title")},{default:c(()=>[b(e(uu),{"tabbar-draggable":be.value,"onUpdate:tabbarDraggable":M[37]||(M[37]=T=>be.value=T),"tabbar-enable":te.value,"onUpdate:tabbarEnable":M[38]||(M[38]=T=>te.value=T),"tabbar-persist":ue.value,"onUpdate:tabbarPersist":M[39]||(M[39]=T=>ue.value=T),"tabbar-show-icon":we.value,"onUpdate:tabbarShowIcon":M[40]||(M[40]=T=>we.value=T),"tabbar-show-maximize":Ve.value,"onUpdate:tabbarShowMaximize":M[41]||(M[41]=T=>Ve.value=T),"tabbar-show-more":Me.value,"onUpdate:tabbarShowMore":M[42]||(M[42]=T=>Me.value=T),"tabbar-style-type":Fe.value,"onUpdate:tabbarStyleType":M[43]||(M[43]=T=>Fe.value=T),"tabbar-wheelable":pe.value,"onUpdate:tabbarWheelable":M[44]||(M[44]=T=>pe.value=T),"tabbar-max-count":ae.value,"onUpdate:tabbarMaxCount":M[45]||(M[45]=T=>ae.value=T),"tabbar-middle-click-to-close":ke.value,"onUpdate:tabbarMiddleClickToClose":M[46]||(M[46]=T=>ke.value=T)},null,8,["tabbar-draggable","tabbar-enable","tabbar-persist","tabbar-show-icon","tabbar-show-maximize","tabbar-show-more","tabbar-style-type","tabbar-wheelable","tabbar-max-count","tabbar-middle-click-to-close"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.widget.title")},{default:c(()=>[b(e(cu),{"app-preferences-button-position":m.value,"onUpdate:appPreferencesButtonPosition":M[47]||(M[47]=T=>m.value=T),"widget-fullscreen":Ta.value,"onUpdate:widgetFullscreen":M[48]||(M[48]=T=>Ta.value=T),"widget-global-search":Ma.value,"onUpdate:widgetGlobalSearch":M[49]||(M[49]=T=>Ma.value=T),"widget-language-toggle":$a.value,"onUpdate:widgetLanguageToggle":M[50]||(M[50]=T=>$a.value=T),"widget-lock-screen":La.value,"onUpdate:widgetLockScreen":M[51]||(M[51]=T=>La.value=T),"widget-notification":Ba.value,"onUpdate:widgetNotification":M[52]||(M[52]=T=>Ba.value=T),"widget-refresh":za.value,"onUpdate:widgetRefresh":M[53]||(M[53]=T=>za.value=T),"widget-sidebar-toggle":Ea.value,"onUpdate:widgetSidebarToggle":M[54]||(M[54]=T=>Ea.value=T),"widget-theme-toggle":Va.value,"onUpdate:widgetThemeToggle":M[55]||(M[55]=T=>Va.value=T)},null,8,["app-preferences-button-position","widget-fullscreen","widget-global-search","widget-language-toggle","widget-lock-screen","widget-notification","widget-refresh","widget-sidebar-toggle","widget-theme-toggle"])]),_:1},8,["title"]),b(e(Ee),{title:e(g)("preferences.footer.title")},{default:c(()=>[b(e(Qd),{"footer-enable":He.value,"onUpdate:footerEnable":M[56]||(M[56]=T=>He.value=T),"footer-fixed":Re.value,"onUpdate:footerFixed":M[57]||(M[57]=T=>Re.value=T)},null,8,["footer-enable","footer-fixed"])]),_:1},8,["title"]),Xt.value?(i(),y(e(Ee),{key:0,title:e(g)("preferences.copyright.title")},{default:c(()=>[b(e(Zd),{"copyright-company-name":Pt.value,"onUpdate:copyrightCompanyName":M[58]||(M[58]=T=>Pt.value=T),"copyright-company-site-link":It.value,"onUpdate:copyrightCompanySiteLink":M[59]||(M[59]=T=>It.value=T),"copyright-date":ct.value,"onUpdate:copyrightDate":M[60]||(M[60]=T=>ct.value=T),"copyright-enable":St.value,"onUpdate:copyrightEnable":M[61]||(M[61]=T=>St.value=T),"copyright-icp":Mt.value,"onUpdate:copyrightIcp":M[62]||(M[62]=T=>Mt.value=T),"copyright-icp-link":ne.value,"onUpdate:copyrightIcpLink":M[63]||(M[63]=T=>ne.value=T),disabled:!He.value},null,8,["copyright-company-name","copyright-company-site-link","copyright-date","copyright-enable","copyright-icp","copyright-icp-link","disabled"])]),_:1},8,["title"])):z("",!0)]),shortcutKey:c(()=>[b(e(Ee),{title:e(g)("preferences.shortcutKeys.global")},{default:c(()=>[b(e(pu),{"shortcut-keys-enable":Te.value,"onUpdate:shortcutKeysEnable":M[64]||(M[64]=T=>Te.value=T),"shortcut-keys-global-search":et.value,"onUpdate:shortcutKeysGlobalSearch":M[65]||(M[65]=T=>et.value=T),"shortcut-keys-lock-screen":je.value,"onUpdate:shortcutKeysLockScreen":M[66]||(M[66]=T=>je.value=T),"shortcut-keys-logout":qe.value,"onUpdate:shortcutKeysLogout":M[67]||(M[67]=T=>qe.value=T)},null,8,["shortcut-keys-enable","shortcut-keys-global-search","shortcut-keys-lock-screen","shortcut-keys-logout"])]),_:1},8,["title"])]),_:1},8,["modelValue","tabs"])])]),_:1},8,["description","title"])]))}}),Go=V({__name:"preferences",setup(o){const[t,a]=Fo({connectedComponent:$u}),l=w(()=>{const n={};for(const[r,d]of Object.entries(D))for(const[u,f]of Object.entries(d))n[`${r}${Ua(u)}`]=f;return n}),s=w(()=>{const n={};for(const[r,d]of Object.entries(D))if(typeof d=="object")for(const u of Object.keys(d))n[`update:${r}${Ua(u)}`]=f=>{rt({[r]:{[u]:f}}),r==="app"&&u==="locale"&&vo(f)};else n[r]=d;return n});return(n,r)=>(i(),C("div",null,[b(e(t),he(Y(Y({},n.$attrs),l.value),So(s.value)),null,16),E("div",{onClick:r[0]||(r[0]=()=>e(a).open())},[B(n.$slots,"default",{},()=>[b(e(Pe),{title:e(g)("preferences.title"),class:"bg-primary flex-col-center size-10 cursor-pointer rounded-l-lg rounded-r-none border-none"},{default:c(()=>[b(e(Do),{class:"size-5"})]),_:1},8,["title"])])])]))}}),Bu=V({__name:"preferences-button",emits:["clearPreferencesAndLogout"],setup(o,{emit:t}){const a=t;function l(){a("clearPreferencesAndLogout")}return(s,n)=>(i(),y(Go,{onClearPreferencesAndLogout:l},{default:c(()=>[b(e(Je),null,{default:c(()=>[b(e(Do),{class:"text-foreground size-4"})]),_:1})]),_:1}))}}),Vu={class:"hover:bg-accent ml-1 mr-2 cursor-pointer rounded-full p-1.5"},Eu={class:"hover:text-accent-foreground flex-center"},Lu={class:"ml-2 w-full"},zu={key:0,class:"text-foreground mb-1 flex items-center text-sm font-medium"},Au={class:"text-muted-foreground text-xs font-normal"},Zc=V({name:"UserDropdown",__name:"user-dropdown",props:{avatar:{default:""},description:{default:""},enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]},tagText:{default:""},text:{default:""},trigger:{default:"click"},hoverDelay:{default:500}},emits:["logout"],setup(o,{emit:t}){const a=o,l=t,{globalLockScreenShortcutKey:s,globalLogoutShortcutKey:n}=ft(),r=ht(),[d,u]=zt({connectedComponent:Ti}),[f,p]=zt({onConfirm(){K()}}),h=Ha("refTrigger"),m=Ha("refContent"),[v,k]=Rr([h,m],()=>a.hoverDelay);ve(()=>a.trigger==="hover"||a.trigger==="both",P=>{P?k.enable():k.disable()},{immediate:!0});const O=w(()=>Vt()?"Alt":"⌥"),U=w(()=>a.enableShortcutKey&&n.value),_=w(()=>a.enableShortcutKey&&s.value),W=w(()=>a.enableShortcutKey&&D.shortcutKeys.enable);function F(){u.open()}function I(P){u.close(),r.lockScreen(P)}function L(){p.open(),v.value=!1}function K(){l("logout"),p.close()}if(W.value){const P=_o();Ft(P["Alt+KeyQ"],()=>{U.value&&L()}),Ft(P["Alt+KeyL"],()=>{_.value&&F()})}return(P,$)=>(i(),C(Q,null,[e(D).widget.lockScreen?(i(),y(e(d),{key:0,avatar:P.avatar,text:P.text,onSubmit:I},null,8,["avatar","text"])):z("",!0),b(e(f),{"cancel-text":e(g)("common.cancel"),"confirm-text":e(g)("common.confirm"),"fullscreen-button":!1,title:e(g)("common.prompt"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[A(S(e(g)("ui.widgets.logoutTip")),1)]),_:1},8,["cancel-text","confirm-text","title"]),b(e(ga),{open:e(v),"onUpdate:open":$[0]||($[0]=N=>va(v)?v.value=N:null)},{default:c(()=>[b(e(ya),{ref_key:"refTrigger",ref:h,disabled:a.trigger==="hover"},{default:c(()=>[E("div",Vu,[E("div",Eu,[b(e(Et),{alt:P.text,src:P.avatar,class:"size-8",dot:""},null,8,["alt","src"])])])]),_:1},8,["disabled"]),b(e(wa),{class:"mr-2 min-w-[240px] p-0 pb-1"},{default:c(()=>{var N;return[E("div",{ref_key:"refContent",ref:m},[b(e(Ws),{class:"flex items-center p-3"},{default:c(()=>[b(e(Et),{alt:P.text,src:P.avatar,class:"size-12",dot:"","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["alt","src"]),E("div",Lu,[P.tagText||P.text||P.$slots.tagText?(i(),C("div",zu,[A(S(P.text)+" ",1),B(P.$slots,"tagText",{},()=>[P.tagText?(i(),y(e(Bs),{key:0,class:"ml-2 text-green-400"},{default:c(()=>[A(S(P.tagText),1)]),_:1})):z("",!0)])])):z("",!0),E("div",Au,S(P.description),1)])]),_:3}),(N=P.menus)!=null&&N.length?(i(),y(e(Ht),{key:0})):z("",!0),(i(!0),C(Q,null,me(P.menus,X=>(i(),y(e($t),{key:X.text,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:X.handler},{default:c(()=>[b(e(Ke),{icon:X.icon,class:"mr-2 size-4"},null,8,["icon"]),A(" "+S(X.text),1)]),_:2},1032,["onClick"]))),128)),b(e(Ht)),e(D).widget.lockScreen?(i(),y(e($t),{key:1,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:F},{default:c(()=>[b(e(Po),{class:"mr-2 size-4"}),A(" "+S(e(g)("ui.widgets.lockScreen.title"))+" ",1),_.value?(i(),y(e(Ga),{key:0},{default:c(()=>[A(S(O.value)+" L ",1)]),_:1})):z("",!0)]),_:1})):z("",!0),e(D).widget.lockScreen?(i(),y(e(Ht),{key:2})):z("",!0),b(e($t),{class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:L},{default:c(()=>[b(e(ws),{class:"mr-2 size-4"}),A(" "+S(e(g)("common.logout"))+" ",1),U.value?(i(),y(e(Ga),{key:0},{default:c(()=>[A(S(O.value)+" Q ",1)]),_:1})):z("",!0)]),_:1})],512)]}),_:3})]),_:3},8,["open"])],64))}}),Pu=V({__name:"layout-content",props:{contentCompact:{},contentCompactWidth:{},padding:{},paddingBottom:{},paddingLeft:{},paddingRight:{},paddingTop:{}},setup(o){const t=o,{contentElement:a,overlayStyle:l}=nn(),s=w(()=>{const{contentCompact:n,padding:r,paddingBottom:d,paddingLeft:u,paddingRight:f,paddingTop:p}=t,h=n==="compact"?{margin:"0 auto",width:`${t.contentCompactWidth}px`}:{};return xe(Y({},h),{flex:1,padding:`${r}px`,paddingBottom:`${d}px`,paddingLeft:`${u}px`,paddingRight:`${f}px`,paddingTop:`${p}px`})});return(n,r)=>(i(),C("main",{ref_key:"contentElement",ref:a,style:fe(s.value),class:"bg-background-deep relative"},[b(e(sn),{style:fe(e(l))},{default:c(()=>[B(n.$slots,"overlay")]),_:3},8,["style"]),B(n.$slots,"default")],4))}}),Iu=V({__name:"layout-footer",props:{fixed:{type:Boolean},height:{},show:{type:Boolean,default:!0},width:{},zIndex:{}},setup(o){const t=o,a=w(()=>{const{fixed:l,height:s,show:n,width:r,zIndex:d}=t;return{height:`${s}px`,marginBottom:n?"0":`-${s}px`,position:l?"fixed":"static",width:r,zIndex:d}});return(l,s)=>(i(),C("footer",{style:fe(a.value),class:"bg-background-deep bottom-0 w-full transition-all duration-200"},[B(l.$slots,"default")],4))}}),Ou=V({__name:"layout-header",props:{fullWidth:{type:Boolean},height:{},isMobile:{type:Boolean},show:{type:Boolean},sidebarWidth:{},theme:{},width:{},zIndex:{}},setup(o){const t=o,a=Ge(),l=w(()=>{const{fullWidth:n,height:r,show:d}=t,u=!d||!n?void 0:0;return{height:`${r}px`,marginTop:d?0:`-${r}px`,right:u}}),s=w(()=>({minWidth:`${t.isMobile?40:t.sidebarWidth}px`}));return(n,r)=>(i(),C("header",{class:R([n.theme,"border-border bg-header top-0 flex w-full flex-[0_0_auto] items-center border-b pl-2 transition-[margin-top] duration-200"]),style:fe(l.value)},[e(a).logo?(i(),C("div",{key:0,style:fe(s.value)},[B(n.$slots,"logo")],4)):z("",!0),B(n.$slots,"toggle-button"),B(n.$slots,"default")],6))}}),Qa=V({__name:"sidebar-collapse-button",props:{collapsed:{type:Boolean},collapsedModifiers:{}},emits:["update:collapsed"],setup(o){const t=x(o,"collapsed");function a(){t.value=!t.value}return(l,s)=>(i(),C("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 left-3 z-10 cursor-pointer rounded-sm p-1",onClick:Le(a,["stop"])},[t.value?(i(),y(e(rn),{key:0,class:"size-4"})):(i(),y(e(dn),{key:1,class:"size-4"}))]))}}),eo=V({__name:"sidebar-fixed-button",props:{expandOnHover:{type:Boolean},expandOnHoverModifiers:{}},emits:["update:expandOnHover"],setup(o){const t=x(o,"expandOnHover");function a(){t.value=!t.value}return(l,s)=>(i(),C("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 right-3 z-10 cursor-pointer rounded-sm p-[5px] transition-all duration-300",onClick:a},[t.value?(i(),y(e(qt),{key:1,class:"size-3.5"})):(i(),y(e(Oo),{key:0,class:"size-3.5"}))]))}}),Du=V({__name:"layout-sidebar",props:_e({collapseHeight:{default:42},collapseWidth:{default:48},domVisible:{type:Boolean,default:!0},extraWidth:{},fixedExtra:{type:Boolean,default:!1},headerHeight:{},isSidebarMixed:{type:Boolean,default:!1},marginTop:{default:0},mixedWidth:{default:70},paddingTop:{default:0},show:{type:Boolean,default:!0},showCollapseButton:{type:Boolean,default:!0},showFixedButton:{type:Boolean,default:!0},theme:{},width:{},zIndex:{default:0}},{collapse:{type:Boolean},collapseModifiers:{},extraCollapse:{type:Boolean},extraCollapseModifiers:{},expandOnHovering:{type:Boolean},expandOnHoveringModifiers:{},expandOnHover:{type:Boolean},expandOnHoverModifiers:{},extraVisible:{type:Boolean},extraVisibleModifiers:{}}),emits:_e(["leave"],["update:collapse","update:extraCollapse","update:expandOnHovering","update:expandOnHover","update:extraVisible"]),setup(o,{emit:t}){const a=o,l=t,s=x(o,"collapse"),n=x(o,"extraCollapse"),r=x(o,"expandOnHovering"),d=x(o,"expandOnHover"),u=x(o,"extraVisible"),f=On(document.body),p=Ge(),h=Wt(),m=w(()=>L(!0)),v=w(()=>{const{isSidebarMixed:$,marginTop:N,paddingTop:X,zIndex:ee}=a;return Y(xe(Y({"--scroll-shadow":"var(--sidebar)"},L(!1)),{height:`calc(100% - ${N}px)`,marginTop:`${N}px`,paddingTop:`${X}px`,zIndex:ee}),$&&u.value?{transition:"none"}:{})}),k=w(()=>{const{extraWidth:$,show:N,width:X,zIndex:ee}=a;return{left:`${X}px`,width:u.value&&N?`${$}px`:0,zIndex:ee}}),O=w(()=>{const{headerHeight:$}=a;return{height:`${$-1}px`}}),U=w(()=>{const{collapseWidth:$,fixedExtra:N,isSidebarMixed:X,mixedWidth:ee}=a;return X&&N?{width:`${s.value?$:ee}px`}:{}}),_=w(()=>{const{collapseHeight:$,headerHeight:N}=a;return Y({height:`calc(100% - ${N+$}px)`,paddingTop:"8px"},U.value)}),W=w(()=>{const{headerHeight:$,isSidebarMixed:N}=a;return Y(xe(Y({},N?{display:"flex",justifyContent:"center"}:{}),{height:`${$-1}px`}),U.value)}),F=w(()=>{const{collapseHeight:$,headerHeight:N}=a;return{height:`calc(100% - ${N+$}px)`}}),I=w(()=>({height:`${a.collapseHeight}px`}));ha(()=>{u.value=a.fixedExtra?!0:u.value});function L($){const{extraWidth:N,fixedExtra:X,isSidebarMixed:ee,show:ie,width:ce}=a;let q=ce===0?"0px":`${ce+(ee&&X&&u.value?N:0)}px`;const{collapseWidth:Z}=a;return $&&r.value&&!d.value&&(q=`${Z}px`),xe(Y({},q==="0px"?{overflow:"hidden"}:{}),{flex:`0 0 ${q}`,marginLeft:ie?0:`-${q}`,maxWidth:q,minWidth:q,width:q})}function K($){($==null?void 0:$.offsetX)<10||d.value||(r.value||(s.value=!1),a.isSidebarMixed&&(f.value=!0),r.value=!0)}function P(){l("leave"),a.isSidebarMixed&&(f.value=!1),!d.value&&(r.value=!1,s.value=!0,u.value=!1)}return($,N)=>(i(),C(Q,null,[$.domVisible?(i(),C("div",{key:0,class:R([$.theme,"h-full transition-all duration-150"]),style:fe(m.value)},null,6)):z("",!0),E("aside",{class:R([[$.theme,{"bg-sidebar-deep":$.isSidebarMixed,"bg-sidebar border-border border-r":!$.isSidebarMixed}],"fixed left-0 top-0 h-full transition-all duration-150"]),style:fe(v.value),onMouseenter:K,onMouseleave:P},[!s.value&&!$.isSidebarMixed&&$.showFixedButton?(i(),y(e(eo),{key:0,"expand-on-hover":d.value,"onUpdate:expandOnHover":N[0]||(N[0]=X=>d.value=X)},null,8,["expand-on-hover"])):z("",!0),e(p).logo?(i(),C("div",{key:1,style:fe(W.value)},[B($.$slots,"logo")],4)):z("",!0),b(e(Lt),{style:fe(_.value),shadow:"","shadow-border":""},{default:c(()=>[B($.$slots,"default")]),_:3},8,["style"]),E("div",{style:fe(I.value)},null,4),$.showCollapseButton&&!$.isSidebarMixed?(i(),y(e(Qa),{key:2,collapsed:s.value,"onUpdate:collapsed":N[1]||(N[1]=X=>s.value=X)},null,8,["collapsed"])):z("",!0),$.isSidebarMixed?(i(),C("div",{key:3,ref_key:"asideRef",ref:h,class:R([{"border-l":u.value},"border-border bg-sidebar fixed top-0 h-full overflow-hidden border-r transition-all duration-200"]),style:fe(k.value)},[$.isSidebarMixed&&d.value?(i(),y(e(Qa),{key:0,collapsed:n.value,"onUpdate:collapsed":N[2]||(N[2]=X=>n.value=X)},null,8,["collapsed"])):z("",!0),n.value?z("",!0):(i(),y(e(eo),{key:1,"expand-on-hover":d.value,"onUpdate:expandOnHover":N[3]||(N[3]=X=>d.value=X)},null,8,["expand-on-hover"])),n.value?z("",!0):(i(),C("div",{key:2,style:fe(O.value),class:"pl-2"},[B($.$slots,"extra-title")],4)),b(e(Lt),{style:fe(F.value),class:"border-border py-2",shadow:"","shadow-border":""},{default:c(()=>[B($.$slots,"extra")]),_:3},8,["style"])],6)):z("",!0)],38)],64))}}),Uu=V({__name:"layout-tabbar",props:{height:{}},setup(o){const t=o,a=w(()=>{const{height:l}=t;return{height:`${l}px`}});return(l,s)=>(i(),C("section",{style:fe(a.value),class:"border-border bg-background flex w-full border-b transition-all"},[B(l.$slots,"default")],4))}});function Hu(o){const t=w(()=>o.isMobile?"sidebar-nav":o.layout),a=w(()=>t.value==="full-content"),l=w(()=>t.value==="sidebar-mixed-nav"),s=w(()=>t.value==="header-nav"),n=w(()=>t.value==="mixed-nav"||t.value==="header-sidebar-nav"),r=w(()=>t.value==="header-mixed-nav");return{currentLayout:t,isFullContent:a,isHeaderMixedNav:r,isHeaderNav:s,isMixedNav:n,isSidebarMixedNav:l}}const Ru={class:"relative flex min-h-full w-full"},Wu=V({name:"VbenLayout",__name:"vben-layout",props:_e({contentCompact:{default:"wide"},contentCompactWidth:{default:1200},contentPadding:{default:0},contentPaddingBottom:{default:0},contentPaddingLeft:{default:0},contentPaddingRight:{default:0},contentPaddingTop:{default:0},footerEnable:{type:Boolean,default:!1},footerFixed:{type:Boolean,default:!0},footerHeight:{default:32},headerHeight:{default:50},headerHidden:{type:Boolean,default:!1},headerMode:{default:"fixed"},headerTheme:{},headerToggleSidebarButton:{type:Boolean,default:!0},headerVisible:{type:Boolean,default:!0},isMobile:{type:Boolean,default:!1},layout:{default:"sidebar-nav"},sidebarCollapse:{type:Boolean},sidebarCollapsedButton:{type:Boolean,default:!0},sidebarCollapseShowTitle:{type:Boolean,default:!1},sidebarEnable:{type:Boolean},sidebarExtraCollapsedWidth:{default:60},sidebarFixedButton:{type:Boolean,default:!0},sidebarHidden:{type:Boolean,default:!1},sidebarMixedWidth:{default:80},sidebarTheme:{default:"dark"},sidebarWidth:{default:180},sideCollapseWidth:{default:60},tabbarEnable:{type:Boolean,default:!0},tabbarHeight:{default:40},zIndex:{default:200}},{sidebarCollapse:{type:Boolean,default:!1},sidebarCollapseModifiers:{},sidebarExtraVisible:{type:Boolean},sidebarExtraVisibleModifiers:{},sidebarExtraCollapse:{type:Boolean,default:!1},sidebarExtraCollapseModifiers:{},sidebarExpandOnHover:{type:Boolean,default:!1},sidebarExpandOnHoverModifiers:{},sidebarEnable:{type:Boolean,default:!0},sidebarEnableModifiers:{}}),emits:_e(["sideMouseLeave","toggleSidebar"],["update:sidebarCollapse","update:sidebarExtraVisible","update:sidebarExtraCollapse","update:sidebarExpandOnHover","update:sidebarEnable"]),setup(o,{emit:t}){const a=o,l=t,s=x(o,"sidebarCollapse"),n=x(o,"sidebarExtraVisible"),r=x(o,"sidebarExtraCollapse"),d=x(o,"sidebarExpandOnHover"),u=x(o,"sidebarEnable"),f=G(!1),p=G(!1),h=G(),{arrivedState:m,directions:v,isScrolling:k,y:O}=Dn(document),{setLayoutHeaderHeight:U}=un(),{setLayoutFooterHeight:_}=cn(),{y:W}=Un({target:h,type:"client"}),{currentLayout:F,isFullContent:I,isHeaderMixedNav:L,isHeaderNav:K,isMixedNav:P,isSidebarMixedNav:$}=Hu(a),N=w(()=>a.headerMode==="auto"),X=w(()=>{let H=0;return a.headerVisible&&!a.headerHidden&&(H+=a.headerHeight),a.tabbarEnable&&(H+=a.tabbarHeight),H}),ee=w(()=>{const{sidebarCollapseShowTitle:H,sidebarMixedWidth:oe,sideCollapseWidth:de}=a;return H||$.value||L.value?oe:de}),ie=w(()=>!K.value&&u.value),ce=w(()=>{const{headerHeight:H,isMobile:oe}=a;return P.value&&!oe?H:0}),q=w(()=>{const{isMobile:H,sidebarHidden:oe,sidebarMixedWidth:de,sidebarWidth:He}=a;let Re=0;return oe||!ie.value||oe&&!$.value&&!P.value&&!L.value||((L.value||$.value)&&!H?Re=de:s.value?Re=H?0:ee.value:Re=He),Re}),Z=w(()=>{const{sidebarExtraCollapsedWidth:H,sidebarWidth:oe}=a;return r.value?H:oe}),ye=w(()=>F.value==="mixed-nav"||F.value==="sidebar-mixed-nav"||F.value==="sidebar-nav"||F.value==="header-mixed-nav"||F.value==="header-sidebar-nav"),Se=w(()=>{const{headerMode:H}=a;return P.value||H==="fixed"||H==="auto-scroll"||H==="auto"}),Be=w(()=>ye.value&&u.value&&!a.sidebarHidden),Oe=w(()=>!s.value&&a.isMobile),$e=w(()=>{let H="100%",oe="unset";if(Se.value&&F.value!=="header-nav"&&F.value!=="mixed-nav"&&F.value!=="header-sidebar-nav"&&Be.value&&!a.isMobile)if(($.value||L.value)&&d.value&&n.value){const He=s.value?ee.value:a.sidebarMixedWidth,Re=r.value?a.sidebarExtraCollapsedWidth:a.sidebarWidth;oe=`${He+Re}px`,H=`calc(100% - ${oe})`}else oe=f.value&&!d.value?`${ee.value}px`:`${q.value}px`,H=`calc(100% - ${oe})`;return{sidebarAndExtraWidth:oe,width:H}}),j=w(()=>{let H="",oe=0;if(!P.value||a.sidebarHidden)H="100%";else if(u.value){const de=d.value?a.sidebarWidth:ee.value;oe=s.value?ee.value:de,H=`calc(100% - ${s.value?q.value:de}px)`}else H="100%";return{marginLeft:`${oe}px`,width:H}}),re=w(()=>{const H=Se.value,{footerEnable:oe,footerFixed:de,footerHeight:He}=a;return{marginTop:H&&!I.value&&!p.value&&(!N.value||O.value<X.value)?`${X.value}px`:0,paddingBottom:`${oe&&de?He:0}px`}}),te=w(()=>{const{zIndex:H}=a,oe=P.value?1:0;return H+oe}),we=w(()=>{const H=Se.value;return{height:I.value?"0":`${X.value}px`,left:P.value?0:$e.value.sidebarAndExtraWidth,position:H?"fixed":"static",top:p.value||I.value?`-${X.value}px`:0,width:$e.value.width,"z-index":te.value}}),Me=w(()=>{const{isMobile:H,zIndex:oe}=a;let de=H||ye.value?1:-1;return P.value&&(de+=1),oe+de}),Ve=w(()=>a.footerFixed?$e.value.width:"100%"),ue=w(()=>({zIndex:a.zIndex})),be=w(()=>a.isMobile||a.headerToggleSidebarButton&&ye.value&&!$.value&&!P.value&&!a.isMobile),pe=w(()=>!ye.value||P.value||a.isMobile);ve(()=>a.isMobile,H=>{H&&(s.value=!0)},{immediate:!0}),ve([()=>X.value,()=>I.value],([H])=>{U(I.value?0:H)},{immediate:!0}),ve(()=>a.footerHeight,H=>{_(H)},{immediate:!0});{const H=()=>{W.value>X.value?p.value=!0:p.value=!1};ve([()=>a.headerMode,()=>W.value],()=>{if(!N.value||P.value||I.value){a.headerMode!=="auto-scroll"&&(p.value=!1);return}p.value=!0,H()},{immediate:!0})}{const H=jt((oe,de,He)=>{if(O.value<X.value){p.value=!1;return}if(He){p.value=!1;return}oe?p.value=!1:de&&(p.value=!0)},300);ve(()=>O.value,()=>{a.headerMode!=="auto-scroll"||P.value||I.value||k.value&&H(v.top,v.bottom,m.top)})}function Fe(){s.value=!0}function ae(){a.isMobile?s.value=!1:l("toggleSidebar")}const ke=ma;return(H,oe)=>(i(),C("div",Ru,[ie.value?(i(),y(e(Du),{key:0,collapse:s.value,"onUpdate:collapse":oe[0]||(oe[0]=de=>s.value=de),"expand-on-hover":d.value,"onUpdate:expandOnHover":oe[1]||(oe[1]=de=>d.value=de),"expand-on-hovering":f.value,"onUpdate:expandOnHovering":oe[2]||(oe[2]=de=>f.value=de),"extra-collapse":r.value,"onUpdate:extraCollapse":oe[3]||(oe[3]=de=>r.value=de),"extra-visible":n.value,"onUpdate:extraVisible":oe[4]||(oe[4]=de=>n.value=de),"show-collapse-button":H.sidebarCollapsedButton,"show-fixed-button":H.sidebarFixedButton,"collapse-width":ee.value,"dom-visible":!H.isMobile,"extra-width":Z.value,"fixed-extra":d.value,"header-height":e(P)?0:H.headerHeight,"is-sidebar-mixed":e($)||e(L),"margin-top":ce.value,"mixed-width":H.sidebarMixedWidth,show:Be.value,theme:H.sidebarTheme,width:q.value,"z-index":Me.value,onLeave:oe[5]||(oe[5]=()=>l("sideMouseLeave"))},yt({extra:c(()=>[B(H.$slots,"side-extra")]),"extra-title":c(()=>[B(H.$slots,"side-extra-title")]),default:c(()=>[e($)||e(L)?B(H.$slots,"mixed-menu",{key:0}):B(H.$slots,"menu",{key:1})]),_:2},[ye.value&&!e(P)?{name:"logo",fn:c(()=>[B(H.$slots,"logo")]),key:"0"}:void 0]),1032,["collapse","expand-on-hover","expand-on-hovering","extra-collapse","extra-visible","show-collapse-button","show-fixed-button","collapse-width","dom-visible","extra-width","fixed-extra","header-height","is-sidebar-mixed","margin-top","mixed-width","show","theme","width","z-index"])):z("",!0),E("div",{ref_key:"contentRef",ref:h,class:"flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in"},[E("div",{class:R([[{"shadow-[0_16px_24px_hsl(var(--background))]":e(O)>20},e(pn)],"overflow-hidden transition-all duration-200"]),style:fe(we.value)},[H.headerVisible?(i(),y(e(Ou),{key:0,"full-width":!ye.value,height:H.headerHeight,"is-mobile":H.isMobile,show:!e(I)&&!H.headerHidden,"sidebar-width":H.sidebarWidth,theme:H.headerTheme,width:$e.value.width,"z-index":te.value},yt({"toggle-button":c(()=>[be.value?(i(),y(e(Je),{key:0,class:"my-0 mr-1 rounded-md",onClick:ae},{default:c(()=>[b(e(fn),{class:"size-4"})]),_:1})):z("",!0)]),default:c(()=>[B(H.$slots,"header")]),_:2},[pe.value?{name:"logo",fn:c(()=>[B(H.$slots,"logo")]),key:"0"}:void 0]),1032,["full-width","height","is-mobile","show","sidebar-width","theme","width","z-index"])):z("",!0),H.tabbarEnable?(i(),y(e(Uu),{key:1,height:H.tabbarHeight,style:fe(j.value)},{default:c(()=>[B(H.$slots,"tabbar")]),_:3},8,["height","style"])):z("",!0)],6),b(e(Pu),{id:e(ke),"content-compact":H.contentCompact,"content-compact-width":H.contentCompactWidth,padding:H.contentPadding,"padding-bottom":H.contentPaddingBottom,"padding-left":H.contentPaddingLeft,"padding-right":H.contentPaddingRight,"padding-top":H.contentPaddingTop,style:fe(re.value),class:"transition-[margin-top] duration-200"},{overlay:c(()=>[B(H.$slots,"content-overlay")]),default:c(()=>[B(H.$slots,"content")]),_:3},8,["id","content-compact","content-compact-width","padding","padding-bottom","padding-left","padding-right","padding-top","style"]),H.footerEnable?(i(),y(e(Iu),{key:0,fixed:H.footerFixed,height:H.footerHeight,show:!e(I),width:Ve.value,"z-index":H.zIndex},{default:c(()=>[B(H.$slots,"footer")]),_:3},8,["fixed","height","show","width","z-index"])):z("",!0)],512),B(H.$slots,"extra"),Oe.value?(i(),C("div",{key:1,style:fe(ue.value),class:"bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200",onClick:Fe},null,4)):z("",!0)]))}});function Nu(){const o=G(!1),t=G(0),a=mt(),l=500,s=w(()=>D.transition.loading),n=()=>{if(!s.value)return;const r=performance.now()-t.value;r<l?setTimeout(()=>{o.value=!1},l-r):o.value=!1};return a.beforeEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||(t.value=performance.now(),o.value=!0),!0)),a.afterEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||n(),!0)),{spinning:o}}const Fu=V({name:"LayoutContentSpinner",__name:"content-spinner",setup(o){const{spinning:t}=Nu();return(a,l)=>(i(),y(e(go),{spinning:e(t)},null,8,["spinning"]))}}),Ku={key:0,class:"relative size-full"},ju=["src","onLoad"],Gu=V({name:"IFrameRouterView",__name:"iframe-router-view",setup(o){const t=G([]),a=_t(),l=st(),s=w(()=>D.tabbar.enable),n=w(()=>s.value?a.getTabs.filter(m=>{var v;return!!((v=m.meta)!=null&&v.iframeSrc)}):l.meta.iframeSrc?[l]:[]),r=w(()=>new Set(n.value.map(m=>m.name))),d=w(()=>n.value.length>0);function u(m){return m.name===l.name}function f(m){const{meta:v,name:k}=m;return!k||!a.renderRouteView?!1:s.value?!(v!=null&&v.keepAlive)&&r.value.has(k)&&k!==l.name?!1:a.getTabs.some(O=>O.name===k):u(m)}function p(m){t.value[m]=!1}function h(m){const v=t.value[m];return v===void 0?!0:v}return(m,v)=>d.value?(i(!0),C(Q,{key:0},me(n.value,(k,O)=>(i(),C(Q,{key:k.fullPath},[f(k)?Ie((i(),C("div",Ku,[b(e(go),{spinning:h(O)},null,8,["spinning"]),E("iframe",{src:k.meta.iframeSrc,class:"size-full",onLoad:U=>p(O)},null,40,ju)],512)),[[De,u(k)]]):z("",!0)],64))),128)):z("",!0)}}),qu={class:"relative h-full"},Yu=V({name:"LayoutContent",__name:"content",setup(o){const t=_t(),{keepAlive:a}=ft(),{getCachedTabs:l,getExcludeCachedTabs:s,renderRouteView:n}=bo(t),r=w(()=>{const{transition:f}=D;return f.name&&f.enable});function d(f){const{tabbar:p,transition:h}=D,m=h.name;if(!(!m||!h.enable))return!p.enable||!a,m}function u(f,p){var v;if(!f){console.error("Component view not found，please check the route configuration");return}const h=p.name;if(!h)return f;const m=(v=f==null?void 0:f.type)==null?void 0:v.name;return m||m===h||(f.type||(f.type={}),f.type.name=h),f}return(f,p)=>(i(),C("div",qu,[b(e(Gu)),b(e(mn),null,{default:c(({Component:h,route:m})=>[r.value?(i(),y(it,{key:0,name:d(m),appear:"",mode:"out-in"},{default:c(()=>[e(a)?(i(),y(Ra,{key:0,exclude:e(s),include:e(l)},[e(n)?Ie((i(),y(Ae(u(h,m)),{key:e(Xe)(m)})),[[De,!m.meta.iframeSrc]]):z("",!0)],1032,["exclude","include"])):e(n)?(i(),y(Ae(h),{key:e(Xe)(m)})):z("",!0)]),_:2},1032,["name"])):(i(),C(Q,{key:1},[e(a)?(i(),y(Ra,{key:0,exclude:e(s),include:e(l)},[e(n)?Ie((i(),y(Ae(u(h,m)),{key:e(Xe)(m)})),[[De,!m.meta.iframeSrc]]):z("",!0)],1032,["exclude","include"])):e(n)?(i(),y(Ae(h),{key:e(Xe)(m)})):z("",!0)],64))]),_:1})]))}}),Xu={class:"flex-center text-muted-foreground relative h-full w-full text-xs"},Ju=V({name:"LayoutFooter",__name:"footer",setup(o){return(t,a)=>(i(),C("div",Xu,[B(t.$slots,"default")]))}}),Zu={class:"flex-center hidden lg:block"},Qu={class:"flex h-full min-w-0 flex-shrink-0 items-center"},lt=50,ec=V({name:"LayoutHeader",__name:"header",props:{theme:{default:"light"}},emits:["clearPreferencesAndLogout"],setup(o,{emit:t}){const a=t,l=ht(),{globalSearchShortcutKey:s,preferencesButtonPosition:n}=ft(),r=Ge(),{refresh:d}=Wo(),u=w(()=>{const h=[{index:lt+100,name:"user-dropdown"}];return D.widget.globalSearch&&h.push({index:lt,name:"global-search"}),n.value.header&&h.push({index:lt+10,name:"preferences"}),D.widget.themeToggle&&h.push({index:lt+20,name:"theme-toggle"}),D.widget.languageToggle&&h.push({index:lt+30,name:"language-toggle"}),D.widget.fullscreen&&h.push({index:lt+40,name:"fullscreen"}),D.widget.notification&&h.push({index:lt+50,name:"notification"}),Object.keys(r).forEach(m=>{const v=m.split("-");m.startsWith("header-right")&&h.push({index:Number(v[2]),name:m})}),h.sort((m,v)=>m.index-v.index)}),f=w(()=>{const h=[];return D.widget.refresh&&h.push({index:0,name:"refresh"}),Object.keys(r).forEach(m=>{const v=m.split("-");m.startsWith("header-left")&&h.push({index:Number(v[2]),name:m})}),h.sort((m,v)=>m.index-v.index)});function p(){a("clearPreferencesAndLogout")}return(h,m)=>(i(),C(Q,null,[(i(!0),C(Q,null,me(f.value.filter(v=>v.index<lt),v=>B(h.$slots,v.name,{key:v.name},()=>[v.name==="refresh"?(i(),y(e(Je),{key:0,class:"my-0 mr-1 rounded-md",onClick:e(d)},{default:c(()=>[b(e(xa),{class:"size-4"})]),_:1},8,["onClick"])):z("",!0)],!0)),128)),E("div",Zu,[B(h.$slots,"breadcrumb",{},void 0,!0)]),(i(!0),C(Q,null,me(f.value.filter(v=>v.index>lt),v=>B(h.$slots,v.name,{key:v.name},void 0,!0)),128)),E("div",{class:R([`menu-align-${e(D).header.menuAlign}`,"flex h-full min-w-0 flex-1 items-center"])},[B(h.$slots,"menu",{},void 0,!0)],2),E("div",Qu,[(i(!0),C(Q,null,me(u.value,v=>B(h.$slots,v.name,{key:v.name},()=>[v.name==="global-search"?(i(),y(e(ki),{key:0,"enable-shortcut-key":e(s),menus:e(l).accessMenus,class:"mr-1 sm:mr-4"},null,8,["enable-shortcut-key","menus"])):v.name==="preferences"?(i(),y(e(Bu),{key:1,class:"mr-1",onClearPreferencesAndLogout:p})):v.name==="theme-toggle"?(i(),y(e(Yn),{key:2,class:"mr-1 mt-[2px]"})):v.name==="language-toggle"?(i(),y(e(Xn),{key:3,class:"mr-1"})):v.name==="fullscreen"?(i(),y(e(Lr),{key:4,class:"mr-1"})):z("",!0)],!0)),128))])],64))}}),tc=ze(ec,[["__scopeId","data-v-7670467e"]]),ac={class:"relative mr-1 flex size-1.5"},oc=V({__name:"menu-badge-dot",props:{dotClass:{default:""},dotStyle:{default:()=>({})}},setup(o){return(t,a)=>(i(),C("span",ac,[E("span",{class:R([t.dotClass,"absolute inline-flex h-full w-full animate-ping rounded-full opacity-75"]),style:fe(t.dotStyle)},null,6),E("span",{class:R([t.dotClass,"relative inline-flex size-1.5 rounded-full"]),style:fe(t.dotStyle)},null,6)]))}}),qo=V({__name:"menu-badge",props:{hasChildren:{type:Boolean},badge:{},badgeType:{},badgeVariants:{}},setup(o){const t=o,a={default:"bg-green-500",destructive:"bg-destructive",primary:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500"},l=w(()=>t.badgeType==="dot"),s=w(()=>{const{badgeVariants:r}=t;return r?a[r]||r:a.default}),n=w(()=>s.value&&Hn(s.value)?{backgroundColor:s.value}:{});return(r,d)=>l.value||r.badge?(i(),C("span",{key:0,class:R([r.$attrs.class,"absolute"])},[l.value?(i(),y(oc,{key:0,"dot-class":s.value,"dot-style":n.value},null,8,["dot-class","dot-style"])):(i(),C("div",{key:1,class:R([s.value,"text-primary-foreground flex-center rounded-xl px-1.5 py-0.5 text-[10px]"]),style:fe(n.value)},S(r.badge),7))],2)):z("",!0)}}),lc=["onClick","onMouseenter"],nc=V({name:"NormalMenu",__name:"normal-menu",props:{activePath:{default:""},collapse:{type:Boolean,default:!1},menus:{default:()=>[]},rounded:{type:Boolean},theme:{default:"dark"}},emits:["enter","select"],setup(o,{emit:t}){const a=o,l=t,{b:s,e:n,is:r}=dt("normal-menu");function d(u){return a.activePath===u.path&&u.activeIcon||u.icon}return(u,f)=>(i(),C("ul",{class:R([[u.theme,e(s)(),e(r)("collapse",u.collapse),e(r)(u.theme,!0),e(r)("rounded",u.rounded)],"relative"])},[(i(!0),C(Q,null,me(u.menus,p=>(i(),C("li",{key:p.path,class:R([e(n)("item"),e(r)("active",u.activePath===p.path)]),onClick:()=>l("select",p),onMouseenter:()=>l("enter",p)},[b(e(Ke),{class:R(e(n)("icon")),icon:d(p),fallback:""},null,8,["class","icon"]),E("span",{class:R([e(n)("name"),"truncate"])},S(p.name),3)],42,lc))),128))],2))}}),sc=ze(nc,[["__scopeId","data-v-3ebda870"]]);function Yo(o,t){var l,s;let a=o.parent;for(;a&&!t.includes((s=(l=a==null?void 0:a.type)==null?void 0:l.name)!=null?s:"");)a=a.parent;return a}const Rt=o=>{const t=Array.isArray(o)?o:[o],a=[];return t.forEach(l=>{var s;Array.isArray(l)?a.push(...Rt(l)):Wa(l)&&Array.isArray(l.children)?a.push(...Rt(l.children)):(a.push(l),Wa(l)&&((s=l.component)!=null&&s.subTree)&&a.push(...Rt(l.component.subTree)))}),a};function Xo(){const o=Gt();if(!o)throw new Error("instance is required");const t=w(()=>{var n;let l=o.parent;const s=[o.props.path];for(;(l==null?void 0:l.type.name)!=="Menu";)l!=null&&l.props.path&&s.unshift(l.props.path),l=(n=l==null?void 0:l.parent)!=null?n:null;return s});return{parentMenu:w(()=>Yo(o,["Menu","SubMenu"])),parentPaths:t}}function Jo(o){return w(()=>{var a;return{"--menu-level":o?(a=o==null?void 0:o.level)!=null?a:1:0}})}const Zo=Symbol("menuContext");function rc(o){kt(Zo,o)}function Qo(o){const t=Gt();kt(`subMenu:${t==null?void 0:t.uid}`,o)}function _a(){if(!Gt())throw new Error("instance is required");return xt(Zo)}function el(){const o=Gt();if(!o)throw new Error("instance is required");const t=Yo(o,["Menu","SubMenu"]);return xt(`subMenu:${t==null?void 0:t.uid}`)}const ic=V({name:"MenuItem",__name:"menu-item",props:{activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},emits:["click"],setup(o,{emit:t}){const a=o,l=t,s=Ge(),{b:n,e:r,is:d}=dt("menu-item"),u=dt("menu"),f=_a(),p=el(),{parentMenu:h,parentPaths:m}=Xo(),v=w(()=>a.path===(f==null?void 0:f.activePath)),k=w(()=>v.value&&a.activeIcon||a.icon),O=w(()=>{var I;return((I=h.value)==null?void 0:I.type.name)==="Menu"}),U=w(()=>{var I;return((I=f.props)==null?void 0:I.collapseShowTitle)&&O.value&&f.props.collapse}),_=w(()=>{var I;return f.props.mode==="vertical"&&O.value&&((I=f.props)==null?void 0:I.collapse)&&s.title}),W=ut({active:v,parentPaths:m.value,path:a.path||""});function F(){var I;a.disabled||((I=f==null?void 0:f.handleMenuItemClick)==null||I.call(f,{parentPaths:m.value,path:a.path}),l("click",W))}return Qe(()=>{var I,L;(I=p==null?void 0:p.addSubMenu)==null||I.call(p,W),(L=f==null?void 0:f.addMenuItem)==null||L.call(f,W)}),ba(()=>{var I,L;(I=p==null?void 0:p.removeSubMenu)==null||I.call(p,W),(L=f==null?void 0:f.removeMenuItem)==null||L.call(f,W)}),(I,L)=>(i(),C("li",{class:R([e(f).theme,e(n)(),e(d)("active",v.value),e(d)("disabled",I.disabled),e(d)("collapse-show-title",U.value)]),role:"menuitem",onClick:Le(F,["stop"])},[_.value?(i(),y(e(bt),{key:0,"content-class":[e(f).theme],side:"right"},{trigger:c(()=>[E("div",{class:R([e(u).be("tooltip","trigger")])},[b(e(Ke),{class:R(e(u).e("icon")),icon:k.value,fallback:""},null,8,["class","icon"]),B(I.$slots,"default"),U.value?(i(),C("span",{key:0,class:R(e(u).e("name"))},[B(I.$slots,"title")],2)):z("",!0)],2)]),default:c(()=>[B(I.$slots,"title")]),_:3},8,["content-class"])):z("",!0),Ie(E("div",{class:R([e(r)("content")])},[e(f).props.mode!=="horizontal"?(i(),y(e(qo),he({key:0,class:"right-2"},a),null,16)):z("",!0),b(e(Ke),{class:R(e(u).e("icon")),icon:k.value},null,8,["class","icon"]),B(I.$slots,"default"),B(I.$slots,"title")],2),[[De,!_.value]])],2))}});function dc(o,t={}){const{enable:a=!0,delay:l=320}=t;function s(){if(!(typeof a=="boolean"?a:a.value))return;const d=document.querySelector("aside li[role=menuitem].is-active");d&&d.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}const n=ia(s,l);return ve(o,()=>{(typeof a=="boolean"?a:a.value)&&n()}),{scrollToActiveItem:s}}const uc=V({name:"CollapseTransition",__name:"collapse-transition",setup(o){const t=l=>{l.style.maxHeight="",l.style.overflow=l.dataset.oldOverflow,l.style.paddingTop=l.dataset.oldPaddingTop,l.style.paddingBottom=l.dataset.oldPaddingBottom},a={afterEnter(l){l.style.maxHeight="",l.style.overflow=l.dataset.oldOverflow},afterLeave(l){t(l)},beforeEnter(l){l.dataset||(l.dataset={}),l.dataset.oldPaddingTop=l.style.paddingTop,l.dataset.oldMarginTop=l.style.marginTop,l.dataset.oldPaddingBottom=l.style.paddingBottom,l.dataset.oldMarginBottom=l.style.marginBottom,l.style.height&&(l.dataset.elExistsHeight=l.style.height),l.style.maxHeight=0,l.style.paddingTop=0,l.style.marginTop=0,l.style.paddingBottom=0,l.style.marginBottom=0},beforeLeave(l){l.dataset||(l.dataset={}),l.dataset.oldPaddingTop=l.style.paddingTop,l.dataset.oldMarginTop=l.style.marginTop,l.dataset.oldPaddingBottom=l.style.paddingBottom,l.dataset.oldMarginBottom=l.style.marginBottom,l.dataset.oldOverflow=l.style.overflow,l.style.maxHeight=`${l.scrollHeight}px`,l.style.overflow="hidden"},enter(l){requestAnimationFrame(()=>{l.dataset.oldOverflow=l.style.overflow,l.dataset.elExistsHeight?l.style.maxHeight=l.dataset.elExistsHeight:l.scrollHeight===0?l.style.maxHeight=0:l.style.maxHeight=`${l.scrollHeight}px`,l.style.paddingTop=l.dataset.oldPaddingTop,l.style.paddingBottom=l.dataset.oldPaddingBottom,l.style.marginTop=l.dataset.oldMarginTop,l.style.marginBottom=l.dataset.oldMarginBottom,l.style.overflow="hidden"})},enterCancelled(l){t(l)},leave(l){l.scrollHeight!==0&&(l.style.maxHeight=0,l.style.paddingTop=0,l.style.paddingBottom=0,l.style.marginTop=0,l.style.marginBottom=0)},leaveCancelled(l){t(l)}};return(l,s)=>(i(),y(it,he({name:"collapse-transition"},So(a)),{default:c(()=>[B(l.$slots,"default")]),_:3},16))}}),to=V({name:"SubMenuContent",__name:"sub-menu-content",props:{isMenuMore:{type:Boolean,default:!1},isTopLevelMenuSubmenu:{type:Boolean},level:{default:0},activeIcon:{},disabled:{type:Boolean},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(o){const t=o,a=_a(),{b:l,e:s,is:n}=dt("sub-menu-content"),r=dt("menu"),d=w(()=>a==null?void 0:a.openedMenus.includes(t.path)),u=w(()=>a.props.collapse),f=w(()=>t.level===1),p=w(()=>a.props.collapseShowTitle&&f.value&&u.value),h=w(()=>a==null?void 0:a.props.mode),m=w(()=>h.value==="horizontal"||!(f.value&&u.value)),v=w(()=>h.value==="vertical"&&f.value&&u.value&&!p.value),k=w(()=>h.value==="horizontal"&&!f.value||h.value==="vertical"&&u.value?da:ca),O=w(()=>d.value?{transform:"rotate(180deg)"}:{});return(U,_)=>(i(),C("div",{class:R([e(l)(),e(n)("collapse-show-title",p.value),e(n)("more",U.isMenuMore)])},[B(U.$slots,"default"),U.isMenuMore?z("",!0):(i(),y(e(Ke),{key:0,class:R(e(r).e("icon")),icon:U.icon,fallback:""},null,8,["class","icon"])),v.value?z("",!0):(i(),C("div",{key:1,class:R([e(s)("title")])},[B(U.$slots,"title")],2)),U.isMenuMore?z("",!0):Ie((i(),y(Ae(k.value),{key:2,class:R([[e(s)("icon-arrow")],"size-4"]),style:fe(O.value)},null,8,["class","style"])),[[De,m.value]])],2))}}),tl=V({name:"SubMenu",__name:"sub-menu",props:{isSubMenuMore:{type:Boolean,default:!1},activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(o){var ce;const t=o,{parentMenu:a,parentPaths:l}=Xo(),{b:s,is:n}=dt("sub-menu"),r=dt("menu"),d=_a(),u=el(),f=Jo(u),p=G(!1),h=G({}),m=G({}),v=G(null);Qo({addSubMenu:K,handleMouseleave:X,level:((ce=u==null?void 0:u.level)!=null?ce:0)+1,mouseInChild:p,removeSubMenu:P});const k=w(()=>d==null?void 0:d.openedMenus.includes(t.path)),O=w(()=>{var q;return((q=a.value)==null?void 0:q.type.name)==="Menu"}),U=w(()=>{var q;return(q=d==null?void 0:d.props.mode)!=null?q:"vertical"}),_=w(()=>d==null?void 0:d.props.rounded),W=w(()=>{var q;return(q=u==null?void 0:u.level)!=null?q:0}),F=w(()=>W.value===1),I=w(()=>{const q=U.value==="horizontal",Z=q&&F.value?"bottom":"right";return{collisionPadding:{top:20},side:Z,sideOffset:q?5:10}}),L=w(()=>{let q=!1;return Object.values(h.value).forEach(Z=>{Z.active&&(q=!0)}),Object.values(m.value).forEach(Z=>{Z.active&&(q=!0)}),q});function K(q){m.value[q.path]=q}function P(q){Reflect.deleteProperty(m.value,q.path)}function $(){const q=d==null?void 0:d.props.mode;t.disabled||d!=null&&d.props.collapse&&q==="vertical"||q==="horizontal"||d==null||d.handleSubMenuClick({active:L.value,parentPaths:l.value,path:t.path})}function N(q,Z=300){var ye,Se;if(q.type!=="focus"){if(!(d!=null&&d.props.collapse)&&(d==null?void 0:d.props.mode)==="vertical"||t.disabled){u&&(u.mouseInChild.value=!0);return}u&&(u.mouseInChild.value=!0),v.value&&window.clearTimeout(v.value),v.value=setTimeout(()=>{d==null||d.openMenu(t.path,l.value)},Z),(Se=(ye=a.value)==null?void 0:ye.vnode.el)==null||Se.dispatchEvent(new MouseEvent("mouseenter"))}}function X(q=!1){var Z;if(!(d!=null&&d.props.collapse)&&(d==null?void 0:d.props.mode)==="vertical"&&u){u.mouseInChild.value=!1;return}v.value&&window.clearTimeout(v.value),u&&(u.mouseInChild.value=!1),v.value=setTimeout(()=>{!p.value&&(d==null||d.closeMenu(t.path,l.value))},300),q&&((Z=u==null?void 0:u.handleMouseleave)==null||Z.call(u,!0))}const ee=w(()=>L.value&&t.activeIcon||t.icon),ie=ut({active:L,parentPaths:l,path:t.path});return Qe(()=>{var q,Z;(q=u==null?void 0:u.addSubMenu)==null||q.call(u,ie),(Z=d==null?void 0:d.addSubMenu)==null||Z.call(d,ie)}),ba(()=>{var q,Z;(q=u==null?void 0:u.removeSubMenu)==null||q.call(u,ie),(Z=d==null?void 0:d.removeSubMenu)==null||Z.call(d,ie)}),(q,Z)=>(i(),C("li",{class:R([e(s)(),e(n)("opened",k.value),e(n)("active",L.value),e(n)("disabled",q.disabled)]),onFocus:N,onMouseenter:N,onMouseleave:Z[3]||(Z[3]=()=>X())},[e(d).isMenuPopup?(i(),y(e(Ar),{key:0,"content-class":[e(d).theme,e(r).e("popup-container"),e(n)(e(d).theme,!0),k.value?"":"hidden","overflow-auto","max-h-[calc(var(--radix-hover-card-content-available-height)-20px)]"],"content-props":I.value,open:!0,"open-delay":0},{trigger:c(()=>[b(to,{class:R(e(n)("active",L.value)),icon:ee.value,"is-menu-more":q.isSubMenuMore,"is-top-level-menu-submenu":O.value,level:W.value,path:q.path,onClick:Le($,["stop"])},{title:c(()=>[B(q.$slots,"title")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"])]),default:c(()=>[E("div",{class:R([e(r).is(U.value,!0),e(r).e("popup")]),onFocus:Z[0]||(Z[0]=ye=>N(ye,100)),onMouseenter:Z[1]||(Z[1]=ye=>N(ye,100)),onMouseleave:Z[2]||(Z[2]=()=>X(!0))},[E("ul",{class:R([e(r).b(),e(n)("rounded",_.value)]),style:fe(e(f))},[B(q.$slots,"default")],6)],34)]),_:3},8,["content-class","content-props"])):(i(),C(Q,{key:1},[b(to,{class:R(e(n)("active",L.value)),icon:ee.value,"is-menu-more":q.isSubMenuMore,"is-top-level-menu-submenu":O.value,level:W.value,path:q.path,onClick:Le($,["stop"])},{title:c(()=>[B(q.$slots,"title")]),default:c(()=>[B(q.$slots,"content")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"]),b(uc,null,{default:c(()=>[Ie(E("ul",{class:R([e(r).b(),e(n)("rounded",_.value)]),style:fe(e(f))},[B(q.$slots,"default")],6),[[De,k.value]])]),_:3})],64))],34))}}),cc=V({name:"Menu",__name:"menu",props:{accordion:{type:Boolean,default:!0},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{default:"vertical"},rounded:{type:Boolean,default:!0},scrollToActive:{type:Boolean,default:!1},theme:{default:"dark"}},emits:["close","open","select"],setup(o,{emit:t}){const a=o,l=t,{b:s,is:n}=dt("menu"),r=Jo(),d=Ge(),u=G(),f=G(-1),p=G(a.defaultOpeneds&&!a.collapse?[...a.defaultOpeneds]:[]),h=G(a.defaultActive),m=G({}),v=G({}),k=G(!1),O=w(()=>a.mode==="horizontal"||a.mode==="vertical"&&a.collapse),U=w(()=>{var Me,Ve;const j=(Ve=(Me=d.default)==null?void 0:Me.call(d))!=null?Ve:[],re=Rt(j),te=f.value===-1?re:re.slice(0,f.value),we=f.value===-1?[]:re.slice(f.value);return{showSlotMore:we.length>0,slotDefault:te,slotMore:we}});ve(()=>a.collapse,j=>{j&&(p.value=[])}),ve(m.value,N),ve(()=>a.defaultActive,(j="")=>{m.value[j]||(h.value=""),X(j)});let _;ha(()=>{a.mode==="horizontal"?_=Wn(u,K).stop:_==null||_()}),rc(ut({activePath:h,addMenuItem:ye,addSubMenu:Se,closeMenu:q,handleMenuItemClick:ee,handleSubMenuClick:ie,isMenuPopup:O,openedMenus:p,openMenu:Z,props:a,removeMenuItem:Oe,removeSubMenu:Be,subMenus:v,theme:Rn(a,"theme"),items:m})),Qo({addSubMenu:Se,level:1,mouseInChild:k,removeSubMenu:Be});function W(j){const re=getComputedStyle(j),te=Number.parseInt(re.marginLeft,10),we=Number.parseInt(re.marginRight,10);return j.offsetWidth+te+we||0}function F(){var pe,Fe,ae;if(!u.value)return-1;const j=[...(Fe=(pe=u.value)==null?void 0:pe.childNodes)!=null?Fe:[]].filter(ke=>ke.nodeName!=="#comment"&&(ke.nodeName!=="#text"||ke.nodeValue)),re=46,te=getComputedStyle(u==null?void 0:u.value),we=Number.parseInt(te.paddingLeft,10),Me=Number.parseInt(te.paddingRight,10),Ve=((ae=u.value)==null?void 0:ae.clientWidth)-we-Me;let ue=0,be=0;return j.forEach((ke,H)=>{ue+=W(ke),ue<=Ve-re&&(be=H+1)}),be===j.length?-1:be}function I(j,re=33.34){let te;return()=>{te&&clearTimeout(te),te=setTimeout(()=>{j()},re)}}let L=!0;function K(){if(f.value===F())return;const j=()=>{f.value=-1,We(()=>{f.value=F()})};j(),L?j():I(j)(),L=!1}const P=w(()=>a.scrollToActive&&a.mode==="vertical"&&!a.collapse),{scrollToActiveItem:$}=dc(h,{enable:P,delay:320});ve(h,()=>{$()});function N(){$e().forEach(re=>{const te=v.value[re];te&&Z(re,te.parentPaths)})}function X(j){const re=m.value,te=re[j]||h.value&&re[h.value]||re[a.defaultActive||""];h.value=te?te.path:j}function ee(j){const{collapse:re,mode:te}=a;(te==="horizontal"||re)&&(p.value=[]);const{parentPaths:we,path:Me}=j;!Me||!we||l("select",Me,we)}function ie({parentPaths:j,path:re}){p.value.includes(re)?q(re,j):Z(re,j)}function ce(j){const re=p.value.indexOf(j);re!==-1&&p.value.splice(re,1)}function q(j,re){var te,we;a.accordion&&(p.value=(we=(te=v.value[j])==null?void 0:te.parentPaths)!=null?we:[]),ce(j),l("close",j,re)}function Z(j,re){if(!p.value.includes(j)){if(a.accordion){const te=$e();te.includes(j)&&(re=te),p.value=p.value.filter(we=>re.includes(we))}p.value.push(j),l("open",j,re)}}function ye(j){m.value[j.path]=j}function Se(j){v.value[j.path]=j}function Be(j){Reflect.deleteProperty(v.value,j.path)}function Oe(j){Reflect.deleteProperty(m.value,j.path)}function $e(){const j=h.value&&m.value[h.value];return!j||a.mode==="horizontal"||a.collapse?[]:j.parentPaths}return(j,re)=>(i(),C("ul",{ref_key:"menu",ref:u,class:R([j.theme,e(s)(),e(n)(j.mode,!0),e(n)(j.theme,!0),e(n)("rounded",j.rounded),e(n)("collapse",j.collapse),e(n)("menu-align",j.mode==="horizontal")]),style:fe(e(r)),role:"menu"},[j.mode==="horizontal"&&U.value.showSlotMore?(i(),C(Q,{key:0},[(i(!0),C(Q,null,me(U.value.slotDefault,te=>(i(),y(Ae(te),{key:te.key}))),128)),b(tl,{"is-sub-menu-more":"",path:"sub-menu-more"},{title:c(()=>[b(e(hn),{class:"size-4"})]),default:c(()=>[(i(!0),C(Q,null,me(U.value.slotMore,te=>(i(),y(Ae(te),{key:te.key}))),128))]),_:1})],64)):B(j.$slots,"default",{key:1})],6))}}),al=V({name:"SubMenuUi",__name:"sub-menu",props:{menu:{}},setup(o){const t=o,a=w(()=>{const{menu:l}=t;return Reflect.has(l,"children")&&!!l.children&&l.children.length>0});return(l,s)=>a.value?(i(),y(e(tl),{key:`${l.menu.path}_sub`,"active-icon":l.menu.activeIcon,icon:l.menu.icon,path:l.menu.path},{content:c(()=>[b(e(qo),{badge:l.menu.badge,"badge-type":l.menu.badgeType,"badge-variants":l.menu.badgeVariants,class:"right-6"},null,8,["badge","badge-type","badge-variants"])]),title:c(()=>[E("span",null,S(l.menu.name),1)]),default:c(()=>[(i(!0),C(Q,null,me(l.menu.children||[],n=>(i(),y(al,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},8,["active-icon","icon","path"])):(i(),y(e(ic),{key:l.menu.path,"active-icon":l.menu.activeIcon,badge:l.menu.badge,"badge-type":l.menu.badgeType,"badge-variants":l.menu.badgeVariants,icon:l.menu.icon,path:l.menu.path},{title:c(()=>[E("span",null,S(l.menu.name),1)]),_:1},8,["active-icon","badge","badge-type","badge-variants","icon","path"]))}}),ol=V({name:"MenuView",__name:"menu",props:{menus:{},accordion:{type:Boolean},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(o){const a=nt(o);return(l,s)=>(i(),y(e(cc),Ne(Ze(e(a))),{default:c(()=>[(i(!0),C(Q,null,me(l.menus,n=>(i(),y(al,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},16))}});function Sa(){const o=mt(),t=new Map,a=()=>{o.getRoutes().forEach(d=>{t.set(d.path,d)})};a(),o.afterEach(()=>{a()});const l=r=>{var u,f;if(ra(r))return!0;const d=t.get(r);return(f=(u=d==null?void 0:d.meta)==null?void 0:u.openInNewWindow)!=null?f:!1};return{navigation:r=>J(null,null,function*(){var d;try{const u=t.get(r),{openInNewWindow:f=!1,query:p={}}=(d=u==null?void 0:u.meta)!=null?d:{};ra(r)?Nn(r,{target:"_blank"}):f?yo(r):yield o.push({path:r,query:p})}catch(u){throw console.error("Navigation failed:",u),u}}),willOpenedByWindow:r=>l(r)}}const pc=V({__name:"extra-menu",props:{collapse:{type:Boolean},menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(o){const t=st(),{navigation:a}=Sa();function l(s){return J(this,null,function*(){yield a(s)})}return(s,n)=>{var r;return i(),y(e(ol),{accordion:s.accordion,collapse:s.collapse,"default-active":((r=e(t).meta)==null?void 0:r.activePath)||e(t).path,menus:s.menus,rounded:s.rounded,theme:s.theme,mode:"vertical",onSelect:l},null,8,["accordion","collapse","default-active","menus","rounded","theme"])}}}),ao=V({__name:"menu",props:{menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapse:{type:Boolean},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},emits:["open","select"],setup(o,{emit:t}){const a=o,l=t;function s(r){l("select",r,a.mode)}function n(r,d){l("open",r,d)}return(r,d)=>(i(),y(e(ol),{accordion:r.accordion,collapse:r.collapse,"collapse-show-title":r.collapseShowTitle,"default-active":r.defaultActive,menus:r.menus,mode:r.mode,rounded:r.rounded,"scroll-to-active":"",theme:r.theme,onOpen:n,onSelect:s},null,8,["accordion","collapse","collapse-show-title","default-active","menus","mode","rounded","theme"]))}}),fc=V({__name:"mixed-menu",props:{activePath:{},collapse:{type:Boolean},menus:{},rounded:{type:Boolean},theme:{}},emits:["defaultSelect","enter","select"],setup(o,{emit:t}){const a=o,l=t,s=st();return Mo(()=>{const n=ka(a.menus||[],s.path);if(n){const r=(a.menus||[]).find(d=>{var u;return d.path===((u=n.parents)==null?void 0:u[0])});l("defaultSelect",n,r)}}),(n,r)=>(i(),y(e(sc),{"active-path":n.activePath,collapse:n.collapse,menus:n.menus,rounded:n.rounded,theme:n.theme,onEnter:r[0]||(r[0]=d=>l("enter",d)),onSelect:r[1]||(r[1]=d=>l("select",d))},null,8,["active-path","collapse","menus","rounded","theme"]))}});function mc(o){const t=ht(),{navigation:a,willOpenedByWindow:l}=Sa(),s=w(()=>{var _;return(_=o==null?void 0:o.value)!=null?_:t.accessMenus}),n=new Map,r=G([]),d=st(),u=G([]),f=G(!1),p=G(""),h=w(()=>D.app.layout==="header-mixed-nav"?1:0),m=_=>J(null,null,function*(){var I,L,K;const W=(I=_==null?void 0:_.children)!=null?I:[],F=W.length>0;l(_.path)||(u.value=W!=null?W:[],p.value=(K=(L=_.parents)==null?void 0:L[h.value])!=null?K:_.path,f.value=F),F?D.sidebar.autoActivateChild&&(yield a(n.has(_.path)?n.get(_.path):_.path)):yield a(_.path)}),v=(_,W)=>J(null,null,function*(){var F,I,L,K;u.value=(I=(F=W==null?void 0:W.children)!=null?F:r.value)!=null?I:[],p.value=(K=(L=_.parents)==null?void 0:L[h.value])!=null?K:_.path,D.sidebar.expandOnHover&&(f.value=u.value.length>0)}),k=()=>{var I,L;if(D.sidebar.expandOnHover)return;const{findMenu:_,rootMenu:W,rootMenuPath:F}=Bt(s.value,d.path);p.value=(I=F!=null?F:_==null?void 0:_.path)!=null?I:"",u.value=(L=W==null?void 0:W.children)!=null?L:[]},O=_=>{var W,F,I;if(!D.sidebar.expandOnHover){const{findMenu:L}=Bt(s.value,_.path);u.value=(W=L==null?void 0:L.children)!=null?W:[],p.value=(I=(F=_.parents)==null?void 0:F[h.value])!=null?I:_.path,f.value=u.value.length>0}};function U(_){var K,P,$,N;const W=((K=d.meta)==null?void 0:K.activePath)||_,{findMenu:F,rootMenu:I,rootMenuPath:L}=Bt(s.value,W,h.value);r.value=(P=I==null?void 0:I.children)!=null?P:[],L&&n.set(L,W),p.value=($=L!=null?L:F==null?void 0:F.path)!=null?$:"",u.value=(N=I==null?void 0:I.children)!=null?N:[],D.sidebar.expandOnHover&&(f.value=u.value.length>0)}return ve(()=>[d.path,D.app.layout],([_])=>{U(_||"")},{immediate:!0}),{extraActiveMenu:p,extraMenus:u,handleDefaultSelect:v,handleMenuMouseEnter:O,handleMixedMenuSelect:m,handleSideMouseLeave:k,sidebarExtraVisible:f}}function hc(){const{navigation:o,willOpenedByWindow:t}=Sa(),a=ht(),l=st(),s=G([]),n=G(""),r=G(""),d=G([]),u=new Map,{isMixedNav:f,isHeaderMixedNav:p}=ft(),h=w(()=>D.navigation.split&&f.value||p.value),m=w(()=>{const K=D.sidebar.enable;return h.value?K&&s.value.length>0:K}),v=w(()=>a.accessMenus),k=w(()=>h.value?v.value.map(K=>xe(Y({},K),{children:[]})):v.value),O=w(()=>h.value?s.value:v.value),U=w(()=>p.value?O.value:k.value),_=w(()=>{var K,P;return(P=(K=l==null?void 0:l.meta)==null?void 0:K.activePath)!=null?P:l.path}),W=w(()=>{var K,P;return h.value?n.value:(P=(K=l.meta)==null?void 0:K.activePath)!=null?P:l.path}),F=(K,P)=>{var X,ee;if(!h.value||P==="vertical"){o(K);return}const $=v.value.find(ie=>ie.path===K),N=(X=$==null?void 0:$.children)!=null?X:[];t(K)||(n.value=(ee=$==null?void 0:$.path)!=null?ee:"",s.value=N),N.length===0?o(K):$&&D.sidebar.autoActivateChild&&o(u.has($.path)?u.get($.path):$.path)},I=(K,P)=>{P.length<=1&&D.sidebar.autoActivateChild&&o(u.has(K)?u.get(K):K)};function L(K=l.path){var N,X,ee,ie,ce;let{rootMenu:P}=Bt(v.value,K);P||(P=v.value.find(q=>q.path===K));const $=Bt((P==null?void 0:P.children)||[],K,1);r.value=(N=$.rootMenuPath)!=null?N:"",d.value=(ee=(X=$.rootMenu)==null?void 0:X.children)!=null?ee:[],n.value=(ie=P==null?void 0:P.path)!=null?ie:"",s.value=(ce=P==null?void 0:P.children)!=null?ce:[]}return ve(()=>l.path,K=>{var $,N,X,ee;const P=(ee=(X=($=l==null?void 0:l.meta)==null?void 0:$.activePath)!=null?X:(N=l==null?void 0:l.meta)==null?void 0:N.link)!=null?ee:K;t(P)||(L(P),n.value&&u.set(n.value,P))},{immediate:!0}),Mo(()=>{var K;L(((K=l.meta)==null?void 0:K.activePath)||l.path)}),{handleMenuSelect:F,handleMenuOpen:I,headerActive:W,headerMenus:k,sidebarActive:_,sidebarMenus:O,mixHeaderMenus:U,mixExtraMenus:d,sidebarVisible:m}}const bc={class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold"},vc=V({__name:"tool-more",props:{menus:{}},setup(o){return(t,a)=>(i(),y(e(Er),{menus:t.menus,modal:!1},{default:c(()=>[E("div",bc,[b(e(ca),{class:"size-4"})])]),_:1},8,["menus"]))}}),gc=V({__name:"tool-screen",props:{screen:{type:Boolean},screenModifiers:{}},emits:["update:screen"],setup(o){const t=x(o,"screen");function a(){t.value=!t.value}return(l,s)=>(i(),C("div",{class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold",onClick:a},[t.value?(i(),y(e(Io),{key:0,class:"size-4"})):(i(),y(e(Ao),{key:1,class:"size-4"}))]))}}),yc=["data-active-tab","data-index","onClick","onMousedown"],wc={class:"relative size-full px-1"},xc={key:0,class:"tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"},kc={class:"tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"},Cc={class:"tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"},_c={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},Sc=V({name:"VbenTabsChrome",inheritAttrs:!1,__name:"tabs",props:_e({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{default:7},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:_e(["close","unpin"],["update:active"]),setup(o,{emit:t}){const a=o,l=t,s=x(o,"active"),n=G(),r=G(),d=w(()=>{const{gap:p}=a;return{"--gap":`${p}px`}}),u=w(()=>a.tabs.map(p=>{const{fullPath:h,meta:m,name:v,path:k,key:O}=p||{},{affixTab:U,icon:_,newTabTitle:W,tabClosable:F,title:I}=m||{};return{affixTab:!!U,closable:Reflect.has(m,"tabClosable")?!!F:!0,fullPath:h,icon:_,key:O,meta:m,name:v,path:k,title:W||I||v}}));function f(p,h){p.button===1&&h.closable&&!h.affixTab&&u.value.length>1&&a.middleClickToClose&&(p.preventDefault(),p.stopPropagation(),l("close",h.key))}return(p,h)=>(i(),C("div",{ref_key:"contentRef",ref:n,class:R([p.contentClass,"tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"]),style:fe(d.value)},[b(Kt,{name:"slide-left"},{default:c(()=>[(i(!0),C(Q,null,me(u.value,(m,v)=>(i(),C("div",{key:m.key,ref_for:!0,ref_key:"tabRef",ref:r,class:R([[{"is-active":m.key===s.value,draggable:!m.affixTab,"affix-tab":m.affixTab}],"tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"]),"data-active-tab":s.value,"data-index":v,"data-tab-item":"true",onClick:k=>s.value=m.key,onMousedown:k=>f(k,m)},[b(e(Ho),{"handler-data":m,menus:p.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[E("div",wc,[v!==0&&m.key!==s.value?(i(),C("div",xc)):z("",!0),h[0]||(h[0]=E("div",{class:"tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"},[E("div",{class:"tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"}),E("svg",{class:"tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[E("path",{d:"M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z"})]),E("svg",{class:"tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[E("path",{d:"M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z"})])],-1)),E("div",kc,[Ie(b(e(pt),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all",onClick:Le(()=>l("close",m.key),["stop"])},null,8,["onClick"]),[[De,!m.affixTab&&u.value.length>1&&m.closable]]),Ie(b(e(qt),{class:"hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Le(()=>l("unpin",m),["stop"])},null,8,["onClick"]),[[De,m.affixTab&&u.value.length>1&&m.closable]])]),E("div",Cc,[p.showIcon?(i(),y(e(Ke),{key:0,icon:m.icon,class:"mr-1 flex size-4 items-center overflow-hidden"},null,8,["icon"])):z("",!0),E("span",_c,S(m.title),1)])])]),_:2},1032,["handler-data","menus"])],42,yc))),128))]),_:1})],6))}}),Mc=ze(Sc,[["__scopeId","data-v-08fc8c7f"]]),Tc=["data-index","onClick","onMousedown"],$c={class:"relative flex size-full items-center"},Bc={class:"absolute right-1.5 top-1/2 z-[3] translate-y-[-50%] overflow-hidden"},Vc={class:"text-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mx-3 mr-4 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pr-3 transition-all duration-300"},Ec={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},Lc=V({name:"VbenTabs",inheritAttrs:!1,__name:"tabs",props:_e({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:_e(["close","unpin"],["update:active"]),setup(o,{emit:t}){const a=o,l=t,s=x(o,"active"),n=w(()=>({brisk:{content:"h-full after:content-['']  after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1.5px] after:bg-primary after:scale-x-0 after:transition-[transform] after:ease-out after:duration-300 hover:after:scale-x-100 after:origin-left [&.is-active]:after:scale-x-100 [&:not(:first-child)]:border-l last:border-r last:border-r border-border"},card:{content:"h-[calc(100%-6px)] rounded-md ml-2 border border-border  transition-all"},plain:{content:"h-full [&:not(:first-child)]:border-l last:border-r border-border"}})[a.styleType||"plain"]||{content:""}),r=w(()=>a.tabs.map(u=>{const{fullPath:f,meta:p,name:h,path:m,key:v}=u||{},{affixTab:k,icon:O,newTabTitle:U,tabClosable:_,title:W}=p||{};return{affixTab:!!k,closable:Reflect.has(p,"tabClosable")?!!_:!0,fullPath:f,icon:O,key:v,meta:p,name:h,path:m,title:U||W||h}}));function d(u,f){u.button===1&&f.closable&&!f.affixTab&&r.value.length>1&&a.middleClickToClose&&(u.preventDefault(),u.stopPropagation(),l("close",f.key))}return(u,f)=>(i(),C("div",{class:R([u.contentClass,"relative !flex h-full w-max items-center overflow-hidden pr-6"])},[b(Kt,{name:"slide-left"},{default:c(()=>[(i(!0),C(Q,null,me(r.value,(p,h)=>(i(),C("div",{key:p.key,class:R([[{"is-active dark:bg-accent bg-primary/15":p.key===s.value,draggable:!p.affixTab,"affix-tab":p.affixTab},n.value.content],"tab-item [&:not(.is-active)]:hover:bg-accent translate-all group relative flex cursor-pointer select-none"]),"data-index":h,"data-tab-item":"true",onClick:m=>s.value=p.key,onMousedown:m=>d(m,p)},[b(e(Ho),{"handler-data":p,menus:u.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[E("div",$c,[E("div",Bc,[Ie(b(e(pt),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground dark:group-[.is-active]:text-accent-foreground group-[.is-active]:text-primary size-3 cursor-pointer rounded-full transition-all",onClick:Le(()=>l("close",p.key),["stop"])},null,8,["onClick"]),[[De,!p.affixTab&&r.value.length>1&&p.closable]]),Ie(b(e(qt),{class:"hover:bg-accent hover:stroke-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Le(()=>l("unpin",p),["stop"])},null,8,["onClick"]),[[De,p.affixTab&&r.value.length>1&&p.closable]])]),E("div",Vc,[u.showIcon?(i(),y(e(Ke),{key:0,icon:p.icon,class:"mr-2 flex size-4 items-center overflow-hidden",fallback:""},null,8,["icon"])):z("",!0),E("span",Ec,S(p.title),1)])])]),_:2},1032,["handler-data","menus"])],42,Tc))),128))]),_:1})],2))}});function na(o){const t="group";return o.classList.contains(t)?o:o.closest(`.${t}`)}function zc(o,t){const a=G(null);function l(){return J(this,null,function*(){var u;yield We();const n=(u=document.querySelectorAll(`.${o.contentClass}`))==null?void 0:u[0];if(!n){console.warn("Element not found for sortable initialization");return}const r=()=>J(null,null,function*(){var f;n.style.cursor="default",(f=n.querySelector(".draggable"))==null||f.classList.remove("dragging")}),{initializeSortable:d}=bn(n,{filter:(f,p)=>{const h=na(p);return!(h==null?void 0:h.classList.contains("draggable"))||!o.draggable},onEnd(f){const{newIndex:p,oldIndex:h}=f,{srcElement:m}=f.originalEvent;if(!m){r();return}const v=na(m);if(!v){r();return}if(!v.classList.contains("draggable")){r();return}h!==void 0&&p!==void 0&&!Number.isNaN(h)&&!Number.isNaN(p)&&h!==p&&t("sortTabs",h,p),r()},onMove(f){const p=na(f.related);if(p!=null&&p.classList.contains("draggable")&&o.draggable){const h=f.dragged.classList.contains("affix-tab"),m=f.related.classList.contains("affix-tab");return h===m}else return!1},onStart:()=>{var f;n.style.cursor="grabbing",(f=n.querySelector(".draggable"))==null||f.classList.add("dragging")}});a.value=yield d()})}function s(){return J(this,null,function*(){const{isMobile:n}=fa();n.value||(yield We(),l())})}Qe(s),ve(()=>o.styleType,()=>{var n;(n=a.value)==null||n.destroy(),s()}),At(()=>{var n;(n=a.value)==null||n.destroy()})}function Ac(o){let t=null,a=null,l=0;const s=G(null),n=G(null),r=G(!1),d=G(!0),u=G(!1);function f(){var F;const U=(F=s.value)==null?void 0:F.$el;if(!U||!n.value)return{};const _=U.clientWidth,W=n.value.clientWidth;return{scrollbarWidth:_,scrollViewWidth:W}}function p(U,_=150){var I;const{scrollbarWidth:W,scrollViewWidth:F}=f();!W||!F||W>F||(I=n.value)==null||I.scrollBy({behavior:"smooth",left:U==="left"?-(W-_):+(W-_)})}function h(){return J(this,null,function*(){var W,F;yield We();const U=(W=s.value)==null?void 0:W.$el;if(!U)return;const _=U==null?void 0:U.querySelector("div[data-radix-scroll-area-viewport]");n.value=_,v(),yield We(),m(),t==null||t.disconnect(),t=new ResizeObserver(ia(I=>{v(),m()},100)),t.observe(_),l=((F=o.tabs)==null?void 0:F.length)||0,a==null||a.disconnect(),a=new MutationObserver(()=>{const I=_.querySelectorAll('div[data-tab-item="true"]').length;I>l&&m(),I!==l&&(v(),l=I)}),a.observe(_,{attributes:!1,childList:!0,subtree:!0})})}function m(){return J(this,null,function*(){if(!n.value)return;yield We();const U=n.value,{scrollbarWidth:_}=f(),{scrollWidth:W}=U;_>=W||requestAnimationFrame(()=>{const F=U==null?void 0:U.querySelector(".is-active");F==null||F.scrollIntoView({behavior:"smooth",inline:"start"})})})}function v(){return J(this,null,function*(){if(!n.value)return;const{scrollbarWidth:U}=f();r.value=n.value.scrollWidth>U})}const k=ia(({left:U,right:_})=>{d.value=U,u.value=_},100);function O({deltaY:U}){var _;(_=n.value)==null||_.scrollBy({left:U*3})}return ve(()=>o.active,()=>J(null,null,function*(){m()}),{flush:"post"}),ve(()=>o.styleType,()=>{h()}),Qe(h),At(()=>{t==null||t.disconnect(),a==null||a.disconnect(),t=null,a=null}),{handleScrollAt:k,handleWheel:O,initScrollbar:h,scrollbarRef:s,scrollDirection:p,scrollIsAtLeft:d,scrollIsAtRight:u,showScrollButton:r}}const Pc={class:"flex h-full flex-1 overflow-hidden"},Ic=V({name:"TabsView",__name:"tabs-view",props:{active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{},draggable:{type:Boolean,default:!0},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{default:"chrome"},tabs:{},wheelable:{type:Boolean,default:!0}},emits:["close","sortTabs","unpin"],setup(o,{emit:t}){const a=o,l=t,s=Ue(a,l),{handleScrollAt:n,handleWheel:r,scrollbarRef:d,scrollDirection:u,scrollIsAtLeft:f,scrollIsAtRight:p,showScrollButton:h}=Ac(a);function m(v){a.wheelable&&(r(v),v.stopPropagation(),v.preventDefault())}return zc(a,l),(v,k)=>(i(),C("div",Pc,[Ie(E("span",{class:R([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(f),"pointer-events-none opacity-30":e(f)},"border-r px-2"]),onClick:k[0]||(k[0]=O=>e(u)("left"))},[b(e(vn),{class:"size-4 h-full"})],2),[[De,e(h)]]),E("div",{class:R([{"pt-[3px]":v.styleType==="chrome"},"size-full flex-1 overflow-hidden"])},[b(e(Lt),{ref_key:"scrollbarRef",ref:d,"shadow-bottom":!1,"shadow-top":!1,class:"h-full",horizontal:"","scroll-bar-class":"z-10 hidden ",shadow:"","shadow-left":"","shadow-right":"",onScrollAt:e(n),onWheel:m},{default:c(()=>[v.styleType==="chrome"?(i(),y(e(Mc),Ne(he({key:0},Y(Y(Y({},e(s)),v.$attrs),v.$props))),null,16)):(i(),y(e(Lc),Ne(he({key:1},Y(Y(Y({},e(s)),v.$attrs),v.$props))),null,16))]),_:1},8,["onScrollAt"])],2),Ie(E("span",{class:R([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(p),"pointer-events-none opacity-30":e(p)},"hover:bg-muted text-muted-foreground cursor-pointer border-l px-2"]),onClick:k[1]||(k[1]=O=>e(u)("right"))},[b(e(da),{class:"size-4 h-full"})],2),[[De,e(h)]])]))}});function Oc(){const o=mt(),t=st(),a=ht(),l=_t(),{contentIsMaximize:s,toggleMaximize:n}=Ro(),{closeAllTabs:r,closeCurrentTab:d,closeLeftTabs:u,closeOtherTabs:f,closeRightTabs:p,closeTabByKey:h,getTabDisableState:m,openTabInNewWindow:v,refreshTab:k,toggleTabPin:O}=No(),U=w(()=>Xe(t)),{locale:_}=ho(),W=G();ve([()=>l.getTabs,()=>l.updateTime,()=>_.value],([$])=>{W.value=$.map(N=>K(N))});const F=()=>{const $=Fn(o.getRoutes(),N=>{var X;return!!((X=N.meta)!=null&&X.affixTab)});l.setAffixTabs($)},I=$=>{const{fullPath:N,path:X}=l.getTabByKey($);o.push(N||X)},L=$=>J(null,null,function*(){yield h($)});function K($){var N;return xe(Y({},$),{meta:xe(Y({},$==null?void 0:$.meta),{title:g((N=$==null?void 0:$.meta)==null?void 0:N.title)})})}return ve(()=>a.accessMenus,()=>{F()},{immediate:!0}),ve(()=>t.fullPath,()=>{var N,X;const $=(X=(N=t.matched)==null?void 0:N[t.matched.length-1])==null?void 0:X.meta;l.addTab(xe(Y({},t),{meta:$||t.meta}))},{immediate:!0}),{createContextMenus:$=>{var Se,Be;const{disabledCloseAll:N,disabledCloseCurrent:X,disabledCloseLeft:ee,disabledCloseOther:ie,disabledCloseRight:ce,disabledRefresh:q}=m($),Z=(Be=(Se=$==null?void 0:$.meta)==null?void 0:Se.affixTab)!=null?Be:!1;return[{disabled:X,handler:()=>J(null,null,function*(){yield d($)}),icon:pt,key:"close",text:g("preferences.tabbar.contextMenu.close")},{handler:()=>J(null,null,function*(){yield O($)}),icon:Z?Oo:qt,key:"affix",text:Z?g("preferences.tabbar.contextMenu.unpin"):g("preferences.tabbar.contextMenu.pin")},{handler:()=>J(null,null,function*(){s.value||(yield o.push($.fullPath)),n()}),icon:s.value?Io:Ao,key:s.value?"restore-maximize":"maximize",text:s.value?g("preferences.tabbar.contextMenu.restoreMaximize"):g("preferences.tabbar.contextMenu.maximize")},{disabled:q,handler:()=>k(),icon:xa,key:"reload",text:g("preferences.tabbar.contextMenu.reload")},{handler:()=>J(null,null,function*(){yield v($)}),icon:gs,key:"open-in-new-window",separator:!0,text:g("preferences.tabbar.contextMenu.openInNewWindow")},{disabled:ee,handler:()=>J(null,null,function*(){yield u($)}),icon:rs,key:"close-left",text:g("preferences.tabbar.contextMenu.closeLeft")},{disabled:ce,handler:()=>J(null,null,function*(){yield p($)}),icon:ds,key:"close-right",separator:!0,text:g("preferences.tabbar.contextMenu.closeRight")},{disabled:ie,handler:()=>J(null,null,function*(){yield f($)}),icon:ys,key:"close-other",text:g("preferences.tabbar.contextMenu.closeOther")},{disabled:N,handler:r,icon:is,key:"close-all",text:g("preferences.tabbar.contextMenu.closeAll")}].filter(Oe=>l.getMenuList.includes(Oe.key))},currentActive:U,currentTabs:W,handleClick:I,handleClose:L}}const Dc={class:"flex-center h-full"},Uc=V({name:"LayoutTabbar",__name:"tabbar",props:{showIcon:{type:Boolean},theme:{}},setup(o){const t=st(),a=_t(),{contentIsMaximize:l,toggleMaximize:s}=Ro(),{unpinTab:n}=No(),{createContextMenus:r,currentActive:d,currentTabs:u,handleClick:f,handleClose:p}=Oc(),h=w(()=>{const m=a.getTabByKey(d.value);return r(m).map(k=>xe(Y({},k),{label:k.text,value:k.key}))});return D.tabbar.persist||a.closeOtherTabs(t),(m,v)=>(i(),C(Q,null,[b(e(Ic),{active:e(d),class:R(m.theme),"context-menus":e(r),draggable:e(D).tabbar.draggable,"show-icon":m.showIcon,"style-type":e(D).tabbar.styleType,tabs:e(u),wheelable:e(D).tabbar.wheelable,"middle-click-to-close":e(D).tabbar.middleClickToClose,onClose:e(p),onSortTabs:e(a).sortTabs,onUnpin:e(n),"onUpdate:active":e(f)},null,8,["active","class","context-menus","draggable","show-icon","style-type","tabs","wheelable","middle-click-to-close","onClose","onSortTabs","onUnpin","onUpdate:active"]),E("div",Dc,[e(D).tabbar.showMore?(i(),y(e(vc),{key:0,menus:h.value},null,8,["menus"])):z("",!0),e(D).tabbar.showMaximize?(i(),y(e(gc),{key:1,screen:e(l),onChange:e(s),"onUpdate:screen":e(s)},null,8,["screen","onChange","onUpdate:screen"])):z("",!0)])],64))}}),Qc=V({name:"BasicLayout",__name:"layout",emits:["clearPreferencesAndLogout","clickLogo"],setup(o,{emit:t}){const a=t,{isDark:l,isHeaderNav:s,isMixedNav:n,isMobile:r,isSideMixedNav:d,isHeaderMixedNav:u,isHeaderSidebarNav:f,layout:p,preferencesButtonPosition:h,sidebarCollapsed:m,theme:v}=ft(),k=ht(),{refresh:O}=Wo(),U=w(()=>l.value||D.theme.semiDarkSidebar?"dark":"light"),_=w(()=>l.value||D.theme.semiDarkHeader?"dark":"light"),W=w(()=>{const{collapsedShowTitle:ue}=D.sidebar,be=[];return ue&&m.value&&!n.value&&be.push("mx-auto"),d.value&&be.push("flex-center"),be.join(" ")}),F=w(()=>D.navigation.styleType==="rounded"),I=w(()=>r.value&&m.value?!0:s.value||n.value||f.value?!1:m.value||d.value||u.value),L=w(()=>!r.value&&(s.value||n.value||u.value)),{handleMenuSelect:K,handleMenuOpen:P,headerActive:$,headerMenus:N,sidebarActive:X,sidebarMenus:ee,mixHeaderMenus:ie,sidebarVisible:ce}=hc(),{extraActiveMenu:q,extraMenus:Z,handleDefaultSelect:ye,handleMenuMouseEnter:Se,handleMixedMenuSelect:Be,handleSideMouseLeave:Oe,sidebarExtraVisible:$e}=mc(ie);function j(ue,be=!0){return be?Co(ue,pe=>xe(Y({},Na(pe)),{name:g(pe.name)})):ue.map(pe=>xe(Y({},Na(pe)),{name:g(pe.name)}))}function re(){rt({sidebar:{hidden:!D.sidebar.hidden}})}function te(){a("clearPreferencesAndLogout")}function we(){a("clickLogo")}ve(()=>D.app.layout,ue=>J(null,null,function*(){ue==="sidebar-mixed-nav"&&D.sidebar.hidden&&rt({sidebar:{hidden:!1}})})),ve(gn.global.locale,O,{flush:"post"});const Me=Ge(),Ve=w(()=>Object.keys(Me).filter(ue=>ue.startsWith("header-")));return(ue,be)=>(i(),y(e(Wu),{"sidebar-extra-visible":e($e),"onUpdate:sidebarExtraVisible":be[0]||(be[0]=pe=>va($e)?$e.value=pe:null),"content-compact":e(D).app.contentCompact,"content-compact-width":e(D).app.contentCompactWidth,"content-padding":e(D).app.contentPadding,"content-padding-bottom":e(D).app.contentPaddingBottom,"content-padding-left":e(D).app.contentPaddingLeft,"content-padding-right":e(D).app.contentPaddingRight,"content-padding-top":e(D).app.contentPaddingTop,"footer-enable":e(D).footer.enable,"footer-fixed":e(D).footer.fixed,"footer-height":e(D).footer.height,"header-height":e(D).header.height,"header-hidden":e(D).header.hidden,"header-mode":e(D).header.mode,"header-theme":_.value,"header-toggle-sidebar-button":e(D).widget.sidebarToggle,"header-visible":e(D).header.enable,"is-mobile":e(D).app.isMobile,layout:e(p),"sidebar-collapse":e(D).sidebar.collapsed,"sidebar-collapse-show-title":e(D).sidebar.collapsedShowTitle,"sidebar-enable":e(ce),"sidebar-collapsed-button":e(D).sidebar.collapsedButton,"sidebar-fixed-button":e(D).sidebar.fixedButton,"sidebar-expand-on-hover":e(D).sidebar.expandOnHover,"sidebar-extra-collapse":e(D).sidebar.extraCollapse,"sidebar-extra-collapsed-width":e(D).sidebar.extraCollapsedWidth,"sidebar-hidden":e(D).sidebar.hidden,"sidebar-mixed-width":e(D).sidebar.mixedWidth,"sidebar-theme":U.value,"sidebar-width":e(D).sidebar.width,"side-collapse-width":e(D).sidebar.collapseWidth,"tabbar-enable":e(D).tabbar.enable,"tabbar-height":e(D).tabbar.height,"z-index":e(D).app.zIndex,onSideMouseLeave:e(Oe),onToggleSidebar:re,"onUpdate:sidebarCollapse":be[1]||(be[1]=pe=>e(rt)({sidebar:{collapsed:pe}})),"onUpdate:sidebarEnable":be[2]||(be[2]=pe=>e(rt)({sidebar:{enable:pe}})),"onUpdate:sidebarExpandOnHover":be[3]||(be[3]=pe=>e(rt)({sidebar:{expandOnHover:pe}})),"onUpdate:sidebarExtraCollapse":be[4]||(be[4]=pe=>e(rt)({sidebar:{extraCollapse:pe}}))},yt({logo:c(()=>[e(D).logo.enable?(i(),y(e(Ya),{key:0,fit:e(D).logo.fit,class:R(W.value),collapsed:I.value,src:e(D).logo.source,text:e(D).app.name,theme:L.value?_.value:e(v),onClick:we},yt({_:2},[ue.$slots["logo-text"]?{name:"text",fn:c(()=>[B(ue.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","class","collapsed","src","text","theme"])):z("",!0)]),header:c(()=>[b(e(tc),{theme:e(v),onClearPreferencesAndLogout:te},yt({"user-dropdown":c(()=>[B(ue.$slots,"user-dropdown")]),notification:c(()=>[B(ue.$slots,"notification")]),_:2},[!L.value&&e(D).breadcrumb.enable?{name:"breadcrumb",fn:c(()=>[b(e(Qr),{"hide-when-only-one":e(D).breadcrumb.hideOnlyOne,"show-home":e(D).breadcrumb.showHome,"show-icon":e(D).breadcrumb.showIcon,type:e(D).breadcrumb.styleType},null,8,["hide-when-only-one","show-home","show-icon","type"])]),key:"0"}:void 0,L.value?{name:"menu",fn:c(()=>[b(e(ao),{"default-active":e($),menus:j(e(N)),rounded:F.value,theme:_.value,class:"w-full",mode:"horizontal",onSelect:e(K)},null,8,["default-active","menus","rounded","theme","onSelect"])]),key:"1"}:void 0,me(Ve.value,pe=>({name:pe,fn:c(()=>[B(ue.$slots,pe)])}))]),1032,["theme"])]),menu:c(()=>[b(e(ao),{accordion:e(D).navigation.accordion,collapse:e(D).sidebar.collapsed,"collapse-show-title":e(D).sidebar.collapsedShowTitle,"default-active":e(X),menus:j(e(ee)),rounded:F.value,theme:U.value,mode:"vertical",onOpen:e(P),onSelect:e(K)},null,8,["accordion","collapse","collapse-show-title","default-active","menus","rounded","theme","onOpen","onSelect"])]),"mixed-menu":c(()=>[b(e(fc),{"active-path":e(q),menus:j(e(ie),!1),rounded:F.value,theme:U.value,onDefaultSelect:e(ye),onEnter:e(Se),onSelect:e(Be)},null,8,["active-path","menus","rounded","theme","onDefaultSelect","onEnter","onSelect"])]),"side-extra":c(()=>[b(e(pc),{accordion:e(D).navigation.accordion,collapse:e(D).sidebar.extraCollapse,menus:j(e(Z)),rounded:F.value,theme:U.value},null,8,["accordion","collapse","menus","rounded","theme"])]),"side-extra-title":c(()=>[e(D).logo.enable?(i(),y(e(Ya),{key:0,fit:e(D).logo.fit,text:e(D).app.name,theme:e(v)},yt({_:2},[ue.$slots["logo-text"]?{name:"text",fn:c(()=>[B(ue.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","text","theme"])):z("",!0)]),tabbar:c(()=>[e(D).tabbar.enable?(i(),y(e(Uc),{key:0,"show-icon":e(D).tabbar.showIcon,theme:e(v)},null,8,["show-icon","theme"])):z("",!0)]),content:c(()=>[b(e(Yu))]),extra:c(()=>[B(ue.$slots,"extra"),e(D).app.enableCheckUpdates?(i(),y(e(ei),{key:0,"check-updates-interval":e(D).app.checkUpdatesInterval},null,8,["check-updates-interval"])):z("",!0),e(D).widget.lockScreen?(i(),y(it,{key:1,name:"slide-up"},{default:c(()=>[e(k).isLockScreen?B(ue.$slots,"lock-screen",{key:0}):z("",!0)]),_:3})):z("",!0),e(h).fixed?(i(),y(e(Go),{key:2,class:"z-100 fixed bottom-20 right-0",onClearPreferencesAndLogout:te})):z("",!0),b(e(fr))]),_:2},[e(D).transition.loading?{name:"content-overlay",fn:c(()=>[b(e(Fu))]),key:"0"}:void 0,e(D).footer.enable?{name:"footer",fn:c(()=>[b(e(Ju),null,{default:c(()=>[e(D).copyright.enable?(i(),y(e(Jn),Ne(he({key:0},e(D).copyright)),null,16)):z("",!0)]),_:1})]),key:"1"}:void 0]),1032,["sidebar-extra-visible","content-compact","content-compact-width","content-padding","content-padding-bottom","content-padding-left","content-padding-right","content-padding-top","footer-enable","footer-fixed","footer-height","header-height","header-hidden","header-mode","header-theme","header-toggle-sidebar-button","header-visible","is-mobile","layout","sidebar-collapse","sidebar-collapse-show-title","sidebar-enable","sidebar-collapsed-button","sidebar-fixed-button","sidebar-expand-on-hover","sidebar-extra-collapse","sidebar-extra-collapsed-width","sidebar-hidden","sidebar-mixed-width","sidebar-theme","sidebar-width","side-collapse-width","tabbar-enable","tabbar-height","z-index","onSideMouseLeave"]))}});export{Jc as N,Xc as _,Zc as a,Qc as b,Gu as c,Qr as d,ei as e,ki as f,Ti as g,Bu as h,Go as i,zt as u};
