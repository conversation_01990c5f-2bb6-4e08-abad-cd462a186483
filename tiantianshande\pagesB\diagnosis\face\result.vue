<template>
	<view class="face-result-container">
		<!-- 顶部结果概览 -->
		<view class="result-header">
			<view class="header-content">
				<view class="result-image-section">
					<view class="image-wrapper">
						<image
							class="result-image"
							:src="resultData.face_image"
							mode="aspectFit"
							@error="onImageError"
						/>
						<view class="analysis-badge">
							<text class="badge-text">面诊分析完成</text>
						</view>
					</view>
				</view>

				<view class="result-summary">
					<view class="summary-header">
						<text class="summary-title">面诊分析报告</text>
						<text class="summary-date">{{ currentDate }}</text>
					</view>

					<view class="health-score" v-if="resultData.score">
						<view class="score-container">
							<view class="score-circle">
								<text class="score-number">{{ resultData.score }}</text>
								<text class="score-label">分</text>
							</view>
							<view class="score-details">
								<text class="score-desc">面部健康评分</text>
								<view class="score-level" :class="scoreLevelClass">
									<text class="level-text">{{ scoreLevelText }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 主要诊断结果 -->
		<view class="diagnosis-section">
			<view class="section-header">
				<text class="section-title">面部特征分析</text>
			</view>

			<view class="diagnosis-cards">
				<view class="diagnosis-card" v-for="(card, index) in diagnosisCards" :key="index">
					<view class="card-content">
						<view class="card-header">
							<text class="card-icon">{{ card.icon }}</text>
							<text class="card-title">{{ card.title }}</text>
						</view>
						<view class="card-body">
							<text class="card-value">{{ card.value }}</text>
							<text class="card-desc">{{ card.description }}</text>
						</view>
						<view class="card-indicator" :class="card.status"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 详细分析报告 -->
		<view class="analysis-section">
			<view class="section-header">
				<text class="section-title">详细分析报告</text>
			</view>

			<view class="analysis-tabs">
				<view
					class="tab-item"
					:class="{ active: activeTab === 'features' }"
					@click="switchTab('features')"
				>
					<text class="tab-text">面部特征</text>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'health' }"
					@click="switchTab('health')"
				>
					<text class="tab-text">健康状况</text>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'advice' }"
					@click="switchTab('advice')"
				>
					<text class="tab-text">调理建议</text>
				</view>
			</view>

			<view class="tab-content-wrapper">
				<!-- 面部特征 -->
				<view v-if="activeTab === 'features'" class="content-panel features-panel">
					<view class="feature-grid">
						<view class="feature-item" v-for="(feature, index) in faceFeatures" :key="index">
							<view class="feature-header">
								<text class="feature-name">{{ feature.name }}</text>
								<view class="feature-status" :class="feature.status">
									<text class="status-text">{{ feature.statusText }}</text>
								</view>
							</view>
							<view class="feature-description">
								<text class="description-text">{{ feature.description }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 健康状况 -->
				<view v-if="activeTab === 'health'" class="content-panel health-panel">
					<view class="health-analysis">
						<view class="analysis-item" v-for="(item, index) in healthAnalysis" :key="index">
							<view class="item-header">
								<text class="item-title">{{ item.title }}</text>
								<view class="item-level" :class="item.level">
									<text class="level-text">{{ item.levelText }}</text>
								</view>
							</view>
							<view class="item-content">
								<text class="content-text">{{ item.content }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 调理建议 -->
				<view v-if="activeTab === 'advice'" class="content-panel advice-panel">
					<view class="advice-list">
						<view class="advice-item" v-for="(advice, index) in careAdvice" :key="index">
							<view class="advice-header">
								<text class="advice-icon">{{ advice.icon }}</text>
								<text class="advice-title">{{ advice.title }}</text>
							</view>
							<view class="advice-content">
								<text class="advice-text">{{ advice.content }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 个性化调理建议 -->
		<view class="suggestions-section">
			<view class="section-header">
				<text class="section-title">个性化调理建议</text>
				<text class="section-subtitle">基于面诊结果的专业建议</text>
			</view>

			<view class="suggestions-content">
				<view class="suggestion-category" v-for="(category, index) in suggestionCategories" :key="index">
					<view class="category-header">
						<text class="category-icon">{{ category.icon }}</text>
						<text class="category-title">{{ category.title }}</text>
						<view class="category-priority" :class="category.priority.toLowerCase()">
							<text class="priority-text">{{ category.priority }}</text>
						</view>
					</view>
					<view class="category-items">
						<view class="suggestion-item" v-for="(item, itemIndex) in category.items" :key="itemIndex">
							<text class="item-bullet">•</text>
							<text class="item-text">{{ item }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 商品推荐 -->
		<view class="recommend-section" v-if="recommendProducts.length > 0">
			<view class="section-header">
				<text class="section-title">推荐商品</text>
				<text class="section-subtitle">根据您的面诊结果推荐</text>
			</view>

			<view class="product-list">
				<view class="product-item" v-for="(product, index) in recommendProducts" :key="index" @click="goToProduct(product)">
					<view class="product-image">
						<image :src="product.image" mode="aspectFill"/>
					</view>
					<view class="product-info">
						<text class="product-name">{{ product.name }}</text>
						<text class="product-desc">{{ product.description }}</text>
						<view class="product-price">
							<text class="price-symbol">¥</text>
							<text class="price-value">{{ product.price }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="action-section">
			<view class="action-buttons">
				<view class="action-btn secondary" @click="retakePhoto" :class="{ loading: isRetaking }">
					<text class="btn-icon">🔄</text>
					<text class="btn-text">{{ isRetaking ? '处理中...' : '重新检测' }}</text>
				</view>
				<view class="action-btn primary" @click="saveReport" :class="{ loading: isSaving }">
					<text class="btn-icon">💾</text>
					<text class="btn-text">{{ isSaving ? '保存中...' : '保存报告' }}</text>
				</view>
				<view class="action-btn secondary" @click="shareReport" :class="{ loading: isSharing }">
					<text class="btn-icon">📤</text>
					<text class="btn-text">{{ isSharing ? '分享中...' : '分享报告' }}</text>
				</view>
			</view>

			<!-- 专业建议提示 -->
			<view v-if="shouldConsult" class="consult-tip">
				<text class="tip-icon">⚠️</text>
				<text class="tip-text">建议咨询专业医师获取更详细的诊疗建议</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			recordId: '',
			resultData: {},
			activeTab: 'features',
			faceFeatures: [],
			healthAnalysis: [],
			careAdvice: [],
			recommendProducts: [],
			diagnosisCards: [],

			// 个性化调理建议分类
			suggestionCategories: [
				{
					icon: '🍽️',
					title: '饮食调理',
					priority: '重要',
					items: [
						'多食用富含维生素C的水果，如柑橘、猕猴桃等',
						'适量摄入优质蛋白质，如鱼类、豆制品',
						'避免辛辣刺激性食物，减少油腻食品摄入',
						'保持饮食规律，三餐定时定量',
						'多喝温开水，促进新陈代谢'
					]
				},
				{
					icon: '🏃‍♂️',
					title: '运动养生',
					priority: '推荐',
					items: [
						'每天进行30分钟有氧运动，如快走、游泳',
						'练习面部按摩，促进血液循环',
						'适当进行瑜伽或太极，调节身心',
						'避免过度疲劳，注意劳逸结合',
						'运动后注意面部清洁和保湿'
					]
				},
				{
					icon: '😴',
					title: '作息调理',
					priority: '重要',
					items: [
						'保持规律作息，晚上11点前入睡',
						'保证充足睡眠，每天7-8小时',
						'避免熬夜，减少电子设备使用',
						'睡前进行面部护理，使用温和护肤品',
						'创造良好睡眠环境，保持室内通风'
					]
				},
				{
					icon: '🧴',
					title: '护肤保养',
					priority: '建议',
					items: [
						'选择适合肤质的温和洁面产品',
						'定期使用保湿面膜，补充肌肤水分',
						'注意防晒，避免紫外线伤害',
						'使用含有抗氧化成分的护肤品',
						'定期去角质，促进肌肤新陈代谢'
					]
				},
				{
					icon: '🧘‍♀️',
					title: '情志调养',
					priority: '建议',
					items: [
						'保持心情愉悦，避免过度焦虑',
						'学会释放压力，适当放松身心',
						'培养兴趣爱好，丰富精神生活',
						'与家人朋友多交流，获得情感支持',
						'遇到困难时保持乐观积极的心态'
					]
				}
			],

			// 按钮状态控制
			isRetaking: false,        // 重新检测按钮加载状态
			isSaving: false,          // 保存报告按钮加载状态
			isSharing: false,         // 分享报告按钮加载状态
			isReportSaved: false,     // 报告是否已保存
			shouldConsult: false      // 是否建议咨询医师（基于分析结果）
		}
	},
	computed: {
		currentDate() {
			const now = new Date();
			return now.getFullYear() + '-' +
				   String(now.getMonth() + 1).padStart(2, '0') + '-' +
				   String(now.getDate()).padStart(2, '0');
		},
		scoreLevelClass() {
			if (!this.resultData.score) return 'normal';
			const score = parseInt(this.resultData.score);
			if (score >= 80) return 'excellent';
			if (score >= 60) return 'good';
			if (score >= 40) return 'normal';
			return 'poor';
		},
		scoreLevelText() {
			if (!this.resultData.score) return '正常';
			const score = parseInt(this.resultData.score);
			if (score >= 80) return '优秀';
			if (score >= 60) return '良好';
			if (score >= 40) return '正常';
			return '需要关注';
		}
	},
	onLoad(options) {
		if (options.recordId) {
			this.recordId = options.recordId;
			this.getAnalysisRecord();
		}
	},
	methods: {
		// 获取分析记录 - 2025-07-17 修改为使用新的面诊接口
		getAnalysisRecord() {
			console.log('2025-07-17 INFO-[face-result][getAnalysisRecord_001] 开始获取面诊分析记录');

			const app = getApp();

			uni.showLoading({
				title: '加载中...'
			});

			// 2025-07-17 使用新的面诊接口获取记录
			app.post('ApiFaceAnalysis/getRecord', {
				id: this.recordId
			}, (response) => {
				uni.hideLoading();
				console.log('2025-07-17 INFO-[face-result][getAnalysisRecord_002] 获取面诊记录结果:', response);

				if (response && response.code === 1) {
					console.log('2025-07-17 INFO-[face-result][getAnalysisRecord_003] 获取记录成功，开始解析数据');
					this.resultData = response.data;
					this.parseNewAnalysisResult();
				} else {
					console.error('2025-07-17 ERROR-[face-result][getAnalysisRecord_004] 获取记录失败:', response?.msg);
					uni.showToast({
						title: response?.msg || '获取记录失败',
						icon: 'none'
					});

					// 降级到模拟数据，确保页面能正常显示
					this.loadMockData();
				}
			}, (error) => {
				uni.hideLoading();
				console.error('2025-07-17 ERROR-[face-result][getAnalysisRecord_005] 获取记录接口调用失败:', error);

				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});

				// 降级到模拟数据，确保页面能正常显示
				this.loadMockData();
			});
		},

		// 新增：加载模拟数据作为降级处理
		loadMockData() {
			console.log('2025-01-26 12:00:00,015-INFO-[face-result][loadMockData_001] 加载模拟数据');

			// 设置模拟的面诊结果数据
			this.resultData = {
				face_image: '/static/img/default-face.png',
				score: 85,
				analysis_result: JSON.stringify({
					face_color_status: 'normal',
					face_color_text: '正常',
					face_color_desc: '面色红润，气血充足',
					eye_status: 'normal',
					eye_text: '有神',
					eye_desc: '眼神明亮，精神状态良好',
					lip_status: 'normal',
					lip_text: '正常',
					lip_desc: '唇色淡红，血液循环良好',
					luster_status: 'normal',
					luster_text: '正常',
					luster_desc: '面部有光泽，皮肤状态良好',
					qi_blood_level: 'normal',
					qi_blood_text: '正常',
					qi_blood_desc: '气血运行正常，面色红润有光泽',
					organ_level: 'normal',
					organ_text: '正常',
					organ_desc: '脏腑功能正常，面部特征反映良好',
					spirit_level: 'normal',
					spirit_text: '良好',
					spirit_desc: '精神状态良好，眼神有神',
					diet_advice: '保持均衡饮食，多吃新鲜蔬果，少食辛辣刺激食物',
					sleep_advice: '保持规律作息，早睡早起，保证充足睡眠',
					exercise_advice: '适当运动，增强体质，促进气血循环',
					emotion_advice: '保持心情愉悦，避免过度焦虑和压力',
					eye_feature: '正常',
					eye_feature_desc: '眼神明亮有神',
					eye_feature_status: 'normal',
					lip_feature: '正常',
					lip_feature_desc: '唇色淡红润泽',
					lip_feature_status: 'normal',
					face_luster: '正常',
					face_luster_desc: '面部有光泽',
					face_luster_status: 'normal'
				})
			};

			// 解析模拟数据
			this.parseAnalysisResult();
		},

		// 2025-07-17 新增：解析新面诊接口的分析结果
		parseNewAnalysisResult() {
			try {
				console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_001] 开始解析新面诊分析结果');
				console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_002] 原始数据:', this.resultData);

				// 从新接口数据中提取信息
				const analysisResult = this.resultData.analysis_result || {};
				let analysisData = {};

				// 处理 analysis_result 数据
				if (typeof analysisResult === 'string') {
					analysisData = JSON.parse(analysisResult);
				} else if (typeof analysisResult === 'object') {
					analysisData = analysisResult;
				}

				console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_003] 解析后的分析数据:', analysisData);

				// 检查是否有新接口的数据结构
				if (analysisData.raw_report_data) {
					console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_004] 发现新接口数据结构');
					this.parseNewApiData(analysisData.raw_report_data);
				} else if (analysisData.physique_name || analysisData.score) {
					console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_005] 发现API数据结构');
					this.parseNewApiData(analysisData);
				} else {
					console.log('2025-07-17 INFO-[face-result][parseNewAnalysisResult_006] 使用旧版解析方法');
					this.parseAnalysisResult();
					return;
				}

				// 获取推荐商品
				this.getRecommendProducts();

			} catch (error) {
				console.error('2025-07-17 ERROR-[face-result][parseNewAnalysisResult_007] 解析新面诊结果失败:', error);
				// 回退到旧版解析方法
				this.parseAnalysisResult();
			}
		},

		// 2025-07-17 新增：解析新API数据
		parseNewApiData(apiData) {
			console.log('2025-07-17 INFO-[face-result][parseNewApiData_001] 开始解析新API数据:', apiData);

			// 更新基础信息
			if (apiData.score) {
				this.resultData.score = apiData.score;
			}

			// 解析面部特征 - 从 features 数组中提取面部特征
			if (apiData.features && Array.isArray(apiData.features)) {
				this.parseNewFaceFeatures(apiData.features);
			} else {
				this.parseDefaultFaceFeatures();
			}

			// 解析健康分析
			this.parseNewHealthAnalysis(apiData);

			// 解析调理建议 - 从 advices 对象中提取
			if (apiData.advices) {
				this.parseNewCareAdvice(apiData.advices);
			} else {
				this.parseDefaultCareAdvice();
			}

			// 解析诊断卡片
			this.parseNewDiagnosisCards(apiData);
		},

		// 2025-07-17 新增：解析新API的面部特征
		parseNewFaceFeatures(features) {
			console.log('2025-07-17 INFO-[face-result][parseNewFaceFeatures_001] 解析面部特征:', features);

			this.faceFeatures = [];

			features.forEach(feature => {
				if (feature.feature_category === '面部') {
					const status = feature.feature_situation === '正常' ? 'normal' :
								  feature.feature_situation === '异常' ? 'warning' : 'normal';

					this.faceFeatures.push({
						name: feature.feature_group || feature.feature_name,
						status: status,
						statusText: feature.feature_situation || '正常',
						description: feature.feature_interpret || '特征正常'
					});
				}
			});

			// 如果没有面部特征，使用默认数据
			if (this.faceFeatures.length === 0) {
				this.parseDefaultFaceFeatures();
			}
		},

		// 2025-07-17 新增：解析默认面部特征
		parseDefaultFaceFeatures() {
			this.faceFeatures = [
				{
					name: '面色',
					status: 'normal',
					statusText: '正常',
					description: '面色红润，气血充足'
				},
				{
					name: '眼神',
					status: 'normal',
					statusText: '有神',
					description: '眼神明亮，精神状态良好'
				},
				{
					name: '唇色',
					status: 'normal',
					statusText: '正常',
					description: '唇色淡红，血液循环良好'
				},
				{
					name: '面部光泽',
					status: 'normal',
					statusText: '正常',
					description: '面部有光泽，皮肤状态良好'
				}
			];
		},

		// 2025-07-17 新增：解析新API的健康分析
		parseNewHealthAnalysis(apiData) {
			const score = apiData.score || 85;
			const physiqueName = apiData.physique_name || '未知体质';
			const physiqueAnalysis = apiData.physique_analysis || '体质分析中...';
			const riskWarning = apiData.risk_warning || '请注意保持健康的生活方式';

			this.healthAnalysis = [
				{
					title: '体质类型',
					level: score >= 80 ? 'normal' : score >= 60 ? 'warning' : 'danger',
					levelText: physiqueName,
					content: physiqueAnalysis
				},
				{
					title: '健康评分',
					level: score >= 80 ? 'normal' : score >= 60 ? 'warning' : 'danger',
					levelText: score + '分',
					content: this.getScoreDescription(score)
				},
				{
					title: '风险提示',
					level: score >= 80 ? 'normal' : score >= 60 ? 'warning' : 'danger',
					levelText: score >= 80 ? '低风险' : score >= 60 ? '中等风险' : '高风险',
					content: riskWarning
				}
			];
		},

		// 2025-07-17 新增：解析新API的调理建议
		parseNewCareAdvice(advices) {
			console.log('2025-07-17 INFO-[face-result][parseNewCareAdvice_001] 解析调理建议:', advices);

			this.careAdvice = [];

			// 饮食建议
			if (advices.food && Array.isArray(advices.food)) {
				const foodAdvice = advices.food.map(item => item.advice || item.title).join('；');
				this.careAdvice.push({
					icon: '🍎',
					title: '饮食调理',
					content: foodAdvice
				});
			}

			// 运动建议
			if (advices.sport && Array.isArray(advices.sport)) {
				const sportAdvice = advices.sport.map(item => item.advice || item.title).join('；');
				this.careAdvice.push({
					icon: '🏃',
					title: '运动调理',
					content: sportAdvice
				});
			}

			// 生活建议
			if (advices.sleep && Array.isArray(advices.sleep)) {
				const sleepAdvice = advices.sleep.map(item => item.advice || item.title).join('；');
				this.careAdvice.push({
					icon: '💤',
					title: '作息调理',
					content: sleepAdvice
				});
			}

			// 治疗建议
			if (advices.treatment && Array.isArray(advices.treatment)) {
				const treatmentAdvice = advices.treatment.map(item => item.advice || item.title).join('；');
				this.careAdvice.push({
					icon: '💊',
					title: '治疗建议',
					content: treatmentAdvice
				});
			}

			// 如果没有建议，使用默认数据
			if (this.careAdvice.length === 0) {
				this.parseDefaultCareAdvice();
			}
		},

		// 2025-07-17 新增：解析默认调理建议
		parseDefaultCareAdvice() {
			this.careAdvice = [
				{
					icon: '🍎',
					title: '饮食调理',
					content: '保持均衡饮食，多吃新鲜蔬果，少食辛辣刺激食物'
				},
				{
					icon: '💤',
					title: '作息调理',
					content: '保持规律作息，早睡早起，保证充足睡眠'
				},
				{
					icon: '🏃',
					title: '运动调理',
					content: '适当运动，增强体质，促进气血循环'
				},
				{
					icon: '😌',
					title: '情志调理',
					content: '保持心情愉悦，避免过度焦虑和压力'
				}
			];
		},

		// 2025-07-17 新增：解析新API的诊断卡片
		parseNewDiagnosisCards(apiData) {
			const features = apiData.features || [];
			this.diagnosisCards = [];

			// 从特征中提取关键信息
			const faceFeatures = features.filter(f => f.feature_category === '面部');

			if (faceFeatures.length > 0) {
				faceFeatures.slice(0, 3).forEach(feature => {
					const status = feature.feature_situation === '正常' ? 'normal' :
								  feature.feature_situation === '异常' ? 'warning' : 'normal';

					this.diagnosisCards.push({
						icon: this.getFeatureIcon(feature.feature_group),
						title: feature.feature_group || feature.feature_name,
						value: feature.feature_name,
						description: feature.feature_interpret || '特征正常',
						status: status
					});
				});
			} else {
				// 使用默认诊断卡片
				this.diagnosisCards = [
					{
						icon: '👁️',
						title: '眼部特征',
						value: '正常',
						description: '眼神明亮有神',
						status: 'normal'
					},
					{
						icon: '👄',
						title: '唇部特征',
						value: '正常',
						description: '唇色淡红润泽',
						status: 'normal'
					},
					{
						icon: '🌟',
						title: '面部光泽',
						value: '正常',
						description: '面部有光泽',
						status: 'normal'
					}
				];
			}
		},

		// 2025-07-17 新增：根据特征组获取图标
		getFeatureIcon(featureGroup) {
			const iconMap = {
				'面色': '🎨',
				'面部光泽': '🌟',
				'眼神': '👁️',
				'眉头紧皱': '😤',
				'眉毛浓淡': '👁️‍🗨️',
				'眼皮层数': '👁️',
				'唇型': '👄',
				'唇色': '💋'
			};
			return iconMap[featureGroup] || '📋';
		},

		// 2025-07-17 新增：根据评分获取描述
		getScoreDescription(score) {
			if (score >= 90) return '健康状况优秀，请继续保持良好的生活习惯';
			if (score >= 80) return '健康状况良好，建议适当调理以维持最佳状态';
			if (score >= 70) return '健康状况一般，建议加强调理和保养';
			if (score >= 60) return '健康状况偏差，建议及时调理改善';
			return '健康状况较差，建议咨询专业医师进行调理';
		},

		// 解析分析结果（保留旧版本兼容）
		parseAnalysisResult() {
			try {
				console.log('2025-01-26 12:00:00,016-INFO-[face-result][parseAnalysisResult_001] 开始解析分析结果');
				console.log('2025-01-26 12:00:00,017-INFO-[face-result][parseAnalysisResult_002] 原始数据:', this.resultData);

				let analysisData = {};

				// 处理不同格式的 analysis_result
				if (typeof this.resultData.analysis_result === 'string') {
					analysisData = JSON.parse(this.resultData.analysis_result || '{}');
				} else if (typeof this.resultData.analysis_result === 'object') {
					analysisData = this.resultData.analysis_result || {};
				}

				console.log('2025-01-26 12:00:00,018-INFO-[face-result][parseAnalysisResult_003] 解析后的分析数据:', analysisData);

				// 检查是否是API响应格式（包含data.data结构）
				if (analysisData.data && analysisData.data.data) {
					console.log('2025-01-26 12:00:00,019-INFO-[face-result][parseAnalysisResult_004] 检测到API响应格式，提取实际数据');
					const apiData = analysisData.data.data;
					console.log('2025-01-26 12:00:00,020-INFO-[face-result][parseAnalysisResult_005] API数据:', apiData);

					// 从API数据中提取面诊相关信息
					analysisData = this.extractFaceAnalysisFromApi(apiData);
				}

				// 如果还是没有有效数据，尝试从其他字段获取
				if (!analysisData || Object.keys(analysisData).length === 0) {
					console.log('2025-01-26 12:00:00,020-INFO-[face-result][parseAnalysisResult_005] 分析数据为空，使用默认数据');
					analysisData = this.getDefaultAnalysisData();
				}

				// 解析面部特征
				this.parseFaceFeatures(analysisData);

				// 解析健康分析
				this.parseHealthAnalysis(analysisData);

				// 解析调理建议
				this.parseCareAdvice(analysisData);

				// 解析诊断卡片
				this.parseDiagnosisCards(analysisData);

				// 获取推荐商品
				this.getRecommendProducts();

			} catch (error) {
				console.error('2025-01-26 12:00:00,021-ERROR-[face-result][parseAnalysisResult_006] 解析分析结果失败:', error);
				// 使用默认数据
				const defaultData = this.getDefaultAnalysisData();
				this.parseFaceFeatures(defaultData);
				this.parseHealthAnalysis(defaultData);
				this.parseCareAdvice(defaultData);
				this.parseDiagnosisCards(defaultData);
			}
		},

		// 从API数据中提取面诊分析信息
		extractFaceAnalysisFromApi(apiData) {
			console.log('2025-01-26 12:00:00,022-INFO-[face-result][extractFaceAnalysisFromApi_001] 开始提取面诊数据');

			// 初始化分析数据
			let faceAnalysis = this.getDefaultAnalysisData();

			try {
				// 检查是否有体质信息
				if (apiData.physique_name) {
					// 更新评分信息
					this.resultData.score = apiData.score || 85;

					// 根据体质类型设置面部特征分析
					const physiqueName = apiData.physique_name;
					const score = apiData.score || 85;

					// 根据体质和评分生成面诊分析
					faceAnalysis = this.generateFaceAnalysisFromPhysique(physiqueName, score);
				}

				// 检查是否有症状信息
				if (apiData.typical_symptom) {
					faceAnalysis.symptoms = apiData.typical_symptom;
				}

				// 检查是否有建议信息
				if (apiData.advices) {
					if (apiData.advices.food) {
						faceAnalysis.diet_advice = Array.isArray(apiData.advices.food)
							? apiData.advices.food.join('；')
							: apiData.advices.food;
					}
					if (apiData.advices.sport) {
						faceAnalysis.exercise_advice = Array.isArray(apiData.advices.sport)
							? apiData.advices.sport.join('；')
							: apiData.advices.sport;
					}
					if (apiData.advices.life) {
						faceAnalysis.sleep_advice = Array.isArray(apiData.advices.life)
							? apiData.advices.life.join('；')
							: apiData.advices.life;
					}
				}

				console.log('2025-01-26 12:00:00,023-INFO-[face-result][extractFaceAnalysisFromApi_002] 提取的面诊数据:', faceAnalysis);

			} catch (error) {
				console.error('2025-01-26 12:00:00,024-ERROR-[face-result][extractFaceAnalysisFromApi_003] 提取面诊数据失败:', error);
			}

			return faceAnalysis;
		},

		// 根据体质信息生成面诊分析
		generateFaceAnalysisFromPhysique(physiqueName, score) {
			const analysis = this.getDefaultAnalysisData();

			// 根据评分设置状态
			const getStatusByScore = (score) => {
				if (score >= 80) return { status: 'normal', text: '良好' };
				if (score >= 60) return { status: 'normal', text: '正常' };
				if (score >= 40) return { status: 'warning', text: '需关注' };
				return { status: 'danger', text: '需调理' };
			};

			const statusInfo = getStatusByScore(score);

			// 根据体质类型调整分析结果
			const physiqueAnalysis = {
				'平和质': {
					face_color_desc: '面色红润有光泽，气血充足，体质平和',
					eye_desc: '眼神明亮有神，精神状态良好',
					lip_desc: '唇色淡红润泽，血液循环良好'
				},
				'气虚质': {
					face_color_desc: '面色偏淡，可能存在气虚现象，需要补气调理',
					eye_desc: '眼神略显疲倦，精神状态一般，建议多休息',
					lip_desc: '唇色偏淡，可能气血不足'
				},
				'阳虚质': {
					face_color_desc: '面色偏白，阳气不足，需要温阳调理',
					eye_desc: '眼神缺乏神采，精神状态欠佳',
					lip_desc: '唇色偏淡，循环较差'
				},
				'阴虚质': {
					face_color_desc: '面色偏红，可能阴虚火旺，需要滋阴润燥',
					eye_desc: '眼神略显干涩，需要滋阴调理',
					lip_desc: '唇色偏红，可能内热较重'
				}
			};

			const physiqueInfo = physiqueAnalysis[physiqueName] || physiqueAnalysis['平和质'];

			// 更新分析结果
			analysis.face_color_status = statusInfo.status;
			analysis.face_color_text = statusInfo.text;
			analysis.face_color_desc = physiqueInfo.face_color_desc;

			analysis.eye_status = statusInfo.status;
			analysis.eye_text = statusInfo.text;
			analysis.eye_desc = physiqueInfo.eye_desc;

			analysis.lip_status = statusInfo.status;
			analysis.lip_text = statusInfo.text;
			analysis.lip_desc = physiqueInfo.lip_desc;

			// 设置健康分析
			analysis.qi_blood_text = statusInfo.text;
			analysis.qi_blood_desc = `根据面诊分析，您的体质类型为${physiqueName}，评分${score}分，${physiqueInfo.face_color_desc}`;

			return analysis;
		},

		// 获取默认分析数据
		getDefaultAnalysisData() {
			return {
				face_color_status: 'normal',
				face_color_text: '正常',
				face_color_desc: '面色红润，气血充足',
				eye_status: 'normal',
				eye_text: '有神',
				eye_desc: '眼神明亮，精神状态良好',
				lip_status: 'normal',
				lip_text: '正常',
				lip_desc: '唇色淡红，血液循环良好',
				luster_status: 'normal',
				luster_text: '正常',
				luster_desc: '面部有光泽，皮肤状态良好',
				qi_blood_level: 'normal',
				qi_blood_text: '正常',
				qi_blood_desc: '气血运行正常，面色红润有光泽',
				organ_level: 'normal',
				organ_text: '正常',
				organ_desc: '脏腑功能正常，面部特征反映良好',
				spirit_level: 'normal',
				spirit_text: '良好',
				spirit_desc: '精神状态良好，眼神有神',
				diet_advice: '保持均衡饮食，多吃新鲜蔬果，少食辛辣刺激食物',
				sleep_advice: '保持规律作息，早睡早起，保证充足睡眠',
				exercise_advice: '适当运动，增强体质，促进气血循环',
				emotion_advice: '保持心情愉悦，避免过度焦虑和压力',
				eye_feature: '正常',
				eye_feature_desc: '眼神明亮有神',
				eye_feature_status: 'normal',
				lip_feature: '正常',
				lip_feature_desc: '唇色淡红润泽',
				lip_feature_status: 'normal',
				face_luster: '正常',
				face_luster_desc: '面部有光泽',
				face_luster_status: 'normal'
			};
		},

		// 解析面部特征
		parseFaceFeatures(data) {
			this.faceFeatures = [
				{
					name: '面色',
					status: data.face_color_status || 'normal',
					statusText: data.face_color_text || '正常',
					description: data.face_color_desc || '面色红润，气血充足'
				},
				{
					name: '眼神',
					status: data.eye_status || 'normal',
					statusText: data.eye_text || '有神',
					description: data.eye_desc || '眼神明亮，精神状态良好'
				},
				{
					name: '唇色',
					status: data.lip_status || 'normal',
					statusText: data.lip_text || '正常',
					description: data.lip_desc || '唇色淡红，血液循环良好'
				},
				{
					name: '面部光泽',
					status: data.luster_status || 'normal',
					statusText: data.luster_text || '正常',
					description: data.luster_desc || '面部有光泽，皮肤状态良好'
				}
			];
		},

		// 解析健康分析
		parseHealthAnalysis(data) {
			this.healthAnalysis = [
				{
					title: '气血状况',
					level: data.qi_blood_level || 'normal',
					levelText: data.qi_blood_text || '正常',
					content: data.qi_blood_desc || '气血运行正常，面色红润有光泽'
				},
				{
					title: '脏腑功能',
					level: data.organ_level || 'normal',
					levelText: data.organ_text || '正常',
					content: data.organ_desc || '脏腑功能正常，面部特征反映良好'
				},
				{
					title: '精神状态',
					level: data.spirit_level || 'normal',
					levelText: data.spirit_text || '良好',
					content: data.spirit_desc || '精神状态良好，眼神有神'
				}
			];
		},

		// 解析调理建议
		parseCareAdvice(data) {
			this.careAdvice = [
				{
					icon: '🍎',
					title: '饮食调理',
					content: data.diet_advice || '保持均衡饮食，多吃新鲜蔬果，少食辛辣刺激食物'
				},
				{
					icon: '💤',
					title: '作息调理',
					content: data.sleep_advice || '保持规律作息，早睡早起，保证充足睡眠'
				},
				{
					icon: '🏃',
					title: '运动调理',
					content: data.exercise_advice || '适当运动，增强体质，促进气血循环'
				},
				{
					icon: '😌',
					title: '情志调理',
					content: data.emotion_advice || '保持心情愉悦，避免过度焦虑和压力'
				}
			];
		},

		// 解析诊断卡片
		parseDiagnosisCards(data) {
			this.diagnosisCards = [
				{
					icon: '👁️',
					title: '眼部特征',
					value: data.eye_feature || '正常',
					description: data.eye_feature_desc || '眼神明亮有神',
					status: data.eye_feature_status || 'normal'
				},
				{
					icon: '👄',
					title: '唇部特征',
					value: data.lip_feature || '正常',
					description: data.lip_feature_desc || '唇色淡红润泽',
					status: data.lip_feature_status || 'normal'
				},
				{
					icon: '🌟',
					title: '面部光泽',
					value: data.face_luster || '正常',
					description: data.face_luster_desc || '面部有光泽',
					status: data.face_luster_status || 'normal'
				}
			];
		},

		// 获取推荐商品 - 2025-07-17 修改为使用新的面诊接口
		getRecommendProducts() {
			const app = getApp();

			// 2025-07-17 使用新的面诊接口获取推荐商品
			app.post('ApiFaceAnalysis/getRecommendProducts', {
				record_id: this.recordId,
				diagnosis_type: 2 // 面诊
			}, (response) => {
				if (response && response.code === 1) {
					this.recommendProducts = response.data || [];
				}
			}, (error) => {
				console.log('2025-07-17 INFO-[face-result][getRecommendProducts] 获取推荐商品失败，使用空数组');
				this.recommendProducts = [];
			});
		},

		// 切换标签
		switchTab(tab) {
			this.activeTab = tab;
		},

		// 图片加载错误
		onImageError() {
			console.log('图片加载失败');
		},

		// 返回
		goBack() {
			uni.navigateBack();
		},

		// 重新检测
		retakePhoto() {
			console.log('2025-07-17 INFO-[face-result][retakePhoto_001] 重新检测按钮被点击');

			if (this.isRetaking) return;

			this.isRetaking = true;

			// 添加触觉反馈
			uni.vibrateShort();

			// 跳转到拍摄页面
			uni.redirectTo({
				url: '/pagesB/diagnosis/face/camera-new',
				success: () => {
					console.log('2025-07-17 INFO-[face-result][retakePhoto_002] 跳转到拍摄页面成功');
				},
				fail: (error) => {
					console.error('2025-07-17 ERROR-[face-result][retakePhoto_003] 跳转失败:', error);
					this.isRetaking = false;
					uni.showToast({
						title: '跳转失败，请重试',
						icon: 'none'
					});
				}
			});
		},

		// 保存报告
		saveReport() {
			console.log('2025-07-17 INFO-[face-result][saveReport_001] 保存报告按钮被点击');

			if (this.isSaving || this.isReportSaved) return;

			this.isSaving = true;

			// 添加触觉反馈
			uni.vibrateShort();

			// 模拟保存过程
			setTimeout(() => {
				this.isSaving = false;
				this.isReportSaved = true;

				uni.showToast({
					title: '报告保存成功',
					icon: 'success'
				});

				console.log('2025-07-17 INFO-[face-result][saveReport_002] 报告保存成功');
			}, 2000);
		},

		// 分享报告
		shareReport() {
			console.log('2025-07-17 INFO-[face-result][shareReport_001] 分享报告按钮被点击');

			if (this.isSharing) return;

			this.isSharing = true;

			// 添加触觉反馈
			uni.vibrateShort();

			// 模拟分享过程
			setTimeout(() => {
				this.isSharing = false;

				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});

				console.log('2025-07-17 INFO-[face-result][shareReport_002] 分享完成');
			}, 1000);
		},

		// 分享结果（保留原方法兼容性）
		shareResult() {
			this.shareReport();
		},

		// 获取面部特征图标
		getFeatureIcon(featureName) {
			const iconMap = {
				'眼部': '👁️',
				'鼻部': '👃',
				'嘴部': '👄',
				'面色': '🌈',
				'皮肤': '✨',
				'轮廓': '🔷',
				'气色': '🌟',
				'精神': '😊'
			};

			// 根据特征名称匹配图标
			for (let key in iconMap) {
				if (featureName.includes(key)) {
					return iconMap[key];
				}
			}

			return '📋'; // 默认图标
		},

		// 跳转到商品详情
		goToProduct(product) {
			uni.navigateTo({
				url: `/pages/product/detail?id=${product.id}`
			});
		}
	}
}
</script>

<style scoped>
/* 全局样式 - 简洁医学专业风格 */
.face-result-container {
	background: #f5f7fa;
	min-height: 100vh;
	overflow: hidden;
	padding-bottom: 120rpx;
}

/* 顶部结果概览 */
.result-header {
	position: relative;
	z-index: 1;
	padding: 40rpx 30rpx 30rpx;
}

.header-content {
	background: white;
	border-radius: 15rpx;
	padding: 30rpx;
	border: 1rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	display: flex;
	gap: 30rpx;
}

.result-image-section {
	flex-shrink: 0;
}

.image-wrapper {
	position: relative;
	width: 180rpx;
	height: 180rpx;
	border-radius: 15rpx;
	overflow: hidden;
	border: 2rpx solid #e8e8e8;
}

.result-image {
	width: 100%;
	height: 100%;
}

.analysis-badge {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	background: rgba(46, 125, 138, 0.9);
	padding: 8rpx 15rpx;
	text-align: center;
}

.badge-text {
	font-size: 22rpx;
	color: white;
	font-weight: 500;
}

.result-summary {
	flex: 1;
}

.summary-header {
	margin-bottom: 25rpx;
}

.summary-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #1A4B52;
	display: block;
	margin-bottom: 8rpx;
}

.summary-date {
	font-size: 24rpx;
	color: #666;
}

.health-score {
	display: flex;
	align-items: center;
}

.score-container {
	display: flex;
	align-items: center;
	gap: 25rpx;
}

.score-circle {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: #f8f9fa;
	border: 4rpx solid #2E7D8A;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.score-number {
	font-size: 32rpx;
	font-weight: bold;
	color: #2E7D8A;
}

.score-label {
	font-size: 20rpx;
	color: #2E7D8A;
	margin-top: -5rpx;
}

.score-details {
	flex: 1;
}

.score-desc {
	font-size: 28rpx;
	display: block;
	margin-bottom: 10rpx;
}

.score-level {
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	display: inline-block;
}

.score-level.excellent {
	background: rgba(76, 175, 80, 0.3);
}

.score-level.good {
	background: rgba(139, 195, 74, 0.3);
}

.score-level.normal {
	background: rgba(255, 193, 7, 0.3);
}

.score-level.poor {
	background: rgba(244, 67, 54, 0.3);
}

.level-text {
	font-size: 24rpx;
	color: #fff;
}

/* 主要诊断结果 */
.diagnosis-section,
.analysis-section,
.suggestions-section,
.recommend-section {
	position: relative;
	z-index: 1;
	margin: 30rpx;
	background: white;
	border-radius: 15rpx;
	padding: 30rpx;
	border: 1rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
	margin-bottom: 25rpx;
	padding-bottom: 15rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #1A4B52;
	display: block;
	margin-bottom: 8rpx;
}

.section-subtitle {
	font-size: 24rpx;
	color: #666;
}

.diagnosis-cards {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
	gap: 20rpx;
}

.diagnosis-card {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 25rpx;
	border: 1rpx solid #e8e8e8;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
}

.diagnosis-card:hover {
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transform: translateY(-2rpx);
}

.card-content {
	position: relative;
	z-index: 2;
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.card-icon {
	font-size: 28rpx;
	margin-right: 12rpx;
	color: #2E7D8A;
}

.card-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #1A4B52;
	flex: 1;
}

.card-body {
	margin-bottom: 10rpx;
}

.card-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #2E7D8A;
	display: block;
	margin-bottom: 8rpx;
}

.card-desc {
	font-size: 22rpx;
	color: #666;
	line-height: 1.5;
}

.card-indicator {
	position: absolute;
	top: 0;
	right: 0;
	width: 8rpx;
	height: 100%;
}

.card-indicator.normal {
	background: #4caf50;
}

.card-indicator.warning {
	background: #ff9800;
}

.card-indicator.danger {
	background: #f44336;
}

.analysis-tabs {
	display: flex;
	background: #f5f5f5;
	border-radius: 15rpx;
	padding: 8rpx;
	margin-bottom: 30rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 10rpx;
	transition: all 0.3s;
}

.tab-item.active {
	background: white;
	box-shadow: 0 2rpx 6rpx rgba(46, 125, 138, 0.15);
}

.tab-text {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
	transition: all 0.3s ease;
}

.tab-item.active .tab-text {
	color: #2E7D8A;
	font-weight: bold;
}

/* 内容面板 */
.content-panel {
	min-height: 200rpx;
	animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
	from { opacity: 0; transform: translateY(10rpx); }
	to { opacity: 1; transform: translateY(0); }
}

.feature-grid, .health-analysis, .advice-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.feature-item, .analysis-item, .advice-item {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 25rpx;
	border: 1rpx solid #e8e8e8;
	transition: all 0.3s ease;
}

.feature-item:hover, .analysis-item:hover, .advice-item:hover {
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	transform: translateY(-1rpx);
}

.feature-header, .item-header, .advice-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.feature-name, .item-title, .advice-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #1A4B52;
	flex: 1;
}

.feature-status, .item-level {
	padding: 6rpx 12rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
	font-weight: 500;
	min-width: 60rpx;
	text-align: center;
}

.feature-status.normal, .item-level.normal {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
	border: 1rpx solid rgba(76, 175, 80, 0.2);
}

.feature-status.warning, .item-level.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
	border: 1rpx solid rgba(255, 152, 0, 0.2);
}

.feature-status.danger, .item-level.danger {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
	border: 1rpx solid rgba(244, 67, 54, 0.2);
}

.status-text, .level-text {
	font-size: 20rpx;
	font-weight: 500;
}

.feature-description, .item-content, .advice-content {
	margin-top: 15rpx;
}

.description-text, .content-text, .advice-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
}

.advice-icon {
	font-size: 28rpx;
	margin-right: 12rpx;
	color: #2E7D8A;
}

.product-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.product-item {
	display: flex;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	align-items: center;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 10rpx;
	overflow: hidden;
	margin-right: 20rpx;
}

.product-image image {
	width: 100%;
	height: 100%;
}

.product-info {
	flex: 1;
}

.product-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.product-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
	line-height: 1.4;
}

.product-price {
	display: flex;
	align-items: baseline;
}

.price-symbol {
	font-size: 24rpx;
	color: #ff4757;
	margin-right: 4rpx;
}

.price-value {
	font-size: 32rpx;
	font-weight: 600;
	color: #ff4757;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 30rpx;
	border-top: 1px solid #eee;
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.secondary-btn {
	background: #f5f5f5;
	color: #666;
}

.primary-btn {
	color: #fff;
}

.btn-text {
	font-size: 32rpx;
	font-weight: 600;
}

/* 个性化调理建议 */
.suggestions-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.suggestions-content {
	margin-top: 20rpx;
}

.suggestion-category {
	margin-bottom: 30rpx;
	border-radius: 16rpx;
	background: #f8f9fa;
	padding: 25rpx;
	border: 1rpx solid #e8e8e8;
}

.suggestion-category:last-child {
	margin-bottom: 0;
}

.category-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.category-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
}

.category-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
}

.category-priority {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 500;
}

.category-priority.重要 {
	background: #fff2f0;
	color: #ff4d4f;
	border: 1rpx solid #ffccc7;
}

.category-priority.推荐 {
	background: #f6ffed;
	color: #52c41a;
	border: 1rpx solid #b7eb8f;
}

.category-priority.建议 {
	background: #e6f7ff;
	color: #1890ff;
	border: 1rpx solid #91d5ff;
}

.category-items {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.suggestion-item {
	display: flex;
	align-items: flex-start;
	line-height: 1.6;
}

.item-bullet {
	color: #1890ff;
	margin-right: 10rpx;
	font-weight: 600;
	margin-top: 2rpx;
}

.item-text {
	font-size: 26rpx;
	color: #666;
	flex: 1;
}

/* 新的底部操作区域 */
.action-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	border-top: 1rpx solid #e8e8e8;
	z-index: 100;
}

.action-buttons {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 30rpx;
}

.action-btn.secondary {
	background: #f5f5f5;
	color: #666;
	border: 1rpx solid #e8e8e8;
	transition: all 0.3s ease;
}

.action-btn.secondary:active {
	background: #e8e8e8;
	transform: scale(0.98);
}

.action-btn.primary {
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	color: #fff;
	box-shadow: 0 4rpx 15rpx rgba(24, 144, 255, 0.3);
}

.action-btn.primary:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(24, 144, 255, 0.4);
}

.action-btn.loading {
	opacity: 0.7;
	pointer-events: none;
}

.btn-icon {
	margin-right: 8rpx;
	font-size: 24rpx;
}

.consult-tip {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 15rpx 30rpx;
	background: #fff7e6;
	border-top: 1rpx solid #ffd591;
	gap: 10rpx;
}

.tip-icon {
	font-size: 24rpx;
	color: #fa8c16;
}

.tip-text {
	font-size: 24rpx;
	color: #fa8c16;
	text-align: center;
}
</style>