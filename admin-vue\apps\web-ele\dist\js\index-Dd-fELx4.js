var Jl=Object.defineProperty,xl=Object.defineProperties;var _l=Object.getOwnPropertyDescriptors;var Dt=Object.getOwnPropertySymbols;var en=Object.prototype.hasOwnProperty,tn=Object.prototype.propertyIsEnumerable;var Bt=(e,l,n)=>l in e?Jl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[l]=n,Z=(e,l)=>{for(var n in l||(l={}))en.call(l,n)&&Bt(e,n,l[n]);if(Dt)for(var n of Dt(l))tn.call(l,n)&&Bt(e,n,l[n]);return e},me=(e,l)=>xl(e,_l(l));import{m as bt,n as Se,ay as ue,k as Ne,aH as ln,v as nn,R as Ft,l as ae,q as an,ac as on,Q as sn,o as Ee,B as ve,bj as rn,bk as Me,a6 as un,F as cn,a as dn,s as xt,bl as fn,aF as _t,r as mn,aI as vn,a8 as pn,ab as Ce,t as Ae,bm as hn,ar as gn,y as bn,w as Sn}from"./bootstrap-CYivmKoJ.js";import{u as At,E as yn,B as wn,d as On,a as In}from"./index-owS4PRxE.js";import{t as Pt,e as Vn,f as Tn,E as En,u as Mn}from"./index-Bw18sI2e.js";import{d as ke,f as de,g as K,t as he,m as Pe,n as N,e as m,l as ce,j as q,v as el,x as tl,r as k,L as xe,u as V,w as Ve,aa as Cn,H as Te,B as He,F as dt,P as Ht,o as ll,M as Rn,ab as zn,p as re,ar as Ln,E as fe,U as Wt,a3 as We,ah as Nn,i as Ie,a as Ze,V as Kt,aj as Ye,as as kn,N as ut,D as te,O as $n,S as Dn,T as Bn,h as be,q as Oe,R as Gt,G as Ut,ad as jt,a9 as Fn}from"../jse/index-index-SSqEGcIT.js";import{C as nl,U as al,u as An}from"./index-DIXeP0hR.js";import{c as ol,r as sl}from"./raf-BpwMHZQ4.js";import{i as Pn}from"./browser-CSPQ6ERn.js";import{u as Hn,a as Wn}from"./use-form-item-iUVikjOD.js";import{a as Kn,u as Gn}from"./index-CdkCbLvc.js";import{d as qt}from"./error-CYrjCQ5V.js";import{u as Un}from"./use-form-common-props-DZjBwEkr.js";import{c as Xe}from"./isEqual-racMrmQ-.js";import{C as jn}from"./index-DcFMbTQH.js";import"./aria-DGfENwCE.js";import"./_baseFindIndex-D7XfJLKM.js";import"./_baseIteratee-DIAZWcrk.js";const qn=ke({props:{item:{type:Object,required:!0},style:{type:Object},height:Number},setup(){return{ns:Se("select")}}});function Qn(e,l,n,o,u,c){return K(),de("div",{class:N(e.ns.be("group","title")),style:Pe(me(Z({},e.style),{lineHeight:`${e.height}px`}))},he(e.item.label),7)}var Zn=bt(qn,[["render",Qn],["__file","group-item.vue"]]);function Yn(e,{emit:l}){return{hoverItem:()=>{e.disabled||l("hover",e.index)},selectOptionClick:()=>{e.disabled||l("select",e.item,e.index)}}}const il={label:"label",value:"value",disabled:"disabled",options:"options"};function tt(e){const l=m(()=>Z(Z({},il),e.props));return{aliasProps:l,getLabel:r=>ue(r,l.value.label),getValue:r=>ue(r,l.value.value),getDisabled:r=>ue(r,l.value.disabled),getOptions:r=>ue(r,l.value.options)}}const Xn=Ne(Z(Z({allowCreate:Boolean,autocomplete:{type:ae(String),default:"none"},automaticDropdown:Boolean,clearable:Boolean,clearIcon:{type:Ft,default:on},effect:{type:ae(String),default:"light"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},defaultFirstOption:Boolean,disabled:Boolean,estimatedOptionHeight:{type:Number,default:void 0},filterable:Boolean,filterMethod:Function,height:{type:Number,default:274},itemHeight:{type:Number,default:34},id:String,loading:Boolean,loadingText:String,modelValue:{type:ae([Array,String,Number,Boolean,Object])},multiple:Boolean,multipleLimit:{type:Number,default:0},name:String,noDataText:String,noMatchText:String,remoteMethod:Function,reserveKeyword:{type:Boolean,default:!0},options:{type:ae(Array),required:!0},placeholder:{type:String},teleported:At.teleported,persistent:{type:Boolean,default:!0},popperClass:{type:String,default:""},popperOptions:{type:ae(Object),default:()=>({})},remote:Boolean,size:an,props:{type:ae(Object),default:()=>il},valueKey:{type:String,default:"value"},scrollbarAlwaysOn:Boolean,validateEvent:{type:Boolean,default:!0},offset:{type:Number,default:12},showArrow:{type:Boolean,default:!0},placement:{type:ae(String),values:yn,default:"bottom-start"},fallbackPlacements:{type:ae(Array),default:["bottom-start","top-start","right","left"]},tagType:me(Z({},Pt.type),{default:"info"}),tagEffect:me(Z({},Pt.effect),{default:"light"}),tabindex:{type:[String,Number],default:0},appendTo:At.appendTo,fitInputWidth:{type:[Boolean,Number],default:!0,validator(e){return sn(e)||Ee(e)}},suffixIcon:{type:Ft,default:nn}},ln),An(["ariaLabel"]))),Jn=Ne({data:Array,disabled:Boolean,hovering:Boolean,item:{type:ae(Object),required:!0},index:Number,style:Object,selected:Boolean,created:Boolean}),xn={[al]:e=>!0,[nl]:e=>!0,"remove-tag":e=>!0,"visible-change":e=>!0,focus:e=>e instanceof FocusEvent,blur:e=>e instanceof FocusEvent,clear:()=>!0},_n={hover:e=>Ee(e),select:(e,l)=>!0},St=Symbol("ElSelectV2Injection"),ea=ke({props:Jn,emits:_n,setup(e,{emit:l}){const n=el(St),o=Se("select"),{hoverItem:u,selectOptionClick:c}=Yn(e,{emit:l}),{getLabel:r}=tt(n.props);return{ns:o,hoverItem:u,selectOptionClick:c,getLabel:r}}});function ta(e,l,n,o,u,c){return K(),de("li",{"aria-selected":e.selected,style:Pe(e.style),class:N([e.ns.be("dropdown","item"),e.ns.is("selected",e.selected),e.ns.is("disabled",e.disabled),e.ns.is("created",e.created),e.ns.is("hovering",e.hovering)]),onMousemove:e.hoverItem,onClick:ve(e.selectOptionClick,["stop"])},[ce(e.$slots,"default",{item:e.item,index:e.index,disabled:e.disabled},()=>[q("span",null,he(e.getLabel(e.item)),1)])],46,["aria-selected","onMousemove","onClick"])}var la=bt(ea,[["render",ta],["__file","option-item.vue"]]),Qt=Number.isNaN||function(l){return typeof l=="number"&&l!==l};function na(e,l){return!!(e===l||Qt(e)&&Qt(l))}function aa(e,l){if(e.length!==l.length)return!1;for(var n=0;n<e.length;n++)if(!na(e[n],l[n]))return!1;return!0}function oa(e,l){l===void 0&&(l=aa);var n=null;function o(){for(var u=[],c=0;c<arguments.length;c++)u[c]=arguments[c];if(n&&n.lastThis===this&&l(u,n.lastArgs))return n.lastResult;var r=e.apply(this,u);return n={lastResult:r,lastArgs:u,lastThis:this},r}return o.clear=function(){n=null},o}const sa=()=>{const l=tl().proxy.$props;return m(()=>{const n=(o,u,c)=>({});return l.perfMode?rn(n):oa(n)})},ia=50,Zt="itemRendered",Yt="scroll",rl="forward",ul="backward",Ke="auto",cl="smart",dl="start",_e="center",fl="end",Le="horizontal",yt="vertical",ra="ltr",Je="rtl",ft="negative",ml="positive-ascending",vl="positive-descending",ua={[Le]:"left",[yt]:"top"},ca=20,da={[Le]:"deltaX",[yt]:"deltaY"},fa=({atEndEdge:e,atStartEdge:l,layout:n},o)=>{let u,c=0;const r=h=>h<0&&l.value||h>0&&e.value;return{hasReachedEdge:r,onWheel:h=>{ol(u);const v=h[da[n.value]];r(c)&&r(c+v)||(c+=v,Pn()||h.preventDefault(),u=sl(()=>{o(c),c=0}))}}},mt=Me({type:ae([Number,Function]),required:!0}),vt=Me({type:Number}),pt=Me({type:Number,default:2}),ma=Me({type:String,values:["ltr","rtl"],default:"ltr"}),ht=Me({type:Number,default:0}),et=Me({type:Number,required:!0}),pl=Me({type:String,values:["horizontal","vertical"],default:yt}),hl=Ne({className:{type:String,default:""},containerElement:{type:ae([String,Object]),default:"div"},data:{type:ae(Array),default:()=>un([])},direction:ma,height:{type:[String,Number],required:!0},innerElement:{type:[String,Object],default:"div"},style:{type:ae([Object,String,Array])},useIsScrolling:{type:Boolean,default:!1},width:{type:[Number,String],required:!1},perfMode:{type:Boolean,default:!0},scrollbarAlwaysOn:{type:Boolean,default:!1}}),va=Ne(Z({cache:pt,estimatedItemSize:vt,layout:pl,initScrollOffset:ht,total:et,itemSize:mt},hl)),gt={type:Number,default:6},gl={type:Number,default:0},bl={type:Number,default:2};Ne(Z({columnCache:pt,columnWidth:mt,estimatedColumnWidth:vt,estimatedRowHeight:vt,initScrollLeft:ht,initScrollTop:ht,itemKey:{type:ae(Function),default:({columnIndex:e,rowIndex:l})=>`${l}:${e}`},rowCache:pt,rowHeight:mt,totalColumn:et,totalRow:et,hScrollbarSize:gt,vScrollbarSize:gt,scrollbarStartGap:gl,scrollbarEndGap:bl,role:String},hl));const pa=Ne({alwaysOn:Boolean,class:String,layout:pl,total:et,ratio:{type:Number,required:!0},clientSize:{type:Number,required:!0},scrollFrom:{type:Number,required:!0},scrollbarSize:gt,startGap:gl,endGap:bl,visible:Boolean}),ct=(e,l)=>e<l?rl:ul,Ge=e=>e===ra||e===Je||e===Le;let Re=null;function Xt(e=!1){if(Re===null||e){const l=document.createElement("div"),n=l.style;n.width="50px",n.height="50px",n.overflow="scroll",n.direction="rtl";const o=document.createElement("div"),u=o.style;return u.width="100px",u.height="100px",l.appendChild(o),document.body.appendChild(l),l.scrollLeft>0?Re=vl:(l.scrollLeft=1,l.scrollLeft===0?Re=ft:Re=ml),document.body.removeChild(l),Re}return Re}function ha({move:e,size:l,bar:n},o){const u={},c=`translate${n.axis}(${e}px)`;return u[n.size]=l,u.transform=c,o==="horizontal"?u.height="100%":u.width="100%",u}const ga=ke({name:"ElVirtualScrollBar",props:pa,emits:["scroll","start-move","stop-move"],setup(e,{emit:l}){const n=m(()=>e.startGap+e.endGap),o=Se("virtual-scrollbar"),u=Se("scrollbar"),c=k(),r=k();let p=null,h=null;const v=xe({isDragging:!1,traveled:0}),i=m(()=>wn[e.layout]),d=m(()=>e.clientSize-V(n)),I=m(()=>({position:"absolute",width:`${Le===e.layout?d.value:e.scrollbarSize}px`,height:`${Le===e.layout?e.scrollbarSize:d.value}px`,[ua[e.layout]]:"2px",right:"2px",bottom:"2px",borderRadius:"4px"})),C=m(()=>{const y=e.ratio;if(y>=100)return Number.POSITIVE_INFINITY;if(y>=50)return y*d.value/100;const F=d.value/3;return Math.floor(Math.min(Math.max(y*d.value,ca),F))}),L=m(()=>{if(!Number.isFinite(C.value))return{display:"none"};const y=`${C.value}px`;return ha({bar:i.value,size:y,move:v.traveled},e.layout)}),a=m(()=>Math.ceil(e.clientSize-C.value-V(n))),Y=()=>{window.addEventListener("mousemove",E),window.addEventListener("mouseup",X);const y=V(r);y&&(h=document.onselectstart,document.onselectstart=()=>!1,y.addEventListener("touchmove",E,{passive:!0}),y.addEventListener("touchend",X))},B=()=>{window.removeEventListener("mousemove",E),window.removeEventListener("mouseup",X),document.onselectstart=h,h=null;const y=V(r);y&&(y.removeEventListener("touchmove",E),y.removeEventListener("touchend",X))},le=y=>{y.stopImmediatePropagation(),!(y.ctrlKey||[1,2].includes(y.button))&&(v.isDragging=!0,v[i.value.axis]=y.currentTarget[i.value.offset]-(y[i.value.client]-y.currentTarget.getBoundingClientRect()[i.value.direction]),l("start-move"),Y())},X=()=>{v.isDragging=!1,v[i.value.axis]=0,l("stop-move"),B()},E=y=>{const{isDragging:F}=v;if(!F||!r.value||!c.value)return;const G=v[i.value.axis];if(!G)return;ol(p);const x=(c.value.getBoundingClientRect()[i.value.direction]-y[i.value.client])*-1,_=r.value[i.value.offset]-G,S=x-_;p=sl(()=>{v.traveled=Math.max(0,Math.min(S,a.value)),l("scroll",S,a.value)})},J=y=>{const F=Math.abs(y.target.getBoundingClientRect()[i.value.direction]-y[i.value.client]),G=r.value[i.value.offset]/2,x=F-G;v.traveled=Math.max(0,Math.min(x,a.value)),l("scroll",x,a.value)};return Ve(()=>e.scrollFrom,y=>{v.isDragging||(v.traveled=Math.ceil(y*a.value))}),Cn(()=>{B()}),()=>Te("div",{role:"presentation",ref:c,class:[o.b(),e.class,(e.alwaysOn||v.isDragging)&&"always-on"],style:I.value,onMousedown:ve(J,["stop","prevent"]),onTouchstartPrevent:le},Te("div",{ref:r,class:u.e("thumb"),style:L.value,onMousedown:le},[]))}}),Sl=({name:e,getOffset:l,getItemSize:n,getItemOffset:o,getEstimatedTotalSize:u,getStartIndexForOffset:c,getStopIndexForStartIndex:r,initCache:p,clearCache:h,validateProps:v})=>ke({name:e!=null?e:"ElVirtualList",props:va,emits:[Zt,Yt],setup(i,{emit:d,expose:I}){v(i);const C=tl(),L=Se("vl"),a=k(p(i,C)),Y=sa(),B=k(),le=k(),X=k(),E=k({isScrolling:!1,scrollDir:"forward",scrollOffset:Ee(i.initScrollOffset)?i.initScrollOffset:0,updateRequested:!1,isScrollbarDragging:!1,scrollbarAlwaysOn:i.scrollbarAlwaysOn}),J=m(()=>{const{total:g,cache:M}=i,{isScrolling:T,scrollDir:A,scrollOffset:z}=V(E);if(g===0)return[0,0,0,0];const W=c(i,z,V(a)),D=r(i,W,z,V(a)),ge=!T||A===ul?Math.max(1,M):1,pe=!T||A===rl?Math.max(1,M):1;return[Math.max(0,W-ge),Math.max(0,Math.min(g-1,D+pe)),W,D]}),y=m(()=>u(i,V(a))),F=m(()=>Ge(i.layout)),G=m(()=>[{position:"relative",[`overflow-${F.value?"x":"y"}`]:"scroll",WebkitOverflowScrolling:"touch",willChange:"transform"},{direction:i.direction,height:Ee(i.height)?`${i.height}px`:i.height,width:Ee(i.width)?`${i.width}px`:i.width},i.style]),x=m(()=>{const g=V(y),M=V(F);return{height:M?"100%":`${g}px`,pointerEvents:V(E).isScrolling?"none":void 0,width:M?`${g}px`:"100%"}}),_=m(()=>F.value?i.width:i.height),{onWheel:S}=fa({atStartEdge:m(()=>E.value.scrollOffset<=0),atEndEdge:m(()=>E.value.scrollOffset>=y.value),layout:m(()=>i.layout)},g=>{var M,T;(T=(M=X.value).onMouseUp)==null||T.call(M),H(Math.min(E.value.scrollOffset+g,y.value-_.value))});cn(B,"wheel",S,{passive:!1});const O=()=>{const{total:g}=i;if(g>0){const[z,W,D,ge]=V(J);d(Zt,z,W,D,ge)}const{scrollDir:M,scrollOffset:T,updateRequested:A}=V(E);d(Yt,M,T,A)},$=g=>{const{clientHeight:M,scrollHeight:T,scrollTop:A}=g.currentTarget,z=V(E);if(z.scrollOffset===A)return;const W=Math.max(0,Math.min(A,T-M));E.value=me(Z({},z),{isScrolling:!0,scrollDir:ct(z.scrollOffset,W),scrollOffset:W,updateRequested:!1}),re(R)},U=g=>{const{clientWidth:M,scrollLeft:T,scrollWidth:A}=g.currentTarget,z=V(E);if(z.scrollOffset===T)return;const{direction:W}=i;let D=T;if(W===Je)switch(Xt()){case ft:{D=-T;break}case vl:{D=A-M-T;break}}D=Math.max(0,Math.min(D,A-M)),E.value=me(Z({},z),{isScrolling:!0,scrollDir:ct(z.scrollOffset,D),scrollOffset:D,updateRequested:!1}),re(R)},P=g=>{V(F)?U(g):$(g),O()},ee=(g,M)=>{const T=(y.value-_.value)/M*g;H(Math.min(y.value-_.value,T))},H=g=>{g=Math.max(g,0),g!==V(E).scrollOffset&&(E.value=me(Z({},V(E)),{scrollOffset:g,scrollDir:ct(V(E).scrollOffset,g),updateRequested:!0}),re(R))},Q=(g,M=Ke)=>{const{scrollOffset:T}=V(E);g=Math.max(0,Math.min(g,i.total-1)),H(l(i,g,M,T,V(a)))},ie=g=>{const{direction:M,itemSize:T,layout:A}=i,z=Y.value(h&&T,h&&A,h&&M);let W;if(Ln(z,String(g)))W=z[g];else{const D=o(i,g,V(a)),ge=n(i,g,V(a)),pe=V(F),$e=M===Je,De=pe?D:0;z[g]=W={position:"absolute",left:$e?void 0:`${De}px`,right:$e?`${De}px`:void 0,top:pe?0:`${D}px`,height:pe?"100%":`${ge}px`,width:pe?`${ge}px`:"100%"}}return W},R=()=>{E.value.isScrolling=!1,re(()=>{Y.value(-1,null,null)})},w=()=>{const g=B.value;g&&(g.scrollTop=0)};ll(()=>{if(!dn)return;const{initScrollOffset:g}=i,M=V(B);Ee(g)&&M&&(V(F)?M.scrollLeft=g:M.scrollTop=g),O()}),Rn(()=>{const{direction:g,layout:M}=i,{scrollOffset:T,updateRequested:A}=V(E),z=V(B);if(A&&z)if(M===Le)if(g===Je)switch(Xt()){case ft:{z.scrollLeft=-T;break}case ml:{z.scrollLeft=T;break}default:{const{clientWidth:W,scrollWidth:D}=z;z.scrollLeft=D-W-T;break}}else z.scrollLeft=T;else z.scrollTop=T}),zn(()=>{V(B).scrollTop=V(E).scrollOffset});const oe={ns:L,clientSize:_,estimatedTotalSize:y,windowStyle:G,windowRef:B,innerRef:le,innerStyle:x,itemsToRender:J,scrollbarRef:X,states:E,getItemStyle:ie,onScroll:P,onScrollbarScroll:ee,onWheel:S,scrollTo:H,scrollToItem:Q,resetScrollTop:w};return I({windowRef:B,innerRef:le,getItemStyleCache:Y,scrollTo:H,scrollToItem:Q,resetScrollTop:w,states:E}),oe},render(i){var d;const{$slots:I,className:C,clientSize:L,containerElement:a,data:Y,getItemStyle:B,innerElement:le,itemsToRender:X,innerStyle:E,layout:J,total:y,onScroll:F,onScrollbarScroll:G,states:x,useIsScrolling:_,windowStyle:S,ns:O}=i,[$,U]=X,P=He(a),ee=He(le),H=[];if(y>0)for(let w=$;w<=U;w++)H.push(Te(dt,{key:w},(d=I.default)==null?void 0:d.call(I,{data:Y,index:w,isScrolling:_?x.isScrolling:void 0,style:B(w)})));const Q=[Te(ee,{style:E,ref:"innerRef"},Ht(ee)?H:{default:()=>H})],ie=Te(ga,{ref:"scrollbarRef",clientSize:L,layout:J,onScroll:G,ratio:L*100/this.estimatedTotalSize,scrollFrom:x.scrollOffset/(this.estimatedTotalSize-L),total:y}),R=Te(P,{class:[O.e("window"),C],style:S,onScroll:F,ref:"windowRef",key:0},Ht(P)?[Q]:{default:()=>[Q]});return Te("div",{key:0,class:[O.e("wrapper"),x.scrollbarAlwaysOn?"always-on":""]},[R,ie])}}),ba=Sl({name:"ElFixedSizeList",getItemOffset:({itemSize:e},l)=>l*e,getItemSize:({itemSize:e})=>e,getEstimatedTotalSize:({total:e,itemSize:l})=>l*e,getOffset:({height:e,total:l,itemSize:n,layout:o,width:u},c,r,p)=>{const h=Ge(o)?u:e,v=Math.max(0,l*n-h),i=Math.min(v,c*n),d=Math.max(0,(c+1)*n-h);switch(r===cl&&(p>=d-h&&p<=i+h?r=Ke:r=_e),r){case dl:return i;case fl:return d;case _e:{const I=Math.round(d+(i-d)/2);return I<Math.ceil(h/2)?0:I>v+Math.floor(h/2)?v:I}case Ke:default:return p>=d&&p<=i?p:p<d?d:i}},getStartIndexForOffset:({total:e,itemSize:l},n)=>Math.max(0,Math.min(e-1,Math.floor(n/l))),getStopIndexForStartIndex:({height:e,total:l,itemSize:n,layout:o,width:u},c,r)=>{const p=c*n,h=Ge(o)?u:e,v=Math.ceil((h+r-p)/n);return Math.max(0,Math.min(l-1,c+v-1))},initCache(){},clearCache:!0,validateProps(){}}),ze=(e,l,n)=>{const{itemSize:o}=e,{items:u,lastVisitedIndex:c}=n;if(l>c){let r=0;if(c>=0){const p=u[c];r=p.offset+p.size}for(let p=c+1;p<=l;p++){const h=o(p);u[p]={offset:r,size:h},r+=h}n.lastVisitedIndex=l}return u[l]},Sa=(e,l,n)=>{const{items:o,lastVisitedIndex:u}=l;return(u>0?o[u].offset:0)>=n?yl(e,l,0,u,n):ya(e,l,Math.max(0,u),n)},yl=(e,l,n,o,u)=>{for(;n<=o;){const c=n+Math.floor((o-n)/2),r=ze(e,c,l).offset;if(r===u)return c;r<u?n=c+1:r>u&&(o=c-1)}return Math.max(0,n-1)},ya=(e,l,n,o)=>{const{total:u}=e;let c=1;for(;n<u&&ze(e,n,l).offset<o;)n+=c,c*=2;return yl(e,l,Math.floor(n/2),Math.min(n,u-1),o)},Jt=({total:e},{items:l,estimatedItemSize:n,lastVisitedIndex:o})=>{let u=0;if(o>=e&&(o=e-1),o>=0){const p=l[o];u=p.offset+p.size}const r=(e-o-1)*n;return u+r},wa=Sl({name:"ElDynamicSizeList",getItemOffset:(e,l,n)=>ze(e,l,n).offset,getItemSize:(e,l,{items:n})=>n[l].size,getEstimatedTotalSize:Jt,getOffset:(e,l,n,o,u)=>{const{height:c,layout:r,width:p}=e,h=Ge(r)?p:c,v=ze(e,l,u),i=Jt(e,u),d=Math.max(0,Math.min(i-h,v.offset)),I=Math.max(0,v.offset-h+v.size);switch(n===cl&&(o>=I-h&&o<=d+h?n=Ke:n=_e),n){case dl:return d;case fl:return I;case _e:return Math.round(I+(d-I)/2);case Ke:default:return o>=I&&o<=d?o:o<I?I:d}},getStartIndexForOffset:(e,l,n)=>Sa(e,n,l),getStopIndexForStartIndex:(e,l,n,o)=>{const{height:u,total:c,layout:r,width:p}=e,h=Ge(r)?p:u,v=ze(e,l,o),i=n+h;let d=v.offset+v.size,I=l;for(;I<c-1&&d<i;)I++,d+=ze(e,I,o).size;return I},initCache({estimatedItemSize:e=ia},l){const n={items:{},estimatedItemSize:e,lastVisitedIndex:-1};return n.clearCacheAfterIndex=(o,u=!0)=>{var c,r;n.lastVisitedIndex=Math.min(n.lastVisitedIndex,o-1),(c=l.exposed)==null||c.getItemStyleCache(-1),u&&((r=l.proxy)==null||r.$forceUpdate())},n},clearCache:!1,validateProps:({itemSize:e})=>{}}),Oa={loading:Boolean,data:{type:Array,required:!0},hoveringIndex:Number,width:Number};var Ia=ke({name:"ElSelectDropdown",props:Oa,setup(e,{slots:l,expose:n}){const o=el(St),u=Se("select"),{getLabel:c,getValue:r,getDisabled:p}=tt(o.props),h=k([]),v=k(),i=m(()=>e.data.length);Ve(()=>i.value,()=>{var S,O;(O=(S=o.tooltipRef.value)==null?void 0:S.updatePopper)==null||O.call(S)});const d=m(()=>xt(o.props.estimatedOptionHeight)),I=m(()=>d.value?{itemSize:o.props.itemHeight}:{estimatedSize:o.props.estimatedOptionHeight,itemSize:S=>h.value[S]}),C=(S=[],O)=>{const{props:{valueKey:$}}=o;return We(O)?S&&S.some(U=>Nn(ue(U,$))===ue(O,$)):S.includes(O)},L=(S,O)=>{if(We(O)){const{valueKey:$}=o.props;return ue(S,$)===ue(O,$)}else return S===O},a=(S,O)=>o.props.multiple?C(S,r(O)):L(S,r(O)),Y=(S,O)=>{const{disabled:$,multiple:U,multipleLimit:P}=o.props;return $||!O&&(U?P>0&&S.length>=P:!1)},B=S=>e.hoveringIndex===S;n({listRef:v,isSized:d,isItemDisabled:Y,isItemHovering:B,isItemSelected:a,scrollToItem:S=>{const O=v.value;O&&O.scrollToItem(S)},resetScrollTop:()=>{const S=v.value;S&&S.resetScrollTop()}});const J=S=>{const{index:O,data:$,style:U}=S,P=V(d),{itemSize:ee,estimatedSize:H}=V(I),{modelValue:Q}=o.props,{onSelect:ie,onHover:R}=o,w=$[O];if(w.type==="Group")return fe(Zn,{item:w,style:U,height:P?ee:H},null);const oe=a(Q,w),g=Y(Q,oe),M=B(O);return fe(la,Wt(S,{selected:oe,disabled:p(w)||g,created:!!w.created,hovering:M,item:w,onSelect:ie,onHover:R}),{default:T=>{var A;return((A=l.default)==null?void 0:A.call(l,T))||fe("span",null,[c(w)])}})},{onKeyboardNavigate:y,onKeyboardSelect:F}=o,G=()=>{y("forward")},x=()=>{y("backward")},_=S=>{const{code:O}=S,{tab:$,esc:U,down:P,up:ee,enter:H,numpadEnter:Q}=_t;switch([U,P,ee,H,Q].includes(O)&&(S.preventDefault(),S.stopPropagation()),O){case $:case U:break;case P:G();break;case ee:x();break;case H:case Q:F();break}};return()=>{var S,O,$,U;const{data:P,width:ee}=e,{height:H,multiple:Q,scrollbarAlwaysOn:ie}=o.props,R=m(()=>fn?!0:ie),w=V(d)?ba:wa;return fe("div",{class:[u.b("dropdown"),u.is("multiple",Q)],style:{width:`${ee}px`}},[(S=l.header)==null?void 0:S.call(l),((O=l.loading)==null?void 0:O.call(l))||(($=l.empty)==null?void 0:$.call(l))||fe(w,Wt({ref:v},V(I),{className:u.be("dropdown","list"),scrollbarAlwaysOn:R.value,data:P,height:H,width:ee,total:P.length,onKeydown:_}),{default:oe=>fe(J,oe,null)}),(U=l.footer)==null?void 0:U.call(l)])}}});function Va(e,l){const{aliasProps:n,getLabel:o,getValue:u}=tt(e),c=k(0),r=k(),p=m(()=>e.allowCreate&&e.filterable);function h(C){const L=a=>o(a)===C;return e.options&&e.options.some(L)||l.createdOptions.some(L)}function v(C){p.value&&(e.multiple&&C.created?c.value++:r.value=C)}function i(C){if(p.value)if(C&&C.length>0){if(h(C))return;const L={[n.value.value]:C,[n.value.label]:C,created:!0,[n.value.disabled]:!1};l.createdOptions.length>=c.value?l.createdOptions[c.value]=L:l.createdOptions.push(L)}else if(e.multiple)l.createdOptions.length=c.value;else{const L=r.value;l.createdOptions.length=0,L&&L.created&&l.createdOptions.push(L)}}function d(C){if(!p.value||!C||!C.created||C.created&&e.reserveKeyword&&l.inputValue===o(C))return;const L=l.createdOptions.findIndex(a=>u(a)===u(C));~L&&(l.createdOptions.splice(L,1),c.value--)}function I(){p.value&&(l.createdOptions.length=0,c.value=0)}return{createNewOption:i,removeNewOption:d,selectNewOption:v,clearAllNewOption:I}}const Ta=(e,l)=>{const{t:n}=mn(),o=Se("select"),u=Se("input"),{form:c,formItem:r}=Hn(),{inputId:p}=Wn(e,{formItemContext:r}),{aliasProps:h,getLabel:v,getValue:i,getDisabled:d,getOptions:I}=tt(e),{valueOnClear:C,isEmptyValue:L}=vn(e),a=xe({inputValue:"",cachedOptions:[],createdOptions:[],hoveringIndex:-1,inputHovering:!1,selectionWidth:0,collapseItemWidth:0,previousQuery:null,previousValue:void 0,selectedLabel:"",menuVisibleOnFocus:!1,isBeforeHide:!1}),Y=k(-1),B=k(),le=k(),X=k(),E=k(),J=k(),y=k(),F=k(),G=k(),x=k(),_=k(),{isComposing:S,handleCompositionStart:O,handleCompositionEnd:$,handleCompositionUpdate:U}=Kn({afterComposition:t=>kt(t)}),{wrapperRef:P,isFocused:ee,handleBlur:H}=Gn(J,{beforeFocus(){return oe.value},afterFocus(){e.automaticDropdown&&!w.value&&(w.value=!0,a.menuVisibleOnFocus=!0)},beforeBlur(t){var s,f;return((s=X.value)==null?void 0:s.isFocusInsideContent(t))||((f=E.value)==null?void 0:f.isFocusInsideContent(t))},afterBlur(){var t;w.value=!1,a.menuVisibleOnFocus=!1,e.validateEvent&&((t=r==null?void 0:r.validate)==null||t.call(r,"blur").catch(s=>qt()))}}),Q=m(()=>De("")),ie=m(()=>e.loading?!1:e.options.length>0||a.createdOptions.length>0),R=k([]),w=k(!1),oe=m(()=>e.disabled||(c==null?void 0:c.disabled)),g=m(()=>{var t;return(t=c==null?void 0:c.statusIcon)!=null?t:!1}),M=m(()=>{const t=R.value.length*e.itemHeight;return t>e.height?e.height:t}),T=m(()=>e.multiple?Ie(e.modelValue)&&e.modelValue.length>0:!L(e.modelValue)),A=m(()=>e.clearable&&!oe.value&&a.inputHovering&&T.value),z=m(()=>e.remote&&e.filterable?"":e.suffixIcon),W=m(()=>z.value&&o.is("reverse",w.value)),D=m(()=>(r==null?void 0:r.validateState)||""),ge=m(()=>{if(D.value)return pn[D.value]}),pe=m(()=>e.remote?300:0),$e=m(()=>e.loading?e.loadingText||n("el.select.loading"):e.remote&&!a.inputValue&&!ie.value?!1:e.filterable&&a.inputValue&&ie.value&&R.value.length===0?e.noMatchText||n("el.select.noMatch"):ie.value?null:e.noDataText||n("el.select.noData")),De=t=>{const s=new RegExp(Vn(t),"i"),f=e.filterable&&Ze(e.filterMethod),b=e.filterable&&e.remote&&Ze(e.remoteMethod),j=se=>f||b?!0:t?s.test(v(se)||""):!0;return e.loading?[]:[...a.createdOptions,...e.options].reduce((se,ye)=>{const we=I(ye);if(Ie(we)){const Qe=we.filter(j);Qe.length>0&&se.push({label:v(ye),type:"Group"},...Qe)}else(e.remote||j(ye))&&se.push(ye);return se},[])},wt=()=>{R.value=De(a.inputValue)},Ot=m(()=>{const t=new Map;return Q.value.forEach((s,f)=>{t.set(ne(i(s)),{option:s,index:f})}),t}),Ue=m(()=>{const t=new Map;return R.value.forEach((s,f)=>{t.set(ne(i(s)),{option:s,index:f})}),t}),wl=m(()=>R.value.every(t=>d(t))),It=Un(),Ol=m(()=>It.value==="small"?"small":"default"),Be=()=>{var t;if(Ee(e.fitInputWidth)){Y.value=e.fitInputWidth;return}const s=((t=B.value)==null?void 0:t.offsetWidth)||200;!e.fitInputWidth&&ie.value?re(()=>{Y.value=Math.max(s,Il())}):Y.value=s},Il=()=>{var t,s;const b=document.createElement("canvas").getContext("2d"),j=o.be("dropdown","item"),ye=(((s=(t=G.value)==null?void 0:t.listRef)==null?void 0:s.innerRef)||document).querySelector(`.${j}`);if(ye===null||b===null)return 0;const we=getComputedStyle(ye),Qe=Number.parseFloat(we.paddingLeft)+Number.parseFloat(we.paddingRight);return b.font=`bold ${we.font.replace(new RegExp(`\\b${we.fontWeight}\\b`),"")}`,R.value.reduce((Zl,Yl)=>{const Xl=b.measureText(v(Yl));return Math.max(Xl.width,Zl)},0)+Qe},Vl=()=>{if(!le.value)return 0;const t=window.getComputedStyle(le.value);return Number.parseFloat(t.gap||"6px")},Tl=m(()=>{const t=Vl();return{maxWidth:`${_.value&&e.maxCollapseTags===1?a.selectionWidth-a.collapseItemWidth-t:a.selectionWidth}px`}}),El=m(()=>({maxWidth:`${a.selectionWidth}px`})),Ml=m(()=>Ie(e.modelValue)?e.modelValue.length===0&&!a.inputValue:e.filterable?!a.inputValue:!0),Cl=m(()=>{var t;const s=(t=e.placeholder)!=null?t:n("el.select.placeholder");return e.multiple||!T.value?s:a.selectedLabel}),Rl=m(()=>{var t,s;return(s=(t=X.value)==null?void 0:t.popperRef)==null?void 0:s.contentRef}),zl=m(()=>{if(e.multiple){const t=e.modelValue.length;if(e.modelValue.length>0&&Ue.value.has(e.modelValue[t-1])){const{index:s}=Ue.value.get(e.modelValue[t-1]);return s}}else if(!L(e.modelValue)&&Ue.value.has(e.modelValue)){const{index:t}=Ue.value.get(e.modelValue);return t}return-1}),Ll=m({get(){return w.value&&$e.value!==!1},set(t){w.value=t}}),Nl=m(()=>e.multiple?e.collapseTags?a.cachedOptions.slice(0,e.maxCollapseTags):a.cachedOptions:[]),kl=m(()=>e.multiple?e.collapseTags?a.cachedOptions.slice(e.maxCollapseTags):[]:[]),{createNewOption:Vt,removeNewOption:lt,selectNewOption:Tt,clearAllNewOption:nt}=Va(e,a),at=()=>{oe.value||(a.menuVisibleOnFocus?a.menuVisibleOnFocus=!1:w.value=!w.value)},Et=()=>{a.inputValue.length>0&&!w.value&&(w.value=!0),Vt(a.inputValue),re(()=>{ot(a.inputValue)})},Mt=On(Et,pe.value),ot=t=>{a.previousQuery===t||S.value||(a.previousQuery=t,e.filterable&&Ze(e.filterMethod)?e.filterMethod(t):e.filterable&&e.remote&&Ze(e.remoteMethod)&&e.remoteMethod(t),e.defaultFirstOption&&(e.filterable||e.remote)&&R.value.length?re($l):re(jl))},$l=()=>{const t=R.value.filter(b=>!b.disabled&&b.type!=="Group"),s=t.find(b=>b.created),f=t[0];a.hoveringIndex=st(R.value,s||f)},Dl=t=>{Xe(e.modelValue,t)||l(nl,t)},Fe=t=>{l(al,t),Dl(t),a.previousValue=e.multiple?String(t):t,re(()=>{if(e.multiple&&Ie(e.modelValue)){const s=a.cachedOptions.slice(),f=e.modelValue.map(b=>$t(b,s));Xe(a.cachedOptions,f)||(a.cachedOptions=f)}else qe(!0)})},st=(t=[],s)=>{if(!We(s))return t.indexOf(s);const f=e.valueKey;let b=-1;return t.some((j,se)=>ue(j,f)===ue(s,f)?(b=se,!0):!1),b},ne=t=>We(t)?ue(t,e.valueKey):t,Ct=()=>{Be()},Rt=()=>{a.selectionWidth=Number.parseFloat(window.getComputedStyle(le.value).width)},Bl=()=>{a.collapseItemWidth=_.value.getBoundingClientRect().width},it=()=>{var t,s;(s=(t=X.value)==null?void 0:t.updatePopper)==null||s.call(t)},zt=()=>{var t,s;(s=(t=E.value)==null?void 0:t.updatePopper)==null||s.call(t)},Lt=t=>{if(e.multiple){let s=e.modelValue.slice();const f=st(s,i(t));f>-1?(s=[...s.slice(0,f),...s.slice(f+1)],a.cachedOptions.splice(f,1),lt(t)):(e.multipleLimit<=0||s.length<e.multipleLimit)&&(s=[...s,i(t)],a.cachedOptions.push(t),Tt(t)),Fe(s),t.created&&ot(""),e.filterable&&!e.reserveKeyword&&(a.inputValue="")}else a.selectedLabel=v(t),Fe(i(t)),w.value=!1,Tt(t),t.created||nt();je()},Fl=(t,s)=>{let f=e.modelValue.slice();const b=st(f,i(s));b>-1&&!oe.value&&(f=[...e.modelValue.slice(0,b),...e.modelValue.slice(b+1)],a.cachedOptions.splice(b,1),Fe(f),l("remove-tag",i(s)),lt(s)),t.stopPropagation(),je()},je=()=>{var t;(t=J.value)==null||t.focus()},Al=()=>{var t;if(w.value){w.value=!1,re(()=>{var s;return(s=J.value)==null?void 0:s.blur()});return}(t=J.value)==null||t.blur()},Pl=()=>{a.inputValue.length>0?a.inputValue="":w.value=!1},Hl=t=>Tn(t,s=>!a.cachedOptions.some(f=>i(f)===s&&d(f))),Wl=t=>{if(e.multiple&&t.code!==_t.delete&&a.inputValue.length===0){t.preventDefault();const s=e.modelValue.slice(),f=Hl(s);if(f<0)return;const b=s[f];s.splice(f,1);const j=a.cachedOptions[f];a.cachedOptions.splice(f,1),lt(j),Fe(s),l("remove-tag",b)}},Kl=()=>{let t;Ie(e.modelValue)?t=[]:t=C.value,a.selectedLabel="",w.value=!1,Fe(t),l("clear"),nt(),je()},Nt=(t,s=void 0)=>{const f=R.value;if(!["forward","backward"].includes(t)||oe.value||f.length<=0||wl.value||S.value)return;if(!w.value)return at();xt(s)&&(s=a.hoveringIndex);let b=-1;t==="forward"?(b=s+1,b>=f.length&&(b=0)):t==="backward"&&(b=s-1,(b<0||b>=f.length)&&(b=f.length-1));const j=f[b];if(d(j)||j.type==="Group")return Nt(t,b);a.hoveringIndex=b,rt(b)},Gl=()=>{if(w.value)~a.hoveringIndex&&R.value[a.hoveringIndex]&&Lt(R.value[a.hoveringIndex]);else return at()},Ul=t=>{a.hoveringIndex=t!=null?t:-1},jl=()=>{e.multiple?a.hoveringIndex=R.value.findIndex(t=>e.modelValue.some(s=>ne(s)===ne(i(t)))):a.hoveringIndex=R.value.findIndex(t=>ne(i(t))===ne(e.modelValue))},kt=t=>{if(a.inputValue=t.target.value,e.remote)Mt();else return Et()},ql=t=>{if(w.value=!1,ee.value){const s=new FocusEvent("focus",t);H(s)}},Ql=()=>(a.isBeforeHide=!1,re(()=>{~zl.value&&rt(a.hoveringIndex)})),rt=t=>{G.value.scrollToItem(t)},$t=(t,s)=>{const f=ne(t);if(Ot.value.has(f)){const{option:b}=Ot.value.get(f);return b}if(s&&s.length){const b=s.find(j=>ne(i(j))===f);if(b)return b}return{[h.value.value]:t,[h.value.label]:t}},qe=(t=!1)=>{if(e.multiple)if(e.modelValue.length>0){const s=a.cachedOptions.slice();a.cachedOptions.length=0,a.previousValue=e.modelValue.toString();for(const f of e.modelValue){const b=$t(f,s);a.cachedOptions.push(b)}}else a.cachedOptions=[],a.previousValue=void 0;else if(T.value){a.previousValue=e.modelValue;const s=R.value,f=s.findIndex(b=>ne(i(b))===ne(e.modelValue));~f?a.selectedLabel=v(s[f]):(!a.selectedLabel||t)&&(a.selectedLabel=ne(e.modelValue))}else a.selectedLabel="",a.previousValue=void 0;nt(),Be()};return Ve(()=>e.fitInputWidth,()=>{Be()}),Ve(w,t=>{t?(e.persistent||Be(),ot("")):(a.inputValue="",a.previousQuery=null,a.isBeforeHide=!0,Vt("")),l("visible-change",t)}),Ve(()=>e.modelValue,(t,s)=>{var f;(!t||Ie(t)&&t.length===0||e.multiple&&!Xe(t.toString(),a.previousValue)||!e.multiple&&ne(t)!==ne(a.previousValue))&&qe(!0),!Xe(t,s)&&e.validateEvent&&((f=r==null?void 0:r.validate)==null||f.call(r,"change").catch(j=>qt()))},{deep:!0}),Ve(()=>e.options,()=>{const t=J.value;(!t||t&&document.activeElement!==t)&&qe()},{deep:!0,flush:"post"}),Ve(()=>R.value,()=>(Be(),G.value&&re(G.value.resetScrollTop))),Kt(()=>{a.isBeforeHide||wt()}),Kt(()=>{const{valueKey:t,options:s}=e,f=new Map;for(const b of s){const j=i(b);let se=j;if(We(se)&&(se=ue(j,t)),f.get(se))break;f.set(se,!0)}}),ll(()=>{qe()}),Ce(B,Ct),Ce(le,Rt),Ce(G,it),Ce(P,it),Ce(x,zt),Ce(_,Bl),{inputId:p,collapseTagSize:Ol,currentPlaceholder:Cl,expanded:w,emptyText:$e,popupHeight:M,debounce:pe,allOptions:Q,filteredOptions:R,iconComponent:z,iconReverse:W,tagStyle:Tl,collapseTagStyle:El,popperSize:Y,dropdownMenuVisible:Ll,hasModelValue:T,shouldShowPlaceholder:Ml,selectDisabled:oe,selectSize:It,needStatusIcon:g,showClearBtn:A,states:a,isFocused:ee,nsSelect:o,nsInput:u,inputRef:J,menuRef:G,tagMenuRef:x,tooltipRef:X,tagTooltipRef:E,selectRef:B,wrapperRef:P,selectionRef:le,prefixRef:y,suffixRef:F,collapseItemRef:_,popperRef:Rl,validateState:D,validateIcon:ge,showTagList:Nl,collapseTagList:kl,debouncedOnInputChange:Mt,deleteTag:Fl,getLabel:v,getValue:i,getDisabled:d,getValueKey:ne,handleClear:Kl,handleClickOutside:ql,handleDel:Wl,handleEsc:Pl,focus:je,blur:Al,handleMenuEnter:Ql,handleResize:Ct,resetSelectionWidth:Rt,updateTooltip:it,updateTagTooltip:zt,updateOptions:wt,toggleMenu:at,scrollTo:rt,onInput:kt,onKeyboardNavigate:Nt,onKeyboardSelect:Gl,onSelect:Lt,onHover:Ul,handleCompositionStart:O,handleCompositionEnd:$,handleCompositionUpdate:U}},Ea=ke({name:"ElSelectV2",components:{ElSelectMenu:Ia,ElTag:En,ElTooltip:In,ElIcon:bn},directives:{ClickOutside:jn},props:Xn,emits:xn,setup(e,{emit:l}){const n=m(()=>{const{modelValue:p,multiple:h}=e,v=h?[]:void 0;return Ie(p)?h?p:v:h?v:p}),o=Ta(xe(me(Z({},jt(e)),{modelValue:n})),l),{calculatorRef:u,inputStyle:c}=Mn();Fn(St,{props:xe(me(Z({},jt(e)),{height:o.popupHeight,modelValue:n})),expanded:o.expanded,tooltipRef:o.tooltipRef,onSelect:o.onSelect,onHover:o.onHover,onKeyboardNavigate:o.onKeyboardNavigate,onKeyboardSelect:o.onKeyboardSelect});const r=m(()=>e.multiple?o.states.cachedOptions.map(p=>p.label):o.states.selectedLabel);return me(Z({},o),{modelValue:n,selectedLabel:r,calculatorRef:u,inputStyle:c})}});function Ma(e,l,n,o,u,c){const r=Ye("el-tag"),p=Ye("el-tooltip"),h=Ye("el-icon"),v=Ye("el-select-menu"),i=kn("click-outside");return ut((K(),de("div",{ref:"selectRef",class:N([e.nsSelect.b(),e.nsSelect.m(e.selectSize)]),onMouseenter:d=>e.states.inputHovering=!0,onMouseleave:d=>e.states.inputHovering=!1},[fe(p,{ref:"tooltipRef",visible:e.dropdownMenuVisible,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"gpu-acceleration":!1,"stop-popper-mouse-event":!1,"popper-options":e.popperOptions,"fallback-placements":e.fallbackPlacements,effect:e.effect,placement:e.placement,pure:"",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,trigger:"click",persistent:e.persistent,"append-to":e.appendTo,"show-arrow":e.showArrow,offset:e.offset,onBeforeShow:e.handleMenuEnter,onHide:d=>e.states.isBeforeHide=!1},{default:te(()=>[q("div",{ref:"wrapperRef",class:N([e.nsSelect.e("wrapper"),e.nsSelect.is("focused",e.isFocused),e.nsSelect.is("hovering",e.states.inputHovering),e.nsSelect.is("filterable",e.filterable),e.nsSelect.is("disabled",e.selectDisabled)]),onClick:ve(e.toggleMenu,["prevent"])},[e.$slots.prefix?(K(),de("div",{key:0,ref:"prefixRef",class:N(e.nsSelect.e("prefix"))},[ce(e.$slots,"prefix")],2)):be("v-if",!0),q("div",{ref:"selectionRef",class:N([e.nsSelect.e("selection"),e.nsSelect.is("near",e.multiple&&!e.$slots.prefix&&!!e.modelValue.length)])},[e.multiple?ce(e.$slots,"tag",{key:0},()=>[(K(!0),de(dt,null,Gt(e.showTagList,d=>(K(),de("div",{key:e.getValueKey(e.getValue(d)),class:N(e.nsSelect.e("selected-item"))},[fe(r,{closable:!e.selectDisabled&&!e.getDisabled(d),size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:Pe(e.tagStyle),onClose:I=>e.deleteTag(I,d)},{default:te(()=>[q("span",{class:N(e.nsSelect.e("tags-text"))},[ce(e.$slots,"label",{label:e.getLabel(d),value:e.getValue(d)},()=>[Ut(he(e.getLabel(d)),1)])],2)]),_:2},1032,["closable","size","type","effect","style","onClose"])],2))),128)),e.collapseTags&&e.modelValue.length>e.maxCollapseTags?(K(),Oe(p,{key:0,ref:"tagTooltipRef",disabled:e.dropdownMenuVisible||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:te(()=>[q("div",{ref:"collapseItemRef",class:N(e.nsSelect.e("selected-item"))},[fe(r,{closable:!1,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,style:Pe(e.collapseTagStyle),"disable-transitions":""},{default:te(()=>[q("span",{class:N(e.nsSelect.e("tags-text"))}," + "+he(e.modelValue.length-e.maxCollapseTags),3)]),_:1},8,["size","type","effect","style"])],2)]),content:te(()=>[q("div",{ref:"tagMenuRef",class:N(e.nsSelect.e("selection"))},[(K(!0),de(dt,null,Gt(e.collapseTagList,d=>(K(),de("div",{key:e.getValueKey(e.getValue(d)),class:N(e.nsSelect.e("selected-item"))},[fe(r,{class:"in-tooltip",closable:!e.selectDisabled&&!e.getDisabled(d),size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",onClose:I=>e.deleteTag(I,d)},{default:te(()=>[q("span",{class:N(e.nsSelect.e("tags-text"))},[ce(e.$slots,"label",{label:e.getLabel(d),value:e.getValue(d)},()=>[Ut(he(e.getLabel(d)),1)])],2)]),_:2},1032,["closable","size","type","effect","onClose"])],2))),128))],2)]),_:3},8,["disabled","effect","teleported"])):be("v-if",!0)]):be("v-if",!0),q("div",{class:N([e.nsSelect.e("selected-item"),e.nsSelect.e("input-wrapper"),e.nsSelect.is("hidden",!e.filterable)])},[ut(q("input",{id:e.inputId,ref:"inputRef","onUpdate:modelValue":d=>e.states.inputValue=d,style:Pe(e.inputStyle),autocomplete:e.autocomplete,tabindex:e.tabindex,"aria-autocomplete":"list","aria-haspopup":"listbox",autocapitalize:"off","aria-expanded":e.expanded,"aria-label":e.ariaLabel,class:N([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,role:"combobox",readonly:!e.filterable,spellcheck:"false",type:"text",name:e.name,onInput:e.onInput,onCompositionstart:e.handleCompositionStart,onCompositionupdate:e.handleCompositionUpdate,onCompositionend:e.handleCompositionEnd,onKeydown:[Ae(ve(d=>e.onKeyboardNavigate("backward"),["stop","prevent"]),["up"]),Ae(ve(d=>e.onKeyboardNavigate("forward"),["stop","prevent"]),["down"]),Ae(ve(e.onKeyboardSelect,["stop","prevent"]),["enter"]),Ae(ve(e.handleEsc,["stop","prevent"]),["esc"]),Ae(ve(e.handleDel,["stop"]),["delete"])],onClick:ve(e.toggleMenu,["stop"])},null,46,["id","onUpdate:modelValue","autocomplete","tabindex","aria-expanded","aria-label","disabled","readonly","name","onInput","onCompositionstart","onCompositionupdate","onCompositionend","onKeydown","onClick"]),[[hn,e.states.inputValue]]),e.filterable?(K(),de("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:N(e.nsSelect.e("input-calculator")),textContent:he(e.states.inputValue)},null,10,["textContent"])):be("v-if",!0)],2),e.shouldShowPlaceholder?(K(),de("div",{key:1,class:N([e.nsSelect.e("selected-item"),e.nsSelect.e("placeholder"),e.nsSelect.is("transparent",!e.hasModelValue||e.expanded&&!e.states.inputValue)])},[e.hasModelValue?ce(e.$slots,"label",{key:0,label:e.currentPlaceholder,value:e.modelValue},()=>[q("span",null,he(e.currentPlaceholder),1)]):(K(),de("span",{key:1},he(e.currentPlaceholder),1))],2)):be("v-if",!0)],2),q("div",{ref:"suffixRef",class:N(e.nsSelect.e("suffix"))},[e.iconComponent?ut((K(),Oe(h,{key:0,class:N([e.nsSelect.e("caret"),e.nsInput.e("icon"),e.iconReverse])},{default:te(()=>[(K(),Oe(He(e.iconComponent)))]),_:1},8,["class"])),[[gn,!e.showClearBtn]]):be("v-if",!0),e.showClearBtn&&e.clearIcon?(K(),Oe(h,{key:1,class:N([e.nsSelect.e("caret"),e.nsInput.e("icon"),e.nsSelect.e("clear")]),onClick:ve(e.handleClear,["prevent","stop"])},{default:te(()=>[(K(),Oe(He(e.clearIcon)))]),_:1},8,["class","onClick"])):be("v-if",!0),e.validateState&&e.validateIcon&&e.needStatusIcon?(K(),Oe(h,{key:2,class:N([e.nsInput.e("icon"),e.nsInput.e("validateIcon"),e.nsInput.is("loading",e.validateState==="validating")])},{default:te(()=>[(K(),Oe(He(e.validateIcon)))]),_:1},8,["class"])):be("v-if",!0)],2)],10,["onClick"])]),content:te(()=>[fe(v,{ref:"menuRef",data:e.filteredOptions,width:e.popperSize,"hovering-index":e.states.hoveringIndex,"scrollbar-always-on":e.scrollbarAlwaysOn},$n({default:te(d=>[ce(e.$slots,"default",Dn(Bn(d)))]),_:2},[e.$slots.header?{name:"header",fn:te(()=>[q("div",{class:N(e.nsSelect.be("dropdown","header"))},[ce(e.$slots,"header")],2)])}:void 0,e.$slots.loading&&e.loading?{name:"loading",fn:te(()=>[q("div",{class:N(e.nsSelect.be("dropdown","loading"))},[ce(e.$slots,"loading")],2)])}:e.loading||e.filteredOptions.length===0?{name:"empty",fn:te(()=>[q("div",{class:N(e.nsSelect.be("dropdown","empty"))},[ce(e.$slots,"empty",{},()=>[q("span",null,he(e.emptyText),1)])],2)])}:void 0,e.$slots.footer?{name:"footer",fn:te(()=>[q("div",{class:N(e.nsSelect.be("dropdown","footer"))},[ce(e.$slots,"footer")],2)])}:void 0]),1032,["data","width","hovering-index","scrollbar-always-on"])]),_:3},8,["visible","teleported","popper-class","popper-options","fallback-placements","effect","placement","transition","persistent","append-to","show-arrow","offset","onBeforeShow","onHide"])],42,["onMouseenter","onMouseleave"])),[[i,e.handleClickOutside,e.popperRef]])}var Ca=bt(Ea,[["render",Ma],["__file","select.vue"]]);const Qa=Sn(Ca);export{Qa as ElSelectV2,Qa as default,St as selectV2InjectionKey};
