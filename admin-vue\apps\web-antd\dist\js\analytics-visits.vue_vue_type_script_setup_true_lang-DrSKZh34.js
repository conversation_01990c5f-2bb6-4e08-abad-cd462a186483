import{u as s,_ as o}from"./use-echarts-B0FXnrLE.js";import{a4 as i,P as n,a9 as c,aa as p,ab as m,a7 as _}from"../jse/index-index-BAMHRxBA.js";const h=i({__name:"analytics-visits",setup(f){const e=n(),{renderEcharts:t}=s(e);return c(()=>{t({grid:{bottom:0,containLabel:!0,left:"1%",right:"1%",top:"2 %"},series:[{barMaxWidth:80,data:[3e3,2e3,3333,5e3,3200,4200,3200,2100,3e3,5100,6e3,3200,4800],type:"bar"}],tooltip:{axisPointer:{lineStyle:{width:1}},trigger:"axis"},xAxis:{data:Array.from({length:12}).map((r,a)=>`${a+1}月`),type:"category"},yAxis:{max:8e3,splitNumber:4,type:"value"}})}),(r,a)=>(m(),p(_(o),{ref_key:"chartRef",ref:e},null,512))}});export{h as _};
