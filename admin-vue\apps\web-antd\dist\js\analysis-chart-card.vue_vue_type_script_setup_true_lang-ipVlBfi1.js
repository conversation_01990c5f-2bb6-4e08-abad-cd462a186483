import{a4 as r,av as o,ab as n,aV as d,a7 as t,aW as p,a8 as l,aa as i,ac as c,x as u,ai as f,aj as m}from"../jse/index-index-BAMHRxBA.js";const h=r({__name:"Card",props:{class:{}},setup(s){const a=s;return(e,_)=>(n(),o("div",{class:d(t(p)("bg-card text-card-foreground border-border rounded-xl border",a.class))},[l(e.$slots,"default")],2))}}),C=r({__name:"CardContent",props:{class:{}},setup(s){const a=s;return(e,_)=>(n(),o("div",{class:d(t(p)("p-6 pt-0",a.class))},[l(e.$slots,"default")],2))}}),x=r({__name:"CardHeader",props:{class:{}},setup(s){const a=s;return(e,_)=>(n(),o("div",{class:d(t(p)("flex flex-col gap-y-1.5 p-5",a.class))},[l(e.$slots,"default")],2))}}),$=r({__name:"CardTitle",props:{class:{}},setup(s){const a=s;return(e,_)=>(n(),o("h3",{class:d(t(p)("font-semibold leading-none tracking-tight",a.class))},[l(e.$slots,"default")],2))}}),g=r({name:"AnalysisChartCard",__name:"analysis-chart-card",props:{title:{}},setup(s){return(a,e)=>(n(),i(t(h),null,{default:c(()=>[u(t(x),null,{default:c(()=>[u(t($),{class:"text-xl"},{default:c(()=>[f(m(a.title),1)]),_:1})]),_:1}),u(t(C),null,{default:c(()=>[l(a.$slots,"default")]),_:3})]),_:3}))}});export{x as _,$ as a,C as b,h as c,g as d};
