var Se=Object.defineProperty,Be=Object.defineProperties;var Le=Object.getOwnPropertyDescriptors;var Y=Object.getOwnPropertySymbols;var Ee=Object.prototype.hasOwnProperty,_e=Object.prototype.propertyIsEnumerable;var Z=(e,o,n)=>o in e?Se(e,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[o]=n,E=(e,o)=>{for(var n in o||(o={}))Ee.call(o,n)&&Z(e,n,o[n]);if(Y)for(var n of Y(o))_e.call(o,n)&&Z(e,n,o[n]);return e},F=(e,o)=>Be(e,Le(o));var P=(e,o,n)=>new Promise((l,b)=>{var d=c=>{try{t(n.next(c))}catch(r){b(r)}},v=c=>{try{t(n.throw(c))}catch(r){b(r)}},t=c=>c.done?l(c.value):Promise.resolve(c.value).then(d,v);t((n=n.apply(e,o)).next())});import{o as ee,Q as A,q as oe,s as O,as as M,m as q,n as H,B as G,aE as w,k as Ie,l as Fe,w as Ne,aq as ue}from"./bootstrap-CYivmKoJ.js";import{u as se,U,C as R,p as $e}from"./index-DIXeP0hR.js";import{P as ae,v as $,e as f,x as ie,w as re,p as ce,r as j,i as N,a3 as Ge,ah as le,d as _,a1 as de,q as be,g as x,D as ve,j as ne,f as S,h as K,n as C,u,N as z,ac as D,l as Q,F as we,G as me,t as fe,B as he,m as ze,a9 as De,ad as Ue}from"../jse/index-index-SSqEGcIT.js";import{b as Pe,u as te}from"./use-form-common-props-DZjBwEkr.js";import{u as W,a as pe}from"./use-form-item-iUVikjOD.js";import{d as ke}from"./error-CYrjCQ5V.js";import{c as Te}from"./isEqual-racMrmQ-.js";import{u as T}from"./index-DuhtAOZf.js";const ge=E({modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},value:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueValue:{type:[String,Number],default:void 0},falseValue:{type:[String,Number],default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},border:Boolean,size:oe,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0}},se(["ariaControls"])),Ce={[U]:e=>ae(e)||ee(e)||A(e),change:e=>ae(e)||ee(e)||A(e)},I=Symbol("checkboxGroupContextKey"),Ae=({model:e,isChecked:o})=>{const n=$(I,void 0),l=f(()=>{var d,v;const t=(d=n==null?void 0:n.max)==null?void 0:d.value,c=(v=n==null?void 0:n.min)==null?void 0:v.value;return!O(t)&&e.value.length>=t&&!o.value||!O(c)&&e.value.length<=c&&o.value});return{isDisabled:Pe(f(()=>(n==null?void 0:n.disabled.value)||l.value)),isLimitDisabled:l}},Oe=(e,{model:o,isLimitExceeded:n,hasOwnLabel:l,isDisabled:b,isLabeledByFormItem:d})=>{const v=$(I,void 0),{formItem:t}=W(),{emit:c}=ie();function r(a){var h,p,i,k;return[!0,e.trueValue,e.trueLabel].includes(a)?(p=(h=e.trueValue)!=null?h:e.trueLabel)!=null?p:!0:(k=(i=e.falseValue)!=null?i:e.falseLabel)!=null?k:!1}function s(a,h){c(R,r(a),h)}function m(a){if(n.value)return;const h=a.target;c(R,r(h.checked),a)}function V(a){return P(this,null,function*(){n.value||!l.value&&!b.value&&d.value&&(a.composedPath().some(i=>i.tagName==="LABEL")||(o.value=r([!1,e.falseValue,e.falseLabel].includes(o.value)),yield ce(),s(o.value,a)))})}const B=f(()=>(v==null?void 0:v.validateEvent)||e.validateEvent);return re(()=>e.modelValue,()=>{B.value&&(t==null||t.validate("change").catch(a=>ke()))}),{handleChange:m,onClickRoot:V}},Me=e=>{const o=j(!1),{emit:n}=ie(),l=$(I,void 0),b=f(()=>O(l)===!1),d=j(!1),v=f({get(){var t,c;return b.value?(t=l==null?void 0:l.modelValue)==null?void 0:t.value:(c=e.modelValue)!=null?c:o.value},set(t){var c,r;b.value&&N(t)?(d.value=((c=l==null?void 0:l.max)==null?void 0:c.value)!==void 0&&t.length>(l==null?void 0:l.max.value)&&t.length>v.value.length,d.value===!1&&((r=l==null?void 0:l.changeEvent)==null||r.call(l,t))):(n(U,t),o.value=t)}});return{model:v,isGroup:b,isLimitExceeded:d}},Re=(e,o,{model:n})=>{const l=$(I,void 0),b=j(!1),d=f(()=>M(e.value)?e.label:e.value),v=f(()=>{const s=n.value;return A(s)?s:N(s)?Ge(d.value)?s.map(le).some(m=>Te(m,d.value)):s.map(le).includes(d.value):s!=null?s===e.trueValue||s===e.trueLabel:!!s}),t=te(f(()=>{var s;return(s=l==null?void 0:l.size)==null?void 0:s.value}),{prop:!0}),c=te(f(()=>{var s;return(s=l==null?void 0:l.size)==null?void 0:s.value})),r=f(()=>!!o.default||!M(d.value));return{checkboxButtonSize:t,isChecked:v,isFocused:b,checkboxSize:c,hasOwnLabel:r,actualValue:d}},xe=(e,o)=>{const{formItem:n}=W(),{model:l,isGroup:b,isLimitExceeded:d}=Me(e),{isFocused:v,isChecked:t,checkboxButtonSize:c,checkboxSize:r,hasOwnLabel:s,actualValue:m}=Re(e,o,{model:l}),{isDisabled:V}=Ae({model:l,isChecked:t}),{inputId:B,isLabeledByFormItem:a}=pe(e,{formItemContext:n,disableIdGeneration:s,disableIdManagement:b}),{handleChange:h,onClickRoot:p}=Oe(e,{model:l,isLimitExceeded:d,hasOwnLabel:s,isDisabled:V,isLabeledByFormItem:a});return(()=>{function k(){var y,g;N(l.value)&&!l.value.includes(m.value)?l.value.push(m.value):l.value=(g=(y=e.trueValue)!=null?y:e.trueLabel)!=null?g:!0}e.checked&&k()})(),T({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>b.value&&M(e.value))),T({from:"true-label",replacement:"true-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>!!e.trueLabel)),T({from:"false-label",replacement:"false-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>!!e.falseLabel)),{inputId:B,isLabeledByFormItem:a,isChecked:t,isDisabled:V,isFocused:v,checkboxButtonSize:c,checkboxSize:r,hasOwnLabel:s,model:l,actualValue:m,handleChange:h,onClickRoot:p}},je=_({name:"ElCheckbox"}),Ke=_(F(E({},je),{props:ge,emits:Ce,setup(e){const o=e,n=de(),{inputId:l,isLabeledByFormItem:b,isChecked:d,isDisabled:v,isFocused:t,checkboxSize:c,hasOwnLabel:r,model:s,actualValue:m,handleChange:V,onClickRoot:B}=xe(o,n),a=H("checkbox"),h=f(()=>[a.b(),a.m(c.value),a.is("disabled",v.value),a.is("bordered",o.border),a.is("checked",d.value)]),p=f(()=>[a.e("input"),a.is("disabled",v.value),a.is("checked",d.value),a.is("indeterminate",o.indeterminate),a.is("focus",t.value)]);return(i,k)=>(x(),be(he(!u(r)&&u(b)?"span":"label"),{class:C(u(h)),"aria-controls":i.indeterminate?i.ariaControls:null,onClick:u(B)},{default:ve(()=>{var y,g,J,X;return[ne("span",{class:C(u(p))},[i.trueValue||i.falseValue||i.trueLabel||i.falseLabel?z((x(),S("input",{key:0,id:u(l),"onUpdate:modelValue":L=>D(s)?s.value=L:null,class:C(u(a).e("original")),type:"checkbox",indeterminate:i.indeterminate,name:i.name,tabindex:i.tabindex,disabled:u(v),"true-value":(g=(y=i.trueValue)!=null?y:i.trueLabel)!=null?g:!0,"false-value":(X=(J=i.falseValue)!=null?J:i.falseLabel)!=null?X:!1,onChange:u(V),onFocus:L=>t.value=!0,onBlur:L=>t.value=!1,onClick:G(()=>{},["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[w,u(s)]]):z((x(),S("input",{key:1,id:u(l),"onUpdate:modelValue":L=>D(s)?s.value=L:null,class:C(u(a).e("original")),type:"checkbox",indeterminate:i.indeterminate,disabled:u(v),value:u(m),name:i.name,tabindex:i.tabindex,onChange:u(V),onFocus:L=>t.value=!0,onBlur:L=>t.value=!1,onClick:G(()=>{},["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","disabled","value","name","tabindex","onChange","onFocus","onBlur","onClick"])),[[w,u(s)]]),ne("span",{class:C(u(a).e("inner"))},null,2)],2),u(r)?(x(),S("span",{key:0,class:C(u(a).e("label"))},[Q(i.$slots,"default"),i.$slots.default?K("v-if",!0):(x(),S(we,{key:0},[me(fe(i.label),1)],64))],2)):K("v-if",!0)]}),_:3},8,["class","aria-controls","onClick"]))}}));var qe=q(Ke,[["__file","checkbox.vue"]]);const He=_({name:"ElCheckboxButton"}),Qe=_(F(E({},He),{props:ge,emits:Ce,setup(e){const o=e,n=de(),{isFocused:l,isChecked:b,isDisabled:d,checkboxButtonSize:v,model:t,actualValue:c,handleChange:r}=xe(o,n),s=$(I,void 0),m=H("checkbox"),V=f(()=>{var a,h,p,i;const k=(h=(a=s==null?void 0:s.fill)==null?void 0:a.value)!=null?h:"";return{backgroundColor:k,borderColor:k,color:(i=(p=s==null?void 0:s.textColor)==null?void 0:p.value)!=null?i:"",boxShadow:k?`-1px 0 0 0 ${k}`:void 0}}),B=f(()=>[m.b("button"),m.bm("button",v.value),m.is("disabled",d.value),m.is("checked",b.value),m.is("focus",l.value)]);return(a,h)=>{var p,i,k,y;return x(),S("label",{class:C(u(B))},[a.trueValue||a.falseValue||a.trueLabel||a.falseLabel?z((x(),S("input",{key:0,"onUpdate:modelValue":g=>D(t)?t.value=g:null,class:C(u(m).be("button","original")),type:"checkbox",name:a.name,tabindex:a.tabindex,disabled:u(d),"true-value":(i=(p=a.trueValue)!=null?p:a.trueLabel)!=null?i:!0,"false-value":(y=(k=a.falseValue)!=null?k:a.falseLabel)!=null?y:!1,onChange:u(r),onFocus:g=>l.value=!0,onBlur:g=>l.value=!1,onClick:G(()=>{},["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[w,u(t)]]):z((x(),S("input",{key:1,"onUpdate:modelValue":g=>D(t)?t.value=g:null,class:C(u(m).be("button","original")),type:"checkbox",name:a.name,tabindex:a.tabindex,disabled:u(d),value:u(c),onChange:u(r),onFocus:g=>l.value=!0,onBlur:g=>l.value=!1,onClick:G(()=>{},["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","value","onChange","onFocus","onBlur","onClick"])),[[w,u(t)]]),a.$slots.default||a.label?(x(),S("span",{key:2,class:C(u(m).be("button","inner")),style:ze(u(b)?u(V):void 0)},[Q(a.$slots,"default",{},()=>[me(fe(a.label),1)])],6)):K("v-if",!0)],2)}}}));var Ve=q(Qe,[["__file","checkbox-button.vue"]]);const We=Ie(E({modelValue:{type:Fe(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:oe,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0}},se(["ariaLabel"]))),Je={[U]:e=>N(e),change:e=>N(e)},Xe=_({name:"ElCheckboxGroup"}),Ye=_(F(E({},Xe),{props:We,emits:Je,setup(e,{emit:o}){const n=e,l=H("checkbox"),{formItem:b}=W(),{inputId:d,isLabeledByFormItem:v}=pe(n,{formItemContext:b}),t=r=>P(null,null,function*(){o(U,r),yield ce(),o(R,r)}),c=f({get(){return n.modelValue},set(r){t(r)}});return De(I,F(E({},$e(Ue(n),["size","min","max","disabled","validateEvent","fill","textColor"])),{modelValue:c,changeEvent:t})),re(()=>n.modelValue,()=>{n.validateEvent&&(b==null||b.validate("change").catch(r=>ke()))}),(r,s)=>{var m;return x(),be(he(r.tag),{id:u(d),class:C(u(l).b("group")),role:"group","aria-label":u(v)?void 0:r.ariaLabel||"checkbox-group","aria-labelledby":u(v)?(m=u(b))==null?void 0:m.labelId:void 0},{default:ve(()=>[Q(r.$slots,"default")]),_:3},8,["id","class","aria-label","aria-labelledby"])}}}));var ye=q(Ye,[["__file","checkbox-group.vue"]]);const ia=Ne(qe,{CheckboxButton:Ve,CheckboxGroup:ye}),ra=ue(Ve),ca=ue(ye);export{ia as ElCheckbox,ra as ElCheckboxButton,ca as ElCheckboxGroup,Ce as checkboxEmits,I as checkboxGroupContextKey,Je as checkboxGroupEmits,We as checkboxGroupProps,ge as checkboxProps,ia as default};
