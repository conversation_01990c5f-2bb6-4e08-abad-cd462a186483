{"version": 3, "file": "jwt-utils.mjs", "sources": ["../../../../utils/mock-data.ts", "../../../../utils/jwt-utils.ts"], "sourcesContent": null, "names": [], "mappings": ";;;AASO,MAAM,UAAA,GAAyB;AAAA,EACpC;AAAA,IACE,EAAA,EAAI,CAAA;AAAA,IACJ,QAAA,EAAU,QAAA;AAAA,IACV,QAAA,EAAU,MAAA;AAAA,IACV,KAAA,EAAO,CAAC,OAAO,CAAA;AAAA,IACf,QAAA,EAAU;AAAA,GACZ;AAAA,EACA;AAAA,IACE,EAAA,EAAI,CAAA;AAAA,IACJ,QAAA,EAAU,QAAA;AAAA,IACV,QAAA,EAAU,OAAA;AAAA,IACV,KAAA,EAAO,CAAC,OAAO,CAAA;AAAA,IACf,QAAA,EAAU,OAAA;AAAA,IACV,QAAA,EAAU;AAAA,GACZ;AAAA,EACA;AAAA,IACE,EAAA,EAAI,CAAA;AAAA,IACJ,QAAA,EAAU,QAAA;AAAA,IACV,QAAA,EAAU,MAAA;AAAA,IACV,KAAA,EAAO,CAAC,MAAM,CAAA;AAAA,IACd,QAAA,EAAU,MAAA;AAAA,IACV,QAAA,EAAU;AAAA;AAEd;AAEO,MAAM,UAAA,GAAa;AAAA;AAAA,EAExB;AAAA,IACE,KAAA,EAAO,CAAC,WAAA,EAAa,WAAA,EAAa,aAAa,WAAW,CAAA;AAAA,IAC1D,QAAA,EAAU;AAAA,GACZ;AAAA,EACA;AAAA;AAAA,IAEE,KAAA,EAAO,CAAC,WAAA,EAAa,WAAA,EAAa,WAAW,CAAA;AAAA,IAC7C,QAAA,EAAU;AAAA,GACZ;AAAA,EACA;AAAA;AAAA,IAEE,KAAA,EAAO,CAAC,YAAA,EAAc,YAAY,CAAA;AAAA,IAClC,QAAA,EAAU;AAAA;AAEd;AAEA,MAAM,cAAA,GAAiB;AAAA,EACrB;AAAA,IACE,IAAA,EAAM;AAAA,MACJ,KAAA,EAAO,EAAA;AAAA,MACP,KAAA,EAAO;AAAA,KACT;AAAA,IACA,IAAA,EAAM,WAAA;AAAA,IACN,IAAA,EAAM,YAAA;AAAA,IACN,QAAA,EAAU,YAAA;AAAA,IACV,QAAA,EAAU;AAAA,MACR;AAAA,QACE,IAAA,EAAM,WAAA;AAAA,QACN,IAAA,EAAM,YAAA;AAAA,QACN,SAAA,EAAW,4BAAA;AAAA,QACX,IAAA,EAAM;AAAA,UACJ,QAAA,EAAU,IAAA;AAAA,UACV,KAAA,EAAO;AAAA;AACT,OACF;AAAA,MACA;AAAA,QACE,IAAA,EAAM,WAAA;AAAA,QACN,IAAA,EAAM,YAAA;AAAA,QACN,SAAA,EAAW,4BAAA;AAAA,QACX,IAAA,EAAM;AAAA,UACJ,KAAA,EAAO;AAAA;AACT;AACF;AACF;AAEJ,CAAA;AAEA,MAAM,gBAAA,GAAmB,CAAC,IAAA,KAAqC;AAC7D,EAAA,MAAM,aAAA,GAAgB;AAAA,IACpB,KAAA,EAAO;AAAA,MACL,SAAA,EAAW,6BAAA;AAAA,MACX,IAAA,EAAM;AAAA,QACJ,IAAA,EAAM,mBAAA;AAAA,QACN,KAAA,EAAO;AAAA,OACT;AAAA,MACA,IAAA,EAAM,wBAAA;AAAA,MACN,IAAA,EAAM;AAAA,KACR;AAAA,IACA,KAAA,EAAO;AAAA,MACL,SAAA,EAAW,6BAAA;AAAA,MACX,IAAA,EAAM;AAAA,QACJ,IAAA,EAAM,mBAAA;AAAA,QACN,KAAA,EAAO;AAAA,OACT;AAAA,MACA,IAAA,EAAM,wBAAA;AAAA,MACN,IAAA,EAAM;AAAA,KACR;AAAA,IACA,IAAA,EAAM;AAAA,MACJ,SAAA,EAAW,4BAAA;AAAA,MACX,IAAA,EAAM;AAAA,QACJ,IAAA,EAAM,mBAAA;AAAA,QACN,KAAA,EAAO;AAAA,OACT;AAAA,MACA,IAAA,EAAM,uBAAA;AAAA,MACN,IAAA,EAAM;AAAA;AACR,GACF;AAEA,EAAA,OAAO;AAAA,IACL;AAAA,MACE,IAAA,EAAM;AAAA,QACJ,IAAA,EAAM,wBAAA;AAAA,QACN,SAAA,EAAW,IAAA;AAAA,QACX,KAAA,EAAO,GAAA;AAAA,QACP,KAAA,EAAO;AAAA,OACT;AAAA,MACA,IAAA,EAAM,OAAA;AAAA,MACN,IAAA,EAAM,QAAA;AAAA,MACN,QAAA,EAAU,eAAA;AAAA,MACV,QAAA,EAAU;AAAA,QACR;AAAA,UACE,IAAA,EAAM,aAAA;AAAA,UACN,IAAA,EAAM,cAAA;AAAA,UACN,IAAA,EAAM;AAAA,YACJ,IAAA,EAAM,uBAAA;AAAA,YACN,KAAA,EAAO;AAAA,WACT;AAAA,UACA,QAAA,EAAU,4BAAA;AAAA,UACV,QAAA,EAAU;AAAA,YACR;AAAA,cACE,IAAA,EAAM,uBAAA;AAAA,cACN,IAAA,EAAM,4BAAA;AAAA,cACN,SAAA,EAAW,qBAAA;AAAA,cACX,IAAA,EAAM;AAAA,gBACJ,IAAA,EAAM,2BAAA;AAAA,gBACN,KAAA,EAAO;AAAA;AACT,aACF;AAAA,YACA;AAAA,cACE,IAAA,EAAM,yBAAA;AAAA,cACN,IAAA,EAAM,8BAAA;AAAA,cACN,SAAA,EAAW,8BAAA;AAAA,cACX,IAAA,EAAM;AAAA,gBACJ,IAAA,EAAM,mBAAA;AAAA,gBACN,KAAA,EAAO;AAAA;AACT,aACF;AAAA,YACA;AAAA,cACE,IAAA,EAAM,0BAAA;AAAA,cACN,IAAA,EAAM,gCAAA;AAAA,cACN,SAAA,EAAW,gCAAA;AAAA,cACX,IAAA,EAAM;AAAA,gBACJ,SAAA,EAAW,CAAC,SAAS,CAAA;AAAA,gBACrB,IAAA,EAAM,mBAAA;AAAA,gBACN,wBAAA,EAA0B,IAAA;AAAA,gBAC1B,KAAA,EAAO;AAAA;AACT,aACF;AAAA,YACA,cAAc,IAAI;AAAA;AACpB;AACF;AACF;AACF,GACF;AACF,CAAA;AAEO,MAAM,UAAA,GAAa;AAAA,EACxB;AAAA,IACE,OAAO,CAAC,GAAG,gBAAgB,GAAG,gBAAA,CAAiB,OAAO,CAAC,CAAA;AAAA,IACvD,QAAA,EAAU;AAAA,GACZ;AAAA,EACA;AAAA,IACE,OAAO,CAAC,GAAG,gBAAgB,GAAG,gBAAA,CAAiB,OAAO,CAAC,CAAA;AAAA,IACvD,QAAA,EAAU;AAAA,GACZ;AAAA,EACA;AAAA,IACE,OAAO,CAAC,GAAG,gBAAgB,GAAG,gBAAA,CAAiB,MAAM,CAAC,CAAA;AAAA,IACtD,QAAA,EAAU;AAAA;AAEd;AAEO,MAAM,cAAA,GAAiB;AAAA,EAC5B;AAAA,IACE,EAAA,EAAI,CAAA;AAAA,IACJ,IAAA,EAAM,WAAA;AAAA,IACN,MAAA,EAAQ,CAAA;AAAA,IACR,IAAA,EAAM,MAAA;AAAA,IACN,IAAA,EAAM,eAAA;AAAA,IACN,IAAA,EAAM,YAAA;AAAA,IACN,SAAA,EAAW,4BAAA;AAAA,IACX,IAAA,EAAM;AAAA,MACJ,IAAA,EAAM,kBAAA;AAAA,MACN,KAAA,EAAO,0BAAA;AAAA,MACP,QAAA,EAAU,IAAA;AAAA,MACV,KAAA,EAAO;AAAA;AACT,GACF;AAAA,EACA;AAAA,IACE,EAAA,EAAI,CAAA;AAAA,IACJ,IAAA,EAAM;AAAA,MACJ,IAAA,EAAM,iBAAA;AAAA,MACN,KAAA,EAAO,IAAA;AAAA,MACP,KAAA,EAAO,cAAA;AAAA,MACP,KAAA,EAAO,KAAA;AAAA,MACP,SAAA,EAAW,QAAA;AAAA,MACX,aAAA,EAAe;AAAA,KACjB;AAAA,IACA,MAAA,EAAQ,CAAA;AAAA,IACR,IAAA,EAAM,SAAA;AAAA,IACN,IAAA,EAAM,QAAA;AAAA,IACN,IAAA,EAAM,SAAA;AAAA,IACN,QAAA,EAAU;AAAA,MACR;AAAA,QACE,EAAA,EAAI,GAAA;AAAA,QACJ,GAAA,EAAK,CAAA;AAAA,QACL,IAAA,EAAM,cAAA;AAAA,QACN,IAAA,EAAM,YAAA;AAAA,QACN,QAAA,EAAU,kBAAA;AAAA,QACV,MAAA,EAAQ,CAAA;AAAA,QACR,IAAA,EAAM,MAAA;AAAA,QACN,IAAA,EAAM;AAAA,UACJ,IAAA,EAAM,aAAA;AAAA,UACN,KAAA,EAAO;AAAA,SACT;AAAA,QACA,SAAA,EAAW,mBAAA;AAAA,QACX,QAAA,EAAU;AAAA,UACR;AAAA,YACE,EAAA,EAAI,KAAA;AAAA,YACJ,GAAA,EAAK,GAAA;AAAA,YACL,IAAA,EAAM,kBAAA;AAAA,YACN,MAAA,EAAQ,CAAA;AAAA,YACR,IAAA,EAAM,QAAA;AAAA,YACN,QAAA,EAAU,oBAAA;AAAA,YACV,IAAA,EAAM,EAAE,KAAA,EAAO,eAAA;AAAgB,WACjC;AAAA,UACA;AAAA,YACE,EAAA,EAAI,KAAA;AAAA,YACJ,GAAA,EAAK,GAAA;AAAA,YACL,IAAA,EAAM,gBAAA;AAAA,YACN,MAAA,EAAQ,CAAA;AAAA,YACR,IAAA,EAAM,QAAA;AAAA,YACN,QAAA,EAAU,kBAAA;AAAA,YACV,IAAA,EAAM,EAAE,KAAA,EAAO,aAAA;AAAc,WAC/B;AAAA,UACA;AAAA,YACE,EAAA,EAAI,KAAA;AAAA,YACJ,GAAA,EAAK,GAAA;AAAA,YACL,IAAA,EAAM,kBAAA;AAAA,YACN,MAAA,EAAQ,CAAA;AAAA,YACR,IAAA,EAAM,QAAA;AAAA,YACN,QAAA,EAAU,oBAAA;AAAA,YACV,IAAA,EAAM,EAAE,KAAA,EAAO,eAAA;AAAgB;AACjC;AACF,OACF;AAAA,MACA;AAAA,QACE,EAAA,EAAI,GAAA;AAAA,QACJ,GAAA,EAAK,CAAA;AAAA,QACL,IAAA,EAAM,cAAA;AAAA,QACN,IAAA,EAAM,YAAA;AAAA,QACN,MAAA,EAAQ,CAAA;AAAA,QACR,IAAA,EAAM,MAAA;AAAA,QACN,QAAA,EAAU,kBAAA;AAAA,QACV,IAAA,EAAM;AAAA,UACJ,IAAA,EAAM,2BAAA;AAAA,UACN,KAAA,EAAO;AAAA,SACT;AAAA,QACA,SAAA,EAAW,mBAAA;AAAA,QACX,QAAA,EAAU;AAAA,UACR;AAAA,YACE,EAAA,EAAI,KAAA;AAAA,YACJ,GAAA,EAAK,GAAA;AAAA,YACL,IAAA,EAAM,kBAAA;AAAA,YACN,MAAA,EAAQ,CAAA;AAAA,YACR,IAAA,EAAM,QAAA;AAAA,YACN,QAAA,EAAU,oBAAA;AAAA,YACV,IAAA,EAAM,EAAE,KAAA,EAAO,eAAA;AAAgB,WACjC;AAAA,UACA;AAAA,YACE,EAAA,EAAI,KAAA;AAAA,YACJ,GAAA,EAAK,GAAA;AAAA,YACL,IAAA,EAAM,gBAAA;AAAA,YACN,MAAA,EAAQ,CAAA;AAAA,YACR,IAAA,EAAM,QAAA;AAAA,YACN,QAAA,EAAU,kBAAA;AAAA,YACV,IAAA,EAAM,EAAE,KAAA,EAAO,aAAA;AAAc,WAC/B;AAAA,UACA;AAAA,YACE,EAAA,EAAI,KAAA;AAAA,YACJ,GAAA,EAAK,GAAA;AAAA,YACL,IAAA,EAAM,kBAAA;AAAA,YACN,MAAA,EAAQ,CAAA;AAAA,YACR,IAAA,EAAM,QAAA;AAAA,YACN,QAAA,EAAU,oBAAA;AAAA,YACV,IAAA,EAAM,EAAE,KAAA,EAAO,eAAA;AAAgB;AACjC;AACF;AACF;AACF,GACF;AAAA,EACA;AAAA,IACE,EAAA,EAAI,CAAA;AAAA,IACJ,IAAA,EAAM;AAAA,MACJ,SAAA,EAAW,KAAA;AAAA,MACX,KAAA,EAAO,IAAA;AAAA,MACP,KAAA,EAAO,kBAAA;AAAA,MACP,IAAA,EAAM;AAAA,KACR;AAAA,IACA,IAAA,EAAM,SAAA;AAAA,IACN,IAAA,EAAM,aAAA;AAAA,IACN,IAAA,EAAM,SAAA;AAAA,IACN,MAAA,EAAQ,CAAA;AAAA,IACR,QAAA,EAAU;AAAA,MACR;AAAA,QACE,EAAA,EAAI,GAAA;AAAA,QACJ,GAAA,EAAK,CAAA;AAAA,QACL,IAAA,EAAM,cAAA;AAAA,QACN,IAAA,EAAM,sBAAA;AAAA,QACN,SAAA,EAAW,YAAA;AAAA,QACX,IAAA,EAAM,UAAA;AAAA,QACN,MAAA,EAAQ,CAAA;AAAA,QACR,IAAA,EAAM;AAAA,UACJ,IAAA,EAAM,aAAA;AAAA,UACN,SAAA,EAAW,sBAAA;AAAA,UACX,KAAA,EAAO;AAAA;AACT,OACF;AAAA,MACA;AAAA,QACE,EAAA,EAAI,GAAA;AAAA,QACJ,GAAA,EAAK,CAAA;AAAA,QACL,IAAA,EAAM,YAAA;AAAA,QACN,IAAA,EAAM,oBAAA;AAAA,QACN,SAAA,EAAW,YAAA;AAAA,QACX,IAAA,EAAM,MAAA;AAAA,QACN,MAAA,EAAQ,CAAA;AAAA,QACR,IAAA,EAAM;AAAA,UACJ,IAAA,EAAM,oBAAA;AAAA,UACN,IAAA,EAAM,0CAAA;AAAA,UACN,KAAA,EAAO;AAAA;AACT,OACF;AAAA,MACA;AAAA,QACE,EAAA,EAAI,GAAA;AAAA,QACJ,GAAA,EAAK,CAAA;AAAA,QACL,IAAA,EAAM,WAAA;AAAA,QACN,IAAA,EAAM,mBAAA;AAAA,QACN,SAAA,EAAW,YAAA;AAAA,QACX,IAAA,EAAM,MAAA;AAAA,QACN,MAAA,EAAQ,CAAA;AAAA,QACR,IAAA,EAAM;AAAA,UACJ,IAAA,EAAM,+BAAA;AAAA,UACN,SAAA,EAAW,KAAA;AAAA,UACX,IAAA,EAAM,sBAAA;AAAA,UACN,KAAA,EAAO;AAAA;AACT;AACF;AACF,GACF;AAAA,EACA;AAAA,IACE,EAAA,EAAI,EAAA;AAAA,IACJ,SAAA,EAAW,mBAAA;AAAA,IACX,IAAA,EAAM,MAAA;AAAA,IACN,MAAA,EAAQ,CAAA;AAAA,IACR,IAAA,EAAM;AAAA,MACJ,IAAA,EAAM,kBAAA;AAAA,MACN,KAAA,EAAO,IAAA;AAAA,MACP,KAAA,EAAO;AAAA,KACT;AAAA,IACA,IAAA,EAAM,OAAA;AAAA,IACN,IAAA,EAAM;AAAA;AAEV;AAEO,SAAS,WAAW,KAAA,EAAc;AACvC,EAAA,MAAM,MAAgB,EAAC;AACvB,EAAA,KAAA,CAAM,OAAA,CAAQ,CAAC,IAAA,KAAS;AACtB,IAAA,GAAA,CAAI,IAAA,CAAK,KAAK,EAAE,CAAA;AAChB,IAAA,IAAI,IAAA,CAAK,QAAA,IAAY,IAAA,CAAK,QAAA,CAAS,SAAS,CAAA,EAAG;AAC7C,MAAA,GAAA,CAAI,IAAA,CAAK,GAAG,UAAA,CAAW,IAAA,CAAK,QAAQ,CAAC,CAAA;AAAA;AACvC,GACD,CAAA;AACD,EAAA,OAAO,GAAA;AACT;;AC9XA,MAAA,mBAAA,GAAA,qBAAA;AACA,MAAA,oBAAA,GAAA,sBAAA;AAOA,SAAA,oBAAA,IAAA,EAAA;AACA,EAAA,OAAA,IAAA,IAAA,CAAA,IAAA,EAAA,qBAAA,EAAA,SAAA,EAAA,MAAA,CAAA;AACA;AAEA,SAAA,qBAAA,IAAA,EAAA;AACA,EAAA,OAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA,oBAAA,EAAA;AAAA,IACA,SAAA,EAAA;AAAA,GACA,CAAA;AACA;AAEA,SAAA,kBACA,KAAA,EACA;AACA,EAAA,MAAA,UAAA,GAAA,SAAA,CAAA,KAAA,EAAA,eAAA,CAAA;AACA,EAAA,IAAA,EAAA,UAAA,IAAA,IAAA,GAAA,MAAA,GAAA,UAAA,CAAA,UAAA,CAAA,QAAA,CAAA,CAAA,EAAA;AACA,IAAA,OAAA,IAAA;AAAA;AAGA,EAAA,MAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AACA,EAAA,IAAA;AACA,IAAA,MAAA,OAAA,GAAA,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,mBAAA,CAAA;AAEA,IAAA,MAAA,WAAA,OAAA,CAAA,QAAA;AACA,IAAA,MAAA,OAAA,UAAA,CAAA,IAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,aAAA,QAAA,CAAA;AACA,IAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,GAAA,UAAA,GAAA,IAAA;AACA,IAAA,OAAA,QAAA;AAAA,GACA,CAAA,MAAA;AACA,IAAA,OAAA,IAAA;AAAA;AAEA;AAEA,SAAA,mBACA,KAAA,EACA;AACA,EAAA,IAAA;AACA,IAAA,MAAA,OAAA,GAAA,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,oBAAA,CAAA;AACA,IAAA,MAAA,WAAA,OAAA,CAAA,QAAA;AACA,IAAA,MAAA,OAAA,UAAA,CAAA,IAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,aAAA,QAAA,CAAA;AACA,IAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,GAAA,UAAA,GAAA,IAAA;AACA,IAAA,OAAA,QAAA;AAAA,GACA,CAAA,MAAA;AACA,IAAA,OAAA,IAAA;AAAA;AAEA;;;;"}