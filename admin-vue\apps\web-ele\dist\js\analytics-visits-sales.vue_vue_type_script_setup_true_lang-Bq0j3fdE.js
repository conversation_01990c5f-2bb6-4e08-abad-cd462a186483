import{u as t,_ as s}from"./use-echarts-Bu2i2yR9.js";import{d as o,r as i,o as c,q as u,g as m,u as l}from"../jse/index-index-SSqEGcIT.js";const d=o({__name:"analytics-visits-sales",setup(p){const e=i(),{renderEcharts:r}=t(e);return c(()=>{r({series:[{animationDelay(){return Math.random()*400},animationEasing:"exponentialInOut",animationType:"scale",center:["50%","50%"],color:["#5ab1ef","#b6a2de","#67e0e3","#2ec7c9"],data:[{name:"外包",value:500},{name:"定制",value:310},{name:"技术支持",value:274},{name:"远程",value:400}].sort((a,n)=>a.value-n.value),name:"商业占比",radius:"80%",roseType:"radius",type:"pie"}],tooltip:{trigger:"item"}})}),(a,n)=>(m(),u(l(s),{ref_key:"chartRef",ref:e},null,512))}});export{d as _};
