var f=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable;var l=(s,a)=>{var t={};for(var e in s)b.call(s,e)&&a.indexOf(e)<0&&(t[e]=s[e]);if(s!=null&&f)for(var e of f(s))a.indexOf(e)<0&&v.call(s,e)&&(t[e]=s[e]);return t};import{bv as h,bw as y,bx as B,by as C}from"./bootstrap-BmSDnAET.js";import{a4 as c,aa as d,ab as i,ac as u,a8 as p,af as x,ag as P,a7 as n,J as m,ad as _,aW as g}from"../jse/index-index-BAMHRxBA.js";const V=c({__name:"Tabs",props:{defaultValue:{},orientation:{},dir:{},activationMode:{},modelValue:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(s,{emit:a}){const r=h(s,a);return(o,$)=>(i(),d(n(y),x(P(n(r))),{default:u(()=>[p(o.$slots,"default")]),_:3},16))}}),M=c({__name:"TabsContent",props:{value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(s){const a=s,t=m(()=>{const o=a,{class:e}=o;return l(o,["class"])});return(e,r)=>(i(),d(n(B),_({class:n(g)("ring-offset-background focus-visible:ring-ring mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",a.class)},t.value),{default:u(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}}),S=c({__name:"TabsList",props:{loop:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(s){const a=s,t=m(()=>{const o=a,{class:e}=o;return l(o,["class"])});return(e,r)=>(i(),d(n(C),_(t.value,{class:n(g)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1",a.class)}),{default:u(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}});export{V as _,S as a,M as b};
