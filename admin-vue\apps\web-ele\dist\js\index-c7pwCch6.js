var Sr=Object.defineProperty;var Pt=Object.getOwnPropertySymbols;var Pr=Object.prototype.hasOwnProperty,Lr=Object.prototype.propertyIsEnumerable;var Lt=(e,t,r)=>t in e?Sr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,tt=(e,t)=>{for(var r in t||(t={}))Pr.call(t,r)&&Lt(e,r,t[r]);if(Pt)for(var r of Pt(t))Lr.call(t,r)&&Lt(e,r,t[r]);return e};import{au as Rt,av as Rr}from"./bootstrap-CYivmKoJ.js";import{r as $t,Q as $r,ac as pe,ae as Bt,w as jt,b as Br,x as jr,H as Ir,o as Hr,z as kr,V as Vr}from"../jse/index-index-SSqEGcIT.js";var N="top",X="bottom",Y="right",U="left",Ot="auto",Fe=[N,X,Y,U],Ee="start",Ve="end",Nr="clippingParents",tr="viewport",Ie="popper",Ur="reference",It=Fe.reduce(function(e,t){return e.concat([t+"-"+Ee,t+"-"+Ve])},[]),rr=[].concat(Fe,[Ot]).reduce(function(e,t){return e.concat([t,t+"-"+Ee,t+"-"+Ve])},[]),Fr="beforeRead",Wr="read",Xr="afterRead",Yr="beforeMain",qr="main",_r="afterMain",zr="beforeWrite",Gr="write",Kr="afterWrite",Jr=[Fr,Wr,Xr,Yr,qr,_r,zr,Gr,Kr];function re(e){return e?(e.nodeName||"").toLowerCase():null}function G(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Ce(e){var t=G(e).Element;return e instanceof t||e instanceof Element}function W(e){var t=G(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function nr(e){if(typeof ShadowRoot=="undefined")return!1;var t=G(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Qr(e){var t=e.state;Object.keys(t.elements).forEach(function(r){var n=t.styles[r]||{},i=t.attributes[r]||{},a=t.elements[r];!W(a)||!re(a)||(Object.assign(a.style,n),Object.keys(i).forEach(function(s){var f=i[s];f===!1?a.removeAttribute(s):a.setAttribute(s,f===!0?"":f)}))})}function Zr(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(n){var i=t.elements[n],a=t.attributes[n]||{},s=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:r[n]),f=s.reduce(function(p,c){return p[c]="",p},{});!W(i)||!re(i)||(Object.assign(i.style,f),Object.keys(a).forEach(function(p){i.removeAttribute(p)}))})}}var ir={name:"applyStyles",enabled:!0,phase:"write",fn:Qr,effect:Zr,requires:["computeStyles"]};function ee(e){return e.split("-")[0]}var ge=Math.max,at=Math.min,De=Math.round;function Me(e,t){t===void 0&&(t=!1);var r=e.getBoundingClientRect(),n=1,i=1;if(W(e)&&t){var a=e.offsetHeight,s=e.offsetWidth;s>0&&(n=De(r.width)/s||1),a>0&&(i=De(r.height)/a||1)}return{width:r.width/n,height:r.height/i,top:r.top/i,right:r.right/n,bottom:r.bottom/i,left:r.left/n,x:r.left/n,y:r.top/i}}function xt(e){var t=Me(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function or(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&nr(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function ne(e){return G(e).getComputedStyle(e)}function en(e){return["table","td","th"].indexOf(re(e))>=0}function le(e){return((Ce(e)?e.ownerDocument:e.document)||window.document).documentElement}function ut(e){return re(e)==="html"?e:e.assignedSlot||e.parentNode||(nr(e)?e.host:null)||le(e)}function Ht(e){return!W(e)||ne(e).position==="fixed"?null:e.offsetParent}function tn(e){var t=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,r=navigator.userAgent.indexOf("Trident")!==-1;if(r&&W(e)){var n=ne(e);if(n.position==="fixed")return null}for(var i=ut(e);W(i)&&["html","body"].indexOf(re(i))<0;){var a=ne(i);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return i;i=i.parentNode}return null}function We(e){for(var t=G(e),r=Ht(e);r&&en(r)&&ne(r).position==="static";)r=Ht(r);return r&&(re(r)==="html"||re(r)==="body"&&ne(r).position==="static")?t:r||tn(e)||t}function Tt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function He(e,t,r){return ge(e,at(t,r))}function rn(e,t,r){var n=He(e,t,r);return n>r?r:n}function ar(){return{top:0,right:0,bottom:0,left:0}}function sr(e){return Object.assign({},ar(),e)}function ur(e,t){return t.reduce(function(r,n){return r[n]=e,r},{})}var nn=function(t,r){return t=typeof t=="function"?t(Object.assign({},r.rects,{placement:r.placement})):t,sr(typeof t!="number"?t:ur(t,Fe))};function on(e){var t,r=e.state,n=e.name,i=e.options,a=r.elements.arrow,s=r.modifiersData.popperOffsets,f=ee(r.placement),p=Tt(f),c=[U,Y].indexOf(f)>=0,l=c?"height":"width";if(!(!a||!s)){var A=nn(i.padding,r),E=xt(a),O=p==="y"?N:U,m=p==="y"?X:Y,y=r.rects.reference[l]+r.rects.reference[p]-s[p]-r.rects.popper[l],b=s[p]-r.rects.reference[p],w=We(a),T=w?p==="y"?w.clientHeight||0:w.clientWidth||0:0,D=y/2-b/2,o=A[O],C=T-E[l]-A[m],v=T/2-E[l]/2+D,d=He(o,v,C),h=p;r.modifiersData[n]=(t={},t[h]=d,t.centerOffset=d-v,t)}}function an(e){var t=e.state,r=e.options,n=r.element,i=n===void 0?"[data-popper-arrow]":n;i!=null&&(typeof i=="string"&&(i=t.elements.popper.querySelector(i),!i)||or(t.elements.popper,i)&&(t.elements.arrow=i))}var sn={name:"arrow",enabled:!0,phase:"main",fn:on,effect:an,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Se(e){return e.split("-")[1]}var un={top:"auto",right:"auto",bottom:"auto",left:"auto"};function fn(e){var t=e.x,r=e.y,n=window,i=n.devicePixelRatio||1;return{x:De(t*i)/i||0,y:De(r*i)/i||0}}function kt(e){var t,r=e.popper,n=e.popperRect,i=e.placement,a=e.variation,s=e.offsets,f=e.position,p=e.gpuAcceleration,c=e.adaptive,l=e.roundOffsets,A=e.isFixed,E=l===!0?fn(s):typeof l=="function"?l(s):s,O=E.x,m=O===void 0?0:O,y=E.y,b=y===void 0?0:y,w=s.hasOwnProperty("x"),T=s.hasOwnProperty("y"),D=U,o=N,C=window;if(c){var v=We(r),d="clientHeight",h="clientWidth";if(v===G(r)&&(v=le(r),ne(v).position!=="static"&&f==="absolute"&&(d="scrollHeight",h="scrollWidth")),v=v,i===N||(i===U||i===Y)&&a===Ve){o=X;var P=A&&C.visualViewport?C.visualViewport.height:v[d];b-=P-n.height,b*=p?1:-1}if(i===U||(i===N||i===X)&&a===Ve){D=Y;var R=A&&C.visualViewport?C.visualViewport.width:v[h];m-=R-n.width,m*=p?1:-1}}var $=Object.assign({position:f},c&&un);if(p){var L;return Object.assign({},$,(L={},L[o]=T?"0":"",L[D]=w?"0":"",L.transform=(C.devicePixelRatio||1)<=1?"translate("+m+"px, "+b+"px)":"translate3d("+m+"px, "+b+"px, 0)",L))}return Object.assign({},$,(t={},t[o]=T?b+"px":"",t[D]=w?m+"px":"",t.transform="",t))}function pn(e){var t=e.state,r=e.options,n=r.gpuAcceleration,i=n===void 0?!0:n,a=r.adaptive,s=a===void 0?!0:a,f=r.roundOffsets,p=f===void 0?!0:f,c={placement:ee(t.placement),variation:Se(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,kt(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:p})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,kt(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:p})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var ln={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:pn,data:{}},rt={passive:!0};function cn(e){var t=e.state,r=e.instance,n=e.options,i=n.scroll,a=i===void 0?!0:i,s=n.resize,f=s===void 0?!0:s,p=G(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach(function(l){l.addEventListener("scroll",r.update,rt)}),f&&p.addEventListener("resize",r.update,rt),function(){a&&c.forEach(function(l){l.removeEventListener("scroll",r.update,rt)}),f&&p.removeEventListener("resize",r.update,rt)}}var dn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:cn,data:{}},vn={left:"right",right:"left",bottom:"top",top:"bottom"};function ot(e){return e.replace(/left|right|bottom|top/g,function(t){return vn[t]})}var mn={start:"end",end:"start"};function Vt(e){return e.replace(/start|end/g,function(t){return mn[t]})}function At(e){var t=G(e),r=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:r,scrollTop:n}}function Et(e){return Me(le(e)).left+At(e).scrollLeft}function hn(e){var t=G(e),r=le(e),n=t.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,f=0;return n&&(i=n.width,a=n.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(s=n.offsetLeft,f=n.offsetTop)),{width:i,height:a,x:s+Et(e),y:f}}function gn(e){var t,r=le(e),n=At(e),i=(t=e.ownerDocument)==null?void 0:t.body,a=ge(r.scrollWidth,r.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),s=ge(r.scrollHeight,r.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),f=-n.scrollLeft+Et(e),p=-n.scrollTop;return ne(i||r).direction==="rtl"&&(f+=ge(r.clientWidth,i?i.clientWidth:0)-a),{width:a,height:s,x:f,y:p}}function Ct(e){var t=ne(e),r=t.overflow,n=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+i+n)}function fr(e){return["html","body","#document"].indexOf(re(e))>=0?e.ownerDocument.body:W(e)&&Ct(e)?e:fr(ut(e))}function ke(e,t){var r;t===void 0&&(t=[]);var n=fr(e),i=n===((r=e.ownerDocument)==null?void 0:r.body),a=G(n),s=i?[a].concat(a.visualViewport||[],Ct(n)?n:[]):n,f=t.concat(s);return i?f:f.concat(ke(ut(s)))}function gt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function yn(e){var t=Me(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Nt(e,t){return t===tr?gt(hn(e)):Ce(t)?yn(t):gt(gn(le(e)))}function bn(e){var t=ke(ut(e)),r=["absolute","fixed"].indexOf(ne(e).position)>=0,n=r&&W(e)?We(e):e;return Ce(n)?t.filter(function(i){return Ce(i)&&or(i,n)&&re(i)!=="body"&&(r?ne(i).position!=="static":!0)}):[]}function wn(e,t,r){var n=t==="clippingParents"?bn(e):[].concat(t),i=[].concat(n,[r]),a=i[0],s=i.reduce(function(f,p){var c=Nt(e,p);return f.top=ge(c.top,f.top),f.right=at(c.right,f.right),f.bottom=at(c.bottom,f.bottom),f.left=ge(c.left,f.left),f},Nt(e,a));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function pr(e){var t=e.reference,r=e.element,n=e.placement,i=n?ee(n):null,a=n?Se(n):null,s=t.x+t.width/2-r.width/2,f=t.y+t.height/2-r.height/2,p;switch(i){case N:p={x:s,y:t.y-r.height};break;case X:p={x:s,y:t.y+t.height};break;case Y:p={x:t.x+t.width,y:f};break;case U:p={x:t.x-r.width,y:f};break;default:p={x:t.x,y:t.y}}var c=i?Tt(i):null;if(c!=null){var l=c==="y"?"height":"width";switch(a){case Ee:p[c]=p[c]-(t[l]/2-r[l]/2);break;case Ve:p[c]=p[c]+(t[l]/2-r[l]/2);break}}return p}function Ne(e,t){t===void 0&&(t={});var r=t,n=r.placement,i=n===void 0?e.placement:n,a=r.boundary,s=a===void 0?Nr:a,f=r.rootBoundary,p=f===void 0?tr:f,c=r.elementContext,l=c===void 0?Ie:c,A=r.altBoundary,E=A===void 0?!1:A,O=r.padding,m=O===void 0?0:O,y=sr(typeof m!="number"?m:ur(m,Fe)),b=l===Ie?Ur:Ie,w=e.rects.popper,T=e.elements[E?b:l],D=wn(Ce(T)?T:T.contextElement||le(e.elements.popper),s,p),o=Me(e.elements.reference),C=pr({reference:o,element:w,placement:i}),v=gt(Object.assign({},w,C)),d=l===Ie?v:o,h={top:D.top-d.top+y.top,bottom:d.bottom-D.bottom+y.bottom,left:D.left-d.left+y.left,right:d.right-D.right+y.right},P=e.modifiersData.offset;if(l===Ie&&P){var R=P[i];Object.keys(h).forEach(function($){var L=[Y,X].indexOf($)>=0?1:-1,B=[N,X].indexOf($)>=0?"y":"x";h[$]+=R[B]*L})}return h}function On(e,t){t===void 0&&(t={});var r=t,n=r.placement,i=r.boundary,a=r.rootBoundary,s=r.padding,f=r.flipVariations,p=r.allowedAutoPlacements,c=p===void 0?rr:p,l=Se(n),A=l?f?It:It.filter(function(m){return Se(m)===l}):Fe,E=A.filter(function(m){return c.indexOf(m)>=0});E.length===0&&(E=A);var O=E.reduce(function(m,y){return m[y]=Ne(e,{placement:y,boundary:i,rootBoundary:a,padding:s})[ee(y)],m},{});return Object.keys(O).sort(function(m,y){return O[m]-O[y]})}function xn(e){if(ee(e)===Ot)return[];var t=ot(e);return[Vt(e),t,Vt(t)]}function Tn(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var i=r.mainAxis,a=i===void 0?!0:i,s=r.altAxis,f=s===void 0?!0:s,p=r.fallbackPlacements,c=r.padding,l=r.boundary,A=r.rootBoundary,E=r.altBoundary,O=r.flipVariations,m=O===void 0?!0:O,y=r.allowedAutoPlacements,b=t.options.placement,w=ee(b),T=w===b,D=p||(T||!m?[ot(b)]:xn(b)),o=[b].concat(D).reduce(function(ie,_){return ie.concat(ee(_)===Ot?On(t,{placement:_,boundary:l,rootBoundary:A,padding:c,flipVariations:m,allowedAutoPlacements:y}):_)},[]),C=t.rects.reference,v=t.rects.popper,d=new Map,h=!0,P=o[0],R=0;R<o.length;R++){var $=o[R],L=ee($),B=Se($)===Ee,V=[N,X].indexOf(L)>=0,q=V?"width":"height",I=Ne(t,{placement:$,boundary:l,rootBoundary:A,altBoundary:E,padding:c}),H=V?B?Y:U:B?X:N;C[q]>v[q]&&(H=ot(H));var j=ot(H),K=[];if(a&&K.push(I[L]<=0),f&&K.push(I[H]<=0,I[j]<=0),K.every(function(ie){return ie})){P=$,h=!1;break}d.set($,K)}if(h)for(var J=m?3:1,ce=function(_){var oe=o.find(function(ye){var ae=d.get(ye);if(ae)return ae.slice(0,_).every(function(be){return be})});if(oe)return P=oe,"break"},Q=J;Q>0;Q--){var de=ce(Q);if(de==="break")break}t.placement!==P&&(t.modifiersData[n]._skip=!0,t.placement=P,t.reset=!0)}}var An={name:"flip",enabled:!0,phase:"main",fn:Tn,requiresIfExists:["offset"],data:{_skip:!1}};function Ut(e,t,r){return r===void 0&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function Ft(e){return[N,Y,X,U].some(function(t){return e[t]>=0})}function En(e){var t=e.state,r=e.name,n=t.rects.reference,i=t.rects.popper,a=t.modifiersData.preventOverflow,s=Ne(t,{elementContext:"reference"}),f=Ne(t,{altBoundary:!0}),p=Ut(s,n),c=Ut(f,i,a),l=Ft(p),A=Ft(c);t.modifiersData[r]={referenceClippingOffsets:p,popperEscapeOffsets:c,isReferenceHidden:l,hasPopperEscaped:A},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":l,"data-popper-escaped":A})}var Cn={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:En};function Dn(e,t,r){var n=ee(e),i=[U,N].indexOf(n)>=0?-1:1,a=typeof r=="function"?r(Object.assign({},t,{placement:e})):r,s=a[0],f=a[1];return s=s||0,f=(f||0)*i,[U,Y].indexOf(n)>=0?{x:f,y:s}:{x:s,y:f}}function Mn(e){var t=e.state,r=e.options,n=e.name,i=r.offset,a=i===void 0?[0,0]:i,s=rr.reduce(function(l,A){return l[A]=Dn(A,t.rects,a),l},{}),f=s[t.placement],p=f.x,c=f.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=p,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=s}var Sn={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Mn};function Pn(e){var t=e.state,r=e.name;t.modifiersData[r]=pr({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}var Ln={name:"popperOffsets",enabled:!0,phase:"read",fn:Pn,data:{}};function Rn(e){return e==="x"?"y":"x"}function $n(e){var t=e.state,r=e.options,n=e.name,i=r.mainAxis,a=i===void 0?!0:i,s=r.altAxis,f=s===void 0?!1:s,p=r.boundary,c=r.rootBoundary,l=r.altBoundary,A=r.padding,E=r.tether,O=E===void 0?!0:E,m=r.tetherOffset,y=m===void 0?0:m,b=Ne(t,{boundary:p,rootBoundary:c,padding:A,altBoundary:l}),w=ee(t.placement),T=Se(t.placement),D=!T,o=Tt(w),C=Rn(o),v=t.modifiersData.popperOffsets,d=t.rects.reference,h=t.rects.popper,P=typeof y=="function"?y(Object.assign({},t.rects,{placement:t.placement})):y,R=typeof P=="number"?{mainAxis:P,altAxis:P}:Object.assign({mainAxis:0,altAxis:0},P),$=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,L={x:0,y:0};if(v){if(a){var B,V=o==="y"?N:U,q=o==="y"?X:Y,I=o==="y"?"height":"width",H=v[o],j=H+b[V],K=H-b[q],J=O?-h[I]/2:0,ce=T===Ee?d[I]:h[I],Q=T===Ee?-h[I]:-d[I],de=t.elements.arrow,ie=O&&de?xt(de):{width:0,height:0},_=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:ar(),oe=_[V],ye=_[q],ae=He(0,d[I],ie[I]),be=D?d[I]/2-J-ae-oe-R.mainAxis:ce-ae-oe-R.mainAxis,ue=D?-d[I]/2+J+ae+ye+R.mainAxis:Q+ae+ye+R.mainAxis,we=t.elements.arrow&&We(t.elements.arrow),Xe=we?o==="y"?we.clientTop||0:we.clientLeft||0:0,Le=(B=$==null?void 0:$[o])!=null?B:0,Ye=H+be-Le-Xe,qe=H+ue-Le,Re=He(O?at(j,Ye):j,H,O?ge(K,qe):K);v[o]=Re,L[o]=Re-H}if(f){var $e,_e=o==="x"?N:U,ze=o==="x"?X:Y,se=v[C],fe=C==="y"?"height":"width",Be=se+b[_e],ve=se-b[ze],je=[N,U].indexOf(w)!==-1,Ge=($e=$==null?void 0:$[C])!=null?$e:0,Ke=je?Be:se-d[fe]-h[fe]-Ge+R.altAxis,Je=je?se+d[fe]+h[fe]-Ge-R.altAxis:ve,Qe=O&&je?rn(Ke,se,Je):He(O?Ke:Be,se,O?Je:ve);v[C]=Qe,L[C]=Qe-se}t.modifiersData[n]=L}}var Bn={name:"preventOverflow",enabled:!0,phase:"main",fn:$n,requiresIfExists:["offset"]};function jn(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function In(e){return e===G(e)||!W(e)?At(e):jn(e)}function Hn(e){var t=e.getBoundingClientRect(),r=De(t.width)/e.offsetWidth||1,n=De(t.height)/e.offsetHeight||1;return r!==1||n!==1}function kn(e,t,r){r===void 0&&(r=!1);var n=W(t),i=W(t)&&Hn(t),a=le(t),s=Me(e,i),f={scrollLeft:0,scrollTop:0},p={x:0,y:0};return(n||!n&&!r)&&((re(t)!=="body"||Ct(a))&&(f=In(t)),W(t)?(p=Me(t,!0),p.x+=t.clientLeft,p.y+=t.clientTop):a&&(p.x=Et(a))),{x:s.left+f.scrollLeft-p.x,y:s.top+f.scrollTop-p.y,width:s.width,height:s.height}}function Vn(e){var t=new Map,r=new Set,n=[];e.forEach(function(a){t.set(a.name,a)});function i(a){r.add(a.name);var s=[].concat(a.requires||[],a.requiresIfExists||[]);s.forEach(function(f){if(!r.has(f)){var p=t.get(f);p&&i(p)}}),n.push(a)}return e.forEach(function(a){r.has(a.name)||i(a)}),n}function Nn(e){var t=Vn(e);return Jr.reduce(function(r,n){return r.concat(t.filter(function(i){return i.phase===n}))},[])}function Un(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}function Fn(e){var t=e.reduce(function(r,n){var i=r[n.name];return r[n.name]=i?Object.assign({},i,n,{options:Object.assign({},i.options,n.options),data:Object.assign({},i.data,n.data)}):n,r},{});return Object.keys(t).map(function(r){return t[r]})}var Wt={placement:"bottom",modifiers:[],strategy:"absolute"};function Xt(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function Wn(e){e===void 0&&(e={});var t=e,r=t.defaultModifiers,n=r===void 0?[]:r,i=t.defaultOptions,a=i===void 0?Wt:i;return function(f,p,c){c===void 0&&(c=a);var l={placement:"bottom",orderedModifiers:[],options:Object.assign({},Wt,a),modifiersData:{},elements:{reference:f,popper:p},attributes:{},styles:{}},A=[],E=!1,O={state:l,setOptions:function(w){var T=typeof w=="function"?w(l.options):w;y(),l.options=Object.assign({},a,l.options,T),l.scrollParents={reference:Ce(f)?ke(f):f.contextElement?ke(f.contextElement):[],popper:ke(p)};var D=Nn(Fn([].concat(n,l.options.modifiers)));return l.orderedModifiers=D.filter(function(o){return o.enabled}),m(),O.update()},forceUpdate:function(){if(!E){var w=l.elements,T=w.reference,D=w.popper;if(Xt(T,D)){l.rects={reference:kn(T,We(D),l.options.strategy==="fixed"),popper:xt(D)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(R){return l.modifiersData[R.name]=Object.assign({},R.data)});for(var o=0;o<l.orderedModifiers.length;o++){if(l.reset===!0){l.reset=!1,o=-1;continue}var C=l.orderedModifiers[o],v=C.fn,d=C.options,h=d===void 0?{}:d,P=C.name;typeof v=="function"&&(l=v({state:l,options:h,name:P,instance:O})||l)}}}},update:Un(function(){return new Promise(function(b){O.forceUpdate(),b(l)})}),destroy:function(){y(),E=!0}};if(!Xt(f,p))return O;O.setOptions(c).then(function(b){!E&&c.onFirstUpdate&&c.onFirstUpdate(b)});function m(){l.orderedModifiers.forEach(function(b){var w=b.name,T=b.options,D=T===void 0?{}:T,o=b.effect;if(typeof o=="function"){var C=o({state:l,name:w,instance:O,options:D}),v=function(){};A.push(C||v)}})}function y(){A.forEach(function(b){return b()}),A=[]}return O}}var Xn=[dn,Ln,ln,ir,Sn,An,Bn,sn,Cn],Yn=Wn({defaultModifiers:Xn}),qn="tippy-box",lr="tippy-content",cr="tippy-backdrop",dr="tippy-arrow",vr="tippy-svg-arrow",he={passive:!0,capture:!0},mr=function(){return document.body};function ct(e,t,r){if(Array.isArray(e)){var n=e[t];return n==null?Array.isArray(r)?r[t]:r:n}return e}function Dt(e,t){var r={}.toString.call(e);return r.indexOf("[object")===0&&r.indexOf(t+"]")>-1}function hr(e,t){return typeof e=="function"?e.apply(void 0,t):e}function Yt(e,t){if(t===0)return e;var r;return function(n){clearTimeout(r),r=setTimeout(function(){e(n)},t)}}function _n(e){return e.split(/\s+/).filter(Boolean)}function Te(e){return[].concat(e)}function qt(e,t){e.indexOf(t)===-1&&e.push(t)}function zn(e){return e.filter(function(t,r){return e.indexOf(t)===r})}function gr(e){return e.split("-")[0]}function Pe(e){return[].slice.call(e)}function _t(e){return Object.keys(e).reduce(function(t,r){return e[r]!==void 0&&(t[r]=e[r]),t},{})}function Ae(){return document.createElement("div")}function ft(e){return["Element","Fragment"].some(function(t){return Dt(e,t)})}function Gn(e){return Dt(e,"NodeList")}function Mt(e){return Dt(e,"MouseEvent")}function Kn(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function Jn(e){return ft(e)?[e]:Gn(e)?Pe(e):Array.isArray(e)?e:Pe(document.querySelectorAll(e))}function dt(e,t){e.forEach(function(r){r&&(r.style.transitionDuration=t+"ms")})}function Ue(e,t){e.forEach(function(r){r&&r.setAttribute("data-state",t)})}function yr(e){var t,r=Te(e),n=r[0];return n!=null&&(t=n.ownerDocument)!=null&&t.body?n.ownerDocument:document}function Qn(e,t){var r=t.clientX,n=t.clientY;return e.every(function(i){var a=i.popperRect,s=i.popperState,f=i.props,p=f.interactiveBorder,c=gr(s.placement),l=s.modifiersData.offset;if(!l)return!0;var A=c==="bottom"?l.top.y:0,E=c==="top"?l.bottom.y:0,O=c==="right"?l.left.x:0,m=c==="left"?l.right.x:0,y=a.top-n+A>p,b=n-a.bottom-E>p,w=a.left-r+O>p,T=r-a.right-m>p;return y||b||w||T})}function vt(e,t,r){var n=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(i){e[n](i,r)})}function zt(e,t){for(var r=t;r;){var n;if(e.contains(r))return!0;r=r.getRootNode==null||(n=r.getRootNode())==null?void 0:n.host}return!1}var Z={isTouch:!1},Gt=0;function Zn(){Z.isTouch||(Z.isTouch=!0,window.performance&&document.addEventListener("mousemove",br))}function br(){var e=performance.now();e-Gt<20&&(Z.isTouch=!1,document.removeEventListener("mousemove",br)),Gt=e}function ei(){var e=document.activeElement;if(Kn(e)){var t=e._tippy;e.blur&&!t.state.isVisible&&e.blur()}}function ti(){document.addEventListener("touchstart",Zn,he),window.addEventListener("blur",ei)}var ri=typeof window!="undefined"&&typeof document!="undefined",ni=ri?!!window.msCrypto:!1,ii={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},oi={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},z=Object.assign({appendTo:mr,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},ii,oi),ai=Object.keys(z),si=function(t){var r=Object.keys(t);r.forEach(function(n){z[n]=t[n]})};function wr(e){var t=e.plugins||[],r=t.reduce(function(n,i){var a=i.name,s=i.defaultValue;if(a){var f;n[a]=e[a]!==void 0?e[a]:(f=z[a])!=null?f:s}return n},{});return Object.assign({},e,r)}function ui(e,t){var r=t?Object.keys(wr(Object.assign({},z,{plugins:t}))):ai,n=r.reduce(function(i,a){var s=(e.getAttribute("data-tippy-"+a)||"").trim();if(!s)return i;if(a==="content")i[a]=s;else try{i[a]=JSON.parse(s)}catch(f){i[a]=s}return i},{});return n}function Kt(e,t){var r=Object.assign({},t,{content:hr(t.content,[e])},t.ignoreAttributes?{}:ui(e,t.plugins));return r.aria=Object.assign({},z.aria,r.aria),r.aria={expanded:r.aria.expanded==="auto"?t.interactive:r.aria.expanded,content:r.aria.content==="auto"?t.interactive?null:"describedby":r.aria.content},r}var fi=function(){return"innerHTML"};function yt(e,t){e[fi()]=t}function Jt(e){var t=Ae();return e===!0?t.className=dr:(t.className=vr,ft(e)?t.appendChild(e):yt(t,e)),t}function Qt(e,t){ft(t.content)?(yt(e,""),e.appendChild(t.content)):typeof t.content!="function"&&(t.allowHTML?yt(e,t.content):e.textContent=t.content)}function st(e){var t=e.firstElementChild,r=Pe(t.children);return{box:t,content:r.find(function(n){return n.classList.contains(lr)}),arrow:r.find(function(n){return n.classList.contains(dr)||n.classList.contains(vr)}),backdrop:r.find(function(n){return n.classList.contains(cr)})}}function Or(e){var t=Ae(),r=Ae();r.className=qn,r.setAttribute("data-state","hidden"),r.setAttribute("tabindex","-1");var n=Ae();n.className=lr,n.setAttribute("data-state","hidden"),Qt(n,e.props),t.appendChild(r),r.appendChild(n),i(e.props,e.props);function i(a,s){var f=st(t),p=f.box,c=f.content,l=f.arrow;s.theme?p.setAttribute("data-theme",s.theme):p.removeAttribute("data-theme"),typeof s.animation=="string"?p.setAttribute("data-animation",s.animation):p.removeAttribute("data-animation"),s.inertia?p.setAttribute("data-inertia",""):p.removeAttribute("data-inertia"),p.style.maxWidth=typeof s.maxWidth=="number"?s.maxWidth+"px":s.maxWidth,s.role?p.setAttribute("role",s.role):p.removeAttribute("role"),(a.content!==s.content||a.allowHTML!==s.allowHTML)&&Qt(c,e.props),s.arrow?l?a.arrow!==s.arrow&&(p.removeChild(l),p.appendChild(Jt(s.arrow))):p.appendChild(Jt(s.arrow)):l&&p.removeChild(l)}return{popper:t,onUpdate:i}}Or.$$tippy=!0;var pi=1,nt=[],mt=[];function li(e,t){var r=Kt(e,Object.assign({},z,wr(_t(t)))),n,i,a,s=!1,f=!1,p=!1,c=!1,l,A,E,O=[],m=Yt(Ye,r.interactiveDebounce),y,b=pi++,w=null,T=zn(r.plugins),D={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},o={id:b,reference:e,popper:Ae(),popperInstance:w,props:r,state:D,plugins:T,clearDelayTimeouts:Ke,setProps:Je,setContent:Qe,show:Tr,hide:Ar,hideWithInteractivity:Er,enable:je,disable:Ge,unmount:Cr,destroy:Dr};if(!r.render)return o;var C=r.render(o),v=C.popper,d=C.onUpdate;v.setAttribute("data-tippy-root",""),v.id="tippy-"+o.id,o.popper=v,e._tippy=o,v._tippy=o;var h=T.map(function(u){return u.fn(o)}),P=e.hasAttribute("aria-expanded");return we(),J(),H(),j("onCreate",[o]),r.showOnCreate&&Be(),v.addEventListener("mouseenter",function(){o.props.interactive&&o.state.isVisible&&o.clearDelayTimeouts()}),v.addEventListener("mouseleave",function(){o.props.interactive&&o.props.trigger.indexOf("mouseenter")>=0&&V().addEventListener("mousemove",m)}),o;function R(){var u=o.props.touch;return Array.isArray(u)?u:[u,0]}function $(){return R()[0]==="hold"}function L(){var u;return!!((u=o.props.render)!=null&&u.$$tippy)}function B(){return y||e}function V(){var u=B().parentNode;return u?yr(u):document}function q(){return st(v)}function I(u){return o.state.isMounted&&!o.state.isVisible||Z.isTouch||l&&l.type==="focus"?0:ct(o.props.delay,u?0:1,z.delay)}function H(u){u===void 0&&(u=!1),v.style.pointerEvents=o.props.interactive&&!u?"":"none",v.style.zIndex=""+o.props.zIndex}function j(u,g,x){if(x===void 0&&(x=!0),h.forEach(function(M){M[u]&&M[u].apply(M,g)}),x){var S;(S=o.props)[u].apply(S,g)}}function K(){var u=o.props.aria;if(u.content){var g="aria-"+u.content,x=v.id,S=Te(o.props.triggerTarget||e);S.forEach(function(M){var k=M.getAttribute(g);if(o.state.isVisible)M.setAttribute(g,k?k+" "+x:x);else{var F=k&&k.replace(x,"").trim();F?M.setAttribute(g,F):M.removeAttribute(g)}})}}function J(){if(!(P||!o.props.aria.expanded)){var u=Te(o.props.triggerTarget||e);u.forEach(function(g){o.props.interactive?g.setAttribute("aria-expanded",o.state.isVisible&&g===B()?"true":"false"):g.removeAttribute("aria-expanded")})}}function ce(){V().removeEventListener("mousemove",m),nt=nt.filter(function(u){return u!==m})}function Q(u){if(!(Z.isTouch&&(p||u.type==="mousedown"))){var g=u.composedPath&&u.composedPath()[0]||u.target;if(!(o.props.interactive&&zt(v,g))){if(Te(o.props.triggerTarget||e).some(function(x){return zt(x,g)})){if(Z.isTouch||o.state.isVisible&&o.props.trigger.indexOf("click")>=0)return}else j("onClickOutside",[o,u]);o.props.hideOnClick===!0&&(o.clearDelayTimeouts(),o.hide(),f=!0,setTimeout(function(){f=!1}),o.state.isMounted||oe())}}}function de(){p=!0}function ie(){p=!1}function _(){var u=V();u.addEventListener("mousedown",Q,!0),u.addEventListener("touchend",Q,he),u.addEventListener("touchstart",ie,he),u.addEventListener("touchmove",de,he)}function oe(){var u=V();u.removeEventListener("mousedown",Q,!0),u.removeEventListener("touchend",Q,he),u.removeEventListener("touchstart",ie,he),u.removeEventListener("touchmove",de,he)}function ye(u,g){be(u,function(){!o.state.isVisible&&v.parentNode&&v.parentNode.contains(v)&&g()})}function ae(u,g){be(u,g)}function be(u,g){var x=q().box;function S(M){M.target===x&&(vt(x,"remove",S),g())}if(u===0)return g();vt(x,"remove",A),vt(x,"add",S),A=S}function ue(u,g,x){x===void 0&&(x=!1);var S=Te(o.props.triggerTarget||e);S.forEach(function(M){M.addEventListener(u,g,x),O.push({node:M,eventType:u,handler:g,options:x})})}function we(){$()&&(ue("touchstart",Le,{passive:!0}),ue("touchend",qe,{passive:!0})),_n(o.props.trigger).forEach(function(u){if(u!=="manual")switch(ue(u,Le),u){case"mouseenter":ue("mouseleave",qe);break;case"focus":ue(ni?"focusout":"blur",Re);break;case"focusin":ue("focusout",Re);break}})}function Xe(){O.forEach(function(u){var g=u.node,x=u.eventType,S=u.handler,M=u.options;g.removeEventListener(x,S,M)}),O=[]}function Le(u){var g,x=!1;if(!(!o.state.isEnabled||$e(u)||f)){var S=((g=l)==null?void 0:g.type)==="focus";l=u,y=u.currentTarget,J(),!o.state.isVisible&&Mt(u)&&nt.forEach(function(M){return M(u)}),u.type==="click"&&(o.props.trigger.indexOf("mouseenter")<0||s)&&o.props.hideOnClick!==!1&&o.state.isVisible?x=!0:Be(u),u.type==="click"&&(s=!x),x&&!S&&ve(u)}}function Ye(u){var g=u.target,x=B().contains(g)||v.contains(g);if(!(u.type==="mousemove"&&x)){var S=fe().concat(v).map(function(M){var k,F=M._tippy,Oe=(k=F.popperInstance)==null?void 0:k.state;return Oe?{popperRect:M.getBoundingClientRect(),popperState:Oe,props:r}:null}).filter(Boolean);Qn(S,u)&&(ce(),ve(u))}}function qe(u){var g=$e(u)||o.props.trigger.indexOf("click")>=0&&s;if(!g){if(o.props.interactive){o.hideWithInteractivity(u);return}ve(u)}}function Re(u){o.props.trigger.indexOf("focusin")<0&&u.target!==B()||o.props.interactive&&u.relatedTarget&&v.contains(u.relatedTarget)||ve(u)}function $e(u){return Z.isTouch?$()!==u.type.indexOf("touch")>=0:!1}function _e(){ze();var u=o.props,g=u.popperOptions,x=u.placement,S=u.offset,M=u.getReferenceClientRect,k=u.moveTransition,F=L()?st(v).arrow:null,Oe=M?{getBoundingClientRect:M,contextElement:M.contextElement||B()}:e,St={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(Ze){var xe=Ze.state;if(L()){var Mr=q(),lt=Mr.box;["placement","reference-hidden","escaped"].forEach(function(et){et==="placement"?lt.setAttribute("data-placement",xe.placement):xe.attributes.popper["data-popper-"+et]?lt.setAttribute("data-"+et,""):lt.removeAttribute("data-"+et)}),xe.attributes.popper={}}}},me=[{name:"offset",options:{offset:S}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!k}},St];L()&&F&&me.push({name:"arrow",options:{element:F,padding:3}}),me.push.apply(me,(g==null?void 0:g.modifiers)||[]),o.popperInstance=Yn(Oe,v,Object.assign({},g,{placement:x,onFirstUpdate:E,modifiers:me}))}function ze(){o.popperInstance&&(o.popperInstance.destroy(),o.popperInstance=null)}function se(){var u=o.props.appendTo,g,x=B();o.props.interactive&&u===mr||u==="parent"?g=x.parentNode:g=hr(u,[x]),g.contains(v)||g.appendChild(v),o.state.isMounted=!0,_e()}function fe(){return Pe(v.querySelectorAll("[data-tippy-root]"))}function Be(u){o.clearDelayTimeouts(),u&&j("onTrigger",[o,u]),_();var g=I(!0),x=R(),S=x[0],M=x[1];Z.isTouch&&S==="hold"&&M&&(g=M),g?n=setTimeout(function(){o.show()},g):o.show()}function ve(u){if(o.clearDelayTimeouts(),j("onUntrigger",[o,u]),!o.state.isVisible){oe();return}if(!(o.props.trigger.indexOf("mouseenter")>=0&&o.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(u.type)>=0&&s)){var g=I(!1);g?i=setTimeout(function(){o.state.isVisible&&o.hide()},g):a=requestAnimationFrame(function(){o.hide()})}}function je(){o.state.isEnabled=!0}function Ge(){o.hide(),o.state.isEnabled=!1}function Ke(){clearTimeout(n),clearTimeout(i),cancelAnimationFrame(a)}function Je(u){if(!o.state.isDestroyed){j("onBeforeUpdate",[o,u]),Xe();var g=o.props,x=Kt(e,Object.assign({},g,_t(u),{ignoreAttributes:!0}));o.props=x,we(),g.interactiveDebounce!==x.interactiveDebounce&&(ce(),m=Yt(Ye,x.interactiveDebounce)),g.triggerTarget&&!x.triggerTarget?Te(g.triggerTarget).forEach(function(S){S.removeAttribute("aria-expanded")}):x.triggerTarget&&e.removeAttribute("aria-expanded"),J(),H(),d&&d(g,x),o.popperInstance&&(_e(),fe().forEach(function(S){requestAnimationFrame(S._tippy.popperInstance.forceUpdate)})),j("onAfterUpdate",[o,u])}}function Qe(u){o.setProps({content:u})}function Tr(){var u=o.state.isVisible,g=o.state.isDestroyed,x=!o.state.isEnabled,S=Z.isTouch&&!o.props.touch,M=ct(o.props.duration,0,z.duration);if(!(u||g||x||S)&&!B().hasAttribute("disabled")&&(j("onShow",[o],!1),o.props.onShow(o)!==!1)){if(o.state.isVisible=!0,L()&&(v.style.visibility="visible"),H(),_(),o.state.isMounted||(v.style.transition="none"),L()){var k=q(),F=k.box,Oe=k.content;dt([F,Oe],0)}E=function(){var me;if(!(!o.state.isVisible||c)){if(c=!0,v.offsetHeight,v.style.transition=o.props.moveTransition,L()&&o.props.animation){var pt=q(),Ze=pt.box,xe=pt.content;dt([Ze,xe],M),Ue([Ze,xe],"visible")}K(),J(),qt(mt,o),(me=o.popperInstance)==null||me.forceUpdate(),j("onMount",[o]),o.props.animation&&L()&&ae(M,function(){o.state.isShown=!0,j("onShown",[o])})}},se()}}function Ar(){var u=!o.state.isVisible,g=o.state.isDestroyed,x=!o.state.isEnabled,S=ct(o.props.duration,1,z.duration);if(!(u||g||x)&&(j("onHide",[o],!1),o.props.onHide(o)!==!1)){if(o.state.isVisible=!1,o.state.isShown=!1,c=!1,s=!1,L()&&(v.style.visibility="hidden"),ce(),oe(),H(!0),L()){var M=q(),k=M.box,F=M.content;o.props.animation&&(dt([k,F],S),Ue([k,F],"hidden"))}K(),J(),o.props.animation?L()&&ye(S,o.unmount):o.unmount()}}function Er(u){V().addEventListener("mousemove",m),qt(nt,m),m(u)}function Cr(){o.state.isVisible&&o.hide(),o.state.isMounted&&(ze(),fe().forEach(function(u){u._tippy.unmount()}),v.parentNode&&v.parentNode.removeChild(v),mt=mt.filter(function(u){return u!==o}),o.state.isMounted=!1,j("onHidden",[o]))}function Dr(){o.state.isDestroyed||(o.clearDelayTimeouts(),o.unmount(),Xe(),delete e._tippy,o.state.isDestroyed=!0,j("onDestroy",[o]))}}function te(e,t){t===void 0&&(t={});var r=z.plugins.concat(t.plugins||[]);ti();var n=Object.assign({},t,{plugins:r}),i=Jn(e),a=i.reduce(function(s,f){var p=f&&li(f,n);return p&&s.push(p),s},[]);return ft(e)?a[0]:a}te.defaultProps=z;te.setDefaultProps=si;te.currentInput=Z;Object.assign({},ir,{effect:function(t){var r=t.state,n={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(r.elements.popper.style,n.popper),r.styles=n,r.elements.arrow&&Object.assign(r.elements.arrow.style,n.arrow)}});var ci={name:"animateFill",defaultValue:!1,fn:function(t){var r;if(!((r=t.props.render)!=null&&r.$$tippy))return{};var n=st(t.popper),i=n.box,a=n.content,s=t.props.animateFill?di():null;return{onCreate:function(){s&&(i.insertBefore(s,i.firstElementChild),i.setAttribute("data-animatefill",""),i.style.overflow="hidden",t.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){if(s){var p=i.style.transitionDuration,c=Number(p.replace("ms",""));a.style.transitionDelay=Math.round(c/10)+"ms",s.style.transitionDuration=p,Ue([s],"visible")}},onShow:function(){s&&(s.style.transitionDuration="0ms")},onHide:function(){s&&Ue([s],"hidden")}}}};function di(){var e=Ae();return e.className=cr,Ue([e],"hidden"),e}var bt={clientX:0,clientY:0},it=[];function xr(e){var t=e.clientX,r=e.clientY;bt={clientX:t,clientY:r}}function vi(e){e.addEventListener("mousemove",xr)}function mi(e){e.removeEventListener("mousemove",xr)}var hi={name:"followCursor",defaultValue:!1,fn:function(t){var r=t.reference,n=yr(t.props.triggerTarget||r),i=!1,a=!1,s=!0,f=t.props;function p(){return t.props.followCursor==="initial"&&t.state.isVisible}function c(){n.addEventListener("mousemove",E)}function l(){n.removeEventListener("mousemove",E)}function A(){i=!0,t.setProps({getReferenceClientRect:null}),i=!1}function E(y){var b=y.target?r.contains(y.target):!0,w=t.props.followCursor,T=y.clientX,D=y.clientY,o=r.getBoundingClientRect(),C=T-o.left,v=D-o.top;(b||!t.props.interactive)&&t.setProps({getReferenceClientRect:function(){var h=r.getBoundingClientRect(),P=T,R=D;w==="initial"&&(P=h.left+C,R=h.top+v);var $=w==="horizontal"?h.top:R,L=w==="vertical"?h.right:P,B=w==="horizontal"?h.bottom:R,V=w==="vertical"?h.left:P;return{width:L-V,height:B-$,top:$,right:L,bottom:B,left:V}}})}function O(){t.props.followCursor&&(it.push({instance:t,doc:n}),vi(n))}function m(){it=it.filter(function(y){return y.instance!==t}),it.filter(function(y){return y.doc===n}).length===0&&mi(n)}return{onCreate:O,onDestroy:m,onBeforeUpdate:function(){f=t.props},onAfterUpdate:function(b,w){var T=w.followCursor;i||T!==void 0&&f.followCursor!==T&&(m(),T?(O(),t.state.isMounted&&!a&&!p()&&c()):(l(),A()))},onMount:function(){t.props.followCursor&&!a&&(s&&(E(bt),s=!1),p()||c())},onTrigger:function(b,w){Mt(w)&&(bt={clientX:w.clientX,clientY:w.clientY}),a=w.type==="focus"},onHidden:function(){t.props.followCursor&&(A(),l(),s=!0)}}}};function gi(e,t){var r;return{popperOptions:Object.assign({},e.popperOptions,{modifiers:[].concat((((r=e.popperOptions)==null?void 0:r.modifiers)||[]).filter(function(n){var i=n.name;return i!==t.name}),[t])})}}var yi={name:"inlinePositioning",defaultValue:!1,fn:function(t){var r=t.reference;function n(){return!!t.props.inlinePositioning}var i,a=-1,s=!1,f=[],p={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(O){var m=O.state;n()&&(f.indexOf(m.placement)!==-1&&(f=[]),i!==m.placement&&f.indexOf(m.placement)===-1&&(f.push(m.placement),t.setProps({getReferenceClientRect:function(){return c(m.placement)}})),i=m.placement)}};function c(E){return bi(gr(E),r.getBoundingClientRect(),Pe(r.getClientRects()),a)}function l(E){s=!0,t.setProps(E),s=!1}function A(){s||l(gi(t.props,p))}return{onCreate:A,onAfterUpdate:A,onTrigger:function(O,m){if(Mt(m)){var y=Pe(t.reference.getClientRects()),b=y.find(function(T){return T.left-2<=m.clientX&&T.right+2>=m.clientX&&T.top-2<=m.clientY&&T.bottom+2>=m.clientY}),w=y.indexOf(b);a=w>-1?w:a}},onHidden:function(){a=-1}}}};function bi(e,t,r,n){if(r.length<2||e===null)return t;if(r.length===2&&n>=0&&r[0].left>r[1].right)return r[n]||t;switch(e){case"top":case"bottom":{var i=r[0],a=r[r.length-1],s=e==="top",f=i.top,p=a.bottom,c=s?i.left:a.left,l=s?i.right:a.right,A=l-c,E=p-f;return{top:f,bottom:p,left:c,right:l,width:A,height:E}}case"left":case"right":{var O=Math.min.apply(Math,r.map(function(v){return v.left})),m=Math.max.apply(Math,r.map(function(v){return v.right})),y=r.filter(function(v){return e==="left"?v.left===O:v.right===m}),b=y[0].top,w=y[y.length-1].bottom,T=O,D=m,o=D-T,C=w-b;return{top:b,bottom:w,left:T,right:D,width:o,height:C}}default:return t}}var wi={name:"sticky",defaultValue:!1,fn:function(t){var r=t.reference,n=t.popper;function i(){return t.popperInstance?t.popperInstance.state.elements.reference:r}function a(c){return t.props.sticky===!0||t.props.sticky===c}var s=null,f=null;function p(){var c=a("reference")?i().getBoundingClientRect():null,l=a("popper")?n.getBoundingClientRect():null;(c&&Zt(s,c)||l&&Zt(f,l))&&t.popperInstance&&t.popperInstance.update(),s=c,f=l,t.state.isMounted&&requestAnimationFrame(p)}return{onMount:function(){t.props.sticky&&p()}}}};function Zt(e,t){return e&&t?e.top!==t.top||e.right!==t.right||e.bottom!==t.bottom||e.left!==t.left:!0}te.setDefaultProps({render:Or});te.setDefaultProps({onShow:e=>{if(!e.props.content)return!1}});const Oi=e=>e instanceof Object&&"$"in e&&"$el"in e;function xi(e,t={},r={mount:!0,appName:"Tippy"}){r=Object.assign({mount:!0,appName:"Tippy"},r);const n=jr(),i=$t(),a=$t({isEnabled:!1,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1}),s=$r();let f=null;const p=()=>f||(f=document.createDocumentFragment(),f),c=d=>{let h,P=pe(d)?d.value:d;return Br(P)?(s.value||(s.value=Rt({name:r.appName,setup:()=>()=>pe(d)?d.value:d}),n&&Object.assign(s.value._context,n.appContext),s.value.mount(p())),h=()=>p()):typeof P=="object"?(s.value||(s.value=Rt({name:r.appName,setup:()=>()=>Ir(pe(d)?d.value:d)}),n&&Object.assign(s.value._context,n.appContext),s.value.mount(p())),h=()=>p()):h=P,h},l=d=>{let h={};return pe(d)?h=d.value||{}:Bt(d)?h=tt({},d):h=tt({},d),h.content&&(h.content=c(h.content)),h.triggerTarget&&(h.triggerTarget=pe(h.triggerTarget)?h.triggerTarget.value:h.triggerTarget),(!h.plugins||!Array.isArray(h.plugins))&&(h.plugins=[]),h.plugins=h.plugins.filter(P=>P.name!=="vueTippyReactiveState"),h.plugins.push({name:"vueTippyReactiveState",fn:()=>({onCreate(){a.value.isEnabled=!0},onMount(){a.value.isMounted=!0},onShow(){a.value.isMounted=!0,a.value.isVisible=!0},onShown(){a.value.isShown=!0},onHide(){a.value.isMounted=!1,a.value.isVisible=!1},onHidden(){a.value.isShown=!1},onUnmounted(){a.value.isMounted=!1},onDestroy(){a.value.isDestroyed=!0}})}),h},A=()=>{i.value&&i.value.setProps(l(t))},E=()=>{!i.value||!t.content||i.value.setContent(c(t.content))},O=d=>{var h;(h=i.value)===null||h===void 0||h.setContent(c(d))},m=d=>{var h;(h=i.value)===null||h===void 0||h.setProps(l(d))},y=()=>{var d;i.value&&(i.value.destroy(),i.value=void 0),f=null,(d=s.value)===null||d===void 0||d.unmount(),s.value=void 0},b=()=>{var d;(d=i.value)===null||d===void 0||d.show()},w=()=>{var d;(d=i.value)===null||d===void 0||d.hide()},T=()=>{var d;(d=i.value)===null||d===void 0||d.disable(),a.value.isEnabled=!1},D=()=>{var d;(d=i.value)===null||d===void 0||d.enable(),a.value.isEnabled=!0},o=()=>{var d;(d=i.value)===null||d===void 0||d.unmount()},C=()=>{if(!e)return;let d=pe(e)?e.value:e;typeof d=="function"&&(d=d()),Oi(d)&&(d=d.$el),d&&(i.value=te(d,l(t)),d.$tippy=v)},v={tippy:i,refresh:A,refreshContent:E,setContent:O,setProps:m,destroy:y,hide:w,show:b,disable:T,enable:D,unmount:o,mount:C,state:a};return r.mount&&(n?n.isMounted?C():Hr(C):C()),n&&kr(()=>{y()}),pe(t)||Bt(t)?jt(t,A,{immediate:!1}):pe(t.content)&&jt(t.content,E,{immediate:!1}),v}const Ti=["a11y","allowHTML","arrow","flip","flipOnUpdate","hideOnClick","ignoreAttributes","inertia","interactive","lazy","multiple","showOnInit","touch","touchHold"];let er={};Object.keys(te.defaultProps).forEach(e=>{Ti.includes(e)?er[e]={type:Boolean,default:function(){return te.defaultProps[e]}}:er[e]={default:function(){return te.defaultProps[e]}}});const wt=te.setDefaultProps;wt({ignoreAttributes:!0,plugins:[wi,yi,hi,ci]});function Ai(e){return{mounted(r,n,i){const a=typeof n.value=="string"?{content:n.value}:n.value||{},s=Object.keys(n.modifiers||{}),f=s.find(c=>c!=="arrow"),p=s.includes("arrow");f&&(a.placement=a.placement||f),p&&(a.arrow=a.arrow===void 0?!0:a.arrow),i.props&&i.props.onTippyShow&&(a.onShow=function(...c){var l;return(l=i.props)==null?void 0:l.onTippyShow(...c)}),i.props&&i.props.onTippyShown&&(a.onShown=function(...c){var l;return(l=i.props)==null?void 0:l.onTippyShown(...c)}),i.props&&i.props.onTippyHidden&&(a.onHidden=function(...c){var l;return(l=i.props)==null?void 0:l.onTippyHidden(...c)}),i.props&&i.props.onTippyHide&&(a.onHide=function(...c){var l;return(l=i.props)==null?void 0:l.onTippyHide(...c)}),i.props&&i.props.onTippyMount&&(a.onMount=function(...c){var l;return(l=i.props)==null?void 0:l.onTippyMount(...c)}),r.getAttribute("title")&&!a.content&&(a.content=r.getAttribute("title"),r.removeAttribute("title")),r.getAttribute("content")&&!a.content&&(a.content=r.getAttribute("content")),xi(r,a)},unmounted(r){r.$tippy?r.$tippy.destroy():r._tippy&&r._tippy.destroy()},updated(r,n){const i=typeof n.value=="string"?{content:n.value,theme:e.value?"":"light"}:Object.assign({theme:e.value?"":"light"},n.value);r.getAttribute("title")&&!i.content&&(i.content=r.getAttribute("title"),r.removeAttribute("title")),r.getAttribute("content")&&!i.content&&(i.content=r.getAttribute("content")),r.$tippy?r.$tippy.setProps(i||{}):r._tippy&&r._tippy.setProps(i||{})}}}const{isDark:ht}=Rr();function Mi(e,t){wt(tt({allowHTML:!0,delay:[500,200],theme:ht.value?"":"light"},t)),(!t||!Reflect.has(t,"theme")||t.theme==="auto")&&Vr(()=>{wt({theme:ht.value?"":"light"})}),e.directive("tippy",Ai(ht))}export{Mi as initTippy};
