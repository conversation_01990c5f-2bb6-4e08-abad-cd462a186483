import{_ as P,b as k,c as B,d as T,a as w,f as A,e as b,N as y,h as S,g as V,i as v}from"./layout.vue_vue_type_script_setup_true_lang-B8Xt-gbJ.js";import{A as F,_ as I,a as N}from"./authentication-CLX_ohXy.js";import{_ as U,a as D}from"./theme-toggle.vue_vue_type_script_setup_true_lang-BWxNdLTa.js";import{_ as a}from"./bootstrap-CYivmKoJ.js";import{r as o,f as t,g as s}from"../jse/index-index-SSqEGcIT.js";import"./avatar.vue_vue_type_script_setup_true_lang-DRkNZSlI.js";import"./use-drawer-Cga87ueS.js";import"./TabsList.vue_vue_type_script_setup_true_lang-Byr6YgCt.js";import"./rotate-cw-D8Gh4ARV.js";import"./index-wKOw6cfx.js";const r=o(!1);function d(){function e(){r.value=!0}return{handleOpenPreference:e,openPreferences:r}}const n={};function c(e,i){return s(),t("div")}const h=a(n,[["render",c]]);export{F as AuthPageLayout,I as AuthenticationColorToggle,N as AuthenticationLayoutToggle,P as BasicLayout,k as Breadcrumb,B as CheckUpdates,T as GlobalSearch,w as IFrameRouterView,h as IFrameView,U as LanguageToggle,A as LockScreen,b as LockScreenModal,y as Notification,S as Preferences,V as PreferencesButton,D as ThemeToggle,v as UserDropdown,d as useOpenPreferences};
