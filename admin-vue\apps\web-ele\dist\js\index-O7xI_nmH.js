var pe=Object.defineProperty,fe=Object.defineProperties;var be=Object.getOwnPropertyDescriptors;var j=Object.getOwnPropertySymbols;var ve=Object.prototype.hasOwnProperty,Ne=Object.prototype.propertyIsEnumerable;var J=(t,c,u)=>c in t?pe(t,c,{enumerable:!0,configurable:!0,writable:!0,value:u}):t[c]=u,$=(t,c)=>{for(var u in c||(c={}))ve.call(c,u)&&J(t,u,c[u]);if(j)for(var u of j(c))Ne.call(c,u)&&J(t,u,c[u]);return t},Q=(t,c)=>fe(t,be(c));import{o as f,p as I,k as Ve,q as he,m as ye,r as Ie,n as ge,s as S,t as _,v as we,x as Ee,y as X,z as Se,A as _e,B as z,w as Pe}from"./bootstrap-CYivmKoJ.js";import{E as Ae}from"./index-C3SWEjhj.js";import{U as V,I as C,C as re,u as Fe}from"./index-DIXeP0hR.js";import{v as Z}from"./index-pE9ts8eW.js";import{u as Ce}from"./use-form-item-iUVikjOD.js";import{d as ee,t as ke}from"./error-CYrjCQ5V.js";import{u as xe,b as Be}from"./use-form-common-props-DZjBwEkr.js";import{d as ae,r as De,L as Te,e as y,w as Me,o as $e,M as ze,f as K,g as v,N as ne,h as te,E as O,u as a,n as L,l as P,D as A,q as F,O as Ke,P as Oe}from"../jse/index-index-SSqEGcIT.js";import"./browser-CSPQ6ERn.js";import"./index-CdkCbLvc.js";import"./aria-DGfENwCE.js";const Le=Ve($({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:{type:[Number,null]},readonly:Boolean,disabled:Boolean,size:he,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:t=>t===null||f(t)||["min","max"].includes(t),default:null},name:String,placeholder:String,precision:{type:Number,validator:t=>t>=0&&t===Number.parseInt(`${t}`,10)},validateEvent:{type:Boolean,default:!0}},Fe(["ariaLabel"]))),Ue={[re]:(t,c)=>c!==t,blur:t=>t instanceof FocusEvent,focus:t=>t instanceof FocusEvent,[C]:t=>f(t)||I(t),[V]:t=>f(t)||I(t)},qe=ae({name:"ElInputNumber"}),Ge=ae(Q($({},qe),{props:Le,emits:Ue,setup(t,{expose:c,emit:u}){const l=t,{t:U}=Ie(),m=ge("input-number"),b=De(),s=Te({currentValue:l.modelValue,userInput:null}),{formItem:N}=Ce(),q=y(()=>f(l.modelValue)&&l.modelValue<=l.min),G=y(()=>f(l.modelValue)&&l.modelValue>=l.max),le=y(()=>{const e=W(l.step);return S(l.precision)?Math.max(W(l.modelValue),e):(e>l.precision,l.precision)}),k=y(()=>l.controls&&l.controlsPosition==="right"),R=xe(),h=Be(),x=y(()=>{if(s.userInput!==null)return s.userInput;let e=s.currentValue;if(I(e))return"";if(f(e)){if(Number.isNaN(e))return"";S(l.precision)||(e=e.toFixed(l.precision))}return e}),B=(e,n)=>{if(S(n)&&(n=le.value),n===0)return Math.round(e);let r=String(e);const o=r.indexOf(".");if(o===-1||!r.replace(".","").split("")[o+n])return e;const w=r.length;return r.charAt(w-1)==="5"&&(r=`${r.slice(0,Math.max(0,w-1))}6`),Number.parseFloat(Number(r).toFixed(n))},W=e=>{if(I(e))return 0;const n=e.toString(),r=n.indexOf(".");let o=0;return r!==-1&&(o=n.length-r-1),o},Y=(e,n=1)=>f(e)?B(e+l.step*n):s.currentValue,D=()=>{if(l.readonly||h.value||G.value)return;const e=Number(x.value)||0,n=Y(e);g(n),u(C,s.currentValue),M()},T=()=>{if(l.readonly||h.value||q.value)return;const e=Number(x.value)||0,n=Y(e,-1);g(n),u(C,s.currentValue),M()},H=(e,n)=>{const{max:r,min:o,step:i,precision:p,stepStrictly:w,valueOnClear:E}=l;r<o&&ke("InputNumber","min should not be greater than max.");let d=Number(e);if(I(e)||Number.isNaN(d))return null;if(e===""){if(E===null)return null;d=Oe(E)?{min:o,max:r}[E]:E}return w&&(d=B(Math.round(d/i)*i,p),d!==e&&n&&u(V,d)),S(p)||(d=B(d,p)),(d>r||d<o)&&(d=d>r?r:o,n&&u(V,d)),d},g=(e,n=!0)=>{var r;const o=s.currentValue,i=H(e);if(!n){u(V,i);return}o===i&&e||(s.userInput=null,u(V,i),o!==i&&u(re,i,o),l.validateEvent&&((r=N==null?void 0:N.validate)==null||r.call(N,"change").catch(p=>ee())),s.currentValue=i)},ue=e=>{s.userInput=e;const n=e===""?null:Number(e);u(C,n),g(n,!1)},se=e=>{const n=e!==""?Number(e):"";(f(n)&&!Number.isNaN(n)||e==="")&&g(n),M(),s.userInput=null},oe=()=>{var e,n;(n=(e=b.value)==null?void 0:e.focus)==null||n.call(e)},ie=()=>{var e,n;(n=(e=b.value)==null?void 0:e.blur)==null||n.call(e)},ce=e=>{u("focus",e)},de=e=>{var n,r;s.userInput=null,s.currentValue===null&&((n=b.value)!=null&&n.input)&&(b.value.input.value=""),u("blur",e),l.validateEvent&&((r=N==null?void 0:N.validate)==null||r.call(N,"blur").catch(o=>ee()))},M=()=>{s.currentValue!==l.modelValue&&(s.currentValue=l.modelValue)},me=e=>{document.activeElement===e.target&&e.preventDefault()};return Me(()=>l.modelValue,(e,n)=>{const r=H(e,!0);s.userInput===null&&r!==n&&(s.currentValue=r)},{immediate:!0}),$e(()=>{var e;const{min:n,max:r,modelValue:o}=l,i=(e=b.value)==null?void 0:e.input;if(i.setAttribute("role","spinbutton"),Number.isFinite(r)?i.setAttribute("aria-valuemax",String(r)):i.removeAttribute("aria-valuemax"),Number.isFinite(n)?i.setAttribute("aria-valuemin",String(n)):i.removeAttribute("aria-valuemin"),i.setAttribute("aria-valuenow",s.currentValue||s.currentValue===0?String(s.currentValue):""),i.setAttribute("aria-disabled",String(h.value)),!f(o)&&o!=null){let p=Number(o);Number.isNaN(p)&&(p=null),u(V,p)}i.addEventListener("wheel",me,{passive:!1})}),ze(()=>{var e,n;const r=(e=b.value)==null?void 0:e.input;r==null||r.setAttribute("aria-valuenow",`${(n=s.currentValue)!=null?n:""}`)}),c({focus:oe,blur:ie}),(e,n)=>(v(),K("div",{class:L([a(m).b(),a(m).m(a(R)),a(m).is("disabled",a(h)),a(m).is("without-controls",!e.controls),a(m).is("controls-right",a(k))]),onDragstart:z(()=>{},["prevent"])},[e.controls?ne((v(),K("span",{key:0,role:"button","aria-label":a(U)("el.inputNumber.decrease"),class:L([a(m).e("decrease"),a(m).is("disabled",a(q))]),onKeydown:_(T,["enter"])},[P(e.$slots,"decrease-icon",{},()=>[O(a(X),null,{default:A(()=>[a(k)?(v(),F(a(we),{key:0})):(v(),F(a(Ee),{key:1}))]),_:1})])],42,["aria-label","onKeydown"])),[[a(Z),T]]):te("v-if",!0),e.controls?ne((v(),K("span",{key:1,role:"button","aria-label":a(U)("el.inputNumber.increase"),class:L([a(m).e("increase"),a(m).is("disabled",a(G))]),onKeydown:_(D,["enter"])},[P(e.$slots,"increase-icon",{},()=>[O(a(X),null,{default:A(()=>[a(k)?(v(),F(a(Se),{key:0})):(v(),F(a(_e),{key:1}))]),_:1})])],42,["aria-label","onKeydown"])),[[a(Z),D]]):te("v-if",!0),O(a(Ae),{id:e.id,ref_key:"input",ref:b,type:"number",step:e.step,"model-value":a(x),placeholder:e.placeholder,readonly:e.readonly,disabled:a(h),size:a(R),max:e.max,min:e.min,name:e.name,"aria-label":e.ariaLabel,"validate-event":!1,onKeydown:[_(z(D,["prevent"]),["up"]),_(z(T,["prevent"]),["down"])],onBlur:de,onFocus:ce,onInput:ue,onChange:se},Ke({_:2},[e.$slots.prefix?{name:"prefix",fn:A(()=>[P(e.$slots,"prefix")])}:void 0,e.$slots.suffix?{name:"suffix",fn:A(()=>[P(e.$slots,"suffix")])}:void 0]),1032,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","aria-label","onKeydown"])],42,["onDragstart"]))}}));var Re=ye(Ge,[["__file","input-number.vue"]]);const an=Pe(Re);export{an as ElInputNumber,an as default,Ue as inputNumberEmits,Le as inputNumberProps};
