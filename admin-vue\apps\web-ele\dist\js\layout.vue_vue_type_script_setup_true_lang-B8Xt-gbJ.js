var Yl=Object.defineProperty,Xl=Object.defineProperties;var Jl=Object.getOwnPropertyDescriptors;var At=Object.getOwnPropertySymbols;var $a=Object.prototype.hasOwnProperty,Ba=Object.prototype.propertyIsEnumerable;var Zt=(l,a,t)=>a in l?Yl(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,Q=(l,a)=>{for(var t in a||(a={}))$a.call(a,t)&&Zt(l,t,a[t]);if(At)for(var t of At(a))Ba.call(a,t)&&Zt(l,t,a[t]);return l},we=(l,a)=>Xl(l,Jl(a));var ke=(l,a)=>{var t={};for(var o in l)$a.call(l,o)&&a.indexOf(o)<0&&(t[o]=l[o]);if(l!=null&&At)for(var o of At(l))a.indexOf(o)<0&&Ba.call(l,o)&&(t[o]=l[o]);return t};var kt=(l,a,t)=>Zt(l,typeof a!="symbol"?a+"":a,t);var X=(l,a,t)=>new Promise((o,s)=>{var n=d=>{try{i(t.next(d))}catch(p){s(p)}},r=d=>{try{i(t.throw(d))}catch(p){s(p)}},i=d=>d.done?o(d.value):Promise.resolve(d.value).then(n,r);i((t=t.apply(l,a)).next())});import{bY as Zl,c as he,X as Ql,bZ as eo,b_ as na,S as We,aP as to,aR as qa,aS as ao,ae as dt,aQ as lo,aT as oo,ah as at,aU as no,aV as so,b$ as ro,c0 as io,c1 as uo,c2 as co,c3 as po,c4 as fo,c5 as mo,c6 as ho,c7 as bo,c8 as vo,c9 as go,ca as yo,cb as xo,cc as wo,cd as ko,ce as Co,cf as Mo,cg as So,_ as $e,g as He,ch as To,G as _o,aj as De,B as _e,bu as Rt,ci as sa,cj as $o,ck as Bo,cl as Vo,cm as Lo,cn as Eo,co as zo,aD as nt,cp as Po,ai as Ao,av as ut,f as ct,cq as lt,aW as Ya,aX as Io,aY as Xa,aZ as Uo,a_ as Ja,a$ as Ho,b0 as Oo,b1 as Do,b2 as Wo,$ as g,ar as Pe,bm as Ro,J as Za,K as Qa,t as el,cr as tl,a4 as pt,cs as al,ct as No,bi as ft,a5 as gt,cu as Fo,cv as Ko,cw as jo,cx as Go,cy as qo,bg as Yo,cz as Xo,bh as ll,cA as Jo,a1 as Zo,aB as Qo,cB as en,cC as tn,cD as an,cE as ln,cF as on,cG as ol,cH as nn,cI as st,cJ as sn,cK as rn,cL as dn,cM as un}from"./bootstrap-CYivmKoJ.js";import{aG as cn,aH as pn,aI as nl,I as U,ah as fn,d as L,f as k,g as u,n as N,u as e,k as se,l as E,q as x,D as c,E as m,S as Fe,T as rt,v as Nt,e as y,r as Y,h as I,m as ce,U as ve,Y as ye,aJ as mn,Z as w,w as me,F as J,R as de,a as Mt,Q as Dt,aK as Ft,az as hn,o as Ye,j as B,t as T,G as A,B as ze,aL as bn,am as ot,z as Lt,aM as vn,aN as gn,L as mt,V as ra,aa as ia,aA as Qt,ao as yn,ap as xn,p as Ne,a9 as Kt,aq as wn,H as Va,aO as kn,aP as sl,aQ as It,N as Le,aR as Cn,aS as Mn,aT as la,aU as rl,aV as _t,aW as Wt,aX as Sn,aY as Ut,aZ as Tn,ac as da,a1 as Ke,s as ht,a8 as _n,a_ as $n,a$ as Bn,b0 as Vn,b1 as La,b2 as Ln,b3 as Ea,au as il,c as za,b4 as En,b5 as zn,b6 as Pn,O as vt,ak as Pa,b7 as An,b as Aa,x as jt,b8 as oa,y as In,b9 as Un,$ as Hn,aD as dl,ba as On,bb as Ia}from"../jse/index-index-SSqEGcIT.js";import{_ as $t}from"./avatar.vue_vue_type_script_setup_true_lang-DRkNZSlI.js";import{d as ua,e as ca,f as pa,g as St,h as Dn,i as ul,j as cl,S as Wn,M as Rn,k as Nn,a as Fn,_ as Kn,c as jn}from"./theme-toggle.vue_vue_type_script_setup_true_lang-BWxNdLTa.js";import{X as Et,V as Gn,u as pl}from"./use-drawer-Cga87ueS.js";import{_ as qn,a as Yn,b as Xn}from"./TabsList.vue_vue_type_script_setup_true_lang-Byr6YgCt.js";import{R as fa}from"./rotate-cw-D8Gh4ARV.js";import{d as Jn}from"./index-wKOw6cfx.js";function ma(l,a){for(const t of l){if(t.path===a)return t;const o=t.children&&ma(t.children,a);if(o)return o}return null}function Tt(l,a,t=0){var r;const o=ma(l,a),s=(r=o==null?void 0:o.parents)==null?void 0:r[t],n=s?l.find(i=>i.path===s):void 0;return{findMenu:o,rootMenu:n,rootMenuPath:s}}const yt=Zl("core-tabbar",{actions:{_bulkCloseByKeys(l){return X(this,null,function*(){const a=new Set(l);this.tabs=this.tabs.filter(t=>!a.has(Ge(t))),yield this.updateCacheTabs()})},_close(l){if(Qe(l))return;const a=this.tabs.findIndex(t=>et(t,l));a!==-1&&this.tabs.splice(a,1)},_goToDefaultTab(l){return X(this,null,function*(){if(this.getTabs.length<=0)return;const a=this.getTabs[0];a&&(yield this._goToTab(a,l))})},_goToTab(l,a){return X(this,null,function*(){const{params:t,path:o,query:s}=l,n={params:t||{},path:o,query:s||{}};yield a.replace(n)})},addTab(l){var o,s;let a=Zn(l);if(a.key||(a.key=qe(l)),!Qn(a))return a;const t=this.tabs.findIndex(n=>et(n,a));if(t===-1){const n=U.tabbar.maxCount,r=(s=(o=l==null?void 0:l.meta)==null?void 0:o.maxNumOfOpenTab)!=null?s:-1;if(r>0&&this.tabs.filter(i=>i.name===l.name).length>=r){const i=this.tabs.findIndex(d=>d.name===l.name);i!==-1&&this.tabs.splice(i,1)}else if(n>0&&this.tabs.length>=n){const i=this.tabs.findIndex(d=>!Reflect.has(d.meta,"affixTab")||!d.meta.affixTab);i!==-1&&this.tabs.splice(i,1)}this.tabs.push(a)}else{const n=fn(this.tabs)[t],r=we(Q(Q({},n),a),{meta:Q(Q({},n==null?void 0:n.meta),a.meta)});if(n){const i=n.meta;Reflect.has(i,"affixTab")&&(r.meta.affixTab=i.affixTab),Reflect.has(i,"newTabTitle")&&(r.meta.newTabTitle=i.newTabTitle)}a=r,this.tabs.splice(t,1,r)}return this.updateCacheTabs(),a},closeAllTabs(l){return X(this,null,function*(){const a=this.tabs.filter(t=>Qe(t));this.tabs=a.length>0?a:[...this.tabs].splice(0,1),yield this._goToDefaultTab(l),this.updateCacheTabs()})},closeLeftTabs(l){return X(this,null,function*(){const a=this.tabs.findIndex(s=>et(s,l));if(a<1)return;const t=this.tabs.slice(0,a),o=[];for(const s of t)Qe(s)||o.push(s.key);yield this._bulkCloseByKeys(o)})},closeOtherTabs(l){return X(this,null,function*(){const a=this.tabs.map(o=>Ge(o)),t=[];for(const o of a)if(o!==Ge(l)){const s=this.tabs.find(n=>Ge(n)===o);if(!s)continue;Qe(s)||t.push(s.key)}yield this._bulkCloseByKeys(t)})},closeRightTabs(l){return X(this,null,function*(){const a=this.tabs.findIndex(t=>et(t,l));if(a!==-1&&a<this.tabs.length-1){const t=this.tabs.slice(a+1),o=[];for(const s of t)Qe(s)||o.push(s.key);yield this._bulkCloseByKeys(o)}})},closeTab(l,a){return X(this,null,function*(){const{currentRoute:t}=a;if(qe(t.value)!==Ge(l)){this._close(l),this.updateCacheTabs();return}const o=this.getTabs.findIndex(r=>Ge(r)===qe(t.value)),s=this.getTabs[o-1],n=this.getTabs[o+1];n?(this._close(l),yield this._goToTab(n,a)):s?(this._close(l),yield this._goToTab(s,a)):console.error("Failed to close the tab; only one tab remains open.")})},closeTabByKey(l,a){return X(this,null,function*(){const t=decodeURIComponent(l),o=this.tabs.findIndex(n=>Ge(n)===t);if(o===-1)return;const s=this.tabs[o];s&&(yield this.closeTab(s,a))})},getTabByKey(l){return this.getTabs.find(a=>Ge(a)===l)},openTabInNewWindow(l){return X(this,null,function*(){nl(l.fullPath||l.path)})},pinTab(l){return X(this,null,function*(){var n;const a=this.tabs.findIndex(r=>et(r,l));if(a===-1)return;const t=this.tabs[a];l.meta.affixTab=!0,l.meta.title=(n=t==null?void 0:t.meta)==null?void 0:n.title,this.tabs.splice(a,1,l);const s=this.tabs.filter(r=>Qe(r)).findIndex(r=>et(r,l));yield this.sortTabs(a,s)})},refresh(l){return X(this,null,function*(){if(typeof l=="string")return yield this.refreshByName(l);const{currentRoute:a}=l,{name:t}=a.value;this.excludeCachedTabs.add(t),this.renderRouteView=!1,cn(),yield new Promise(o=>setTimeout(o,200)),this.excludeCachedTabs.delete(t),this.renderRouteView=!0,pn()})},refreshByName(l){return X(this,null,function*(){this.excludeCachedTabs.add(l),yield new Promise(a=>setTimeout(a,200)),this.excludeCachedTabs.delete(l)})},resetTabTitle(l){return X(this,null,function*(){var t;if((t=l==null?void 0:l.meta)!=null&&t.newTabTitle)return;const a=this.tabs.find(o=>et(o,l));a&&(a.meta.newTabTitle=void 0,yield this.updateCacheTabs())})},setAffixTabs(l){for(const a of l)a.meta.affixTab=!0,this.addTab(es(a))},setMenuList(l){this.menuList=l},setTabTitle(l,a){return X(this,null,function*(){const t=this.tabs.find(o=>et(o,l));t&&(t.meta.newTabTitle=a,yield this.updateCacheTabs())})},setUpdateTime(){this.updateTime=Date.now()},sortTabs(l,a){return X(this,null,function*(){const t=this.tabs[l];t&&(this.tabs.splice(l,1),this.tabs.splice(a,0,t),this.dragEndIndex=this.dragEndIndex+1)})},toggleTabPin(l){return X(this,null,function*(){var t,o;yield((o=(t=l==null?void 0:l.meta)==null?void 0:t.affixTab)!=null?o:!1)?this.unpinTab(l):this.pinTab(l)})},unpinTab(l){return X(this,null,function*(){var n;const a=this.tabs.findIndex(r=>et(r,l));if(a===-1)return;const t=this.tabs[a];l.meta.affixTab=!1,l.meta.title=(n=t==null?void 0:t.meta)==null?void 0:n.title,this.tabs.splice(a,1,l);const s=this.tabs.filter(r=>Qe(r)).length;yield this.sortTabs(a,s)})},updateCacheTabs(){return X(this,null,function*(){var a;const l=new Set;for(const t of this.tabs){if(!((a=t.meta)==null?void 0:a.keepAlive))continue;(t.matched||[]).forEach((n,r)=>{r>0&&l.add(n.name)});const s=t.name;l.add(s)}this.cachedTabs=l})}},getters:{affixTabs(){return this.tabs.filter(a=>Qe(a)).sort((a,t)=>{var n,r,i,d;const o=(r=(n=a.meta)==null?void 0:n.affixTabOrder)!=null?r:0,s=(d=(i=t.meta)==null?void 0:i.affixTabOrder)!=null?d:0;return o-s})},getCachedTabs(){return[...this.cachedTabs]},getExcludeCachedTabs(){return[...this.excludeCachedTabs]},getMenuList(){return this.menuList},getTabs(){const l=this.tabs.filter(a=>!Qe(a));return[...this.affixTabs,...l].filter(Boolean)}},persist:[{pick:["tabs"],storage:sessionStorage}],state:()=>({cachedTabs:new Set,dragEndIndex:0,excludeCachedTabs:new Set,menuList:["close","affix","maximize","reload","open-in-new-window","close-left","close-right","close-other","close-all"],renderRouteView:!0,tabs:[],updateTime:Date.now()})});function Zn(l){if(!l)return l;const s=l,{matched:a,meta:t}=s,o=ke(s,["matched","meta"]);return we(Q({},o),{matched:a?a.map(n=>({meta:n.meta,name:n.name,path:n.path})):void 0,meta:we(Q({},t),{newTabTitle:t.newTabTitle})})}function Qe(l){var a,t;return(t=(a=l==null?void 0:l.meta)==null?void 0:a.affixTab)!=null?t:!1}function Qn(l){var t;const a=(t=l==null?void 0:l.matched)!=null?t:[];return!l.meta.hideInTab&&a.every(o=>!o.meta.hideInTab)}function qe(l){const{fullPath:a,path:t,meta:{fullPathKey:o}={},query:s={}}=l,n=Array.isArray(s.pageKey)?s.pageKey[0]:s.pageKey;let r;n?r=n:r=o===!1?t:a!=null?a:t;try{return decodeURIComponent(r)}catch(i){return r}}function Ge(l){var a;return(a=l.key)!=null?a:qe(l)}function et(l,a){return Ge(l)===Ge(a)}function es(l){return{meta:l.meta,name:l.name,path:l.path,key:qe(l)}}const ts=he("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);const as=he("arrow-left-to-line",[["path",{d:"M3 19V5",key:"rwsyhb"}],["path",{d:"m13 6-6 6 6 6",key:"1yhaz7"}],["path",{d:"M7 12h14",key:"uoisry"}]]);const ls=he("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);const os=he("arrow-right-to-line",[["path",{d:"M17 12H3",key:"8awo09"}],["path",{d:"m11 18 6-6-6-6",key:"8c2y43"}],["path",{d:"M21 5v14",key:"nzette"}]]);const ns=he("arrow-up-to-line",[["path",{d:"M5 3h14",key:"7usisc"}],["path",{d:"m18 13-6-6-6 6",key:"1kf1n9"}],["path",{d:"M12 7v14",key:"1akyts"}]]);const ss=he("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);const rs=he("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);const is=he("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);const ds=he("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);const us=he("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);const cs=he("corner-down-left",[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]]);const ps=he("expand",[["path",{d:"m15 15 6 6",key:"1s409w"}],["path",{d:"m15 9 6-6",key:"ko1vev"}],["path",{d:"M21 16v5h-5",key:"1ck2sf"}],["path",{d:"M21 8V3h-5",key:"1qoq8a"}],["path",{d:"M3 16v5h5",key:"1t08am"}],["path",{d:"m3 21 6-6",key:"wwnumi"}],["path",{d:"M3 8V3h5",key:"1ln10m"}],["path",{d:"M9 9 3 3",key:"v551iv"}]]);const fs=he("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);const ms=he("fold-horizontal",[["path",{d:"M2 12h6",key:"1wqiqv"}],["path",{d:"M22 12h-6",key:"1eg9hc"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m19 9-3 3 3 3",key:"12ol22"}],["path",{d:"m5 15 3-3-3-3",key:"1kdhjc"}]]);const fl=he("fullscreen",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]]);const ml=he("lock-keyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]);const hs=he("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);const bs=he("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);const vs=he("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);const hl=he("minimize-2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);const gs=he("minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]);const bl=he("pin-off",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M15 9.34V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H7.89",key:"znwnzq"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v1.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h11",key:"c9qhm2"}]]);const Gt=he("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);const ys=he("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);const xs=he("search-x",[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);const Ua=he("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);const vl=he("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);const ws=he("shrink",[["path",{d:"m15 15 6 6m-6-6v4.8m0-4.8h4.8",key:"17vawe"}],["path",{d:"M9 19.8V15m0 0H4.2M9 15l-6 6",key:"chjx8e"}],["path",{d:"M15 4.2V9m0 0h4.8M15 9l6-6",key:"lav6yq"}],["path",{d:"M9 4.2V9m0 0H4.2M9 9 3 3",key:"1pxi2q"}]]);const ks=he("user-round-pen",[["path",{d:"M2 21a8 8 0 0 1 10.821-7.487",key:"1c8h7z"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}]]),Cs=Ql("inline-flex items-center rounded-md border border-border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-accent hover:bg-accent text-primary-foreground shadow",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive-hover",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}}),Ms=L({__name:"Badge",props:{class:{},variant:{}},setup(l){const a=l;return(t,o)=>(u(),k("div",{class:N(e(se)(e(Cs)({variant:t.variant}),a.class))},[E(t.$slots,"default")],2))}}),Ss=L({__name:"Breadcrumb",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("nav",{class:N(a.class),"aria-label":"breadcrumb",role:"navigation"},[E(t.$slots,"default")],2))}}),Ts=L({__name:"BreadcrumbItem",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("li",{class:N(e(se)("hover:text-foreground inline-flex items-center gap-1.5",a.class))},[E(t.$slots,"default")],2))}}),_s=L({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(l){const a=l;return(t,o)=>(u(),x(e(eo),{as:t.as,"as-child":t.asChild,class:N(e(se)("hover:text-foreground transition-colors",a.class))},{default:c(()=>[E(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),$s=L({__name:"BreadcrumbList",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("ol",{class:N(e(se)("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5",a.class))},[E(t.$slots,"default")],2))}}),Bs=L({__name:"BreadcrumbPage",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("span",{class:N(e(se)("text-foreground font-normal",a.class)),"aria-current":"page","aria-disabled":"true",role:"link"},[E(t.$slots,"default")],2))}}),Vs=L({__name:"BreadcrumbSeparator",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("li",{class:N(e(se)("[&>svg]:size-3.5",a.class)),"aria-hidden":"true",role:"presentation"},[E(t.$slots,"default",{},()=>[m(e(na))])],2))}}),Ls=L({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(l,{emit:a}){const s=We(l,a);return(n,r)=>(u(),x(e(to),Fe(rt(e(s))),{default:c(()=>[E(n.$slots,"default")]),_:3},16))}}),Es=["data-dismissable-modal"],zs=L({__name:"DialogOverlay",setup(l){qa();const a=Nt("DISMISSABLE_MODAL_ID");return(t,o)=>(u(),k("div",{"data-dismissable-modal":e(a),class:"bg-overlay z-popup inset-0"},null,8,Es))}}),Ps=L({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},appendTo:{default:"body"},class:{},closeClass:{},closeDisabled:{type:Boolean,default:!1},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},showClose:{type:Boolean,default:!0},zIndex:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(l,{expose:a,emit:t}){const o=l,s=t,n=y(()=>{const O=o,{class:v,modal:h,open:b,showClose:C}=O;return ke(O,["class","modal","open","showClose"])});function r(){return o.appendTo==="body"||o.appendTo===document.body||!o.appendTo}const i=y(()=>r()?"fixed":"absolute"),d=We(n,s),p=Y(null);function f(v){var h;v.target===((h=p.value)==null?void 0:h.$el)&&(o.open?s("opened"):s("closed"))}return a({getContentRef:()=>p.value}),(v,h)=>(u(),x(e(ao),{to:v.appendTo},{default:c(()=>[m(dt,{name:"fade"},{default:c(()=>[v.open&&v.modal?(u(),x(zs,{key:0,style:ce(we(Q({},v.zIndex?{zIndex:v.zIndex}:{}),{position:i.value,backdropFilter:v.overlayBlur&&v.overlayBlur>0?`blur(${v.overlayBlur}px)`:"none"})),onClick:h[0]||(h[0]=()=>s("close"))},null,8,["style"])):I("",!0)]),_:1}),m(e(oo),ve({ref_key:"contentRef",ref:p,style:we(Q({},v.zIndex?{zIndex:v.zIndex}:{}),{position:i.value}),onAnimationend:f},e(d),{class:e(se)("z-popup bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] w-full p-6 shadow-lg outline-none sm:rounded-xl",o.class)}),{default:c(()=>[E(v.$slots,"default"),v.showClose?(u(),x(e(lo),{key:0,disabled:v.closeDisabled,class:N(e(se)("data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-3 top-3 h-6 w-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none",o.closeClass)),onClick:h[1]||(h[1]=()=>s("close"))},{default:c(()=>[m(e(Et),{class:"h-4 w-4"})]),_:1},8,["disabled","class"])):I("",!0)]),_:3},16,["style","class"])]),_:3},8,["to"]))}}),Ha=L({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=at(t);return(s,n)=>(u(),x(e(no),ve(e(o),{class:e(se)("text-muted-foreground text-sm",a.class)}),{default:c(()=>[E(s.$slots,"default")]),_:3},16,["class"]))}}),As=L({__name:"DialogFooter",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("div",{class:N(e(se)("flex flex-row flex-col-reverse justify-end gap-x-2",a.class))},[E(t.$slots,"default")],2))}}),Is=L({__name:"DialogHeader",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("div",{class:N(e(se)("flex flex-col gap-y-1.5 text-center sm:text-left",a.class))},[E(t.$slots,"default")],2))}}),Oa=L({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=at(t);return(s,n)=>(u(),x(e(so),ve(e(o),{class:e(se)("text-lg font-semibold leading-none tracking-tight",a.class)}),{default:c(()=>[E(s.$slots,"default")]),_:3},16,["class"]))}}),Us=L({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(l){const a=l,t=y(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=at(t);return(s,n)=>(u(),x(e(ro),ve(e(o),{class:e(se)("px-2 py-1.5 text-sm font-semibold",s.inset&&"pl-8",a.class)}),{default:c(()=>[E(s.$slots,"default")]),_:3},16,["class"]))}}),Ht=L({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),x(e(io),ve(t.value,{class:e(se)("bg-border -mx-1 my-1 h-px",a.class)}),null,16,["class"]))}}),Da=L({__name:"DropdownMenuShortcut",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("span",{class:N(e(se)("ml-auto text-xs tracking-widest opacity-60",a.class))},[E(t.$slots,"default")],2))}}),Hs=L({__name:"HoverCard",props:{defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:a}){const s=We(l,a);return(n,r)=>(u(),x(e(uo),Fe(rt(e(s))),{default:c(()=>[E(n.$slots,"default")]),_:3},16))}}),Os=L({__name:"HoverCardContent",props:{forceMount:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=at(t);return(s,n)=>(u(),x(e(co),null,{default:c(()=>[m(e(po),ve(e(o),{class:e(se)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup w-64 rounded-md border p-4 shadow-md outline-none",a.class)}),{default:c(()=>[E(s.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Ds=L({__name:"HoverCardTrigger",props:{asChild:{type:Boolean},as:{}},setup(l){const a=l;return(t,o)=>(u(),x(e(fo),Fe(rt(a)),{default:c(()=>[E(t.$slots,"default")]),_:3},16))}}),Ws=L({__name:"NumberField",props:{defaultValue:{},modelValue:{},min:{},max:{},step:{},formatOptions:{},locale:{},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:modelValue"],setup(l,{emit:a}){const t=l,o=a,s=y(()=>{const d=t,{class:r}=d;return ke(d,["class"])}),n=We(s,o);return(r,i)=>(u(),x(e(mo),ve(e(n),{class:e(se)("grid gap-1.5",t.class)}),{default:c(()=>[E(r.$slots,"default")]),_:3},16,["class"]))}}),Rs=L({__name:"NumberFieldContent",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("div",{class:N(e(se)("relative [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5 [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5",a.class))},[E(t.$slots,"default")],2))}}),Ns=L({__name:"NumberFieldDecrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=at(t);return(s,n)=>(u(),x(e(ho),ve({"data-slot":"decrement"},e(o),{class:e(se)("absolute left-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",a.class)}),{default:c(()=>[E(s.$slots,"default",{},()=>[m(e(bo),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),Fs=L({__name:"NumberFieldIncrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=at(t);return(s,n)=>(u(),x(e(vo),ve({"data-slot":"increment"},e(o),{class:e(se)("absolute right-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",a.class)}),{default:c(()=>[E(s.$slots,"default",{},()=>[m(e(ys),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),Ks=L({__name:"NumberFieldInput",setup(l){return(a,t)=>(u(),x(e(go),{class:N(e(se)("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent py-1 text-center text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50")),"data-slot":"input"},null,8,["class"]))}}),gl=L({__name:"ScrollBar",props:{orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),x(e(yo),ve(t.value,{class:e(se)("flex touch-none select-none transition-colors",o.orientation==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-px",o.orientation==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-px",a.class)}),{default:c(()=>[m(e(xo),{class:"bg-border relative flex-1 rounded-full"})]),_:1},16,["class"]))}}),js=L({__name:"ScrollArea",props:{type:{},dir:{},scrollHideDelay:{},asChild:{type:Boolean},as:{},class:{},onScroll:{type:Function,default:()=>{}},viewportProps:{}},setup(l){const a=l,t=y(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),x(e(wo),ve(t.value,{class:e(se)("relative overflow-hidden",a.class)}),{default:c(()=>[m(e(ko),{"as-child":"",class:"h-full w-full rounded-[inherit] focus:outline-none",onScroll:o.onScroll},{default:c(()=>[E(o.$slots,"default")]),_:3},8,["onScroll"]),m(gl),m(e(Co))]),_:3},16,["class"]))}}),Gs=L({__name:"Switch",props:{defaultChecked:{type:Boolean},checked:{type:Boolean},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},value:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:checked"],setup(l,{emit:a}){const t=l,o=a,s=y(()=>{const d=t,{class:r}=d;return ke(d,["class"])}),n=We(s,o);return(r,i)=>(u(),x(e(Mo),ve(e(n),{class:e(se)("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-input peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t.class)}),{default:c(()=>[m(e(So),{class:N(e(se)("bg-background pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"))},null,8,["class"])]),_:1},16,["class"]))}}),qs=L({name:"VbenButtonGroup",__name:"button-group",props:{border:{type:Boolean,default:!1},gap:{default:0},size:{default:"middle"}},setup(l){return(a,t)=>(u(),k("div",{class:N(e(se)("vben-button-group rounded-md",`size-${a.size}`,a.gap?"with-gap":"no-gap",a.$attrs.class)),style:ce({gap:a.gap?`${a.gap}px`:"0px"})},[E(a.$slots,"default",{},void 0,!0)],6))}}),Ys=$e(qs,[["__scopeId","data-v-ba11c217"]]),Xs={key:0,class:"icon-wrapper"},Js=L({__name:"check-button-group",props:ye({allowClear:{type:Boolean,default:!1},beforeChange:{},btnClass:{},gap:{default:0},maxCount:{default:0},multiple:{type:Boolean,default:!1},options:{},showIcon:{type:Boolean,default:!0},size:{default:"middle"},disabled:{type:Boolean}},{modelValue:{},modelModifiers:{}}),emits:ye(["btnClick"],["update:modelValue"]),setup(l,{emit:a}){const t=l,o=a,s=y(()=>we(Q({},mn(t,["options","btnClass","size","disabled"])),{class:se(t.btnClass)})),n=w(l,"modelValue"),r=Y([]),i=Y([]);me(()=>t.multiple,p=>{p?n.value=r.value:n.value=r.value.length>0?r.value[0]:void 0}),me(()=>n.value,p=>{if(Array.isArray(p)){const f=p.filter(v=>v!==void 0);f.length>0?r.value=t.multiple?[...f]:[f[0]]:r.value=[]}else r.value=p===void 0?[]:[p]},{deep:!0,immediate:!0});function d(p){return X(this,null,function*(){if(t.beforeChange&&Mt(t.beforeChange))try{if(i.value.push(p),(yield t.beforeChange(p,!r.value.includes(p)))===!1)return}finally{i.value.splice(i.value.indexOf(p),1)}if(t.multiple)r.value.includes(p)?r.value=r.value.filter(f=>f!==p):(t.maxCount>0&&r.value.length>=t.maxCount&&(r.value=r.value.slice(0,t.maxCount-1)),r.value.push(p)),n.value=r.value;else if(t.allowClear&&r.value.includes(p)){r.value=[],n.value=void 0,o("btnClick",void 0);return}else r.value=[p],n.value=p;o("btnClick",p)})}return(p,f)=>(u(),x(Ys,{size:t.size,gap:t.gap,class:"vben-check-button-group"},{default:c(()=>[(u(!0),k(J,null,de(t.options,(v,h)=>(u(),x(He,ve({key:h,class:e(se)("border",t.btnClass),disabled:t.disabled||i.value.includes(v.value)||!t.multiple&&i.value.length>0},{ref_for:!0},s.value,{variant:r.value.includes(v.value)?"default":"outline",onClick:b=>d(v.value),type:"button"}),{default:c(()=>[t.showIcon?(u(),k("div",Xs,[E(p.$slots,"icon",{loading:i.value.includes(v.value),checked:r.value.includes(v.value)},()=>[i.value.includes(v.value)?(u(),x(e(To),{key:0,class:"animate-spin"})):r.value.includes(v.value)?(u(),x(e(is),{key:1})):(u(),x(e(ds),{key:2}))],!0)])):I("",!0),E(p.$slots,"option",{label:v.label,value:v.value,data:v},()=>[m(e(_o),{content:v.label},null,8,["content"])],!0)]),_:2},1040,["class","disabled","variant","onClick"]))),128))]),_:3},8,["size","gap"]))}}),Zs=$e(Js,[["__scopeId","data-v-9a0233de"]]),Qs=l=>{const a=Dt(),t=Dt(),o=Y(!1),s=()=>{var i;a.value&&(o.value=a.value.scrollTop>=((i=l==null?void 0:l.visibilityHeight)!=null?i:0))},n=()=>{var i;(i=a.value)==null||i.scrollTo({behavior:"smooth",top:0})},r=Ft(s,300,!0);return hn(t,"scroll",r),Ye(()=>{var i;if(t.value=document,a.value=document.documentElement,l.target){if(a.value=(i=document.querySelector(l.target))!=null?i:void 0,!a.value)throw new Error(`target does not exist: ${l.target}`);t.value=a.value}s()}),{handleClick:n,visible:o}},er=L({name:"BackTop",__name:"back-top",props:{bottom:{default:20},isGroup:{type:Boolean,default:!1},right:{default:24},target:{default:""},visibilityHeight:{default:200}},setup(l){const a=l,t=y(()=>({bottom:`${a.bottom}px`,right:`${a.right}px`})),{handleClick:o,visible:s}=Qs(a);return(n,r)=>(u(),x(dt,{name:"fade-down"},{default:c(()=>[e(s)?(u(),x(e(He),{key:0,style:ce(t.value),class:"dark:bg-accent dark:hover:bg-heavy bg-background hover:bg-heavy data shadow-float z-popup fixed bottom-10 size-10 rounded-full duration-500",size:"icon",variant:"icon",onClick:e(o)},{default:c(()=>[m(e(ns),{class:"size-4"})]),_:1},8,["style","onClick"])):I("",!0)]),_:1}))}}),tr={class:"flex"},ar=["onClick"],lr={class:"flex-center z-10 h-full"},or=L({name:"Breadcrumb",__name:"breadcrumb-background",props:{breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:a}){const t=a;function o(s,n){!n||s===l.breadcrumbs.length-1||t("select",n)}return(s,n)=>(u(),k("ul",tr,[m(Rt,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),k(J,null,de(s.breadcrumbs,(r,i)=>(u(),k("li",{key:`${r.path}-${r.title}-${i}`},[B("a",{href:"javascript:void 0",onClick:_e(d=>o(i,r.path),["stop"])},[B("span",lr,[s.showIcon?(u(),x(e(De),{key:0,icon:r.icon,class:"mr-1 size-4 flex-shrink-0"},null,8,["icon"])):I("",!0),B("span",{class:N({"text-foreground font-normal":i===s.breadcrumbs.length-1})},T(r.title),3)])],8,ar)]))),128))]),_:1})]))}}),nr=$e(or,[["__scopeId","data-v-da1498bb"]]),sr={key:0},rr={class:"flex-center"},ir={class:"flex-center"},dr=L({name:"Breadcrumb",__name:"breadcrumb",props:{breadcrumbs:{},showIcon:{type:Boolean,default:!1},styleType:{}},emits:["select"],setup(l,{emit:a}){const t=a;function o(s){s&&t("select",s)}return(s,n)=>(u(),x(e(Ss),null,{default:c(()=>[m(e($s),null,{default:c(()=>[m(Rt,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),k(J,null,de(s.breadcrumbs,(r,i)=>(u(),x(e(Ts),{key:`${r.path}-${r.title}-${i}`},{default:c(()=>{var d,p;return[(p=(d=r.items)==null?void 0:d.length)!=null&&p?(u(),k("div",sr,[m(e(ua),null,{default:c(()=>[m(e(ca),{class:"flex items-center gap-1"},{default:c(()=>[s.showIcon?(u(),x(e(De),{key:0,icon:r.icon,class:"size-5"},null,8,["icon"])):I("",!0),A(" "+T(r.title)+" ",1),m(e(sa),{class:"size-4"})]),_:2},1024),m(e(pa),{align:"start"},{default:c(()=>[(u(!0),k(J,null,de(r.items,f=>(u(),x(e(St),{key:`sub-${f.path}`,onClick:_e(v=>o(f.path),["stop"])},{default:c(()=>[A(T(f.title),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)])):i!==s.breadcrumbs.length-1?(u(),x(e(_s),{key:1,href:"javascript:void 0",onClick:_e(f=>o(r.path),["stop"])},{default:c(()=>[B("div",rr,[s.showIcon?(u(),x(e(De),{key:0,class:N([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):I("",!0),A(" "+T(r.title),1)])]),_:2},1032,["onClick"])):(u(),x(e(Bs),{key:2},{default:c(()=>[B("div",ir,[s.showIcon?(u(),x(e(De),{key:0,class:N([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):I("",!0),A(" "+T(r.title),1)])]),_:2},1024)),i<s.breadcrumbs.length-1&&!r.isHome?(u(),x(e(Vs),{key:3})):I("",!0)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}))}}),ur=L({__name:"breadcrumb-view",props:{class:{},breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:a}){const s=We(l,a);return(n,r)=>(u(),k(J,null,[n.styleType==="normal"?(u(),x(dr,ve({key:0},e(s),{class:"vben-breadcrumb"}),null,16)):I("",!0),n.styleType==="background"?(u(),x(nr,ve({key:1},e(s),{class:"vben-breadcrumb"}),null,16)):I("",!0)],64))}}),cr=$e(ur,[["__scopeId","data-v-4cd036dd"]]),pr=L({__name:"ContextMenu",props:{dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(l,{emit:a}){const s=We(l,a);return(n,r)=>(u(),x(e($o),Fe(rt(e(s))),{default:c(()=>[E(n.$slots,"default")]),_:3},16))}}),fr=L({__name:"ContextMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(l,{emit:a}){const t=l,o=a,s=y(()=>{const d=t,{class:r}=d;return ke(d,["class"])}),n=We(s,o);return(r,i)=>(u(),x(e(Bo),null,{default:c(()=>[m(e(Vo),ve(e(n),{class:e(se)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",t.class)}),{default:c(()=>[E(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),mr=L({__name:"ContextMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},emits:["select"],setup(l,{emit:a}){const t=l,o=a,s=y(()=>{const d=t,{class:r}=d;return ke(d,["class"])}),n=We(s,o);return(r,i)=>(u(),x(e(Lo),ve(e(n),{class:e(se)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[E(r.$slots,"default")]),_:3},16,["class"]))}}),hr=L({__name:"ContextMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),x(e(Eo),ve(t.value,{class:e(se)("bg-border -mx-1 my-1 h-px",a.class)}),null,16,["class"]))}}),br=L({__name:"ContextMenuShortcut",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("span",{class:N(e(se)("text-muted-foreground ml-auto text-xs tracking-widest",a.class))},[E(t.$slots,"default")],2))}}),vr=L({__name:"ContextMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const t=at(l);return(o,s)=>(u(),x(e(zo),Fe(rt(e(t))),{default:c(()=>[E(o.$slots,"default")]),_:3},16))}}),yl=L({__name:"context-menu",props:{dir:{},modal:{type:Boolean},class:{},contentClass:{},contentProps:{},handlerData:{},itemClass:{},menus:{type:Function}},emits:["update:open"],setup(l,{emit:a}){const t=l,o=a,s=y(()=>{const b=t,{class:d,contentClass:p,contentProps:f,itemClass:v}=b;return ke(b,["class","contentClass","contentProps","itemClass"])}),n=We(s,o),r=y(()=>{var d;return(d=t.menus)==null?void 0:d.call(t,t.handlerData)});function i(d){var p;d.disabled||(p=d==null?void 0:d.handler)==null||p.call(d,t.handlerData)}return(d,p)=>(u(),x(e(pr),Fe(rt(e(n))),{default:c(()=>[m(e(vr),{"as-child":""},{default:c(()=>[E(d.$slots,"default")]),_:3}),m(e(fr),ve({class:d.contentClass},d.contentProps,{class:"side-content z-popup"}),{default:c(()=>[(u(!0),k(J,null,de(r.value,f=>(u(),k(J,{key:f.key},[m(e(mr),{class:N([d.itemClass,"cursor-pointer"]),disabled:f.disabled,inset:f.inset||!f.icon,onClick:v=>i(f)},{default:c(()=>[f.icon?(u(),x(ze(f.icon),{key:0,class:"mr-2 size-4 text-lg"})):I("",!0),A(" "+T(f.text)+" ",1),f.shortcut?(u(),x(e(br),{key:1},{default:c(()=>[A(T(f.shortcut),1)]),_:2},1024)):I("",!0)]),_:2},1032,["class","disabled","inset","onClick"]),f.separator?(u(),x(e(hr),{key:0})):I("",!0)],64))),128))]),_:1},16,["class"])]),_:3},16))}}),gr=L({name:"DropdownMenu",__name:"dropdown-menu",props:{menus:{}},setup(l){const a=l;function t(o){var s;o.disabled||(s=o==null?void 0:o.handler)==null||s.call(o,a)}return(o,s)=>(u(),x(e(ua),null,{default:c(()=>[m(e(ca),{class:"flex h-full items-center gap-1"},{default:c(()=>[E(o.$slots,"default")]),_:3}),m(e(pa),{align:"start"},{default:c(()=>[m(e(Dn),null,{default:c(()=>[(u(!0),k(J,null,de(o.menus,n=>(u(),k(J,{key:n.value},[m(e(St),{disabled:n.disabled,class:"data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer",onClick:r=>t(n)},{default:c(()=>[n.icon?(u(),x(ze(n.icon),{key:0,class:"mr-2 size-4"})):I("",!0),A(" "+T(n.label),1)]),_:2},1032,["disabled","onClick"]),n.separator?(u(),x(e(Ht),{key:0,class:"bg-border"})):I("",!0)],64))),128))]),_:1})]),_:1})]),_:3}))}}),yr=L({name:"FullScreen",__name:"full-screen",setup(l){const{isFullscreen:a,toggle:t}=bn();return a.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),(o,s)=>(u(),x(e(nt),{onClick:e(t)},{default:c(()=>[e(a)?(u(),x(e(gs),{key:0,class:"text-foreground size-4"})):(u(),x(e(vs),{key:1,class:"text-foreground size-4"}))]),_:1},8,["onClick"]))}}),xr={class:"h-full cursor-pointer"},wr=L({__name:"hover-card",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:a}){const t=l,o=a,s=y(()=>{const f=t,{class:r,contentClass:i,contentProps:d}=f;return ke(f,["class","contentClass","contentProps"])}),n=We(s,o);return(r,i)=>(u(),x(e(Hs),Fe(rt(e(n))),{default:c(()=>[m(e(Ds),{"as-child":"",class:"h-full"},{default:c(()=>[B("div",xr,[E(r.$slots,"trigger")])]),_:3}),m(e(Os),ve({class:r.contentClass},r.contentProps,{class:"side-content z-popup"}),{default:c(()=>[E(r.$slots,"default")]),_:3},16,["class"])]),_:3},16))}}),kr=["href"],Cr={class:"text-foreground truncate text-nowrap font-semibold"},Wa=L({name:"VbenLogo",__name:"logo",props:{collapsed:{type:Boolean,default:!1},fit:{default:"cover"},href:{default:"javascript:void 0"},logoSize:{default:32},src:{default:""},text:{},theme:{default:"light"}},setup(l){return(a,t)=>(u(),k("div",{class:N([a.theme,"flex h-full items-center text-lg"])},[B("a",{class:N([a.$attrs.class,"flex h-full items-center gap-2 overflow-hidden px-3 text-lg leading-normal transition-all duration-500"]),href:a.href},[a.src?(u(),x(e($t),{key:0,alt:a.text,src:a.src,size:a.logoSize,fit:a.fit,class:"relative rounded-none bg-transparent"},null,8,["alt","src","size","fit"])):I("",!0),a.collapsed?I("",!0):E(a.$slots,"text",{key:1},()=>[B("span",Cr,T(a.text),1)])],10,kr)],2))}}),Ra=1,Mr=L({__name:"scrollbar",props:{class:{default:""},horizontal:{type:Boolean,default:!1},scrollBarClass:{},shadow:{type:Boolean,default:!1},shadowBorder:{type:Boolean,default:!1},shadowBottom:{type:Boolean,default:!0},shadowLeft:{type:Boolean,default:!1},shadowRight:{type:Boolean,default:!1},shadowTop:{type:Boolean,default:!0}},emits:["scrollAt"],setup(l,{emit:a}){const t=l,o=a,s=Y(!0),n=Y(!1),r=Y(!1),i=Y(!0),d=y(()=>t.shadow&&t.shadowTop),p=y(()=>t.shadow&&t.shadowBottom),f=y(()=>t.shadow&&t.shadowLeft),v=y(()=>t.shadow&&t.shadowRight),h=y(()=>({"both-shadow":!i.value&&!n.value&&f.value&&v.value,"left-shadow":!i.value&&f.value,"right-shadow":!n.value&&v.value}));function b(C){var K,z,$,R,q,Z;const H=C.target,O=(K=H==null?void 0:H.scrollTop)!=null?K:0,M=(z=H==null?void 0:H.scrollLeft)!=null?z:0,W=($=H==null?void 0:H.clientHeight)!=null?$:0,F=(R=H==null?void 0:H.clientWidth)!=null?R:0,P=(q=H==null?void 0:H.scrollHeight)!=null?q:0,V=(Z=H==null?void 0:H.scrollWidth)!=null?Z:0;s.value=O<=0,i.value=M<=0,r.value=Math.abs(O)+W>=P-Ra,n.value=Math.abs(M)+F>=V-Ra,o("scrollAt",{bottom:r.value,left:i.value,right:n.value,top:s.value})}return(C,H)=>(u(),x(e(js),{class:N([[e(se)(t.class),h.value],"vben-scrollbar relative"]),"on-scroll":b},{default:c(()=>[d.value?(u(),k("div",{key:0,class:N([{"opacity-100":!s.value,"border-border border-t":C.shadowBorder&&!s.value},"scrollbar-top-shadow pointer-events-none absolute top-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):I("",!0),E(C.$slots,"default",{},void 0,!0),p.value?(u(),k("div",{key:1,class:N([{"opacity-100":!s.value&&!r.value,"border-border border-b":C.shadowBorder&&!s.value&&!r.value},"scrollbar-bottom-shadow pointer-events-none absolute bottom-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):I("",!0),C.horizontal?(u(),x(e(gl),{key:2,class:N(C.scrollBarClass),orientation:"horizontal"},null,8,["class"])):I("",!0)]),_:3},8,["class"]))}}),Bt=$e(Mr,[["__scopeId","data-v-c94474ed"]]),Sr={class:"bg-background text-foreground inline-flex h-full w-full items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},Tr=L({__name:"tabs-indicator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=y(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=at(t);return(s,n)=>(u(),x(e(Po),ve(e(o),{class:e(se)("absolute bottom-0 left-0 z-10 h-full w-1/2 translate-x-[--radix-tabs-indicator-position] rounded-full px-0 py-1 pr-1 transition-[width,transform] duration-300",a.class)}),{default:c(()=>[B("div",Sr,[E(s.$slots,"default")])]),_:3},16,["class"]))}}),_r=L({__name:"segmented",props:ye({defaultValue:{default:""},tabs:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=l,t=w(l,"modelValue"),o=y(()=>{var r;return a.defaultValue||((r=a.tabs[0])==null?void 0:r.value)}),s=y(()=>({"grid-template-columns":`repeat(${a.tabs.length}, minmax(0, 1fr))`})),n=y(()=>({width:`${(100/a.tabs.length).toFixed(0)}%`}));return(r,i)=>(u(),x(e(qn),{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=d=>t.value=d),"default-value":o.value},{default:c(()=>[m(e(Yn),{style:ce(s.value),class:"bg-accent relative grid w-full"},{default:c(()=>[m(Tr,{style:ce(n.value)},null,8,["style"]),(u(!0),k(J,null,de(r.tabs,d=>(u(),x(e(Ao),{key:d.value,value:d.value,class:"z-20 inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"},{default:c(()=>[A(T(d.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["style"]),(u(!0),k(J,null,de(r.tabs,d=>(u(),x(e(Xn),{key:d.value,value:d.value},{default:c(()=>[E(r.$slots,d.value)]),_:2},1032,["value"]))),128))]),_:3},8,["modelValue","default-value"]))}});function xl(){const{contentIsMaximize:l}=ut();function a(){const t=l.value;ot({header:{hidden:!t},sidebar:{hidden:!t}})}return{contentIsMaximize:l,toggleMaximize:a}}const ea=500,ta=0;function $r(l,a=ea){const t=typeof a=="number"||Mt(a)?{enterDelay:ta,leaveDelay:a}:Q({enterDelay:ta,leaveDelay:ea},a),o=Y(!1),s=Y(),n=Y(),r=Y([]),i=y(()=>{const M=e(l);return M===null?[]:Array.isArray(M)?M:[M]}),d=Y([]);function p(){r.value.forEach(M=>M.stop()),r.value=[],d.value=i.value.map(M=>{if(!M)return Y(!1);const W=y(()=>{const V=e(M);return V instanceof Element?V:V==null?void 0:V.$el}),F=vn(),P=F.run(()=>gn(W))||Y(!1);return r.value.push(F),P})}const f=y(()=>{const M=e(l);return M===null?0:Array.isArray(M)?M.length:1});p();const v=me(f,p,{deep:!1}),h=y(()=>d.value.every(M=>!M.value));function b(){s.value&&(clearTimeout(s.value),s.value=void 0),n.value&&(clearTimeout(n.value),n.value=void 0)}function C(M){var W,F;if(b(),M){const P=(W=t.enterDelay)!=null?W:ta,V=Mt(P)?P():P;V<=0?o.value=!0:s.value=setTimeout(()=>{o.value=!0,s.value=void 0},V)}else{const P=(F=t.leaveDelay)!=null?F:ea,V=Mt(P)?P():P;V<=0?o.value=!1:n.value=setTimeout(()=>{o.value=!1,n.value=void 0},V)}}const H=me(h,M=>{C(!M)},{immediate:!0}),O={enable(){H.resume()},disable(){H.pause()}};return Lt(()=>{b(),v(),r.value.forEach(M=>M.stop())}),[o,O]}function wl(){const l=ct(),a=yt();function t(){return X(this,null,function*(){yield a.refresh(l)})}return{refresh:t}}function kl(){const l=ct(),a=lt(),t=yt();function o(M){return X(this,null,function*(){yield t.closeLeftTabs(M||a)})}function s(){return X(this,null,function*(){yield t.closeAllTabs(l)})}function n(M){return X(this,null,function*(){yield t.closeRightTabs(M||a)})}function r(M){return X(this,null,function*(){yield t.closeOtherTabs(M||a)})}function i(M){return X(this,null,function*(){yield t.closeTab(M||a,l)})}function d(M){return X(this,null,function*(){yield t.pinTab(M||a)})}function p(M){return X(this,null,function*(){yield t.unpinTab(M||a)})}function f(M){return X(this,null,function*(){yield t.toggleTabPin(M||a)})}function v(M){return X(this,null,function*(){yield t.refresh(M||l)})}function h(M){return X(this,null,function*(){yield t.openTabInNewWindow(M||a)})}function b(M){return X(this,null,function*(){yield t.closeTabByKey(M,l)})}function C(M){return X(this,null,function*(){t.setUpdateTime(),yield t.setTabTitle(a,M)})}function H(){return X(this,null,function*(){t.setUpdateTime(),yield t.resetTabTitle(a)})}function O(M=a){var re;const W=t.getTabs,F=t.affixTabs,P=W.findIndex(ue=>ue.path===M.path),V=W.length<=1,{meta:K}=M,z=(re=K==null?void 0:K.affixTab)!=null?re:!1,$=a.path===M.path,R=P===0||P-F.length<=0||!$,q=!$||P===W.length-1,Z=V||!$||W.length-F.length<=1;return{disabledCloseAll:V,disabledCloseCurrent:!!z||V,disabledCloseLeft:R,disabledCloseOther:Z,disabledCloseRight:q,disabledRefresh:!$}}return{closeAllTabs:s,closeCurrentTab:i,closeLeftTabs:o,closeOtherTabs:r,closeRightTabs:n,closeTabByKey:b,getTabDisableState:O,openTabInNewWindow:h,pinTab:d,refreshTab:v,resetTabTitle:H,setTabTitle:C,toggleTabPin:f,unpinTab:p}}function Br(l,a,t,o){const s=mt({offsetX:0,offsetY:0}),n=Y(!1),r=f=>{const v=f.clientX,h=f.clientY;if(!l.value)return;const b=l.value.getBoundingClientRect(),{offsetX:C,offsetY:H}=s,O=b.left,M=b.top,W=b.width,F=b.height;let P=null;if(o!=null&&o.value){const Z=document.querySelector(o.value);Z&&(P=Z.getBoundingClientRect())}let V,K,z,$;if(P)z=P.left-O+C,V=P.right-O-W+C,$=P.top-M+H,K=P.bottom-M-F+H;else{const Z=document.documentElement,re=Z.clientWidth,ue=Z.clientHeight;z=-O+C,$=-M+H,V=re-O-W+C,K=ue-M-F+H}const R=Z=>{let re=C+Z.clientX-v,ue=H+Z.clientY-h;re=Math.min(Math.max(re,z),V),ue=Math.min(Math.max(ue,$),K),s.offsetX=re,s.offsetY=ue,l.value&&(l.value.style.transform=`translate(${re}px, ${ue}px)`,n.value=!0)},q=()=>{n.value=!1,document.removeEventListener("mousemove",R),document.removeEventListener("mouseup",q)};document.addEventListener("mousemove",R),document.addEventListener("mouseup",q)},i=()=>{const f=Qt(a);f&&l.value&&f.addEventListener("mousedown",r)},d=()=>{const f=Qt(a);f&&l.value&&f.removeEventListener("mousedown",r)},p=()=>{s.offsetX=0,s.offsetY=0;const f=Qt(l);f&&(f.style.transform="none")};return Ye(()=>{ra(()=>{t.value?i():d()})}),ia(()=>{d()}),{dragging:n,resetPosition:p,transform:s}}const Vr=L({__name:"modal",props:{modalApi:{default:void 0},appendToMain:{type:Boolean,default:!1},bordered:{type:Boolean},cancelText:{},centered:{type:Boolean},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmDisabled:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},draggable:{type:Boolean},footer:{type:Boolean},footerClass:{},fullscreen:{type:Boolean},fullscreenButton:{type:Boolean},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean},title:{},titleTooltip:{},zIndex:{}},setup(l){var it,wt;const a=l,t=Ya.getComponents(),o=Y(),s=Y(),n=Y(),r=Y(),i=Y(),d=yn();Kt("DISMISSABLE_MODAL_ID",d);const{$t:p}=Io(),{isMobile:f}=Xa(),v=(wt=(it=a.modalApi)==null?void 0:it.useStore)==null?void 0:wt.call(it),{appendToMain:h,bordered:b,cancelText:C,centered:H,class:O,closable:M,closeOnClickModal:W,closeOnPressEscape:F,confirmDisabled:P,confirmLoading:V,confirmText:K,contentClass:z,description:$,destroyOnClose:R,draggable:q,footer:Z,footerClass:re,fullscreen:ue,fullscreenButton:G,header:ae,headerClass:xe,loading:Ce,modal:Ve,openAutoFocus:Oe,overlayBlur:Be,showCancelButton:j,showConfirmButton:ne,submitting:ee,title:ge,titleTooltip:Se,zIndex:Ae}=Uo(a,v),ie=y(()=>ue.value&&ae.value||f.value),be=y(()=>q.value&&!ie.value&&ae.value),pe=y(()=>h.value?`#${Ja}>div:not(.absolute)>div`:void 0),{dragging:Xe,transform:Je}=Br(n,r,be,pe),Ee=Y(!1),D=Y(!0);me(()=>{var te;return(te=v==null?void 0:v.value)==null?void 0:te.isOpen},te=>X(null,null,function*(){if(te){if(D.value=!1,Ee.value||(Ee.value=!0),yield Ne(),!o.value)return;const Me=o.value.getContentRef();n.value=Me.$el;const{offsetX:Ze,offsetY:je}=Je;n.value.style.transform=`translate(${Ze}px, ${je}px)`}}),{immediate:!0}),xn(()=>{var te;h.value||(te=a.modalApi)==null||te.close()});function oe(){var te;(te=a.modalApi)==null||te.setState(Me=>we(Q({},Me),{fullscreen:!ue.value}))}function fe(te){(!W.value||ee.value)&&(te.preventDefault(),te.stopPropagation())}function Ie(te){(!F.value||ee.value)&&te.preventDefault()}function Ue(te){Oe.value||te==null||te.preventDefault()}function Yt(te){const Me=te.target,Ze=Me==null?void 0:Me.dataset.dismissableModal;(!W.value||Ze!==d||ee.value)&&(te.preventDefault(),te.stopPropagation())}function xt(te){te.preventDefault(),te.stopPropagation()}const zt=y(()=>!e(R)&&e(Ee));function Pt(){var te;D.value=!0,(te=a.modalApi)==null||te.onClosed()}return(te,Me)=>{var Ze;return u(),x(e(Ls),{modal:!1,open:(Ze=e(v))==null?void 0:Ze.isOpen,"onUpdate:open":Me[3]||(Me[3]=()=>{var je;return e(ee)||(je=te.modalApi)==null?void 0:je.close()})},{default:c(()=>{var je;return[m(e(Ps),{ref_key:"contentRef",ref:o,"append-to":pe.value,class:N(e(se)("left-0 right-0 top-[10vh] mx-auto flex max-h-[80%] w-[520px] flex-col p-0",ie.value?"sm:rounded-none":"sm:rounded-[var(--radius)]",e(O),{"border-border border":e(b),"shadow-3xl":!e(b),"left-0 top-0 size-full max-h-full !translate-x-0 !translate-y-0":ie.value,"top-1/2 !-translate-y-1/2":e(H)&&!ie.value,"duration-300":!e(Xe),hidden:D.value})),"force-mount":zt.value,modal:e(Ve),open:(je=e(v))==null?void 0:je.isOpen,"show-close":e(M),"z-index":e(Ae),"overlay-blur":e(Be),"close-class":"top-3",onCloseAutoFocus:xt,onClosed:Pt,"close-disabled":e(ee),onEscapeKeyDown:Ie,onFocusOutside:xt,onInteractOutside:fe,onOpenAutoFocus:Ue,onOpened:Me[2]||(Me[2]=()=>{var Re;return(Re=te.modalApi)==null?void 0:Re.onOpened()}),onPointerDownOutside:Yt},{default:c(()=>[m(e(Is),{ref_key:"headerRef",ref:r,class:N(e(se)("px-5 py-4",{"border-b":e(b),hidden:!e(ae),"cursor-move select-none":be.value},e(xe)))},{default:c(()=>[e(ge)?(u(),x(e(Oa),{key:0,class:"text-left"},{default:c(()=>[E(te.$slots,"title",{},()=>[A(T(e(ge))+" ",1),e(Se)?E(te.$slots,"titleTooltip",{key:0},()=>[m(e(Ho),{"trigger-class":"pb-1"},{default:c(()=>[A(T(e(Se)),1)]),_:1})]):I("",!0)])]),_:3})):I("",!0),e($)?(u(),x(e(Ha),{key:1},{default:c(()=>[E(te.$slots,"description",{},()=>[A(T(e($)),1)])]),_:3})):I("",!0),!e(ge)||!e($)?(u(),x(e(Oo),{key:2},{default:c(()=>[e(ge)?I("",!0):(u(),x(e(Oa),{key:0})),e($)?I("",!0):(u(),x(e(Ha),{key:1}))]),_:1})):I("",!0)]),_:3},8,["class"]),B("div",{ref_key:"wrapperRef",ref:s,class:N(e(se)("relative min-h-40 flex-1 overflow-y-auto p-3",e(z),{"pointer-events-none":e(Ce)||e(ee)}))},[E(te.$slots,"default")],2),e(Ce)||e(ee)?(u(),x(e(Gn),{key:0,spinning:""})):I("",!0),e(G)?(u(),x(e(nt),{key:1,class:"hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-10 top-3 hidden size-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none sm:block",onClick:oe},{default:c(()=>[e(ue)?(u(),x(e(ws),{key:0,class:"size-3.5"})):(u(),x(e(ps),{key:1,class:"size-3.5"}))]),_:1})):I("",!0),e(Z)?(u(),x(e(As),{key:2,ref_key:"footerRef",ref:i,class:N(e(se)("flex-row items-center justify-end p-2",{"border-t":e(b)},e(re)))},{default:c(()=>[E(te.$slots,"prepend-footer"),E(te.$slots,"footer",{},()=>[e(j)?(u(),x(ze(e(t).DefaultButton||e(He)),{key:0,variant:"ghost",disabled:e(ee),onClick:Me[0]||(Me[0]=()=>{var Re;return(Re=te.modalApi)==null?void 0:Re.onCancel()})},{default:c(()=>[E(te.$slots,"cancelText",{},()=>[A(T(e(C)||e(p)("cancel")),1)])]),_:3},8,["disabled"])):I("",!0),E(te.$slots,"center-footer"),e(ne)?(u(),x(ze(e(t).PrimaryButton||e(He)),{key:1,disabled:e(P),loading:e(V)||e(ee),onClick:Me[1]||(Me[1]=()=>{var Re;return(Re=te.modalApi)==null?void 0:Re.onConfirm()})},{default:c(()=>[E(te.$slots,"confirmText",{},()=>[A(T(e(K)||e(p)("confirm")),1)])]),_:3},8,["disabled","loading"])):I("",!0)]),E(te.$slots,"append-footer")]),_:3},8,["class"])):I("",!0)]),_:3},8,["append-to","class","force-mount","modal","open","show-close","z-index","overlay-blur","close-disabled"])]}),_:3},8,["open"])}}});class Lr{constructor(a={}){kt(this,"sharedData",{payload:{}});kt(this,"store");kt(this,"api");kt(this,"state");const v=a,{connectedComponent:t,onBeforeClose:o,onCancel:s,onClosed:n,onConfirm:r,onOpenChange:i,onOpened:d}=v,p=ke(v,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),f={bordered:!0,centered:!1,class:"",closeOnClickModal:!0,closeOnPressEscape:!0,confirmDisabled:!1,confirmLoading:!1,contentClass:"",destroyOnClose:!0,draggable:!1,footer:!0,footerClass:"",fullscreen:!1,fullscreenButton:!0,header:!0,headerClass:"",isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new Do(Q(Q({},f),p),{onUpdate:()=>{var b,C,H;const h=this.store.state;(h==null?void 0:h.isOpen)===((b=this.state)==null?void 0:b.isOpen)?this.state=h:(this.state=h,(H=(C=this.api).onOpenChange)==null||H.call(C,!!(h!=null&&h.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:o,onCancel:s,onClosed:n,onConfirm:r,onOpenChange:i,onOpened:d},wn(this)}close(){return X(this,null,function*(){var t,o,s;((s=yield(o=(t=this.api).onBeforeClose)==null?void 0:o.call(t))!=null?s:!0)&&this.store.setState(n=>we(Q({},n),{isOpen:!1,submitting:!1}))})}getData(){var a,t;return(t=(a=this.sharedData)==null?void 0:a.payload)!=null?t:{}}lock(a=!0){return this.setState({submitting:a})}onCancel(){var a,t;this.api.onCancel?(t=(a=this.api).onCancel)==null||t.call(a):this.close()}onClosed(){var a,t;this.state.isOpen||(t=(a=this.api).onClosed)==null||t.call(a)}onConfirm(){var a,t;(t=(a=this.api).onConfirm)==null||t.call(a)}onOpened(){var a,t;this.state.isOpen&&((t=(a=this.api).onOpened)==null||t.call(a))}open(){this.store.setState(a=>we(Q({},a),{isOpen:!0}))}setData(a){return this.sharedData.payload=a,this}setState(a){return Mt(a)?this.store.setState(a):this.store.setState(t=>Q(Q({},t),a)),this}unlock(){return this.lock(!1)}}const Na=Symbol("VBEN_MODAL_INJECT"),Er={};function Vt(l={}){var d;const{connectedComponent:a}=l;if(a){const p=mt({}),f=Y(!0);return[L((h,{attrs:b,slots:C})=>(Kt(Na,{extendApi(O){Object.setPrototypeOf(p,O)},options:l,reCreateModal(){return X(this,null,function*(){f.value=!1,yield Ne(),f.value=!0})}}),zr(p,Q(Q(Q({},h),b),C)),()=>Va(f.value?a:"div",Q(Q({},h),b),C)),{name:"VbenParentModal",inheritAttrs:!1}),p]}const t=Nt(Na,{}),o=Q(Q(Q({},Er),t.options),l);o.onOpenChange=p=>{var f,v,h;(f=l.onOpenChange)==null||f.call(l,p),(h=(v=t.options)==null?void 0:v.onOpenChange)==null||h.call(v,p)};const s=o.onClosed;o.onClosed=()=>{var p;s==null||s(),o.destroyOnClose&&((p=t.reCreateModal)==null||p.call(t))};const n=new Lr(o),r=n;r.useStore=p=>Wo(n.store,p);const i=L((p,{attrs:f,slots:v})=>()=>Va(Vr,we(Q(Q({},p),f),{modalApi:r}),v),{name:"VbenModal",inheritAttrs:!1});return(d=t.extendApi)==null||d.call(t,r),[i,r]}function zr(l,a){return X(this,null,function*(){var s;if(!a||Object.keys(a).length===0)return;yield Ne();const t=(s=l==null?void 0:l.store)==null?void 0:s.state;if(!t)return;const o=new Set(Object.keys(t));for(const n of Object.keys(a))o.has(n)&&!["class"].includes(n)&&console.warn(`[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${n}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`)})}const Pr=L({__name:"breadcrumb",props:{hideWhenOnlyOne:{type:Boolean},showHome:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1},type:{default:"normal"}},setup(l){const a=l,t=lt(),o=ct(),s=y(()=>{const r=t.matched,i=[];for(const d of r){const{meta:p,path:f}=d,{hideChildrenInMenu:v,hideInBreadcrumb:h,icon:b,name:C,title:H}=p||{};h||v||!f||i.push({icon:b,path:f||t.path,title:H?g(H||C):""})}return a.showHome&&i.unshift({icon:"mdi:home-outline",isHome:!0,path:"/"}),a.hideWhenOnlyOne&&i.length===1?[]:i});function n(r){o.push(r)}return(r,i)=>(u(),x(e(cr),{breadcrumbs:s.value,"show-icon":r.showIcon,"style-type":r.type,class:"ml-2",onSelect:n},null,8,["breadcrumbs","show-icon","style-type"]))}}),Ar=L({name:"CheckUpdates",__name:"check-updates",props:{checkUpdatesInterval:{default:1},checkUpdateUrl:{default:"/"}},setup(l){const a=l;let t=!1;const o=Y(""),s=Y(""),n=Y(),[r,i]=Vt({closable:!1,closeOnPressEscape:!1,closeOnClickModal:!1,onConfirm(){s.value=o.value,window.location.reload()}});function d(){return X(this,null,function*(){try{if(location.hostname==="localhost"||location.hostname==="127.0.0.1")return null;const C=yield fetch(a.checkUpdateUrl,{cache:"no-cache",method:"HEAD",redirect:"manual"});return C.headers.get("etag")||C.headers.get("last-modified")}catch(C){return console.error("Failed to fetch version tag"),null}})}function p(){return X(this,null,function*(){const C=yield d();if(C){if(!s.value){s.value=C;return}s.value!==C&&C&&(clearInterval(n.value),f(C))}})}function f(C){o.value=C,i.open()}function v(){a.checkUpdatesInterval<=0||(n.value=setInterval(p,a.checkUpdatesInterval*60*1e3))}function h(){document.hidden?b():t||(t=!0,p().finally(()=>{t=!1,v()}))}function b(){clearInterval(n.value)}return Ye(()=>{v(),document.addEventListener("visibilitychange",h)}),Lt(()=>{b(),document.removeEventListener("visibilitychange",h)}),(C,H)=>(u(),x(e(r),{"cancel-text":e(g)("common.cancel"),"confirm-text":e(g)("common.refresh"),"fullscreen-button":!1,title:e(g)("ui.widgets.checkUpdatesTitle"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[A(T(e(g)("ui.widgets.checkUpdatesDescription")),1)]),_:1},8,["cancel-text","confirm-text","title"]))}}),Ir={class:"!flex h-full justify-center px-2 sm:max-h-[450px]"},Ur={key:0,class:"text-muted-foreground text-center"},Hr={class:"mb-10 mt-6 text-xs"},Or={class:"text-foreground text-sm font-medium"},Dr={key:1,class:"text-muted-foreground text-center"},Wr={class:"my-10 text-xs"},Rr={class:"w-full"},Nr={key:0,class:"text-muted-foreground mb-2 text-xs"},Fr=["data-index","data-search-item"],Kr={class:"flex-1"},jr=["onClick"],Gr=L({name:"SearchPanel",__name:"search-panel",props:{keyword:{default:""},menus:{default:()=>[]}},emits:["close"],setup(l,{emit:a}){const t=l,o=a,s=ct(),n=kn(`__search-history-${location.hostname}__`,[]),r=Y(-1),i=Dt([]),d=Y([]),p=Ft(f,200);function f(V){if(V=V.trim(),!V){d.value=[];return}const K=P(V),z=[];Mn(i.value,$=>{var R;K.test((R=$.name)==null?void 0:R.toLowerCase())&&z.push($)}),d.value=z,z.length>0&&(r.value=0),r.value=0}function v(){const V=document.querySelector(`[data-search-item="${r.value}"]`);V&&V.scrollIntoView({block:"nearest"})}function h(){return X(this,null,function*(){if(d.value.length===0)return;const V=d.value,K=r.value;if(V.length===0||K<0)return;const z=V[K];z&&(n.value.push(z),H(),yield Ne(),la(z.path)?window.open(z.path,"_blank"):s.push({path:z.path,replace:!0}))})}function b(){d.value.length!==0&&(r.value--,r.value<0&&(r.value=d.value.length-1),v())}function C(){d.value.length!==0&&(r.value++,r.value>d.value.length-1&&(r.value=0),v())}function H(){d.value=[],o("close")}function O(V){var z;const K=(z=V.target)==null?void 0:z.dataset.index;r.value=Number(K)}function M(V){t.keyword?d.value.splice(V,1):n.value.splice(V,1),r.value=Math.max(r.value-1,0),v()}const W=new Set(["$","(",")","*","+",".","?","[","\\","]","^","{","|","}"]);function F(V){return W.has(V)?`\\${V}`:V}function P(V){const K=[...V].map(z=>F(z)).join(".*");return new RegExp(`.*${K}.*`)}return me(()=>t.keyword,V=>{V?p(V):d.value=[...n.value]}),Ye(()=>{i.value=sl(t.menus,V=>we(Q({},V),{name:g(V==null?void 0:V.name)})),n.value.length>0&&(d.value=n.value),It("Enter",h),It("ArrowUp",b),It("ArrowDown",C),It("Escape",H)}),(V,K)=>(u(),x(e(Bt),null,{default:c(()=>[B("div",Ir,[V.keyword&&d.value.length===0?(u(),k("div",Ur,[m(e(xs),{class:"mx-auto mt-4 size-12"}),B("p",Hr,[A(T(e(g)("ui.widgets.search.noResults"))+" ",1),B("span",Or,' "'+T(V.keyword)+'" ',1)])])):I("",!0),!V.keyword&&d.value.length===0?(u(),k("div",Dr,[B("p",Wr,T(e(g)("ui.widgets.search.noRecent")),1)])):I("",!0),Le(B("ul",Rr,[e(n).length>0&&!V.keyword?(u(),k("li",Nr,T(e(g)("ui.widgets.search.recent")),1)):I("",!0),(u(!0),k(J,null,de(e(Cn)(d.value,"path"),(z,$)=>(u(),k("li",{key:z.path,class:N([r.value===$?"active bg-primary text-primary-foreground":"","bg-accent flex-center group mb-3 w-full cursor-pointer rounded-lg px-4 py-4"]),"data-index":$,"data-search-item":$,onClick:h,onMouseenter:O},[m(e(De),{icon:z.icon,class:"mr-2 size-5 flex-shrink-0",fallback:""},null,8,["icon"]),B("span",Kr,T(z.name),1),B("div",{class:"flex-center dark:hover:bg-accent hover:text-primary-foreground rounded-full p-1 hover:scale-110",onClick:_e(R=>M($),["stop"])},[m(e(Et),{class:"size-4"})],8,jr)],42,Fr))),128))],512),[[Pe,d.value.length>0]])])]),_:1}))}}),qr={class:"flex items-center"},Yr=["placeholder"],Xr={class:"flex w-full justify-start text-xs"},Jr={class:"mr-2 flex items-center"},Zr={class:"mr-2 flex items-center"},Qr={class:"flex items-center"},ei={class:"text-muted-foreground group-hover:text-foreground hidden text-xs duration-300 md:block"},ti={key:0,class:"bg-background border-foreground/60 text-muted-foreground group-hover:text-foreground relative hidden rounded-sm rounded-r-xl px-1.5 py-1 text-xs leading-none group-hover:opacity-100 md:block"},ai={key:1},li=L({name:"GlobalSearch",__name:"global-search",props:{enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]}},setup(l){const a=l,t=Y(""),o=Y(),[s,n]=Vt({onCancel(){n.close()},onOpenChange(b){b||(t.value="")}}),r=n.useStore(b=>b.isOpen);function i(){n.close(),t.value=""}const d=rl(),p=_t()?d["ctrl+k"]:d["cmd+k"];Wt(p,()=>{a.enableShortcutKey&&n.open()}),Wt(r,()=>{Ne(()=>{var b;(b=o.value)==null||b.focus()})});const f=b=>{var C;((C=b.key)==null?void 0:C.toLowerCase())==="k"&&(b.metaKey||b.ctrlKey)&&b.preventDefault()},v=()=>{a.enableShortcutKey?window.addEventListener("keydown",f):window.removeEventListener("keydown",f)},h=()=>{r.value?n.close():n.open()};return me(()=>a.enableShortcutKey,v),Ye(()=>{v(),Lt(()=>{window.removeEventListener("keydown",f)})}),(b,C)=>(u(),k("div",null,[m(e(s),{"fullscreen-button":!1,class:"w-[600px]","header-class":"py-2 border-b"},{title:c(()=>[B("div",qr,[m(e(Ua),{class:"text-muted-foreground mr-2 size-4"}),Le(B("input",{ref_key:"searchInputRef",ref:o,"onUpdate:modelValue":C[0]||(C[0]=H=>t.value=H),placeholder:e(g)("ui.widgets.search.searchNavigate"),class:"ring-none placeholder:text-muted-foreground w-[80%] rounded-md border border-none bg-transparent p-2 pl-0 text-sm font-normal outline-none ring-0 ring-offset-transparent focus-visible:ring-transparent"},null,8,Yr),[[Ro,t.value]])])]),footer:c(()=>[B("div",Xr,[B("div",Jr,[m(e(cs),{class:"mr-1 size-3"}),A(" "+T(e(g)("ui.widgets.search.select")),1)]),B("div",Zr,[m(e(ss),{class:"mr-1 size-3"}),m(e(ts),{class:"mr-1 size-3"}),A(" "+T(e(g)("ui.widgets.search.navigate")),1)]),B("div",Qr,[m(e(Jn),{class:"mr-1 size-3"}),A(" "+T(e(g)("ui.widgets.search.close")),1)])])]),default:c(()=>[m(Gr,{keyword:t.value,menus:b.menus,onClose:i},null,8,["keyword","menus"])]),_:1}),B("div",{class:"md:bg-accent group flex h-8 cursor-pointer items-center gap-3 rounded-2xl border-none bg-none px-2 py-0.5 outline-none",onClick:C[1]||(C[1]=H=>h())},[m(e(Ua),{class:"text-muted-foreground group-hover:text-foreground size-4 group-hover:opacity-100"}),B("span",ei,T(e(g)("ui.widgets.search.title")),1),b.enableShortcutKey?(u(),k("span",ti,[A(T(e(_t)()?"Ctrl":"⌘")+" ",1),C[2]||(C[2]=B("kbd",null,"K",-1))])):(u(),k("span",ai))])]))}}),oi=["onKeydown"],ni={class:"w-full"},si={class:"ml-2 flex w-full flex-col items-center"},ri={class:"text-foreground my-6 flex items-center font-medium"},ii=L({name:"LockScreenModal",__name:"lock-screen-modal",props:{avatar:{default:""},text:{default:""}},emits:["submit"],setup(l,{emit:a}){const t=a,[o,{resetForm:s,validate:n,getValues:r}]=Za(mt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:y(()=>[{component:"VbenInputPassword",componentProps:{placeholder:g("ui.widgets.lockScreen.placeholder")},fieldName:"lockScreenPassword",formFieldProps:{validateOnBlur:!1},label:g("authentication.password"),rules:Qa().min(1,{message:g("ui.widgets.lockScreen.placeholder")})}]),showDefaultActions:!1})),[i]=Vt({onConfirm(){d()},onOpenChange(p){p&&s()}});function d(){return X(this,null,function*(){const{valid:p}=yield n(),f=yield r();p&&t("submit",f==null?void 0:f.lockScreenPassword)})}return(p,f)=>(u(),x(e(i),{footer:!1,"fullscreen-button":!1,title:e(g)("ui.widgets.lockScreen.title")},{default:c(()=>[B("div",{class:"mb-10 flex w-full flex-col items-center px-10",onKeydown:el(_e(d,["prevent"]),["enter"])},[B("div",ni,[B("div",si,[m(e($t),{src:p.avatar,class:"size-20","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["src"]),B("div",ri,T(p.text),1)]),m(e(o)),m(e(He),{class:"mt-1 w-full",onClick:d},{default:c(()=>[A(T(e(g)("ui.widgets.lockScreen.screenButton")),1)]),_:1})])],40,oi)]),_:1},8,["title"]))}}),di={class:"bg-background fixed z-[2000] size-full"},ui={class:"size-full"},ci={class:"flex h-full w-full items-center justify-center"},pi={class:"flex w-full justify-center gap-4 px-4 sm:gap-6 md:gap-8"},fi={class:"bg-accent relative flex h-[140px] w-[140px] items-center justify-center rounded-xl text-[36px] sm:h-[160px] sm:w-[160px] sm:text-[42px] md:h-[200px] md:w-[200px] md:text-[72px]"},mi={class:"absolute left-3 top-3 text-xs font-semibold sm:text-sm md:text-xl"},hi={class:"bg-accent flex h-[140px] w-[140px] items-center justify-center rounded-xl text-[36px] sm:h-[160px] sm:w-[160px] sm:text-[42px] md:h-[200px] md:w-[200px] md:text-[72px]"},bi=["onKeydown"],vi={class:"flex-col-center mb-10 w-[90%] max-w-[300px] px-4"},gi={class:"enter-x mb-2 w-full items-center"},yi={class:"enter-y absolute bottom-5 w-full text-center text-xl md:text-2xl xl:text-xl 2xl:text-3xl"},xi={key:0,class:"enter-x mb-2 text-2xl md:text-3xl"},wi={class:"text-base md:text-lg"},ki={class:"text-xl md:text-3xl"},Vc=L({name:"LockScreen",__name:"lock-screen",props:{avatar:{default:""}},emits:["toLogin"],setup(l){const{locale:a}=tl(),t=pt(),o=Sn(),s=Ut(o,"A"),n=Ut(o,"HH"),r=Ut(o,"mm"),i=Ut(o,"YYYY-MM-DD dddd",{locales:a.value}),d=Y(!1),{lockScreenPassword:p}=al(t),[f,{form:v,validate:h}]=Za(mt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:y(()=>[{component:"VbenInputPassword",componentProps:{placeholder:g("ui.widgets.lockScreen.placeholder")},fieldName:"password",label:g("authentication.password"),rules:Qa().min(1,{message:g("authentication.passwordTip")})}]),showDefaultActions:!1})),b=y(()=>{var O;return(p==null?void 0:p.value)===((O=v==null?void 0:v.values)==null?void 0:O.password)});function C(){return X(this,null,function*(){const{valid:O}=yield h();O&&(b.value?t.unlockScreen():v.setFieldError("password",g("authentication.passwordErrorTip")))})}function H(){d.value=!d.value}return qa(),(O,M)=>(u(),k("div",di,[m(dt,{name:"slide-left"},{default:c(()=>[Le(B("div",ui,[B("div",{class:"flex-col-center text-foreground/80 hover:text-foreground group fixed left-1/2 top-6 z-[2001] -translate-x-1/2 cursor-pointer text-xl font-semibold",onClick:H},[m(e(ml),{class:"size-5 transition-all duration-300 group-hover:scale-125"}),B("span",null,T(e(g)("ui.widgets.lockScreen.unlock")),1)]),B("div",ci,[B("div",pi,[B("div",fi,[B("span",mi,T(e(s)),1),A(" "+T(e(n)),1)]),B("div",hi,T(e(r)),1)])])],512),[[Pe,!d.value]])]),_:1}),m(dt,{name:"slide-right"},{default:c(()=>[d.value?(u(),k("div",{key:0,class:"flex-center size-full",onKeydown:el(_e(C,["prevent"]),["enter"])},[B("div",vi,[m(e($t),{src:O.avatar,class:"enter-x mb-6 size-20"},null,8,["src"]),B("div",gi,[m(e(f))]),m(e(He),{class:"enter-x w-full",onClick:C},{default:c(()=>[A(T(e(g)("ui.widgets.lockScreen.entry")),1)]),_:1}),m(e(He),{class:"enter-x my-2 w-full",variant:"ghost",onClick:M[0]||(M[0]=W=>O.$emit("toLogin"))},{default:c(()=>[A(T(e(g)("ui.widgets.lockScreen.backToLogin")),1)]),_:1}),m(e(He),{class:"enter-x mr-2 w-full",variant:"ghost",onClick:H},{default:c(()=>[A(T(e(g)("common.back")),1)]),_:1})])],40,bi)):I("",!0)]),_:1}),B("div",yi,[d.value?(u(),k("div",xi,[A(T(e(n))+":"+T(e(r))+" ",1),B("span",wi,T(e(s)),1)])):I("",!0),B("div",ki,T(e(i)),1)])]))}}),Ci={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Mi={class:"relative"},Si={class:"flex items-center justify-between p-4 py-3"},Ti={class:"text-foreground"},_i={class:"!flex max-h-[360px] w-full flex-col"},$i=["onClick"],Bi={key:0,class:"bg-primary absolute right-2 top-2 h-2 w-2 rounded"},Vi={class:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full"},Li=["src"],Ei={class:"flex flex-col gap-1 leading-none"},zi={class:"font-semibold"},Pi={class:"text-muted-foreground my-1 line-clamp-2 text-xs"},Ai={class:"text-muted-foreground line-clamp-2 text-xs"},Ii={key:1,class:"flex-center text-muted-foreground min-h-[150px] w-full"},Ui={class:"border-border flex items-center justify-between border-t px-4 py-3"},Hi=L({name:"NotificationPopup",__name:"notification",props:{dot:{type:Boolean,default:!1},notifications:{default:()=>[]}},emits:["clear","makeAll","read","viewAll"],setup(l,{emit:a}){const t=a,[o,s]=Tn();function n(){o.value=!1}function r(){t("viewAll"),n()}function i(){t("makeAll")}function d(){t("clear")}function p(f){t("read",f)}return(f,v)=>(u(),x(e(No),{open:e(o),"onUpdate:open":v[1]||(v[1]=h=>da(o)?o.value=h:null),"content-class":"relative right-2 w-[360px] p-0"},{trigger:c(()=>[B("div",{class:"flex-center mr-2 h-full",onClick:v[0]||(v[0]=_e(h=>e(s)(),["stop"]))},[m(e(nt),{class:"bell-button text-foreground relative"},{default:c(()=>[f.dot?(u(),k("span",Ci)):I("",!0),m(e(rs),{class:"size-4"})]),_:1})])]),default:c(()=>[B("div",Mi,[B("div",Si,[B("div",Ti,T(e(g)("ui.widgets.notifications")),1),m(e(nt),{disabled:f.notifications.length<=0,tooltip:e(g)("ui.widgets.markAllAsRead"),onClick:i},{default:c(()=>[m(e(bs),{class:"size-4"})]),_:1},8,["disabled","tooltip"])]),f.notifications.length>0?(u(),x(e(Bt),{key:0},{default:c(()=>[B("ul",_i,[(u(!0),k(J,null,de(f.notifications,h=>(u(),k("li",{key:h.title,class:"hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3",onClick:b=>p(h)},[h.isRead?I("",!0):(u(),k("span",Bi)),B("span",Vi,[B("img",{src:h.avatar,class:"aspect-square h-full w-full object-cover",role:"img"},null,8,Li)]),B("div",Ei,[B("p",zi,T(h.title),1),B("p",Pi,T(h.message),1),B("p",Ai,T(h.date),1)])],8,$i))),128))])]),_:1})):(u(),k("div",Ii,T(e(g)("common.noData")),1)),B("div",Ui,[m(e(He),{disabled:f.notifications.length<=0,size:"sm",variant:"ghost",onClick:d},{default:c(()=>[A(T(e(g)("ui.widgets.clearNotifications")),1)]),_:1},8,["disabled"]),m(e(He),{size:"sm",onClick:r},{default:c(()=>[A(T(e(g)("ui.widgets.viewAll")),1)]),_:1})])])]),_:1},8,["open"]))}}),Lc=$e(Hi,[["__scopeId","data-v-d7a4acd4"]]),Oi={class:"flex flex-col py-4"},Di={class:"mb-3 font-semibold leading-none tracking-tight"},Te=L({name:"PreferenceBlock",__name:"block",props:{title:{default:""}},setup(l){return(a,t)=>(u(),k("div",Oi,[B("h3",Di,T(a.title),1),E(a.$slots,"default")]))}}),Wi={class:"flex items-center text-sm"},Ri={key:0,class:"ml-auto mr-2 text-xs opacity-60"},le=L({name:"PreferenceSwitchItem",__name:"switch-item",props:ye({disabled:{type:Boolean,default:!1},tip:{default:""}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=w(l,"modelValue"),t=Ke();function o(){a.value=!a.value}return(s,n)=>(u(),k("div",{class:N([{"pointer-events-none opacity-50":s.disabled},"hover:bg-accent my-1 flex w-full items-center justify-between rounded-md px-2 py-2.5"]),onClick:o},[B("span",Wi,[E(s.$slots,"default"),e(t).tip||s.tip?(u(),x(e(ft),{key:0,side:"bottom"},{trigger:c(()=>[m(e(gt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(s.$slots,"tip",{},()=>[s.tip?(u(!0),k(J,{key:0},de(s.tip.split(`
`),(r,i)=>(u(),k("p",{key:i},T(r),1))),128)):I("",!0)])]),_:3})):I("",!0)]),s.$slots.shortcut?(u(),k("span",Ri,[E(s.$slots,"shortcut")])):I("",!0),m(e(Gs),{checked:a.value,"onUpdate:checked":n[0]||(n[0]=r=>a.value=r),onClick:n[1]||(n[1]=_e(()=>{},["stop"]))},null,8,["checked"])],2))}}),Ni={key:0,class:"mb-2 mt-3 flex justify-between gap-3 px-2"},Fi=["onClick"],Ki=L({name:"PreferenceAnimation",__name:"animation",props:{transitionProgress:{type:Boolean,default:!1},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{}},emits:["update:transitionProgress","update:transitionName","update:transitionEnable","update:transitionLoading"],setup(l){const a=w(l,"transitionProgress"),t=w(l,"transitionName"),o=w(l,"transitionEnable"),s=w(l,"transitionLoading"),n=["fade","fade-slide","fade-up","fade-down"];function r(i){t.value=i}return(i,d)=>(u(),k(J,null,[m(le,{modelValue:a.value,"onUpdate:modelValue":d[0]||(d[0]=p=>a.value=p)},{default:c(()=>[A(T(e(g)("preferences.animation.progress")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:s.value,"onUpdate:modelValue":d[1]||(d[1]=p=>s.value=p)},{default:c(()=>[A(T(e(g)("preferences.animation.loading")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:o.value,"onUpdate:modelValue":d[2]||(d[2]=p=>o.value=p)},{default:c(()=>[A(T(e(g)("preferences.animation.transition")),1)]),_:1},8,["modelValue"]),o.value?(u(),k("div",Ni,[(u(),k(J,null,de(n,p=>B("div",{key:p,class:N([{"outline-box-active":t.value===p},"outline-box p-2"]),onClick:f=>r(p)},[B("div",{class:N([`${p}-slow`,"bg-accent h-10 w-12 rounded-md"])},null,2)],10,Fi)),64))])):I("",!0)],64))}}),ji={class:"flex items-center text-sm"},qt=L({name:"PreferenceSelectItem",__name:"select-item",props:ye({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=w(l,"modelValue"),t=Ke();return(o,s)=>(u(),k("div",{class:N([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[B("span",ji,[E(o.$slots,"default"),e(t).tip?(u(),x(e(ft),{key:0,side:"bottom"},{trigger:c(()=>[m(e(gt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(o.$slots,"tip")]),_:3})):I("",!0)]),m(e(Fo),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n)},{default:c(()=>[m(e(Ko),{class:"h-8 w-[165px]"},{default:c(()=>[m(e(jo),{placeholder:o.placeholder},null,8,["placeholder"])]),_:1}),m(e(Go),null,{default:c(()=>[(u(!0),k(J,null,de(o.items,n=>(u(),x(e(qo),{key:n.value,value:n.value},{default:c(()=>[A(T(n.label),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])],2))}}),Gi=L({name:"PreferenceGeneralConfig",__name:"general",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{}},emits:["update:appLocale","update:appDynamicTitle","update:appWatermark","update:appEnableCheckUpdates"],setup(l){const a=w(l,"appLocale"),t=w(l,"appDynamicTitle"),o=w(l,"appWatermark"),s=w(l,"appEnableCheckUpdates");return(n,r)=>(u(),k(J,null,[m(qt,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=i=>a.value=i),items:e(Yo)},{default:c(()=>[A(T(e(g)("preferences.language")),1)]),_:1},8,["modelValue","items"]),m(le,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=i=>t.value=i)},{default:c(()=>[A(T(e(g)("preferences.dynamicTitle")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:o.value,"onUpdate:modelValue":r[2]||(r[2]=i=>o.value=i)},{default:c(()=>[A(T(e(g)("preferences.watermark")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:s.value,"onUpdate:modelValue":r[3]||(r[3]=i=>s.value=i)},{default:c(()=>[A(T(e(g)("preferences.checkUpdates")),1)]),_:1},8,["modelValue"])],64))}}),qi={class:"text-sm"},ha=L({name:"PreferenceToggleItem",__name:"toggle-item",props:ye({disabled:{type:Boolean,default:!1},items:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=w(l,"modelValue");return(t,o)=>(u(),k("div",{class:N([{"pointer-events-none opacity-50":t.disabled},"hover:bg-accent flex w-full items-center justify-between rounded-md px-2 py-2"]),disabled:""},[B("span",qi,[E(t.$slots,"default")]),m(e(ul),{modelValue:a.value,"onUpdate:modelValue":o[0]||(o[0]=s=>a.value=s),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(!0),k(J,null,de(t.items,s=>(u(),x(e(cl),{key:s.value,value:s.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 rounded-sm"},{default:c(()=>[A(T(s.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])],2))}}),Yi=L({name:"PreferenceBreadcrumbConfig",__name:"breadcrumb",props:ye({disabled:{type:Boolean}},{breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{}}),emits:["update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbStyleType","update:breadcrumbShowHome","update:breadcrumbHideOnlyOne"],setup(l){const a=l,t=w(l,"breadcrumbEnable"),o=w(l,"breadcrumbShowIcon"),s=w(l,"breadcrumbStyleType"),n=w(l,"breadcrumbShowHome"),r=w(l,"breadcrumbHideOnlyOne"),i=[{label:g("preferences.normal"),value:"normal"},{label:g("preferences.breadcrumb.background"),value:"background"}],d=y(()=>!t.value||a.disabled);return(p,f)=>(u(),k(J,null,[m(le,{modelValue:t.value,"onUpdate:modelValue":f[0]||(f[0]=v=>t.value=v),disabled:p.disabled},{default:c(()=>[A(T(e(g)("preferences.breadcrumb.enable")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:r.value,"onUpdate:modelValue":f[1]||(f[1]=v=>r.value=v),disabled:d.value},{default:c(()=>[A(T(e(g)("preferences.breadcrumb.hideOnlyOne")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:o.value,"onUpdate:modelValue":f[2]||(f[2]=v=>o.value=v),disabled:d.value},{default:c(()=>[A(T(e(g)("preferences.breadcrumb.icon")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:n.value,"onUpdate:modelValue":f[3]||(f[3]=v=>n.value=v),disabled:d.value||!o.value},{default:c(()=>[A(T(e(g)("preferences.breadcrumb.home")),1)]),_:1},8,["modelValue","disabled"]),m(ha,{modelValue:s.value,"onUpdate:modelValue":f[4]||(f[4]=v=>s.value=v),disabled:d.value,items:i},{default:c(()=>[A(T(e(g)("preferences.breadcrumb.style")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Xi={},Ji={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Zi(l,a){return u(),k("svg",Ji,a[0]||(a[0]=[ht('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="53.60438" x="43.484" y="13.66705"></rect><path id="svg_14" d="m3.43932,15.53192c0,-1.08676 1.03344,-2 2.26323,-2l30.33036,0c1.22979,0 2.26323,0.91324 2.26323,2l0,17.24865c0,1.08676 -1.03344,2 -2.26323,2l-30.33036,0c-1.22979,0 -2.26323,-0.91324 -2.26323,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="95.02528" x="3.30419" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const Cl=$e(Xi,[["render",Zi]]),Qi={},ed={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function td(l,a){return u(),k("svg",ed,a[0]||(a[0]=[ht('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="41.98275" x="45.37589" y="13.53192"></rect><path id="svg_14" d="m16.4123,15.53192c0,-1.08676 0.74096,-2 1.62271,-2l21.74653,0c0.88175,0 1.62271,0.91324 1.62271,2l0,17.24865c0,1.08676 -0.74096,2 -1.62271,2l-21.74653,0c-0.88175,0 -1.62271,-0.91324 -1.62271,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="71.10636" x="16.54743" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const ad=$e(Qi,[["render",td]]),ld={},od={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function nd(l,a){return u(),k("svg",od,a[0]||(a[0]=[B("g",null,[B("path",{id:"svg_1",d:"m0.13514,4.13514c0,-2.17352 1.82648,-4 4,-4l96,0c2.17352,0 4,1.82648 4,4l0,58c0,2.17352 -1.82648,4 -4,4l-96,0c-2.17352,0 -4,-1.82648 -4,-4l0,-58z",fill:"currentColor","fill-opacity":"0.02",opacity:"undefined",stroke:"null"}),B("rect",{id:"svg_13",fill:"currentColor","fill-opacity":"0.08",height:"26.57155",rx:"2",stroke:"null",width:"53.18333",x:"45.79979",y:"3.77232"}),B("path",{id:"svg_14",d:"m4.28142,5.96169c0,-1.37748 1.06465,-2.53502 2.33158,-2.53502l31.2463,0c1.26693,0 2.33158,1.15754 2.33158,2.53502l0,21.86282c0,1.37748 -1.06465,2.53502 -2.33158,2.53502l-31.2463,0c-1.26693,0 -2.33158,-1.15754 -2.33158,-2.53502l0,-21.86282z",fill:"currentColor","fill-opacity":"0.08",opacity:"undefined",stroke:"null"}),B("rect",{id:"svg_15",fill:"currentColor","fill-opacity":"0.08",height:"25.02247",rx:"2",stroke:"null",width:"94.39371",x:"4.56735",y:"34.92584"})],-1)]))}const sd=$e(ld,[["render",nd]]),rd={},id={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function dd(l,a){return u(),k("svg",id,a[0]||(a[0]=[ht('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="35.14924" y="4.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="47.25735" y="4.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="59.23033" y="4.07319"></rect></g>',1)]))}const ud=$e(rd,[["render",dd]]),cd={},pd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function fd(l,a){return u(),k("svg",pd,a[0]||(a[0]=[ht('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#b2b2b2" height="1.689" rx="1.395" stroke="null" width="6.52486" x="10.08168" y="3.50832"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="2.89362"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="2.89362"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="5.13843" rx="2" stroke="null" width="5.78397" x="1.5327" y="1.081"></rect><rect id="svg_5" fill="hsl(var(--primary))" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path></g>',1)]))}const md=$e(cd,[["render",fd]]),hd={},bd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function vd(l,a){return u(),k("svg",bd,a[0]||(a[0]=[ht('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect><rect id="svg_5" fill="currentColor" fill-opacity="0.08" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path></g>',1)]))}const gd=$e(hd,[["render",vd]]),yd={},xd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function wd(l,a){return u(),k("svg",xd,a[0]||(a[0]=[ht('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect></g>',1)]))}const kd=$e(yd,[["render",wd]]),Cd={},Md={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Sd(l,a){return u(),k("svg",Md,a[0]||(a[0]=[ht('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104"></rect><path id="svg_2" d="m-3.37838,3.61916a4.4919,4.02457 0 0 1 4.4919,-4.02457l26.35848,0l0,66.40541l-26.35848,0a4.4919,4.02457 0 0 1 -4.4919,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" width="17.66" x="4.906" y="23.884"></rect><rect id="svg_4" fill="#ffffff" height="9.706" rx="2" width="9.811" x="8.83" y="5.881"></rect><path id="svg_5" d="m4.906,35.833c0,-0.75801 0.63699,-1.395 1.395,-1.395l14.87,0c0.75801,0 1.395,0.63699 1.395,1.395l0,-0.001c0,0.75801 -0.63699,1.395 -1.395,1.395l-14.87,0c-0.75801,0 -1.395,-0.63699 -1.395,-1.395l0,0.001z" fill="#ffffff" opacity="undefined"></path><rect id="svg_6" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="44.992"></rect><rect id="svg_7" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="55.546"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="73.53879" x="28.97986" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="32.039" y="3.89903"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="3.49362"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="45.63141" x="56.05157" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="22.82978" x="29.38527" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="72.45771" x="28.97986" y="39.48203"></rect></g>',1)]))}const Td=$e(Cd,[["render",Sd]]),_d=Cl,$d={class:"flex w-full gap-5"},Bd=["onClick"],Vd={class:"text-muted-foreground mt-2 text-center text-xs"},Ld=L({name:"PreferenceLayoutContent",__name:"content",props:{modelValue:{default:"wide"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const a=w(l,"modelValue"),t={compact:ad,wide:_d},o=y(()=>[{name:g("preferences.wide"),type:"wide"},{name:g("preferences.compact"),type:"compact"}]);function s(n){return n===a.value?["outline-box-active"]:[]}return(n,r)=>(u(),k("div",$d,[(u(!0),k(J,null,de(o.value,i=>(u(),k("div",{key:i.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>a.value=i.type},[B("div",{class:N([s(i.type),"outline-box flex-center"])},[(u(),x(ze(t[i.type])))],2),B("div",Vd,T(i.name),1)],8,Bd))),128))]))}}),Ed={class:"flex items-center text-sm"},Ct=L({name:"PreferenceSelectItem",__name:"input-item",props:ye({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=w(l,"modelValue"),t=Ke();return(o,s)=>(u(),k("div",{class:N([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[B("span",Ed,[E(o.$slots,"default"),e(t).tip?(u(),x(e(ft),{key:0,side:"bottom"},{trigger:c(()=>[m(e(gt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(o.$slots,"tip")]),_:3})):I("",!0)]),m(e(Xo),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n),class:"h-8 w-[165px]"},null,8,["modelValue"])],2))}}),zd=L({__name:"copyright",props:ye({disabled:{type:Boolean}},{copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{}}),emits:["update:copyrightEnable","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:copyrightCompanyName","update:copyrightCompanySiteLink"],setup(l){const a=l,t=w(l,"copyrightEnable"),o=w(l,"copyrightDate"),s=w(l,"copyrightIcp"),n=w(l,"copyrightIcpLink"),r=w(l,"copyrightCompanyName"),i=w(l,"copyrightCompanySiteLink"),d=y(()=>a.disabled||!t.value);return(p,f)=>(u(),k(J,null,[m(le,{modelValue:t.value,"onUpdate:modelValue":f[0]||(f[0]=v=>t.value=v),disabled:p.disabled},{default:c(()=>[A(T(e(g)("preferences.copyright.enable")),1)]),_:1},8,["modelValue","disabled"]),m(Ct,{modelValue:r.value,"onUpdate:modelValue":f[1]||(f[1]=v=>r.value=v),disabled:d.value},{default:c(()=>[A(T(e(g)("preferences.copyright.companyName")),1)]),_:1},8,["modelValue","disabled"]),m(Ct,{modelValue:i.value,"onUpdate:modelValue":f[2]||(f[2]=v=>i.value=v),disabled:d.value},{default:c(()=>[A(T(e(g)("preferences.copyright.companySiteLink")),1)]),_:1},8,["modelValue","disabled"]),m(Ct,{modelValue:o.value,"onUpdate:modelValue":f[3]||(f[3]=v=>o.value=v),disabled:d.value},{default:c(()=>[A(T(e(g)("preferences.copyright.date")),1)]),_:1},8,["modelValue","disabled"]),m(Ct,{modelValue:s.value,"onUpdate:modelValue":f[4]||(f[4]=v=>s.value=v),disabled:d.value},{default:c(()=>[A(T(e(g)("preferences.copyright.icp")),1)]),_:1},8,["modelValue","disabled"]),m(Ct,{modelValue:n.value,"onUpdate:modelValue":f[5]||(f[5]=v=>n.value=v),disabled:d.value},{default:c(()=>[A(T(e(g)("preferences.copyright.icpLink")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Pd=L({__name:"footer",props:{footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{}},emits:["update:footerEnable","update:footerFixed"],setup(l){const a=w(l,"footerEnable"),t=w(l,"footerFixed");return(o,s)=>(u(),k(J,null,[m(le,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n)},{default:c(()=>[A(T(e(g)("preferences.footer.visible")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n),disabled:!a.value},{default:c(()=>[A(T(e(g)("preferences.footer.fixed")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Ad=L({__name:"header",props:ye({disabled:{type:Boolean}},{headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{}}),emits:["update:headerEnable","update:headerMode","update:headerMenuAlign"],setup(l){const a=w(l,"headerEnable"),t=w(l,"headerMode"),o=w(l,"headerMenuAlign"),s=[{label:g("preferences.header.modeStatic"),value:"static"},{label:g("preferences.header.modeFixed"),value:"fixed"},{label:g("preferences.header.modeAuto"),value:"auto"},{label:g("preferences.header.modeAutoScroll"),value:"auto-scroll"}],n=[{label:g("preferences.header.menuAlignStart"),value:"start"},{label:g("preferences.header.menuAlignCenter"),value:"center"},{label:g("preferences.header.menuAlignEnd"),value:"end"}];return(r,i)=>(u(),k(J,null,[m(le,{modelValue:a.value,"onUpdate:modelValue":i[0]||(i[0]=d=>a.value=d),disabled:r.disabled},{default:c(()=>[A(T(e(g)("preferences.header.visible")),1)]),_:1},8,["modelValue","disabled"]),m(qt,{modelValue:t.value,"onUpdate:modelValue":i[1]||(i[1]=d=>t.value=d),disabled:!a.value,items:s},{default:c(()=>[A(T(e(g)("preferences.mode")),1)]),_:1},8,["modelValue","disabled"]),m(ha,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=d=>o.value=d),disabled:!a.value,items:n},{default:c(()=>[A(T(e(g)("preferences.header.menuAlign")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Id={class:"flex w-full flex-wrap gap-5"},Ud=["onClick"],Hd={class:"text-muted-foreground flex-center hover:text-foreground mt-2 text-center text-xs"},Od=L({name:"PreferenceLayout",__name:"layout",props:{modelValue:{default:"sidebar-nav"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const a=w(l,"modelValue"),t={"full-content":sd,"header-nav":Cl,"mixed-nav":gd,"sidebar-mixed-nav":kd,"sidebar-nav":Td,"header-mixed-nav":ud,"header-sidebar-nav":md},o=y(()=>[{name:g("preferences.vertical"),tip:g("preferences.verticalTip"),type:"sidebar-nav"},{name:g("preferences.twoColumn"),tip:g("preferences.twoColumnTip"),type:"sidebar-mixed-nav"},{name:g("preferences.horizontal"),tip:g("preferences.horizontalTip"),type:"header-nav"},{name:g("preferences.headerSidebarNav"),tip:g("preferences.headerSidebarNavTip"),type:"header-sidebar-nav"},{name:g("preferences.mixedMenu"),tip:g("preferences.mixedMenuTip"),type:"mixed-nav"},{name:g("preferences.headerTwoColumn"),tip:g("preferences.headerTwoColumnTip"),type:"header-mixed-nav"},{name:g("preferences.fullContent"),tip:g("preferences.fullContentTip"),type:"full-content"}]);function s(n){return n===a.value?["outline-box-active"]:[]}return(n,r)=>(u(),k("div",Id,[(u(!0),k(J,null,de(o.value,i=>(u(),k("div",{key:i.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>a.value=i.type},[B("div",{class:N([s(i.type),"outline-box flex-center"])},[(u(),x(ze(t[i.type])))],2),B("div",Hd,[A(T(i.name)+" ",1),i.tip?(u(),x(e(ft),{key:0,side:"bottom"},{trigger:c(()=>[m(e(gt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[A(" "+T(i.tip),1)]),_:2},1024)):I("",!0)])],8,Ud))),128))]))}}),Dd=L({name:"PreferenceNavigationConfig",__name:"navigation",props:ye({disabled:{type:Boolean},disabledNavigationSplit:{type:Boolean}},{navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{}}),emits:["update:navigationStyleType","update:navigationSplit","update:navigationAccordion"],setup(l){const a=w(l,"navigationStyleType"),t=w(l,"navigationSplit"),o=w(l,"navigationAccordion"),s=[{label:g("preferences.rounded"),value:"rounded"},{label:g("preferences.plain"),value:"plain"}];return(n,r)=>(u(),k(J,null,[m(ha,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=i=>a.value=i),disabled:n.disabled,items:s},{default:c(()=>[A(T(e(g)("preferences.navigationMenu.style")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=i=>t.value=i),disabled:n.disabledNavigationSplit||n.disabled},{tip:c(()=>[A(T(e(g)("preferences.navigationMenu.splitTip")),1)]),default:c(()=>[A(T(e(g)("preferences.navigationMenu.split"))+" ",1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:o.value,"onUpdate:modelValue":r[2]||(r[2]=i=>o.value=i),disabled:n.disabled},{default:c(()=>[A(T(e(g)("preferences.navigationMenu.accordion")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Wd={class:"flex items-center text-sm"},Rd=L({name:"PreferenceCheckboxItem",__name:"checkbox-item",props:ye({disabled:{type:Boolean,default:!1},items:{default:()=>[]},multiple:{type:Boolean,default:!1},onBtnClick:{type:Function,default:()=>{}},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=w(l,"modelValue"),t=Ke();return(o,s)=>(u(),k("div",{class:N([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[B("span",Wd,[E(o.$slots,"default"),e(t).tip?(u(),x(e(ft),{key:0,side:"bottom"},{trigger:c(()=>[m(e(gt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(o.$slots,"tip")]),_:3})):I("",!0)]),m(e(Zs),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n),class:"h-8 w-[165px]",options:o.items,disabled:o.disabled,multiple:o.multiple,onBtnClick:o.onBtnClick},null,8,["modelValue","options","disabled","multiple","onBtnClick"])],2))}}),Nd={class:"flex items-center text-sm"},Ml=L({name:"PreferenceSelectItem",__name:"number-field-item",props:ye({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""},tip:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=w(l,"modelValue"),t=Ke();return(o,s)=>(u(),k("div",{class:N([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[B("span",Nd,[E(o.$slots,"default"),e(t).tip||o.tip?(u(),x(e(ft),{key:0,side:"bottom"},{trigger:c(()=>[m(e(gt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(o.$slots,"tip",{},()=>[o.tip?(u(!0),k(J,{key:0},de(o.tip.split(`
`),(n,r)=>(u(),k("p",{key:r},T(n),1))),128)):I("",!0)])]),_:3})):I("",!0)]),m(e(Ws),ve({modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n)},o.$attrs,{class:"w-[165px]"}),{default:c(()=>[m(e(Rs),null,{default:c(()=>[m(e(Ns)),m(e(Ks)),m(e(Fs))]),_:1})]),_:1},16,["modelValue"])],2))}}),Fd=L({__name:"sidebar",props:ye({currentLayout:{},disabled:{type:Boolean}},{sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarButtons:{default:[]},sidebarButtonsModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{}}),emits:["update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarCollapsed","update:sidebarExpandOnHover","update:sidebarButtons","update:sidebarCollapsedButton","update:sidebarFixedButton"],setup(l){const a=w(l,"sidebarEnable"),t=w(l,"sidebarWidth"),o=w(l,"sidebarCollapsedShowTitle"),s=w(l,"sidebarAutoActivateChild"),n=w(l,"sidebarCollapsed"),r=w(l,"sidebarExpandOnHover"),i=w(l,"sidebarButtons"),d=w(l,"sidebarCollapsedButton"),p=w(l,"sidebarFixedButton");Ye(()=>{d.value&&!i.value.includes("collapsed")&&i.value.push("collapsed"),p.value&&!i.value.includes("fixed")&&i.value.push("fixed")});const f=()=>{d.value=!!i.value.includes("collapsed"),p.value=!!i.value.includes("fixed")};return(v,h)=>(u(),k(J,null,[m(le,{modelValue:a.value,"onUpdate:modelValue":h[0]||(h[0]=b=>a.value=b),disabled:v.disabled},{default:c(()=>[A(T(e(g)("preferences.sidebar.visible")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:n.value,"onUpdate:modelValue":h[1]||(h[1]=b=>n.value=b),disabled:!a.value||v.disabled},{default:c(()=>[A(T(e(g)("preferences.sidebar.collapsed")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:r.value,"onUpdate:modelValue":h[2]||(h[2]=b=>r.value=b),disabled:!a.value||v.disabled||!n.value,tip:e(g)("preferences.sidebar.expandOnHoverTip")},{default:c(()=>[A(T(e(g)("preferences.sidebar.expandOnHover")),1)]),_:1},8,["modelValue","disabled","tip"]),m(le,{modelValue:o.value,"onUpdate:modelValue":h[3]||(h[3]=b=>o.value=b),disabled:!a.value||v.disabled||!n.value},{default:c(()=>[A(T(e(g)("preferences.sidebar.collapsedShowTitle")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:s.value,"onUpdate:modelValue":h[4]||(h[4]=b=>s.value=b),disabled:!a.value||!["sidebar-mixed-nav","mixed-nav","header-mixed-nav"].includes(v.currentLayout)||v.disabled,tip:e(g)("preferences.sidebar.autoActivateChildTip")},{default:c(()=>[A(T(e(g)("preferences.sidebar.autoActivateChild")),1)]),_:1},8,["modelValue","disabled","tip"]),m(Rd,{items:[{label:e(g)("preferences.sidebar.buttonCollapsed"),value:"collapsed"},{label:e(g)("preferences.sidebar.buttonFixed"),value:"fixed"}],multiple:"",modelValue:i.value,"onUpdate:modelValue":h[5]||(h[5]=b=>i.value=b),"on-btn-click":f},{default:c(()=>[A(T(e(g)("preferences.sidebar.buttons")),1)]),_:1},8,["items","modelValue"]),m(Ml,{modelValue:t.value,"onUpdate:modelValue":h[6]||(h[6]=b=>t.value=b),disabled:!a.value||v.disabled,max:320,min:160,step:10},{default:c(()=>[A(T(e(g)("preferences.sidebar.width")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Kd=L({name:"PreferenceTabsConfig",__name:"tabbar",props:ye({disabled:{type:Boolean}},{tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{}}),emits:["update:tabbarEnable","update:tabbarShowIcon","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarMaxCount","update:tabbarMiddleClickToClose"],setup(l){const a=w(l,"tabbarEnable"),t=w(l,"tabbarShowIcon"),o=w(l,"tabbarPersist"),s=w(l,"tabbarDraggable"),n=w(l,"tabbarWheelable"),r=w(l,"tabbarStyleType"),i=w(l,"tabbarShowMore"),d=w(l,"tabbarShowMaximize"),p=w(l,"tabbarMaxCount"),f=w(l,"tabbarMiddleClickToClose"),v=y(()=>[{label:g("preferences.tabbar.styleType.chrome"),value:"chrome"},{label:g("preferences.tabbar.styleType.plain"),value:"plain"},{label:g("preferences.tabbar.styleType.card"),value:"card"},{label:g("preferences.tabbar.styleType.brisk"),value:"brisk"}]);return(h,b)=>(u(),k(J,null,[m(le,{modelValue:a.value,"onUpdate:modelValue":b[0]||(b[0]=C=>a.value=C),disabled:h.disabled},{default:c(()=>[A(T(e(g)("preferences.tabbar.enable")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:o.value,"onUpdate:modelValue":b[1]||(b[1]=C=>o.value=C),disabled:!a.value},{default:c(()=>[A(T(e(g)("preferences.tabbar.persist")),1)]),_:1},8,["modelValue","disabled"]),m(Ml,{modelValue:p.value,"onUpdate:modelValue":b[2]||(b[2]=C=>p.value=C),disabled:!a.value,max:30,min:0,step:5,tip:e(g)("preferences.tabbar.maxCountTip")},{default:c(()=>[A(T(e(g)("preferences.tabbar.maxCount")),1)]),_:1},8,["modelValue","disabled","tip"]),m(le,{modelValue:s.value,"onUpdate:modelValue":b[3]||(b[3]=C=>s.value=C),disabled:!a.value},{default:c(()=>[A(T(e(g)("preferences.tabbar.draggable")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:n.value,"onUpdate:modelValue":b[4]||(b[4]=C=>n.value=C),disabled:!a.value,tip:e(g)("preferences.tabbar.wheelableTip")},{default:c(()=>[A(T(e(g)("preferences.tabbar.wheelable")),1)]),_:1},8,["modelValue","disabled","tip"]),m(le,{modelValue:f.value,"onUpdate:modelValue":b[5]||(b[5]=C=>f.value=C),disabled:!a.value},{default:c(()=>[A(T(e(g)("preferences.tabbar.middleClickClose")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:t.value,"onUpdate:modelValue":b[6]||(b[6]=C=>t.value=C),disabled:!a.value},{default:c(()=>[A(T(e(g)("preferences.tabbar.icon")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:i.value,"onUpdate:modelValue":b[7]||(b[7]=C=>i.value=C),disabled:!a.value},{default:c(()=>[A(T(e(g)("preferences.tabbar.showMore")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:d.value,"onUpdate:modelValue":b[8]||(b[8]=C=>d.value=C),disabled:!a.value},{default:c(()=>[A(T(e(g)("preferences.tabbar.showMaximize")),1)]),_:1},8,["modelValue","disabled"]),m(qt,{modelValue:r.value,"onUpdate:modelValue":b[9]||(b[9]=C=>r.value=C),items:v.value},{default:c(()=>[A(T(e(g)("preferences.tabbar.styleType.title")),1)]),_:1},8,["modelValue","items"])],64))}}),jd=L({name:"PreferenceInterfaceControl",__name:"widget",props:{widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:["update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:appPreferencesButtonPosition","update:widgetRefresh"],setup(l){const a=w(l,"widgetGlobalSearch"),t=w(l,"widgetFullscreen"),o=w(l,"widgetLanguageToggle"),s=w(l,"widgetNotification"),n=w(l,"widgetThemeToggle"),r=w(l,"widgetSidebarToggle"),i=w(l,"widgetLockScreen"),d=w(l,"appPreferencesButtonPosition"),p=w(l,"widgetRefresh"),f=y(()=>[{label:g("preferences.position.auto"),value:"auto"},{label:g("preferences.position.header"),value:"header"},{label:g("preferences.position.fixed"),value:"fixed"}]);return(v,h)=>(u(),k(J,null,[m(le,{modelValue:a.value,"onUpdate:modelValue":h[0]||(h[0]=b=>a.value=b)},{default:c(()=>[A(T(e(g)("preferences.widget.globalSearch")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:n.value,"onUpdate:modelValue":h[1]||(h[1]=b=>n.value=b)},{default:c(()=>[A(T(e(g)("preferences.widget.themeToggle")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:o.value,"onUpdate:modelValue":h[2]||(h[2]=b=>o.value=b)},{default:c(()=>[A(T(e(g)("preferences.widget.languageToggle")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:t.value,"onUpdate:modelValue":h[3]||(h[3]=b=>t.value=b)},{default:c(()=>[A(T(e(g)("preferences.widget.fullscreen")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:s.value,"onUpdate:modelValue":h[4]||(h[4]=b=>s.value=b)},{default:c(()=>[A(T(e(g)("preferences.widget.notification")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:i.value,"onUpdate:modelValue":h[5]||(h[5]=b=>i.value=b)},{default:c(()=>[A(T(e(g)("preferences.widget.lockScreen")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:r.value,"onUpdate:modelValue":h[6]||(h[6]=b=>r.value=b)},{default:c(()=>[A(T(e(g)("preferences.widget.sidebarToggle")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:p.value,"onUpdate:modelValue":h[7]||(h[7]=b=>p.value=b)},{default:c(()=>[A(T(e(g)("preferences.widget.refresh")),1)]),_:1},8,["modelValue"]),m(qt,{modelValue:d.value,"onUpdate:modelValue":h[8]||(h[8]=b=>d.value=b),items:f.value},{default:c(()=>[A(T(e(g)("preferences.position.title")),1)]),_:1},8,["modelValue","items"])],64))}}),Gd=L({name:"PreferenceGeneralConfig",__name:"global",props:{shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysLogout:{type:Boolean},shortcutKeysLogoutModifiers:{},shortcutKeysLockScreen:{type:Boolean},shortcutKeysLockScreenModifiers:{}},emits:["update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysLogout","update:shortcutKeysLockScreen"],setup(l){const a=w(l,"shortcutKeysEnable"),t=w(l,"shortcutKeysGlobalSearch"),o=w(l,"shortcutKeysLogout"),s=w(l,"shortcutKeysLockScreen"),n=y(()=>_t()?"Alt":"⌥");return(r,i)=>(u(),k(J,null,[m(le,{modelValue:a.value,"onUpdate:modelValue":i[0]||(i[0]=d=>a.value=d)},{default:c(()=>[A(T(e(g)("preferences.shortcutKeys.title")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:t.value,"onUpdate:modelValue":i[1]||(i[1]=d=>t.value=d),disabled:!a.value},{shortcut:c(()=>[A(T(e(_t)()?"Ctrl":"⌘")+" ",1),i[4]||(i[4]=B("kbd",null," K ",-1))]),default:c(()=>[A(T(e(g)("preferences.shortcutKeys.search"))+" ",1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=d=>o.value=d),disabled:!a.value},{shortcut:c(()=>[A(T(n.value)+" Q ",1)]),default:c(()=>[A(T(e(g)("preferences.shortcutKeys.logout"))+" ",1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:s.value,"onUpdate:modelValue":i[3]||(i[3]=d=>s.value=d),disabled:!a.value},{shortcut:c(()=>[A(T(n.value)+" L ",1)]),default:c(()=>[A(T(e(g)("ui.widgets.lockScreen.title"))+" ",1)]),_:1},8,["modelValue","disabled"])],64))}}),qd={class:"flex w-full flex-wrap justify-between"},Yd=["onClick"],Xd={class:"flex-center relative size-5 rounded-sm"},Jd=["value"],Zd={class:"text-muted-foreground my-2 text-center text-xs"},Qd=L({name:"PreferenceBuiltinTheme",__name:"builtin",props:ye({isDark:{type:Boolean}},{modelValue:{default:"default"},modelModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{}}),emits:["update:modelValue","update:themeColorPrimary"],setup(l){const a=l,t=Y(),o=w(l,"modelValue"),s=w(l,"themeColorPrimary"),n=Ft(h=>{s.value=h},300,!0,!0),r=y(()=>new _n(s.value||"").toHexString()),i=y(()=>[...$n]);function d(h){switch(h){case"custom":return g("preferences.theme.builtin.custom");case"deep-blue":return g("preferences.theme.builtin.deepBlue");case"deep-green":return g("preferences.theme.builtin.deepGreen");case"default":return g("preferences.theme.builtin.default");case"gray":return g("preferences.theme.builtin.gray");case"green":return g("preferences.theme.builtin.green");case"neutral":return g("preferences.theme.builtin.neutral");case"orange":return g("preferences.theme.builtin.orange");case"pink":return g("preferences.theme.builtin.pink");case"rose":return g("preferences.theme.builtin.rose");case"sky-blue":return g("preferences.theme.builtin.skyBlue");case"slate":return g("preferences.theme.builtin.slate");case"violet":return g("preferences.theme.builtin.violet");case"yellow":return g("preferences.theme.builtin.yellow");case"zinc":return g("preferences.theme.builtin.zinc")}}function p(h){o.value=h.type}function f(h){const b=h.target;n(Bn(b.value))}function v(){var h,b,C;(C=(b=(h=t.value)==null?void 0:h[0])==null?void 0:b.click)==null||C.call(b)}return me(()=>[o.value,a.isDark],([h,b])=>{const C=i.value.find(H=>H.type===h);if(C){const H=b&&C.darkPrimaryColor||C.primaryColor;s.value=H||C.color}}),(h,b)=>(u(),k("div",qd,[(u(!0),k(J,null,de(i.value,C=>(u(),k("div",{key:C.type,class:"flex cursor-pointer flex-col",onClick:H=>p(C)},[B("div",{class:N([{"outline-box-active":C.type===o.value},"outline-box flex-center group cursor-pointer"])},[C.type!=="custom"?(u(),k("div",{key:0,style:ce({backgroundColor:C.color}),class:"mx-10 my-2 size-5 rounded-md"},null,4)):(u(),k("div",{key:1,class:"size-full px-10 py-2",onClick:_e(v,["stop"])},[B("div",Xd,[m(e(ks),{class:"absolute z-10 size-5 opacity-60 group-hover:opacity-100"}),B("input",{ref_for:!0,ref_key:"colorInput",ref:t,value:r.value,class:"absolute inset-0 opacity-0",type:"color",onInput:f},null,40,Jd)])]))],2),B("div",Zd,T(d(C.type)),1)],8,Yd))),128))]))}}),eu=L({name:"PreferenceColorMode",__name:"color-mode",props:{appColorWeakMode:{type:Boolean,default:!1},appColorWeakModeModifiers:{},appColorGrayMode:{type:Boolean,default:!1},appColorGrayModeModifiers:{}},emits:["update:appColorWeakMode","update:appColorGrayMode"],setup(l){const a=w(l,"appColorWeakMode"),t=w(l,"appColorGrayMode");return(o,s)=>(u(),k(J,null,[m(le,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n)},{default:c(()=>[A(T(e(g)("preferences.theme.weakMode")),1)]),_:1},8,["modelValue"]),m(le,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n)},{default:c(()=>[A(T(e(g)("preferences.theme.grayMode")),1)]),_:1},8,["modelValue"])],64))}}),tu=L({name:"PreferenceColorMode",__name:"radius",props:{themeRadius:{default:"0.5"},themeRadiusModifiers:{}},emits:["update:themeRadius"],setup(l){const a=w(l,"themeRadius"),t=[{label:"0",value:"0"},{label:"0.25",value:"0.25"},{label:"0.5",value:"0.5"},{label:"0.75",value:"0.75"},{label:"1",value:"1"}];return(o,s)=>(u(),x(e(ul),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(),k(J,null,de(t,n=>m(e(cl),{key:n.value,value:n.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 w-16 rounded-sm"},{default:c(()=>[A(T(n.label),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]))}}),au={class:"flex w-full flex-wrap justify-between"},lu=["onClick"],ou={class:"text-muted-foreground mt-2 text-center text-xs"},nu=L({name:"PreferenceTheme",__name:"theme",props:{modelValue:{default:"auto"},modelModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{}},emits:["update:modelValue","update:themeSemiDarkSidebar","update:themeSemiDarkHeader"],setup(l){const a=w(l,"modelValue"),t=w(l,"themeSemiDarkSidebar"),o=w(l,"themeSemiDarkHeader"),s=[{icon:Wn,name:"light"},{icon:Rn,name:"dark"},{icon:Nn,name:"auto"}];function n(i){return i===a.value?["outline-box-active"]:[]}function r(i){switch(i){case"auto":return g("preferences.followSystem");case"dark":return g("preferences.theme.dark");case"light":return g("preferences.theme.light")}}return(i,d)=>(u(),k("div",au,[(u(),k(J,null,de(s,p=>B("div",{key:p.name,class:"flex cursor-pointer flex-col",onClick:f=>a.value=p.name},[B("div",{class:N([n(p.name),"outline-box flex-center py-4"])},[(u(),x(ze(p.icon),{class:"mx-9 size-5"}))],2),B("div",ou,T(r(p.name)),1)],8,lu)),64)),m(le,{modelValue:t.value,"onUpdate:modelValue":d[0]||(d[0]=p=>t.value=p),disabled:a.value==="dark",class:"mt-6"},{default:c(()=>[A(T(e(g)("preferences.theme.darkSidebar")),1)]),_:1},8,["modelValue","disabled"]),m(le,{modelValue:o.value,"onUpdate:modelValue":d[1]||(d[1]=p=>o.value=p),disabled:a.value==="dark"},{default:c(()=>[A(T(e(g)("preferences.theme.darkHeader")),1)]),_:1},8,["modelValue","disabled"])]))}}),su={class:"flex items-center"},ru={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},iu={class:"p-1"},du=L({__name:"preferences-drawer",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appLayout:{},appLayoutModifiers:{},appColorGrayMode:{type:Boolean},appColorGrayModeModifiers:{},appColorWeakMode:{type:Boolean},appColorWeakModeModifiers:{},appContentCompact:{},appContentCompactModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},transitionProgress:{type:Boolean},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{},themeBuiltinType:{},themeBuiltinTypeModifiers:{},themeMode:{},themeModeModifiers:{},themeRadius:{},themeRadiusModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{},sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{},headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{},breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{},tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{},navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{},footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{},copyrightSettingShow:{type:Boolean},copyrightSettingShowModifiers:{},copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysGlobalLogout:{type:Boolean},shortcutKeysGlobalLogoutModifiers:{},shortcutKeysGlobalLockScreen:{type:Boolean},shortcutKeysGlobalLockScreenModifiers:{},widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:ye(["clearPreferencesAndLogout"],["update:appLocale","update:appDynamicTitle","update:appLayout","update:appColorGrayMode","update:appColorWeakMode","update:appContentCompact","update:appWatermark","update:appEnableCheckUpdates","update:appPreferencesButtonPosition","update:transitionProgress","update:transitionName","update:transitionLoading","update:transitionEnable","update:themeColorPrimary","update:themeBuiltinType","update:themeMode","update:themeRadius","update:themeSemiDarkSidebar","update:themeSemiDarkHeader","update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsed","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarExpandOnHover","update:sidebarCollapsedButton","update:sidebarFixedButton","update:headerEnable","update:headerMode","update:headerMenuAlign","update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbShowHome","update:breadcrumbStyleType","update:breadcrumbHideOnlyOne","update:tabbarEnable","update:tabbarShowIcon","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarMaxCount","update:tabbarMiddleClickToClose","update:navigationStyleType","update:navigationSplit","update:navigationAccordion","update:footerEnable","update:footerFixed","update:copyrightSettingShow","update:copyrightEnable","update:copyrightCompanyName","update:copyrightCompanySiteLink","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysGlobalLogout","update:shortcutKeysGlobalLockScreen","update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:widgetRefresh"]),setup(l,{emit:a}){const t=a,o=Ya.getMessage(),s=w(l,"appLocale"),n=w(l,"appDynamicTitle"),r=w(l,"appLayout"),i=w(l,"appColorGrayMode"),d=w(l,"appColorWeakMode"),p=w(l,"appContentCompact"),f=w(l,"appWatermark"),v=w(l,"appEnableCheckUpdates"),h=w(l,"appPreferencesButtonPosition"),b=w(l,"transitionProgress"),C=w(l,"transitionName"),H=w(l,"transitionLoading"),O=w(l,"transitionEnable"),M=w(l,"themeColorPrimary"),W=w(l,"themeBuiltinType"),F=w(l,"themeMode"),P=w(l,"themeRadius"),V=w(l,"themeSemiDarkSidebar"),K=w(l,"themeSemiDarkHeader"),z=w(l,"sidebarEnable"),$=w(l,"sidebarWidth"),R=w(l,"sidebarCollapsed"),q=w(l,"sidebarCollapsedShowTitle"),Z=w(l,"sidebarAutoActivateChild"),re=w(l,"sidebarExpandOnHover"),ue=w(l,"sidebarCollapsedButton"),G=w(l,"sidebarFixedButton"),ae=w(l,"headerEnable"),xe=w(l,"headerMode"),Ce=w(l,"headerMenuAlign"),Ve=w(l,"breadcrumbEnable"),Oe=w(l,"breadcrumbShowIcon"),Be=w(l,"breadcrumbShowHome"),j=w(l,"breadcrumbStyleType"),ne=w(l,"breadcrumbHideOnlyOne"),ee=w(l,"tabbarEnable"),ge=w(l,"tabbarShowIcon"),Se=w(l,"tabbarShowMore"),Ae=w(l,"tabbarShowMaximize"),ie=w(l,"tabbarPersist"),be=w(l,"tabbarDraggable"),pe=w(l,"tabbarWheelable"),Xe=w(l,"tabbarStyleType"),Je=w(l,"tabbarMaxCount"),Ee=w(l,"tabbarMiddleClickToClose"),D=w(l,"navigationStyleType"),oe=w(l,"navigationSplit"),fe=w(l,"navigationAccordion"),Ie=w(l,"footerEnable"),Ue=w(l,"footerFixed"),Yt=w(l,"copyrightSettingShow"),xt=w(l,"copyrightEnable"),zt=w(l,"copyrightCompanyName"),Pt=w(l,"copyrightCompanySiteLink"),it=w(l,"copyrightDate"),wt=w(l,"copyrightIcp"),te=w(l,"copyrightIcpLink"),Me=w(l,"shortcutKeysEnable"),Ze=w(l,"shortcutKeysGlobalSearch"),je=w(l,"shortcutKeysGlobalLogout"),Re=w(l,"shortcutKeysGlobalLockScreen"),ga=w(l,"widgetGlobalSearch"),ya=w(l,"widgetFullscreen"),xa=w(l,"widgetLanguageToggle"),wa=w(l,"widgetNotification"),ka=w(l,"widgetThemeToggle"),Ca=w(l,"widgetSidebarToggle"),Ma=w(l,"widgetLockScreen"),Sa=w(l,"widgetRefresh"),{diffPreference:bt,isDark:Il,isFullContent:Xt,isHeaderNav:Ul,isHeaderSidebarNav:Hl,isMixedNav:Ta,isSideMixedNav:Ol,isSideMode:Dl,isSideNav:Wl}=ut(),{copy:Rl}=Vn({legacy:!0}),[Nl]=pl(),_a=Y("appearance"),Fl=y(()=>[{label:g("preferences.appearance"),value:"appearance"},{label:g("preferences.layout"),value:"layout"},{label:g("preferences.shortcutKeys.title"),value:"shortcutKey"},{label:g("preferences.general"),value:"general"}]),Kl=y(()=>!Xt.value&&!Ta.value&&!Ul.value&&U.header.enable);function jl(){return X(this,null,function*(){var Jt;yield Rl(JSON.stringify(bt.value,null,2)),(Jt=o.copyPreferencesSuccess)==null||Jt.call(o,g("preferences.copyPreferencesSuccessTitle"),g("preferences.copyPreferencesSuccess"))})}function Gl(){return X(this,null,function*(){La(),Ln(),t("clearPreferencesAndLogout")})}function ql(){return X(this,null,function*(){bt.value&&(La(),yield ll(U.app.locale))})}return(Jt,S)=>(u(),k("div",null,[m(e(Nl),{description:e(g)("preferences.subtitle"),title:e(g)("preferences.title"),class:"sm:max-w-sm"},{extra:c(()=>[B("div",su,[m(e(nt),{disabled:!e(bt),tooltip:e(g)("preferences.resetTip"),class:"relative"},{default:c(()=>[e(bt)?(u(),k("span",ru)):I("",!0),m(e(fa),{class:"size-4",onClick:ql})]),_:1},8,["disabled","tooltip"])])]),footer:c(()=>[m(e(He),{disabled:!e(bt),class:"mx-4 w-full",size:"sm",variant:"default",onClick:jl},{default:c(()=>[m(e(us),{class:"mr-2 size-3"}),A(" "+T(e(g)("preferences.copyPreferences")),1)]),_:1},8,["disabled"]),m(e(He),{disabled:!e(bt),class:"mr-4 w-full",size:"sm",variant:"ghost",onClick:Gl},{default:c(()=>[A(T(e(g)("preferences.clearAndLogout")),1)]),_:1},8,["disabled"])]),default:c(()=>[B("div",iu,[m(e(_r),{modelValue:_a.value,"onUpdate:modelValue":S[68]||(S[68]=_=>_a.value=_),tabs:Fl.value},{general:c(()=>[m(e(Te),{title:e(g)("preferences.general")},{default:c(()=>[m(e(Gi),{"app-dynamic-title":n.value,"onUpdate:appDynamicTitle":S[0]||(S[0]=_=>n.value=_),"app-enable-check-updates":v.value,"onUpdate:appEnableCheckUpdates":S[1]||(S[1]=_=>v.value=_),"app-locale":s.value,"onUpdate:appLocale":S[2]||(S[2]=_=>s.value=_),"app-watermark":f.value,"onUpdate:appWatermark":S[3]||(S[3]=_=>f.value=_)},null,8,["app-dynamic-title","app-enable-check-updates","app-locale","app-watermark"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.animation.title")},{default:c(()=>[m(e(Ki),{"transition-enable":O.value,"onUpdate:transitionEnable":S[4]||(S[4]=_=>O.value=_),"transition-loading":H.value,"onUpdate:transitionLoading":S[5]||(S[5]=_=>H.value=_),"transition-name":C.value,"onUpdate:transitionName":S[6]||(S[6]=_=>C.value=_),"transition-progress":b.value,"onUpdate:transitionProgress":S[7]||(S[7]=_=>b.value=_)},null,8,["transition-enable","transition-loading","transition-name","transition-progress"])]),_:1},8,["title"])]),appearance:c(()=>[m(e(Te),{title:e(g)("preferences.theme.title")},{default:c(()=>[m(e(nu),{modelValue:F.value,"onUpdate:modelValue":S[8]||(S[8]=_=>F.value=_),"theme-semi-dark-header":K.value,"onUpdate:themeSemiDarkHeader":S[9]||(S[9]=_=>K.value=_),"theme-semi-dark-sidebar":V.value,"onUpdate:themeSemiDarkSidebar":S[10]||(S[10]=_=>V.value=_)},null,8,["modelValue","theme-semi-dark-header","theme-semi-dark-sidebar"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.theme.builtin.title")},{default:c(()=>[m(e(Qd),{modelValue:W.value,"onUpdate:modelValue":S[11]||(S[11]=_=>W.value=_),"theme-color-primary":M.value,"onUpdate:themeColorPrimary":S[12]||(S[12]=_=>M.value=_),"is-dark":e(Il)},null,8,["modelValue","theme-color-primary","is-dark"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.theme.radius")},{default:c(()=>[m(e(tu),{modelValue:P.value,"onUpdate:modelValue":S[13]||(S[13]=_=>P.value=_)},null,8,["modelValue"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.other")},{default:c(()=>[m(e(eu),{"app-color-gray-mode":i.value,"onUpdate:appColorGrayMode":S[14]||(S[14]=_=>i.value=_),"app-color-weak-mode":d.value,"onUpdate:appColorWeakMode":S[15]||(S[15]=_=>d.value=_)},null,8,["app-color-gray-mode","app-color-weak-mode"])]),_:1},8,["title"])]),layout:c(()=>[m(e(Te),{title:e(g)("preferences.layout")},{default:c(()=>[m(e(Od),{modelValue:r.value,"onUpdate:modelValue":S[16]||(S[16]=_=>r.value=_)},null,8,["modelValue"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.content")},{default:c(()=>[m(e(Ld),{modelValue:p.value,"onUpdate:modelValue":S[17]||(S[17]=_=>p.value=_)},null,8,["modelValue"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.sidebar.title")},{default:c(()=>[m(e(Fd),{"sidebar-auto-activate-child":Z.value,"onUpdate:sidebarAutoActivateChild":S[18]||(S[18]=_=>Z.value=_),"sidebar-collapsed":R.value,"onUpdate:sidebarCollapsed":S[19]||(S[19]=_=>R.value=_),"sidebar-collapsed-show-title":q.value,"onUpdate:sidebarCollapsedShowTitle":S[20]||(S[20]=_=>q.value=_),"sidebar-enable":z.value,"onUpdate:sidebarEnable":S[21]||(S[21]=_=>z.value=_),"sidebar-expand-on-hover":re.value,"onUpdate:sidebarExpandOnHover":S[22]||(S[22]=_=>re.value=_),"sidebar-width":$.value,"onUpdate:sidebarWidth":S[23]||(S[23]=_=>$.value=_),"sidebar-collapsed-button":ue.value,"onUpdate:sidebarCollapsedButton":S[24]||(S[24]=_=>ue.value=_),"sidebar-fixed-button":G.value,"onUpdate:sidebarFixedButton":S[25]||(S[25]=_=>G.value=_),"current-layout":r.value,disabled:!e(Dl)},null,8,["sidebar-auto-activate-child","sidebar-collapsed","sidebar-collapsed-show-title","sidebar-enable","sidebar-expand-on-hover","sidebar-width","sidebar-collapsed-button","sidebar-fixed-button","current-layout","disabled"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.header.title")},{default:c(()=>[m(e(Ad),{"header-enable":ae.value,"onUpdate:headerEnable":S[26]||(S[26]=_=>ae.value=_),"header-menu-align":Ce.value,"onUpdate:headerMenuAlign":S[27]||(S[27]=_=>Ce.value=_),"header-mode":xe.value,"onUpdate:headerMode":S[28]||(S[28]=_=>xe.value=_),disabled:e(Xt)},null,8,["header-enable","header-menu-align","header-mode","disabled"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.navigationMenu.title")},{default:c(()=>[m(e(Dd),{"navigation-accordion":fe.value,"onUpdate:navigationAccordion":S[29]||(S[29]=_=>fe.value=_),"navigation-split":oe.value,"onUpdate:navigationSplit":S[30]||(S[30]=_=>oe.value=_),"navigation-style-type":D.value,"onUpdate:navigationStyleType":S[31]||(S[31]=_=>D.value=_),disabled:e(Xt),"disabled-navigation-split":!e(Ta)},null,8,["navigation-accordion","navigation-split","navigation-style-type","disabled","disabled-navigation-split"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.breadcrumb.title")},{default:c(()=>[m(e(Yi),{"breadcrumb-enable":Ve.value,"onUpdate:breadcrumbEnable":S[32]||(S[32]=_=>Ve.value=_),"breadcrumb-hide-only-one":ne.value,"onUpdate:breadcrumbHideOnlyOne":S[33]||(S[33]=_=>ne.value=_),"breadcrumb-show-home":Be.value,"onUpdate:breadcrumbShowHome":S[34]||(S[34]=_=>Be.value=_),"breadcrumb-show-icon":Oe.value,"onUpdate:breadcrumbShowIcon":S[35]||(S[35]=_=>Oe.value=_),"breadcrumb-style-type":j.value,"onUpdate:breadcrumbStyleType":S[36]||(S[36]=_=>j.value=_),disabled:!Kl.value||!(e(Wl)||e(Ol)||e(Hl))},null,8,["breadcrumb-enable","breadcrumb-hide-only-one","breadcrumb-show-home","breadcrumb-show-icon","breadcrumb-style-type","disabled"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.tabbar.title")},{default:c(()=>[m(e(Kd),{"tabbar-draggable":be.value,"onUpdate:tabbarDraggable":S[37]||(S[37]=_=>be.value=_),"tabbar-enable":ee.value,"onUpdate:tabbarEnable":S[38]||(S[38]=_=>ee.value=_),"tabbar-persist":ie.value,"onUpdate:tabbarPersist":S[39]||(S[39]=_=>ie.value=_),"tabbar-show-icon":ge.value,"onUpdate:tabbarShowIcon":S[40]||(S[40]=_=>ge.value=_),"tabbar-show-maximize":Ae.value,"onUpdate:tabbarShowMaximize":S[41]||(S[41]=_=>Ae.value=_),"tabbar-show-more":Se.value,"onUpdate:tabbarShowMore":S[42]||(S[42]=_=>Se.value=_),"tabbar-style-type":Xe.value,"onUpdate:tabbarStyleType":S[43]||(S[43]=_=>Xe.value=_),"tabbar-wheelable":pe.value,"onUpdate:tabbarWheelable":S[44]||(S[44]=_=>pe.value=_),"tabbar-max-count":Je.value,"onUpdate:tabbarMaxCount":S[45]||(S[45]=_=>Je.value=_),"tabbar-middle-click-to-close":Ee.value,"onUpdate:tabbarMiddleClickToClose":S[46]||(S[46]=_=>Ee.value=_)},null,8,["tabbar-draggable","tabbar-enable","tabbar-persist","tabbar-show-icon","tabbar-show-maximize","tabbar-show-more","tabbar-style-type","tabbar-wheelable","tabbar-max-count","tabbar-middle-click-to-close"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.widget.title")},{default:c(()=>[m(e(jd),{"app-preferences-button-position":h.value,"onUpdate:appPreferencesButtonPosition":S[47]||(S[47]=_=>h.value=_),"widget-fullscreen":ya.value,"onUpdate:widgetFullscreen":S[48]||(S[48]=_=>ya.value=_),"widget-global-search":ga.value,"onUpdate:widgetGlobalSearch":S[49]||(S[49]=_=>ga.value=_),"widget-language-toggle":xa.value,"onUpdate:widgetLanguageToggle":S[50]||(S[50]=_=>xa.value=_),"widget-lock-screen":Ma.value,"onUpdate:widgetLockScreen":S[51]||(S[51]=_=>Ma.value=_),"widget-notification":wa.value,"onUpdate:widgetNotification":S[52]||(S[52]=_=>wa.value=_),"widget-refresh":Sa.value,"onUpdate:widgetRefresh":S[53]||(S[53]=_=>Sa.value=_),"widget-sidebar-toggle":Ca.value,"onUpdate:widgetSidebarToggle":S[54]||(S[54]=_=>Ca.value=_),"widget-theme-toggle":ka.value,"onUpdate:widgetThemeToggle":S[55]||(S[55]=_=>ka.value=_)},null,8,["app-preferences-button-position","widget-fullscreen","widget-global-search","widget-language-toggle","widget-lock-screen","widget-notification","widget-refresh","widget-sidebar-toggle","widget-theme-toggle"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.footer.title")},{default:c(()=>[m(e(Pd),{"footer-enable":Ie.value,"onUpdate:footerEnable":S[56]||(S[56]=_=>Ie.value=_),"footer-fixed":Ue.value,"onUpdate:footerFixed":S[57]||(S[57]=_=>Ue.value=_)},null,8,["footer-enable","footer-fixed"])]),_:1},8,["title"]),Yt.value?(u(),x(e(Te),{key:0,title:e(g)("preferences.copyright.title")},{default:c(()=>[m(e(zd),{"copyright-company-name":zt.value,"onUpdate:copyrightCompanyName":S[58]||(S[58]=_=>zt.value=_),"copyright-company-site-link":Pt.value,"onUpdate:copyrightCompanySiteLink":S[59]||(S[59]=_=>Pt.value=_),"copyright-date":it.value,"onUpdate:copyrightDate":S[60]||(S[60]=_=>it.value=_),"copyright-enable":xt.value,"onUpdate:copyrightEnable":S[61]||(S[61]=_=>xt.value=_),"copyright-icp":wt.value,"onUpdate:copyrightIcp":S[62]||(S[62]=_=>wt.value=_),"copyright-icp-link":te.value,"onUpdate:copyrightIcpLink":S[63]||(S[63]=_=>te.value=_),disabled:!Ie.value},null,8,["copyright-company-name","copyright-company-site-link","copyright-date","copyright-enable","copyright-icp","copyright-icp-link","disabled"])]),_:1},8,["title"])):I("",!0)]),shortcutKey:c(()=>[m(e(Te),{title:e(g)("preferences.shortcutKeys.global")},{default:c(()=>[m(e(Gd),{"shortcut-keys-enable":Me.value,"onUpdate:shortcutKeysEnable":S[64]||(S[64]=_=>Me.value=_),"shortcut-keys-global-search":Ze.value,"onUpdate:shortcutKeysGlobalSearch":S[65]||(S[65]=_=>Ze.value=_),"shortcut-keys-lock-screen":Re.value,"onUpdate:shortcutKeysLockScreen":S[66]||(S[66]=_=>Re.value=_),"shortcut-keys-logout":je.value,"onUpdate:shortcutKeysLogout":S[67]||(S[67]=_=>je.value=_)},null,8,["shortcut-keys-enable","shortcut-keys-global-search","shortcut-keys-lock-screen","shortcut-keys-logout"])]),_:1},8,["title"])]),_:1},8,["modelValue","tabs"])])]),_:1},8,["description","title"])]))}}),Sl=L({__name:"preferences",setup(l){const[a,t]=pl({connectedComponent:du}),o=y(()=>{const n={};for(const[r,i]of Object.entries(U))for(const[d,p]of Object.entries(i))n[`${r}${Ea(d)}`]=p;return n}),s=y(()=>{const n={};for(const[r,i]of Object.entries(U))if(typeof i=="object")for(const d of Object.keys(i))n[`update:${r}${Ea(d)}`]=p=>{ot({[r]:{[d]:p}}),r==="app"&&d==="locale"&&ll(p)};else n[r]=i;return n});return(n,r)=>(u(),k("div",null,[m(e(a),ve(Q(Q({},n.$attrs),o.value),il(s.value)),null,16),B("div",{onClick:r[0]||(r[0]=()=>e(t).open())},[E(n.$slots,"default",{},()=>[m(e(He),{title:e(g)("preferences.title"),class:"bg-primary flex-col-center size-10 cursor-pointer rounded-l-lg rounded-r-none border-none"},{default:c(()=>[m(e(vl),{class:"size-5"})]),_:1},8,["title"])])])]))}}),uu=L({__name:"preferences-button",emits:["clearPreferencesAndLogout"],setup(l,{emit:a}){const t=a;function o(){t("clearPreferencesAndLogout")}return(s,n)=>(u(),x(Sl,{onClearPreferencesAndLogout:o},{default:c(()=>[m(e(nt),null,{default:c(()=>[m(e(vl),{class:"text-foreground size-4"})]),_:1})]),_:1}))}}),cu={class:"hover:bg-accent ml-1 mr-2 cursor-pointer rounded-full p-1.5"},pu={class:"hover:text-accent-foreground flex-center"},fu={class:"ml-2 w-full"},mu={key:0,class:"text-foreground mb-1 flex items-center text-sm font-medium"},hu={class:"text-muted-foreground text-xs font-normal"},Ec=L({name:"UserDropdown",__name:"user-dropdown",props:{avatar:{default:""},description:{default:""},enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]},tagText:{default:""},text:{default:""},trigger:{default:"click"},hoverDelay:{default:500}},emits:["logout"],setup(l,{emit:a}){const t=l,o=a,{globalLockScreenShortcutKey:s,globalLogoutShortcutKey:n}=ut(),r=pt(),[i,d]=Vt({connectedComponent:ii}),[p,f]=Vt({onConfirm(){K()}}),v=za("refTrigger"),h=za("refContent"),[b,C]=$r([v,h],()=>t.hoverDelay);me(()=>t.trigger==="hover"||t.trigger==="both",z=>{z?C.enable():C.disable()},{immediate:!0});const H=y(()=>_t()?"Alt":"⌥"),O=y(()=>t.enableShortcutKey&&n.value),M=y(()=>t.enableShortcutKey&&s.value),W=y(()=>t.enableShortcutKey&&U.shortcutKeys.enable);function F(){d.open()}function P(z){d.close(),r.lockScreen(z)}function V(){f.open(),b.value=!1}function K(){o("logout"),f.close()}if(W.value){const z=rl();Wt(z["Alt+KeyQ"],()=>{O.value&&V()}),Wt(z["Alt+KeyL"],()=>{M.value&&F()})}return(z,$)=>(u(),k(J,null,[e(U).widget.lockScreen?(u(),x(e(i),{key:0,avatar:z.avatar,text:z.text,onSubmit:P},null,8,["avatar","text"])):I("",!0),m(e(p),{"cancel-text":e(g)("common.cancel"),"confirm-text":e(g)("common.confirm"),"fullscreen-button":!1,title:e(g)("common.prompt"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[A(T(e(g)("ui.widgets.logoutTip")),1)]),_:1},8,["cancel-text","confirm-text","title"]),m(e(ua),{open:e(b),"onUpdate:open":$[0]||($[0]=R=>da(b)?b.value=R:null)},{default:c(()=>[m(e(ca),{ref_key:"refTrigger",ref:v,disabled:t.trigger==="hover"},{default:c(()=>[B("div",cu,[B("div",pu,[m(e($t),{alt:z.text,src:z.avatar,class:"size-8",dot:""},null,8,["alt","src"])])])]),_:1},8,["disabled"]),m(e(pa),{class:"mr-2 min-w-[240px] p-0 pb-1"},{default:c(()=>{var R;return[B("div",{ref_key:"refContent",ref:h},[m(e(Us),{class:"flex items-center p-3"},{default:c(()=>[m(e($t),{alt:z.text,src:z.avatar,class:"size-12",dot:"","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["alt","src"]),B("div",fu,[z.tagText||z.text||z.$slots.tagText?(u(),k("div",mu,[A(T(z.text)+" ",1),E(z.$slots,"tagText",{},()=>[z.tagText?(u(),x(e(Ms),{key:0,class:"ml-2 text-green-400"},{default:c(()=>[A(T(z.tagText),1)]),_:1})):I("",!0)])])):I("",!0),B("div",hu,T(z.description),1)])]),_:3}),(R=z.menus)!=null&&R.length?(u(),x(e(Ht),{key:0})):I("",!0),(u(!0),k(J,null,de(z.menus,q=>(u(),x(e(St),{key:q.text,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:q.handler},{default:c(()=>[m(e(De),{icon:q.icon,class:"mr-2 size-4"},null,8,["icon"]),A(" "+T(q.text),1)]),_:2},1032,["onClick"]))),128)),m(e(Ht)),e(U).widget.lockScreen?(u(),x(e(St),{key:1,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:F},{default:c(()=>[m(e(ml),{class:"mr-2 size-4"}),A(" "+T(e(g)("ui.widgets.lockScreen.title"))+" ",1),M.value?(u(),x(e(Da),{key:0},{default:c(()=>[A(T(H.value)+" L ",1)]),_:1})):I("",!0)]),_:1})):I("",!0),e(U).widget.lockScreen?(u(),x(e(Ht),{key:2})):I("",!0),m(e(St),{class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:V},{default:c(()=>[m(e(hs),{class:"mr-2 size-4"}),A(" "+T(e(g)("common.logout"))+" ",1),O.value?(u(),x(e(Da),{key:0},{default:c(()=>[A(T(H.value)+" Q ",1)]),_:1})):I("",!0)]),_:1})],512)]}),_:3})]),_:3},8,["open"])],64))}}),bu=L({__name:"layout-content",props:{contentCompact:{},contentCompactWidth:{},padding:{},paddingBottom:{},paddingLeft:{},paddingRight:{},paddingTop:{}},setup(l){const a=l,{contentElement:t,overlayStyle:o}=Jo(),s=y(()=>{const{contentCompact:n,padding:r,paddingBottom:i,paddingLeft:d,paddingRight:p,paddingTop:f}=a,v=n==="compact"?{margin:"0 auto",width:`${a.contentCompactWidth}px`}:{};return we(Q({},v),{flex:1,padding:`${r}px`,paddingBottom:`${i}px`,paddingLeft:`${d}px`,paddingRight:`${p}px`,paddingTop:`${f}px`})});return(n,r)=>(u(),k("main",{ref_key:"contentElement",ref:t,style:ce(s.value),class:"bg-background-deep relative"},[m(e(Zo),{style:ce(e(o))},{default:c(()=>[E(n.$slots,"overlay")]),_:3},8,["style"]),E(n.$slots,"default")],4))}}),vu=L({__name:"layout-footer",props:{fixed:{type:Boolean},height:{},show:{type:Boolean,default:!0},width:{},zIndex:{}},setup(l){const a=l,t=y(()=>{const{fixed:o,height:s,show:n,width:r,zIndex:i}=a;return{height:`${s}px`,marginBottom:n?"0":`-${s}px`,position:o?"fixed":"static",width:r,zIndex:i}});return(o,s)=>(u(),k("footer",{style:ce(t.value),class:"bg-background-deep bottom-0 w-full transition-all duration-200"},[E(o.$slots,"default")],4))}}),gu=L({__name:"layout-header",props:{fullWidth:{type:Boolean},height:{},isMobile:{type:Boolean},show:{type:Boolean},sidebarWidth:{},theme:{},width:{},zIndex:{}},setup(l){const a=l,t=Ke(),o=y(()=>{const{fullWidth:n,height:r,show:i}=a,d=!i||!n?void 0:0;return{height:`${r}px`,marginTop:i?0:`-${r}px`,right:d}}),s=y(()=>({minWidth:`${a.isMobile?40:a.sidebarWidth}px`}));return(n,r)=>(u(),k("header",{class:N([n.theme,"border-border bg-header top-0 flex w-full flex-[0_0_auto] items-center border-b pl-2 transition-[margin-top] duration-200"]),style:ce(o.value)},[e(t).logo?(u(),k("div",{key:0,style:ce(s.value)},[E(n.$slots,"logo")],4)):I("",!0),E(n.$slots,"toggle-button"),E(n.$slots,"default")],6))}}),Fa=L({__name:"sidebar-collapse-button",props:{collapsed:{type:Boolean},collapsedModifiers:{}},emits:["update:collapsed"],setup(l){const a=w(l,"collapsed");function t(){a.value=!a.value}return(o,s)=>(u(),k("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 left-3 z-10 cursor-pointer rounded-sm p-1",onClick:_e(t,["stop"])},[a.value?(u(),x(e(Qo),{key:0,class:"size-4"})):(u(),x(e(en),{key:1,class:"size-4"}))]))}}),Ka=L({__name:"sidebar-fixed-button",props:{expandOnHover:{type:Boolean},expandOnHoverModifiers:{}},emits:["update:expandOnHover"],setup(l){const a=w(l,"expandOnHover");function t(){a.value=!a.value}return(o,s)=>(u(),k("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 right-3 z-10 cursor-pointer rounded-sm p-[5px] transition-all duration-300",onClick:t},[a.value?(u(),x(e(Gt),{key:1,class:"size-3.5"})):(u(),x(e(bl),{key:0,class:"size-3.5"}))]))}}),yu=L({__name:"layout-sidebar",props:ye({collapseHeight:{default:42},collapseWidth:{default:48},domVisible:{type:Boolean,default:!0},extraWidth:{},fixedExtra:{type:Boolean,default:!1},headerHeight:{},isSidebarMixed:{type:Boolean,default:!1},marginTop:{default:0},mixedWidth:{default:70},paddingTop:{default:0},show:{type:Boolean,default:!0},showCollapseButton:{type:Boolean,default:!0},showFixedButton:{type:Boolean,default:!0},theme:{},width:{},zIndex:{default:0}},{collapse:{type:Boolean},collapseModifiers:{},extraCollapse:{type:Boolean},extraCollapseModifiers:{},expandOnHovering:{type:Boolean},expandOnHoveringModifiers:{},expandOnHover:{type:Boolean},expandOnHoverModifiers:{},extraVisible:{type:Boolean},extraVisibleModifiers:{}}),emits:ye(["leave"],["update:collapse","update:extraCollapse","update:expandOnHovering","update:expandOnHover","update:extraVisible"]),setup(l,{emit:a}){const t=l,o=a,s=w(l,"collapse"),n=w(l,"extraCollapse"),r=w(l,"expandOnHovering"),i=w(l,"expandOnHover"),d=w(l,"extraVisible"),p=En(document.body),f=Ke(),v=Dt(),h=y(()=>V(!0)),b=y(()=>{const{isSidebarMixed:$,marginTop:R,paddingTop:q,zIndex:Z}=t;return Q(we(Q({"--scroll-shadow":"var(--sidebar)"},V(!1)),{height:`calc(100% - ${R}px)`,marginTop:`${R}px`,paddingTop:`${q}px`,zIndex:Z}),$&&d.value?{transition:"none"}:{})}),C=y(()=>{const{extraWidth:$,show:R,width:q,zIndex:Z}=t;return{left:`${q}px`,width:d.value&&R?`${$}px`:0,zIndex:Z}}),H=y(()=>{const{headerHeight:$}=t;return{height:`${$-1}px`}}),O=y(()=>{const{collapseWidth:$,fixedExtra:R,isSidebarMixed:q,mixedWidth:Z}=t;return q&&R?{width:`${s.value?$:Z}px`}:{}}),M=y(()=>{const{collapseHeight:$,headerHeight:R}=t;return Q({height:`calc(100% - ${R+$}px)`,paddingTop:"8px"},O.value)}),W=y(()=>{const{headerHeight:$,isSidebarMixed:R}=t;return Q(we(Q({},R?{display:"flex",justifyContent:"center"}:{}),{height:`${$-1}px`}),O.value)}),F=y(()=>{const{collapseHeight:$,headerHeight:R}=t;return{height:`calc(100% - ${R+$}px)`}}),P=y(()=>({height:`${t.collapseHeight}px`}));ra(()=>{d.value=t.fixedExtra?!0:d.value});function V($){const{extraWidth:R,fixedExtra:q,isSidebarMixed:Z,show:re,width:ue}=t;let G=ue===0?"0px":`${ue+(Z&&q&&d.value?R:0)}px`;const{collapseWidth:ae}=t;return $&&r.value&&!i.value&&(G=`${ae}px`),we(Q({},G==="0px"?{overflow:"hidden"}:{}),{flex:`0 0 ${G}`,marginLeft:re?0:`-${G}`,maxWidth:G,minWidth:G,width:G})}function K($){($==null?void 0:$.offsetX)<10||i.value||(r.value||(s.value=!1),t.isSidebarMixed&&(p.value=!0),r.value=!0)}function z(){o("leave"),t.isSidebarMixed&&(p.value=!1),!i.value&&(r.value=!1,s.value=!0,d.value=!1)}return($,R)=>(u(),k(J,null,[$.domVisible?(u(),k("div",{key:0,class:N([$.theme,"h-full transition-all duration-150"]),style:ce(h.value)},null,6)):I("",!0),B("aside",{class:N([[$.theme,{"bg-sidebar-deep":$.isSidebarMixed,"bg-sidebar border-border border-r":!$.isSidebarMixed}],"fixed left-0 top-0 h-full transition-all duration-150"]),style:ce(b.value),onMouseenter:K,onMouseleave:z},[!s.value&&!$.isSidebarMixed&&$.showFixedButton?(u(),x(e(Ka),{key:0,"expand-on-hover":i.value,"onUpdate:expandOnHover":R[0]||(R[0]=q=>i.value=q)},null,8,["expand-on-hover"])):I("",!0),e(f).logo?(u(),k("div",{key:1,style:ce(W.value)},[E($.$slots,"logo")],4)):I("",!0),m(e(Bt),{style:ce(M.value),shadow:"","shadow-border":""},{default:c(()=>[E($.$slots,"default")]),_:3},8,["style"]),B("div",{style:ce(P.value)},null,4),$.showCollapseButton&&!$.isSidebarMixed?(u(),x(e(Fa),{key:2,collapsed:s.value,"onUpdate:collapsed":R[1]||(R[1]=q=>s.value=q)},null,8,["collapsed"])):I("",!0),$.isSidebarMixed?(u(),k("div",{key:3,ref_key:"asideRef",ref:v,class:N([{"border-l":d.value},"border-border bg-sidebar fixed top-0 h-full overflow-hidden border-r transition-all duration-200"]),style:ce(C.value)},[$.isSidebarMixed&&i.value?(u(),x(e(Fa),{key:0,collapsed:n.value,"onUpdate:collapsed":R[2]||(R[2]=q=>n.value=q)},null,8,["collapsed"])):I("",!0),n.value?I("",!0):(u(),x(e(Ka),{key:1,"expand-on-hover":i.value,"onUpdate:expandOnHover":R[3]||(R[3]=q=>i.value=q)},null,8,["expand-on-hover"])),n.value?I("",!0):(u(),k("div",{key:2,style:ce(H.value),class:"pl-2"},[E($.$slots,"extra-title")],4)),m(e(Bt),{style:ce(F.value),class:"border-border py-2",shadow:"","shadow-border":""},{default:c(()=>[E($.$slots,"extra")]),_:3},8,["style"])],6)):I("",!0)],38)],64))}}),xu=L({__name:"layout-tabbar",props:{height:{}},setup(l){const a=l,t=y(()=>{const{height:o}=a;return{height:`${o}px`}});return(o,s)=>(u(),k("section",{style:ce(t.value),class:"border-border bg-background flex w-full border-b transition-all"},[E(o.$slots,"default")],4))}});function wu(l){const a=y(()=>l.isMobile?"sidebar-nav":l.layout),t=y(()=>a.value==="full-content"),o=y(()=>a.value==="sidebar-mixed-nav"),s=y(()=>a.value==="header-nav"),n=y(()=>a.value==="mixed-nav"||a.value==="header-sidebar-nav"),r=y(()=>a.value==="header-mixed-nav");return{currentLayout:a,isFullContent:t,isHeaderMixedNav:r,isHeaderNav:s,isMixedNav:n,isSidebarMixedNav:o}}const ku={class:"relative flex min-h-full w-full"},Cu=L({name:"VbenLayout",__name:"vben-layout",props:ye({contentCompact:{default:"wide"},contentCompactWidth:{default:1200},contentPadding:{default:0},contentPaddingBottom:{default:0},contentPaddingLeft:{default:0},contentPaddingRight:{default:0},contentPaddingTop:{default:0},footerEnable:{type:Boolean,default:!1},footerFixed:{type:Boolean,default:!0},footerHeight:{default:32},headerHeight:{default:50},headerHidden:{type:Boolean,default:!1},headerMode:{default:"fixed"},headerTheme:{},headerToggleSidebarButton:{type:Boolean,default:!0},headerVisible:{type:Boolean,default:!0},isMobile:{type:Boolean,default:!1},layout:{default:"sidebar-nav"},sidebarCollapse:{type:Boolean},sidebarCollapsedButton:{type:Boolean,default:!0},sidebarCollapseShowTitle:{type:Boolean,default:!1},sidebarEnable:{type:Boolean},sidebarExtraCollapsedWidth:{default:60},sidebarFixedButton:{type:Boolean,default:!0},sidebarHidden:{type:Boolean,default:!1},sidebarMixedWidth:{default:80},sidebarTheme:{default:"dark"},sidebarWidth:{default:180},sideCollapseWidth:{default:60},tabbarEnable:{type:Boolean,default:!0},tabbarHeight:{default:40},zIndex:{default:200}},{sidebarCollapse:{type:Boolean,default:!1},sidebarCollapseModifiers:{},sidebarExtraVisible:{type:Boolean},sidebarExtraVisibleModifiers:{},sidebarExtraCollapse:{type:Boolean,default:!1},sidebarExtraCollapseModifiers:{},sidebarExpandOnHover:{type:Boolean,default:!1},sidebarExpandOnHoverModifiers:{},sidebarEnable:{type:Boolean,default:!0},sidebarEnableModifiers:{}}),emits:ye(["sideMouseLeave","toggleSidebar"],["update:sidebarCollapse","update:sidebarExtraVisible","update:sidebarExtraCollapse","update:sidebarExpandOnHover","update:sidebarEnable"]),setup(l,{emit:a}){const t=l,o=a,s=w(l,"sidebarCollapse"),n=w(l,"sidebarExtraVisible"),r=w(l,"sidebarExtraCollapse"),i=w(l,"sidebarExpandOnHover"),d=w(l,"sidebarEnable"),p=Y(!1),f=Y(!1),v=Y(),{arrivedState:h,directions:b,isScrolling:C,y:H}=zn(document),{setLayoutHeaderHeight:O}=tn(),{setLayoutFooterHeight:M}=an(),{y:W}=Pn({target:v,type:"client"}),{currentLayout:F,isFullContent:P,isHeaderMixedNav:V,isHeaderNav:K,isMixedNav:z,isSidebarMixedNav:$}=wu(t),R=y(()=>t.headerMode==="auto"),q=y(()=>{let D=0;return t.headerVisible&&!t.headerHidden&&(D+=t.headerHeight),t.tabbarEnable&&(D+=t.tabbarHeight),D}),Z=y(()=>{const{sidebarCollapseShowTitle:D,sidebarMixedWidth:oe,sideCollapseWidth:fe}=t;return D||$.value||V.value?oe:fe}),re=y(()=>!K.value&&d.value),ue=y(()=>{const{headerHeight:D,isMobile:oe}=t;return z.value&&!oe?D:0}),G=y(()=>{const{isMobile:D,sidebarHidden:oe,sidebarMixedWidth:fe,sidebarWidth:Ie}=t;let Ue=0;return oe||!re.value||oe&&!$.value&&!z.value&&!V.value||((V.value||$.value)&&!D?Ue=fe:s.value?Ue=D?0:Z.value:Ue=Ie),Ue}),ae=y(()=>{const{sidebarExtraCollapsedWidth:D,sidebarWidth:oe}=t;return r.value?D:oe}),xe=y(()=>F.value==="mixed-nav"||F.value==="sidebar-mixed-nav"||F.value==="sidebar-nav"||F.value==="header-mixed-nav"||F.value==="header-sidebar-nav"),Ce=y(()=>{const{headerMode:D}=t;return z.value||D==="fixed"||D==="auto-scroll"||D==="auto"}),Ve=y(()=>xe.value&&d.value&&!t.sidebarHidden),Oe=y(()=>!s.value&&t.isMobile),Be=y(()=>{let D="100%",oe="unset";if(Ce.value&&F.value!=="header-nav"&&F.value!=="mixed-nav"&&F.value!=="header-sidebar-nav"&&Ve.value&&!t.isMobile)if(($.value||V.value)&&i.value&&n.value){const Ie=s.value?Z.value:t.sidebarMixedWidth,Ue=r.value?t.sidebarExtraCollapsedWidth:t.sidebarWidth;oe=`${Ie+Ue}px`,D=`calc(100% - ${oe})`}else oe=p.value&&!i.value?`${Z.value}px`:`${G.value}px`,D=`calc(100% - ${oe})`;return{sidebarAndExtraWidth:oe,width:D}}),j=y(()=>{let D="",oe=0;if(!z.value||t.sidebarHidden)D="100%";else if(d.value){const fe=i.value?t.sidebarWidth:Z.value;oe=s.value?Z.value:fe,D=`calc(100% - ${s.value?G.value:fe}px)`}else D="100%";return{marginLeft:`${oe}px`,width:D}}),ne=y(()=>{const D=Ce.value,{footerEnable:oe,footerFixed:fe,footerHeight:Ie}=t;return{marginTop:D&&!P.value&&!f.value&&(!R.value||H.value<q.value)?`${q.value}px`:0,paddingBottom:`${oe&&fe?Ie:0}px`}}),ee=y(()=>{const{zIndex:D}=t,oe=z.value?1:0;return D+oe}),ge=y(()=>{const D=Ce.value;return{height:P.value?"0":`${q.value}px`,left:z.value?0:Be.value.sidebarAndExtraWidth,position:D?"fixed":"static",top:f.value||P.value?`-${q.value}px`:0,width:Be.value.width,"z-index":ee.value}}),Se=y(()=>{const{isMobile:D,zIndex:oe}=t;let fe=D||xe.value?1:-1;return z.value&&(fe+=1),oe+fe}),Ae=y(()=>t.footerFixed?Be.value.width:"100%"),ie=y(()=>({zIndex:t.zIndex})),be=y(()=>t.isMobile||t.headerToggleSidebarButton&&xe.value&&!$.value&&!z.value&&!t.isMobile),pe=y(()=>!xe.value||z.value||t.isMobile);me(()=>t.isMobile,D=>{D&&(s.value=!0)},{immediate:!0}),me([()=>q.value,()=>P.value],([D])=>{O(P.value?0:D)},{immediate:!0}),me(()=>t.footerHeight,D=>{M(D)},{immediate:!0});{const D=()=>{W.value>q.value?f.value=!0:f.value=!1};me([()=>t.headerMode,()=>W.value],()=>{if(!R.value||z.value||P.value){t.headerMode!=="auto-scroll"&&(f.value=!1);return}f.value=!0,D()},{immediate:!0})}{const D=Ft((oe,fe,Ie)=>{if(H.value<q.value){f.value=!1;return}if(Ie){f.value=!1;return}oe?f.value=!1:fe&&(f.value=!0)},300);me(()=>H.value,()=>{t.headerMode!=="auto-scroll"||z.value||P.value||C.value&&D(b.top,b.bottom,h.top)})}function Xe(){s.value=!0}function Je(){t.isMobile?s.value=!1:o("toggleSidebar")}const Ee=Ja;return(D,oe)=>(u(),k("div",ku,[re.value?(u(),x(e(yu),{key:0,collapse:s.value,"onUpdate:collapse":oe[0]||(oe[0]=fe=>s.value=fe),"expand-on-hover":i.value,"onUpdate:expandOnHover":oe[1]||(oe[1]=fe=>i.value=fe),"expand-on-hovering":p.value,"onUpdate:expandOnHovering":oe[2]||(oe[2]=fe=>p.value=fe),"extra-collapse":r.value,"onUpdate:extraCollapse":oe[3]||(oe[3]=fe=>r.value=fe),"extra-visible":n.value,"onUpdate:extraVisible":oe[4]||(oe[4]=fe=>n.value=fe),"show-collapse-button":D.sidebarCollapsedButton,"show-fixed-button":D.sidebarFixedButton,"collapse-width":Z.value,"dom-visible":!D.isMobile,"extra-width":ae.value,"fixed-extra":i.value,"header-height":e(z)?0:D.headerHeight,"is-sidebar-mixed":e($)||e(V),"margin-top":ue.value,"mixed-width":D.sidebarMixedWidth,show:Ve.value,theme:D.sidebarTheme,width:G.value,"z-index":Se.value,onLeave:oe[5]||(oe[5]=()=>o("sideMouseLeave"))},vt({extra:c(()=>[E(D.$slots,"side-extra")]),"extra-title":c(()=>[E(D.$slots,"side-extra-title")]),default:c(()=>[e($)||e(V)?E(D.$slots,"mixed-menu",{key:0}):E(D.$slots,"menu",{key:1})]),_:2},[xe.value&&!e(z)?{name:"logo",fn:c(()=>[E(D.$slots,"logo")]),key:"0"}:void 0]),1032,["collapse","expand-on-hover","expand-on-hovering","extra-collapse","extra-visible","show-collapse-button","show-fixed-button","collapse-width","dom-visible","extra-width","fixed-extra","header-height","is-sidebar-mixed","margin-top","mixed-width","show","theme","width","z-index"])):I("",!0),B("div",{ref_key:"contentRef",ref:v,class:"flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in"},[B("div",{class:N([[{"shadow-[0_16px_24px_hsl(var(--background))]":e(H)>20},e(ln)],"overflow-hidden transition-all duration-200"]),style:ce(ge.value)},[D.headerVisible?(u(),x(e(gu),{key:0,"full-width":!xe.value,height:D.headerHeight,"is-mobile":D.isMobile,show:!e(P)&&!D.headerHidden,"sidebar-width":D.sidebarWidth,theme:D.headerTheme,width:Be.value.width,"z-index":ee.value},vt({"toggle-button":c(()=>[be.value?(u(),x(e(nt),{key:0,class:"my-0 mr-1 rounded-md",onClick:Je},{default:c(()=>[m(e(on),{class:"size-4"})]),_:1})):I("",!0)]),default:c(()=>[E(D.$slots,"header")]),_:2},[pe.value?{name:"logo",fn:c(()=>[E(D.$slots,"logo")]),key:"0"}:void 0]),1032,["full-width","height","is-mobile","show","sidebar-width","theme","width","z-index"])):I("",!0),D.tabbarEnable?(u(),x(e(xu),{key:1,height:D.tabbarHeight,style:ce(j.value)},{default:c(()=>[E(D.$slots,"tabbar")]),_:3},8,["height","style"])):I("",!0)],6),m(e(bu),{id:e(Ee),"content-compact":D.contentCompact,"content-compact-width":D.contentCompactWidth,padding:D.contentPadding,"padding-bottom":D.contentPaddingBottom,"padding-left":D.contentPaddingLeft,"padding-right":D.contentPaddingRight,"padding-top":D.contentPaddingTop,style:ce(ne.value),class:"transition-[margin-top] duration-200"},{overlay:c(()=>[E(D.$slots,"content-overlay")]),default:c(()=>[E(D.$slots,"content")]),_:3},8,["id","content-compact","content-compact-width","padding","padding-bottom","padding-left","padding-right","padding-top","style"]),D.footerEnable?(u(),x(e(vu),{key:0,fixed:D.footerFixed,height:D.footerHeight,show:!e(P),width:Ae.value,"z-index":D.zIndex},{default:c(()=>[E(D.$slots,"footer")]),_:3},8,["fixed","height","show","width","z-index"])):I("",!0)],512),E(D.$slots,"extra"),Oe.value?(u(),k("div",{key:1,style:ce(ie.value),class:"bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200",onClick:Xe},null,4)):I("",!0)]))}});function Mu(){const l=Y(!1),a=Y(0),t=ct(),o=500,s=y(()=>U.transition.loading),n=()=>{if(!s.value)return;const r=performance.now()-a.value;r<o?setTimeout(()=>{l.value=!1},o-r):l.value=!1};return t.beforeEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||(a.value=performance.now(),l.value=!0),!0)),t.afterEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||n(),!0)),{spinning:l}}const Su=L({name:"LayoutContentSpinner",__name:"content-spinner",setup(l){const{spinning:a}=Mu();return(t,o)=>(u(),x(e(ol),{spinning:e(a)},null,8,["spinning"]))}}),Tu={key:0,class:"relative size-full"},_u=["src","onLoad"],$u=L({name:"IFrameRouterView",__name:"iframe-router-view",setup(l){const a=Y([]),t=yt(),o=lt(),s=y(()=>U.tabbar.enable),n=y(()=>s.value?t.getTabs.filter(h=>{var b;return!!((b=h.meta)!=null&&b.iframeSrc)}):o.meta.iframeSrc?[o]:[]),r=y(()=>new Set(n.value.map(h=>h.name))),i=y(()=>n.value.length>0);function d(h){return h.name===o.name}function p(h){const{meta:b,name:C}=h;return!C||!t.renderRouteView?!1:s.value?!(b!=null&&b.keepAlive)&&r.value.has(C)&&C!==o.name?!1:t.getTabs.some(H=>H.name===C):d(h)}function f(h){a.value[h]=!1}function v(h){const b=a.value[h];return b===void 0?!0:b}return(h,b)=>i.value?(u(!0),k(J,{key:0},de(n.value,(C,H)=>(u(),k(J,{key:C.fullPath},[p(C)?Le((u(),k("div",Tu,[m(e(ol),{spinning:v(H)},null,8,["spinning"]),B("iframe",{src:C.meta.iframeSrc,class:"size-full",onLoad:O=>f(H)},null,40,_u)],512)),[[Pe,d(C)]]):I("",!0)],64))),128)):I("",!0)}}),Bu={class:"relative h-full"},Vu=L({name:"LayoutContent",__name:"content",setup(l){const a=yt(),{keepAlive:t}=ut(),{getCachedTabs:o,getExcludeCachedTabs:s,renderRouteView:n}=al(a),r=y(()=>{const{transition:p}=U;return p.name&&p.enable});function i(p){const{tabbar:f,transition:v}=U,h=v.name;if(!(!h||!v.enable))return!f.enable||!t,h}function d(p,f){var b;if(!p){console.error("Component view not found，please check the route configuration");return}const v=f.name;if(!v)return p;const h=(b=p==null?void 0:p.type)==null?void 0:b.name;return h||h===v||(p.type||(p.type={}),p.type.name=v),p}return(p,f)=>(u(),k("div",Bu,[m(e($u)),m(e(nn),null,{default:c(({Component:v,route:h})=>[r.value?(u(),x(dt,{key:0,name:i(h),appear:"",mode:"out-in"},{default:c(()=>[e(t)?(u(),x(Pa,{key:0,exclude:e(s),include:e(o)},[e(n)?Le((u(),x(ze(d(v,h)),{key:e(qe)(h)})),[[Pe,!h.meta.iframeSrc]]):I("",!0)],1032,["exclude","include"])):e(n)?(u(),x(ze(v),{key:e(qe)(h)})):I("",!0)]),_:2},1032,["name"])):(u(),k(J,{key:1},[e(t)?(u(),x(Pa,{key:0,exclude:e(s),include:e(o)},[e(n)?Le((u(),x(ze(d(v,h)),{key:e(qe)(h)})),[[Pe,!h.meta.iframeSrc]]):I("",!0)],1032,["exclude","include"])):e(n)?(u(),x(ze(v),{key:e(qe)(h)})):I("",!0)],64))]),_:1})]))}}),Lu={class:"flex-center text-muted-foreground relative h-full w-full text-xs"},Eu=L({name:"LayoutFooter",__name:"footer",setup(l){return(a,t)=>(u(),k("div",Lu,[E(a.$slots,"default")]))}}),zu={class:"flex-center hidden lg:block"},Pu={class:"flex h-full min-w-0 flex-shrink-0 items-center"},tt=50,Au=L({name:"LayoutHeader",__name:"header",props:{theme:{default:"light"}},emits:["clearPreferencesAndLogout"],setup(l,{emit:a}){const t=a,o=pt(),{globalSearchShortcutKey:s,preferencesButtonPosition:n}=ut(),r=Ke(),{refresh:i}=wl(),d=y(()=>{const v=[{index:tt+100,name:"user-dropdown"}];return U.widget.globalSearch&&v.push({index:tt,name:"global-search"}),n.value.header&&v.push({index:tt+10,name:"preferences"}),U.widget.themeToggle&&v.push({index:tt+20,name:"theme-toggle"}),U.widget.languageToggle&&v.push({index:tt+30,name:"language-toggle"}),U.widget.fullscreen&&v.push({index:tt+40,name:"fullscreen"}),U.widget.notification&&v.push({index:tt+50,name:"notification"}),Object.keys(r).forEach(h=>{const b=h.split("-");h.startsWith("header-right")&&v.push({index:Number(b[2]),name:h})}),v.sort((h,b)=>h.index-b.index)}),p=y(()=>{const v=[];return U.widget.refresh&&v.push({index:0,name:"refresh"}),Object.keys(r).forEach(h=>{const b=h.split("-");h.startsWith("header-left")&&v.push({index:Number(b[2]),name:h})}),v.sort((h,b)=>h.index-b.index)});function f(){t("clearPreferencesAndLogout")}return(v,h)=>(u(),k(J,null,[(u(!0),k(J,null,de(p.value.filter(b=>b.index<tt),b=>E(v.$slots,b.name,{key:b.name},()=>[b.name==="refresh"?(u(),x(e(nt),{key:0,class:"my-0 mr-1 rounded-md",onClick:e(i)},{default:c(()=>[m(e(fa),{class:"size-4"})]),_:1},8,["onClick"])):I("",!0)],!0)),128)),B("div",zu,[E(v.$slots,"breadcrumb",{},void 0,!0)]),(u(!0),k(J,null,de(p.value.filter(b=>b.index>tt),b=>E(v.$slots,b.name,{key:b.name},void 0,!0)),128)),B("div",{class:N([`menu-align-${e(U).header.menuAlign}`,"flex h-full min-w-0 flex-1 items-center"])},[E(v.$slots,"menu",{},void 0,!0)],2),B("div",Pu,[(u(!0),k(J,null,de(d.value,b=>E(v.$slots,b.name,{key:b.name},()=>[b.name==="global-search"?(u(),x(e(li),{key:0,"enable-shortcut-key":e(s),menus:e(o).accessMenus,class:"mr-1 sm:mr-4"},null,8,["enable-shortcut-key","menus"])):b.name==="preferences"?(u(),x(e(uu),{key:1,class:"mr-1",onClearPreferencesAndLogout:f})):b.name==="theme-toggle"?(u(),x(e(Fn),{key:2,class:"mr-1 mt-[2px]"})):b.name==="language-toggle"?(u(),x(e(Kn),{key:3,class:"mr-1"})):b.name==="fullscreen"?(u(),x(e(yr),{key:4,class:"mr-1"})):I("",!0)],!0)),128))])],64))}}),Iu=$e(Au,[["__scopeId","data-v-7670467e"]]),Uu={class:"relative mr-1 flex size-1.5"},Hu=L({__name:"menu-badge-dot",props:{dotClass:{default:""},dotStyle:{default:()=>({})}},setup(l){return(a,t)=>(u(),k("span",Uu,[B("span",{class:N([a.dotClass,"absolute inline-flex h-full w-full animate-ping rounded-full opacity-75"]),style:ce(a.dotStyle)},null,6),B("span",{class:N([a.dotClass,"relative inline-flex size-1.5 rounded-full"]),style:ce(a.dotStyle)},null,6)]))}}),Tl=L({__name:"menu-badge",props:{hasChildren:{type:Boolean},badge:{},badgeType:{},badgeVariants:{}},setup(l){const a=l,t={default:"bg-green-500",destructive:"bg-destructive",primary:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500"},o=y(()=>a.badgeType==="dot"),s=y(()=>{const{badgeVariants:r}=a;return r?t[r]||r:t.default}),n=y(()=>s.value&&An(s.value)?{backgroundColor:s.value}:{});return(r,i)=>o.value||r.badge?(u(),k("span",{key:0,class:N([r.$attrs.class,"absolute"])},[o.value?(u(),x(Hu,{key:0,"dot-class":s.value,"dot-style":n.value},null,8,["dot-class","dot-style"])):(u(),k("div",{key:1,class:N([s.value,"text-primary-foreground flex-center rounded-xl px-1.5 py-0.5 text-[10px]"]),style:ce(n.value)},T(r.badge),7))],2)):I("",!0)}}),Ou=["onClick","onMouseenter"],Du=L({name:"NormalMenu",__name:"normal-menu",props:{activePath:{default:""},collapse:{type:Boolean,default:!1},menus:{default:()=>[]},rounded:{type:Boolean},theme:{default:"dark"}},emits:["enter","select"],setup(l,{emit:a}){const t=l,o=a,{b:s,e:n,is:r}=st("normal-menu");function i(d){return t.activePath===d.path&&d.activeIcon||d.icon}return(d,p)=>(u(),k("ul",{class:N([[d.theme,e(s)(),e(r)("collapse",d.collapse),e(r)(d.theme,!0),e(r)("rounded",d.rounded)],"relative"])},[(u(!0),k(J,null,de(d.menus,f=>(u(),k("li",{key:f.path,class:N([e(n)("item"),e(r)("active",d.activePath===f.path)]),onClick:()=>o("select",f),onMouseenter:()=>o("enter",f)},[m(e(De),{class:N(e(n)("icon")),icon:i(f),fallback:""},null,8,["class","icon"]),B("span",{class:N([e(n)("name"),"truncate"])},T(f.name),3)],42,Ou))),128))],2))}}),Wu=$e(Du,[["__scopeId","data-v-3ebda870"]]);function _l(l,a){var o,s;let t=l.parent;for(;t&&!a.includes((s=(o=t==null?void 0:t.type)==null?void 0:o.name)!=null?s:"");)t=t.parent;return t}const Ot=l=>{const a=Array.isArray(l)?l:[l],t=[];return a.forEach(o=>{var s;Array.isArray(o)?t.push(...Ot(o)):Aa(o)&&Array.isArray(o.children)?t.push(...Ot(o.children)):(t.push(o),Aa(o)&&((s=o.component)!=null&&s.subTree)&&t.push(...Ot(o.component.subTree)))}),t};function $l(){const l=jt();if(!l)throw new Error("instance is required");const a=y(()=>{var n;let o=l.parent;const s=[l.props.path];for(;(o==null?void 0:o.type.name)!=="Menu";)o!=null&&o.props.path&&s.unshift(o.props.path),o=(n=o==null?void 0:o.parent)!=null?n:null;return s});return{parentMenu:y(()=>_l(l,["Menu","SubMenu"])),parentPaths:a}}function Bl(l){return y(()=>{var t;return{"--menu-level":l?(t=l==null?void 0:l.level)!=null?t:1:0}})}const Vl=Symbol("menuContext");function Ru(l){Kt(Vl,l)}function Ll(l){const a=jt();Kt(`subMenu:${a==null?void 0:a.uid}`,l)}function ba(){if(!jt())throw new Error("instance is required");return Nt(Vl)}function El(){const l=jt();if(!l)throw new Error("instance is required");const a=_l(l,["Menu","SubMenu"]);return Nt(`subMenu:${a==null?void 0:a.uid}`)}const Nu=L({name:"MenuItem",__name:"menu-item",props:{activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},emits:["click"],setup(l,{emit:a}){const t=l,o=a,s=Ke(),{b:n,e:r,is:i}=st("menu-item"),d=st("menu"),p=ba(),f=El(),{parentMenu:v,parentPaths:h}=$l(),b=y(()=>t.path===(p==null?void 0:p.activePath)),C=y(()=>b.value&&t.activeIcon||t.icon),H=y(()=>{var P;return((P=v.value)==null?void 0:P.type.name)==="Menu"}),O=y(()=>{var P;return((P=p.props)==null?void 0:P.collapseShowTitle)&&H.value&&p.props.collapse}),M=y(()=>{var P;return p.props.mode==="vertical"&&H.value&&((P=p.props)==null?void 0:P.collapse)&&s.title}),W=mt({active:b,parentPaths:h.value,path:t.path||""});function F(){var P;t.disabled||((P=p==null?void 0:p.handleMenuItemClick)==null||P.call(p,{parentPaths:h.value,path:t.path}),o("click",W))}return Ye(()=>{var P,V;(P=f==null?void 0:f.addSubMenu)==null||P.call(f,W),(V=p==null?void 0:p.addMenuItem)==null||V.call(p,W)}),ia(()=>{var P,V;(P=f==null?void 0:f.removeSubMenu)==null||P.call(f,W),(V=p==null?void 0:p.removeMenuItem)==null||V.call(p,W)}),(P,V)=>(u(),k("li",{class:N([e(p).theme,e(n)(),e(i)("active",b.value),e(i)("disabled",P.disabled),e(i)("collapse-show-title",O.value)]),role:"menuitem",onClick:_e(F,["stop"])},[M.value?(u(),x(e(ft),{key:0,"content-class":[e(p).theme],side:"right"},{trigger:c(()=>[B("div",{class:N([e(d).be("tooltip","trigger")])},[m(e(De),{class:N(e(d).e("icon")),icon:C.value,fallback:""},null,8,["class","icon"]),E(P.$slots,"default"),O.value?(u(),k("span",{key:0,class:N(e(d).e("name"))},[E(P.$slots,"title")],2)):I("",!0)],2)]),default:c(()=>[E(P.$slots,"title")]),_:3},8,["content-class"])):I("",!0),Le(B("div",{class:N([e(r)("content")])},[e(p).props.mode!=="horizontal"?(u(),x(e(Tl),ve({key:0,class:"right-2"},t),null,16)):I("",!0),m(e(De),{class:N(e(d).e("icon")),icon:C.value},null,8,["class","icon"]),E(P.$slots,"default"),E(P.$slots,"title")],2),[[Pe,!M.value]])],2))}});function Fu(l,a={}){const{enable:t=!0,delay:o=320}=a;function s(){if(!(typeof t=="boolean"?t:t.value))return;const i=document.querySelector("aside li[role=menuitem].is-active");i&&i.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}const n=oa(s,o);return me(l,()=>{(typeof t=="boolean"?t:t.value)&&n()}),{scrollToActiveItem:s}}const Ku=L({name:"CollapseTransition",__name:"collapse-transition",setup(l){const a=o=>{o.style.maxHeight="",o.style.overflow=o.dataset.oldOverflow,o.style.paddingTop=o.dataset.oldPaddingTop,o.style.paddingBottom=o.dataset.oldPaddingBottom},t={afterEnter(o){o.style.maxHeight="",o.style.overflow=o.dataset.oldOverflow},afterLeave(o){a(o)},beforeEnter(o){o.dataset||(o.dataset={}),o.dataset.oldPaddingTop=o.style.paddingTop,o.dataset.oldMarginTop=o.style.marginTop,o.dataset.oldPaddingBottom=o.style.paddingBottom,o.dataset.oldMarginBottom=o.style.marginBottom,o.style.height&&(o.dataset.elExistsHeight=o.style.height),o.style.maxHeight=0,o.style.paddingTop=0,o.style.marginTop=0,o.style.paddingBottom=0,o.style.marginBottom=0},beforeLeave(o){o.dataset||(o.dataset={}),o.dataset.oldPaddingTop=o.style.paddingTop,o.dataset.oldMarginTop=o.style.marginTop,o.dataset.oldPaddingBottom=o.style.paddingBottom,o.dataset.oldMarginBottom=o.style.marginBottom,o.dataset.oldOverflow=o.style.overflow,o.style.maxHeight=`${o.scrollHeight}px`,o.style.overflow="hidden"},enter(o){requestAnimationFrame(()=>{o.dataset.oldOverflow=o.style.overflow,o.dataset.elExistsHeight?o.style.maxHeight=o.dataset.elExistsHeight:o.scrollHeight===0?o.style.maxHeight=0:o.style.maxHeight=`${o.scrollHeight}px`,o.style.paddingTop=o.dataset.oldPaddingTop,o.style.paddingBottom=o.dataset.oldPaddingBottom,o.style.marginTop=o.dataset.oldMarginTop,o.style.marginBottom=o.dataset.oldMarginBottom,o.style.overflow="hidden"})},enterCancelled(o){a(o)},leave(o){o.scrollHeight!==0&&(o.style.maxHeight=0,o.style.paddingTop=0,o.style.paddingBottom=0,o.style.marginTop=0,o.style.marginBottom=0)},leaveCancelled(o){a(o)}};return(o,s)=>(u(),x(dt,ve({name:"collapse-transition"},il(t)),{default:c(()=>[E(o.$slots,"default")]),_:3},16))}}),ja=L({name:"SubMenuContent",__name:"sub-menu-content",props:{isMenuMore:{type:Boolean,default:!1},isTopLevelMenuSubmenu:{type:Boolean},level:{default:0},activeIcon:{},disabled:{type:Boolean},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){const a=l,t=ba(),{b:o,e:s,is:n}=st("sub-menu-content"),r=st("menu"),i=y(()=>t==null?void 0:t.openedMenus.includes(a.path)),d=y(()=>t.props.collapse),p=y(()=>a.level===1),f=y(()=>t.props.collapseShowTitle&&p.value&&d.value),v=y(()=>t==null?void 0:t.props.mode),h=y(()=>v.value==="horizontal"||!(p.value&&d.value)),b=y(()=>v.value==="vertical"&&p.value&&d.value&&!f.value),C=y(()=>v.value==="horizontal"&&!p.value||v.value==="vertical"&&d.value?na:sa),H=y(()=>i.value?{transform:"rotate(180deg)"}:{});return(O,M)=>(u(),k("div",{class:N([e(o)(),e(n)("collapse-show-title",f.value),e(n)("more",O.isMenuMore)])},[E(O.$slots,"default"),O.isMenuMore?I("",!0):(u(),x(e(De),{key:0,class:N(e(r).e("icon")),icon:O.icon,fallback:""},null,8,["class","icon"])),b.value?I("",!0):(u(),k("div",{key:1,class:N([e(s)("title")])},[E(O.$slots,"title")],2)),O.isMenuMore?I("",!0):Le((u(),x(ze(C.value),{key:2,class:N([[e(s)("icon-arrow")],"size-4"]),style:ce(H.value)},null,8,["class","style"])),[[Pe,h.value]])],2))}}),zl=L({name:"SubMenu",__name:"sub-menu",props:{isSubMenuMore:{type:Boolean,default:!1},activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){var ue;const a=l,{parentMenu:t,parentPaths:o}=$l(),{b:s,is:n}=st("sub-menu"),r=st("menu"),i=ba(),d=El(),p=Bl(d),f=Y(!1),v=Y({}),h=Y({}),b=Y(null);Ll({addSubMenu:K,handleMouseleave:q,level:((ue=d==null?void 0:d.level)!=null?ue:0)+1,mouseInChild:f,removeSubMenu:z});const C=y(()=>i==null?void 0:i.openedMenus.includes(a.path)),H=y(()=>{var G;return((G=t.value)==null?void 0:G.type.name)==="Menu"}),O=y(()=>{var G;return(G=i==null?void 0:i.props.mode)!=null?G:"vertical"}),M=y(()=>i==null?void 0:i.props.rounded),W=y(()=>{var G;return(G=d==null?void 0:d.level)!=null?G:0}),F=y(()=>W.value===1),P=y(()=>{const G=O.value==="horizontal",ae=G&&F.value?"bottom":"right";return{collisionPadding:{top:20},side:ae,sideOffset:G?5:10}}),V=y(()=>{let G=!1;return Object.values(v.value).forEach(ae=>{ae.active&&(G=!0)}),Object.values(h.value).forEach(ae=>{ae.active&&(G=!0)}),G});function K(G){h.value[G.path]=G}function z(G){Reflect.deleteProperty(h.value,G.path)}function $(){const G=i==null?void 0:i.props.mode;a.disabled||i!=null&&i.props.collapse&&G==="vertical"||G==="horizontal"||i==null||i.handleSubMenuClick({active:V.value,parentPaths:o.value,path:a.path})}function R(G,ae=300){var xe,Ce;if(G.type!=="focus"){if(!(i!=null&&i.props.collapse)&&(i==null?void 0:i.props.mode)==="vertical"||a.disabled){d&&(d.mouseInChild.value=!0);return}d&&(d.mouseInChild.value=!0),b.value&&window.clearTimeout(b.value),b.value=setTimeout(()=>{i==null||i.openMenu(a.path,o.value)},ae),(Ce=(xe=t.value)==null?void 0:xe.vnode.el)==null||Ce.dispatchEvent(new MouseEvent("mouseenter"))}}function q(G=!1){var ae;if(!(i!=null&&i.props.collapse)&&(i==null?void 0:i.props.mode)==="vertical"&&d){d.mouseInChild.value=!1;return}b.value&&window.clearTimeout(b.value),d&&(d.mouseInChild.value=!1),b.value=setTimeout(()=>{!f.value&&(i==null||i.closeMenu(a.path,o.value))},300),G&&((ae=d==null?void 0:d.handleMouseleave)==null||ae.call(d,!0))}const Z=y(()=>V.value&&a.activeIcon||a.icon),re=mt({active:V,parentPaths:o,path:a.path});return Ye(()=>{var G,ae;(G=d==null?void 0:d.addSubMenu)==null||G.call(d,re),(ae=i==null?void 0:i.addSubMenu)==null||ae.call(i,re)}),ia(()=>{var G,ae;(G=d==null?void 0:d.removeSubMenu)==null||G.call(d,re),(ae=i==null?void 0:i.removeSubMenu)==null||ae.call(i,re)}),(G,ae)=>(u(),k("li",{class:N([e(s)(),e(n)("opened",C.value),e(n)("active",V.value),e(n)("disabled",G.disabled)]),onFocus:R,onMouseenter:R,onMouseleave:ae[3]||(ae[3]=()=>q())},[e(i).isMenuPopup?(u(),x(e(wr),{key:0,"content-class":[e(i).theme,e(r).e("popup-container"),e(n)(e(i).theme,!0),C.value?"":"hidden","overflow-auto","max-h-[calc(var(--radix-hover-card-content-available-height)-20px)]"],"content-props":P.value,open:!0,"open-delay":0},{trigger:c(()=>[m(ja,{class:N(e(n)("active",V.value)),icon:Z.value,"is-menu-more":G.isSubMenuMore,"is-top-level-menu-submenu":H.value,level:W.value,path:G.path,onClick:_e($,["stop"])},{title:c(()=>[E(G.$slots,"title")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"])]),default:c(()=>[B("div",{class:N([e(r).is(O.value,!0),e(r).e("popup")]),onFocus:ae[0]||(ae[0]=xe=>R(xe,100)),onMouseenter:ae[1]||(ae[1]=xe=>R(xe,100)),onMouseleave:ae[2]||(ae[2]=()=>q(!0))},[B("ul",{class:N([e(r).b(),e(n)("rounded",M.value)]),style:ce(e(p))},[E(G.$slots,"default")],6)],34)]),_:3},8,["content-class","content-props"])):(u(),k(J,{key:1},[m(ja,{class:N(e(n)("active",V.value)),icon:Z.value,"is-menu-more":G.isSubMenuMore,"is-top-level-menu-submenu":H.value,level:W.value,path:G.path,onClick:_e($,["stop"])},{title:c(()=>[E(G.$slots,"title")]),default:c(()=>[E(G.$slots,"content")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"]),m(Ku,null,{default:c(()=>[Le(B("ul",{class:N([e(r).b(),e(n)("rounded",M.value)]),style:ce(e(p))},[E(G.$slots,"default")],6),[[Pe,C.value]])]),_:3})],64))],34))}}),ju=L({name:"Menu",__name:"menu",props:{accordion:{type:Boolean,default:!0},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{default:"vertical"},rounded:{type:Boolean,default:!0},scrollToActive:{type:Boolean,default:!1},theme:{default:"dark"}},emits:["close","open","select"],setup(l,{emit:a}){const t=l,o=a,{b:s,is:n}=st("menu"),r=Bl(),i=Ke(),d=Y(),p=Y(-1),f=Y(t.defaultOpeneds&&!t.collapse?[...t.defaultOpeneds]:[]),v=Y(t.defaultActive),h=Y({}),b=Y({}),C=Y(!1),H=y(()=>t.mode==="horizontal"||t.mode==="vertical"&&t.collapse),O=y(()=>{var Se,Ae;const j=(Ae=(Se=i.default)==null?void 0:Se.call(i))!=null?Ae:[],ne=Ot(j),ee=p.value===-1?ne:ne.slice(0,p.value),ge=p.value===-1?[]:ne.slice(p.value);return{showSlotMore:ge.length>0,slotDefault:ee,slotMore:ge}});me(()=>t.collapse,j=>{j&&(f.value=[])}),me(h.value,R),me(()=>t.defaultActive,(j="")=>{h.value[j]||(v.value=""),q(j)});let M;ra(()=>{t.mode==="horizontal"?M=Un(d,K).stop:M==null||M()}),Ru(mt({activePath:v,addMenuItem:xe,addSubMenu:Ce,closeMenu:G,handleMenuItemClick:Z,handleSubMenuClick:re,isMenuPopup:H,openedMenus:f,openMenu:ae,props:t,removeMenuItem:Oe,removeSubMenu:Ve,subMenus:b,theme:In(t,"theme"),items:h})),Ll({addSubMenu:Ce,level:1,mouseInChild:C,removeSubMenu:Ve});function W(j){const ne=getComputedStyle(j),ee=Number.parseInt(ne.marginLeft,10),ge=Number.parseInt(ne.marginRight,10);return j.offsetWidth+ee+ge||0}function F(){var pe,Xe,Je;if(!d.value)return-1;const j=[...(Xe=(pe=d.value)==null?void 0:pe.childNodes)!=null?Xe:[]].filter(Ee=>Ee.nodeName!=="#comment"&&(Ee.nodeName!=="#text"||Ee.nodeValue)),ne=46,ee=getComputedStyle(d==null?void 0:d.value),ge=Number.parseInt(ee.paddingLeft,10),Se=Number.parseInt(ee.paddingRight,10),Ae=((Je=d.value)==null?void 0:Je.clientWidth)-ge-Se;let ie=0,be=0;return j.forEach((Ee,D)=>{ie+=W(Ee),ie<=Ae-ne&&(be=D+1)}),be===j.length?-1:be}function P(j,ne=33.34){let ee;return()=>{ee&&clearTimeout(ee),ee=setTimeout(()=>{j()},ne)}}let V=!0;function K(){if(p.value===F())return;const j=()=>{p.value=-1,Ne(()=>{p.value=F()})};j(),V?j():P(j)(),V=!1}const z=y(()=>t.scrollToActive&&t.mode==="vertical"&&!t.collapse),{scrollToActiveItem:$}=Fu(v,{enable:z,delay:320});me(v,()=>{$()});function R(){Be().forEach(ne=>{const ee=b.value[ne];ee&&ae(ne,ee.parentPaths)})}function q(j){const ne=h.value,ee=ne[j]||v.value&&ne[v.value]||ne[t.defaultActive||""];v.value=ee?ee.path:j}function Z(j){const{collapse:ne,mode:ee}=t;(ee==="horizontal"||ne)&&(f.value=[]);const{parentPaths:ge,path:Se}=j;!Se||!ge||o("select",Se,ge)}function re({parentPaths:j,path:ne}){f.value.includes(ne)?G(ne,j):ae(ne,j)}function ue(j){const ne=f.value.indexOf(j);ne!==-1&&f.value.splice(ne,1)}function G(j,ne){var ee,ge;t.accordion&&(f.value=(ge=(ee=b.value[j])==null?void 0:ee.parentPaths)!=null?ge:[]),ue(j),o("close",j,ne)}function ae(j,ne){if(!f.value.includes(j)){if(t.accordion){const ee=Be();ee.includes(j)&&(ne=ee),f.value=f.value.filter(ge=>ne.includes(ge))}f.value.push(j),o("open",j,ne)}}function xe(j){h.value[j.path]=j}function Ce(j){b.value[j.path]=j}function Ve(j){Reflect.deleteProperty(b.value,j.path)}function Oe(j){Reflect.deleteProperty(h.value,j.path)}function Be(){const j=v.value&&h.value[v.value];return!j||t.mode==="horizontal"||t.collapse?[]:j.parentPaths}return(j,ne)=>(u(),k("ul",{ref_key:"menu",ref:d,class:N([j.theme,e(s)(),e(n)(j.mode,!0),e(n)(j.theme,!0),e(n)("rounded",j.rounded),e(n)("collapse",j.collapse),e(n)("menu-align",j.mode==="horizontal")]),style:ce(e(r)),role:"menu"},[j.mode==="horizontal"&&O.value.showSlotMore?(u(),k(J,{key:0},[(u(!0),k(J,null,de(O.value.slotDefault,ee=>(u(),x(ze(ee),{key:ee.key}))),128)),m(zl,{"is-sub-menu-more":"",path:"sub-menu-more"},{title:c(()=>[m(e(sn),{class:"size-4"})]),default:c(()=>[(u(!0),k(J,null,de(O.value.slotMore,ee=>(u(),x(ze(ee),{key:ee.key}))),128))]),_:1})],64)):E(j.$slots,"default",{key:1})],6))}}),Pl=L({name:"SubMenuUi",__name:"sub-menu",props:{menu:{}},setup(l){const a=l,t=y(()=>{const{menu:o}=a;return Reflect.has(o,"children")&&!!o.children&&o.children.length>0});return(o,s)=>t.value?(u(),x(e(zl),{key:`${o.menu.path}_sub`,"active-icon":o.menu.activeIcon,icon:o.menu.icon,path:o.menu.path},{content:c(()=>[m(e(Tl),{badge:o.menu.badge,"badge-type":o.menu.badgeType,"badge-variants":o.menu.badgeVariants,class:"right-6"},null,8,["badge","badge-type","badge-variants"])]),title:c(()=>[B("span",null,T(o.menu.name),1)]),default:c(()=>[(u(!0),k(J,null,de(o.menu.children||[],n=>(u(),x(Pl,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},8,["active-icon","icon","path"])):(u(),x(e(Nu),{key:o.menu.path,"active-icon":o.menu.activeIcon,badge:o.menu.badge,"badge-type":o.menu.badgeType,"badge-variants":o.menu.badgeVariants,icon:o.menu.icon,path:o.menu.path},{title:c(()=>[B("span",null,T(o.menu.name),1)]),_:1},8,["active-icon","badge","badge-type","badge-variants","icon","path"]))}}),Al=L({name:"MenuView",__name:"menu",props:{menus:{},accordion:{type:Boolean},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(l){const t=at(l);return(o,s)=>(u(),x(e(ju),Fe(rt(e(t))),{default:c(()=>[(u(!0),k(J,null,de(o.menus,n=>(u(),x(Pl,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},16))}});function va(){const l=ct(),a=new Map,t=()=>{l.getRoutes().forEach(i=>{a.set(i.path,i)})};t(),l.afterEach(()=>{t()});const o=r=>{var d,p;if(la(r))return!0;const i=a.get(r);return(p=(d=i==null?void 0:i.meta)==null?void 0:d.openInNewWindow)!=null?p:!1};return{navigation:r=>X(null,null,function*(){var i;try{const d=a.get(r),{openInNewWindow:p=!1,query:f={}}=(i=d==null?void 0:d.meta)!=null?i:{};la(r)?Hn(r,{target:"_blank"}):p?nl(r):yield l.push({path:r,query:f})}catch(d){throw console.error("Navigation failed:",d),d}}),willOpenedByWindow:r=>o(r)}}const Gu=L({__name:"extra-menu",props:{collapse:{type:Boolean},menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(l){const a=lt(),{navigation:t}=va();function o(s){return X(this,null,function*(){yield t(s)})}return(s,n)=>{var r;return u(),x(e(Al),{accordion:s.accordion,collapse:s.collapse,"default-active":((r=e(a).meta)==null?void 0:r.activePath)||e(a).path,menus:s.menus,rounded:s.rounded,theme:s.theme,mode:"vertical",onSelect:o},null,8,["accordion","collapse","default-active","menus","rounded","theme"])}}}),Ga=L({__name:"menu",props:{menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapse:{type:Boolean},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},emits:["open","select"],setup(l,{emit:a}){const t=l,o=a;function s(r){o("select",r,t.mode)}function n(r,i){o("open",r,i)}return(r,i)=>(u(),x(e(Al),{accordion:r.accordion,collapse:r.collapse,"collapse-show-title":r.collapseShowTitle,"default-active":r.defaultActive,menus:r.menus,mode:r.mode,rounded:r.rounded,"scroll-to-active":"",theme:r.theme,onOpen:n,onSelect:s},null,8,["accordion","collapse","collapse-show-title","default-active","menus","mode","rounded","theme"]))}}),qu=L({__name:"mixed-menu",props:{activePath:{},collapse:{type:Boolean},menus:{},rounded:{type:Boolean},theme:{}},emits:["defaultSelect","enter","select"],setup(l,{emit:a}){const t=l,o=a,s=lt();return dl(()=>{const n=ma(t.menus||[],s.path);if(n){const r=(t.menus||[]).find(i=>{var d;return i.path===((d=n.parents)==null?void 0:d[0])});o("defaultSelect",n,r)}}),(n,r)=>(u(),x(e(Wu),{"active-path":n.activePath,collapse:n.collapse,menus:n.menus,rounded:n.rounded,theme:n.theme,onEnter:r[0]||(r[0]=i=>o("enter",i)),onSelect:r[1]||(r[1]=i=>o("select",i))},null,8,["active-path","collapse","menus","rounded","theme"]))}});function Yu(l){const a=pt(),{navigation:t,willOpenedByWindow:o}=va(),s=y(()=>{var M;return(M=l==null?void 0:l.value)!=null?M:a.accessMenus}),n=new Map,r=Y([]),i=lt(),d=Y([]),p=Y(!1),f=Y(""),v=y(()=>U.app.layout==="header-mixed-nav"?1:0),h=M=>X(null,null,function*(){var P,V,K;const W=(P=M==null?void 0:M.children)!=null?P:[],F=W.length>0;o(M.path)||(d.value=W!=null?W:[],f.value=(K=(V=M.parents)==null?void 0:V[v.value])!=null?K:M.path,p.value=F),F?U.sidebar.autoActivateChild&&(yield t(n.has(M.path)?n.get(M.path):M.path)):yield t(M.path)}),b=(M,W)=>X(null,null,function*(){var F,P,V,K;d.value=(P=(F=W==null?void 0:W.children)!=null?F:r.value)!=null?P:[],f.value=(K=(V=M.parents)==null?void 0:V[v.value])!=null?K:M.path,U.sidebar.expandOnHover&&(p.value=d.value.length>0)}),C=()=>{var P,V;if(U.sidebar.expandOnHover)return;const{findMenu:M,rootMenu:W,rootMenuPath:F}=Tt(s.value,i.path);f.value=(P=F!=null?F:M==null?void 0:M.path)!=null?P:"",d.value=(V=W==null?void 0:W.children)!=null?V:[]},H=M=>{var W,F,P;if(!U.sidebar.expandOnHover){const{findMenu:V}=Tt(s.value,M.path);d.value=(W=V==null?void 0:V.children)!=null?W:[],f.value=(P=(F=M.parents)==null?void 0:F[v.value])!=null?P:M.path,p.value=d.value.length>0}};function O(M){var K,z,$,R;const W=((K=i.meta)==null?void 0:K.activePath)||M,{findMenu:F,rootMenu:P,rootMenuPath:V}=Tt(s.value,W,v.value);r.value=(z=P==null?void 0:P.children)!=null?z:[],V&&n.set(V,W),f.value=($=V!=null?V:F==null?void 0:F.path)!=null?$:"",d.value=(R=P==null?void 0:P.children)!=null?R:[],U.sidebar.expandOnHover&&(p.value=d.value.length>0)}return me(()=>[i.path,U.app.layout],([M])=>{O(M||"")},{immediate:!0}),{extraActiveMenu:f,extraMenus:d,handleDefaultSelect:b,handleMenuMouseEnter:H,handleMixedMenuSelect:h,handleSideMouseLeave:C,sidebarExtraVisible:p}}function Xu(){const{navigation:l,willOpenedByWindow:a}=va(),t=pt(),o=lt(),s=Y([]),n=Y(""),r=Y(""),i=Y([]),d=new Map,{isMixedNav:p,isHeaderMixedNav:f}=ut(),v=y(()=>U.navigation.split&&p.value||f.value),h=y(()=>{const K=U.sidebar.enable;return v.value?K&&s.value.length>0:K}),b=y(()=>t.accessMenus),C=y(()=>v.value?b.value.map(K=>we(Q({},K),{children:[]})):b.value),H=y(()=>v.value?s.value:b.value),O=y(()=>f.value?H.value:C.value),M=y(()=>{var K,z;return(z=(K=o==null?void 0:o.meta)==null?void 0:K.activePath)!=null?z:o.path}),W=y(()=>{var K,z;return v.value?n.value:(z=(K=o.meta)==null?void 0:K.activePath)!=null?z:o.path}),F=(K,z)=>{var q,Z;if(!v.value||z==="vertical"){l(K);return}const $=b.value.find(re=>re.path===K),R=(q=$==null?void 0:$.children)!=null?q:[];a(K)||(n.value=(Z=$==null?void 0:$.path)!=null?Z:"",s.value=R),R.length===0?l(K):$&&U.sidebar.autoActivateChild&&l(d.has($.path)?d.get($.path):$.path)},P=(K,z)=>{z.length<=1&&U.sidebar.autoActivateChild&&l(d.has(K)?d.get(K):K)};function V(K=o.path){var R,q,Z,re,ue;let{rootMenu:z}=Tt(b.value,K);z||(z=b.value.find(G=>G.path===K));const $=Tt((z==null?void 0:z.children)||[],K,1);r.value=(R=$.rootMenuPath)!=null?R:"",i.value=(Z=(q=$.rootMenu)==null?void 0:q.children)!=null?Z:[],n.value=(re=z==null?void 0:z.path)!=null?re:"",s.value=(ue=z==null?void 0:z.children)!=null?ue:[]}return me(()=>o.path,K=>{var $,R,q,Z;const z=(Z=(q=($=o==null?void 0:o.meta)==null?void 0:$.activePath)!=null?q:(R=o==null?void 0:o.meta)==null?void 0:R.link)!=null?Z:K;a(z)||(V(z),n.value&&d.set(n.value,z))},{immediate:!0}),dl(()=>{var K;V(((K=o.meta)==null?void 0:K.activePath)||o.path)}),{handleMenuSelect:F,handleMenuOpen:P,headerActive:W,headerMenus:C,sidebarActive:M,sidebarMenus:H,mixHeaderMenus:O,mixExtraMenus:i,sidebarVisible:h}}const Ju={class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold"},Zu=L({__name:"tool-more",props:{menus:{}},setup(l){return(a,t)=>(u(),x(e(gr),{menus:a.menus,modal:!1},{default:c(()=>[B("div",Ju,[m(e(sa),{class:"size-4"})])]),_:1},8,["menus"]))}}),Qu=L({__name:"tool-screen",props:{screen:{type:Boolean},screenModifiers:{}},emits:["update:screen"],setup(l){const a=w(l,"screen");function t(){a.value=!a.value}return(o,s)=>(u(),k("div",{class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold",onClick:t},[a.value?(u(),x(e(hl),{key:0,class:"size-4"})):(u(),x(e(fl),{key:1,class:"size-4"}))]))}}),ec=["data-active-tab","data-index","onClick","onMousedown"],tc={class:"relative size-full px-1"},ac={key:0,class:"tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"},lc={class:"tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"},oc={class:"tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"},nc={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},sc=L({name:"VbenTabsChrome",inheritAttrs:!1,__name:"tabs",props:ye({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{default:7},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:ye(["close","unpin"],["update:active"]),setup(l,{emit:a}){const t=l,o=a,s=w(l,"active"),n=Y(),r=Y(),i=y(()=>{const{gap:f}=t;return{"--gap":`${f}px`}}),d=y(()=>t.tabs.map(f=>{const{fullPath:v,meta:h,name:b,path:C,key:H}=f||{},{affixTab:O,icon:M,newTabTitle:W,tabClosable:F,title:P}=h||{};return{affixTab:!!O,closable:Reflect.has(h,"tabClosable")?!!F:!0,fullPath:v,icon:M,key:H,meta:h,name:b,path:C,title:W||P||b}}));function p(f,v){f.button===1&&v.closable&&!v.affixTab&&d.value.length>1&&t.middleClickToClose&&(f.preventDefault(),f.stopPropagation(),o("close",v.key))}return(f,v)=>(u(),k("div",{ref_key:"contentRef",ref:n,class:N([f.contentClass,"tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"]),style:ce(i.value)},[m(Rt,{name:"slide-left"},{default:c(()=>[(u(!0),k(J,null,de(d.value,(h,b)=>(u(),k("div",{key:h.key,ref_for:!0,ref_key:"tabRef",ref:r,class:N([[{"is-active":h.key===s.value,draggable:!h.affixTab,"affix-tab":h.affixTab}],"tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"]),"data-active-tab":s.value,"data-index":b,"data-tab-item":"true",onClick:C=>s.value=h.key,onMousedown:C=>p(C,h)},[m(e(yl),{"handler-data":h,menus:f.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[B("div",tc,[b!==0&&h.key!==s.value?(u(),k("div",ac)):I("",!0),v[0]||(v[0]=B("div",{class:"tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"},[B("div",{class:"tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"}),B("svg",{class:"tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[B("path",{d:"M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z"})]),B("svg",{class:"tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[B("path",{d:"M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z"})])],-1)),B("div",lc,[Le(m(e(Et),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all",onClick:_e(()=>o("close",h.key),["stop"])},null,8,["onClick"]),[[Pe,!h.affixTab&&d.value.length>1&&h.closable]]),Le(m(e(Gt),{class:"hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:_e(()=>o("unpin",h),["stop"])},null,8,["onClick"]),[[Pe,h.affixTab&&d.value.length>1&&h.closable]])]),B("div",oc,[f.showIcon?(u(),x(e(De),{key:0,icon:h.icon,class:"mr-1 flex size-4 items-center overflow-hidden"},null,8,["icon"])):I("",!0),B("span",nc,T(h.title),1)])])]),_:2},1032,["handler-data","menus"])],42,ec))),128))]),_:1})],6))}}),rc=$e(sc,[["__scopeId","data-v-08fc8c7f"]]),ic=["data-index","onClick","onMousedown"],dc={class:"relative flex size-full items-center"},uc={class:"absolute right-1.5 top-1/2 z-[3] translate-y-[-50%] overflow-hidden"},cc={class:"text-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mx-3 mr-4 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pr-3 transition-all duration-300"},pc={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},fc=L({name:"VbenTabs",inheritAttrs:!1,__name:"tabs",props:ye({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:ye(["close","unpin"],["update:active"]),setup(l,{emit:a}){const t=l,o=a,s=w(l,"active"),n=y(()=>({brisk:{content:"h-full after:content-['']  after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1.5px] after:bg-primary after:scale-x-0 after:transition-[transform] after:ease-out after:duration-300 hover:after:scale-x-100 after:origin-left [&.is-active]:after:scale-x-100 [&:not(:first-child)]:border-l last:border-r last:border-r border-border"},card:{content:"h-[calc(100%-6px)] rounded-md ml-2 border border-border  transition-all"},plain:{content:"h-full [&:not(:first-child)]:border-l last:border-r border-border"}})[t.styleType||"plain"]||{content:""}),r=y(()=>t.tabs.map(d=>{const{fullPath:p,meta:f,name:v,path:h,key:b}=d||{},{affixTab:C,icon:H,newTabTitle:O,tabClosable:M,title:W}=f||{};return{affixTab:!!C,closable:Reflect.has(f,"tabClosable")?!!M:!0,fullPath:p,icon:H,key:b,meta:f,name:v,path:h,title:O||W||v}}));function i(d,p){d.button===1&&p.closable&&!p.affixTab&&r.value.length>1&&t.middleClickToClose&&(d.preventDefault(),d.stopPropagation(),o("close",p.key))}return(d,p)=>(u(),k("div",{class:N([d.contentClass,"relative !flex h-full w-max items-center overflow-hidden pr-6"])},[m(Rt,{name:"slide-left"},{default:c(()=>[(u(!0),k(J,null,de(r.value,(f,v)=>(u(),k("div",{key:f.key,class:N([[{"is-active dark:bg-accent bg-primary/15":f.key===s.value,draggable:!f.affixTab,"affix-tab":f.affixTab},n.value.content],"tab-item [&:not(.is-active)]:hover:bg-accent translate-all group relative flex cursor-pointer select-none"]),"data-index":v,"data-tab-item":"true",onClick:h=>s.value=f.key,onMousedown:h=>i(h,f)},[m(e(yl),{"handler-data":f,menus:d.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[B("div",dc,[B("div",uc,[Le(m(e(Et),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground dark:group-[.is-active]:text-accent-foreground group-[.is-active]:text-primary size-3 cursor-pointer rounded-full transition-all",onClick:_e(()=>o("close",f.key),["stop"])},null,8,["onClick"]),[[Pe,!f.affixTab&&r.value.length>1&&f.closable]]),Le(m(e(Gt),{class:"hover:bg-accent hover:stroke-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:_e(()=>o("unpin",f),["stop"])},null,8,["onClick"]),[[Pe,f.affixTab&&r.value.length>1&&f.closable]])]),B("div",cc,[d.showIcon?(u(),x(e(De),{key:0,icon:f.icon,class:"mr-2 flex size-4 items-center overflow-hidden",fallback:""},null,8,["icon"])):I("",!0),B("span",pc,T(f.title),1)])])]),_:2},1032,["handler-data","menus"])],42,ic))),128))]),_:1})],2))}});function aa(l){const a="group";return l.classList.contains(a)?l:l.closest(`.${a}`)}function mc(l,a){const t=Y(null);function o(){return X(this,null,function*(){var d;yield Ne();const n=(d=document.querySelectorAll(`.${l.contentClass}`))==null?void 0:d[0];if(!n){console.warn("Element not found for sortable initialization");return}const r=()=>X(null,null,function*(){var p;n.style.cursor="default",(p=n.querySelector(".draggable"))==null||p.classList.remove("dragging")}),{initializeSortable:i}=rn(n,{filter:(p,f)=>{const v=aa(f);return!(v==null?void 0:v.classList.contains("draggable"))||!l.draggable},onEnd(p){const{newIndex:f,oldIndex:v}=p,{srcElement:h}=p.originalEvent;if(!h){r();return}const b=aa(h);if(!b){r();return}if(!b.classList.contains("draggable")){r();return}v!==void 0&&f!==void 0&&!Number.isNaN(v)&&!Number.isNaN(f)&&v!==f&&a("sortTabs",v,f),r()},onMove(p){const f=aa(p.related);if(f!=null&&f.classList.contains("draggable")&&l.draggable){const v=p.dragged.classList.contains("affix-tab"),h=p.related.classList.contains("affix-tab");return v===h}else return!1},onStart:()=>{var p;n.style.cursor="grabbing",(p=n.querySelector(".draggable"))==null||p.classList.add("dragging")}});t.value=yield i()})}function s(){return X(this,null,function*(){const{isMobile:n}=Xa();n.value||(yield Ne(),o())})}Ye(s),me(()=>l.styleType,()=>{var n;(n=t.value)==null||n.destroy(),s()}),Lt(()=>{var n;(n=t.value)==null||n.destroy()})}function hc(l){let a=null,t=null,o=0;const s=Y(null),n=Y(null),r=Y(!1),i=Y(!0),d=Y(!1);function p(){var F;const O=(F=s.value)==null?void 0:F.$el;if(!O||!n.value)return{};const M=O.clientWidth,W=n.value.clientWidth;return{scrollbarWidth:M,scrollViewWidth:W}}function f(O,M=150){var P;const{scrollbarWidth:W,scrollViewWidth:F}=p();!W||!F||W>F||(P=n.value)==null||P.scrollBy({behavior:"smooth",left:O==="left"?-(W-M):+(W-M)})}function v(){return X(this,null,function*(){var W,F;yield Ne();const O=(W=s.value)==null?void 0:W.$el;if(!O)return;const M=O==null?void 0:O.querySelector("div[data-radix-scroll-area-viewport]");n.value=M,b(),yield Ne(),h(),a==null||a.disconnect(),a=new ResizeObserver(oa(P=>{b(),h()},100)),a.observe(M),o=((F=l.tabs)==null?void 0:F.length)||0,t==null||t.disconnect(),t=new MutationObserver(()=>{const P=M.querySelectorAll('div[data-tab-item="true"]').length;P>o&&h(),P!==o&&(b(),o=P)}),t.observe(M,{attributes:!1,childList:!0,subtree:!0})})}function h(){return X(this,null,function*(){if(!n.value)return;yield Ne();const O=n.value,{scrollbarWidth:M}=p(),{scrollWidth:W}=O;M>=W||requestAnimationFrame(()=>{const F=O==null?void 0:O.querySelector(".is-active");F==null||F.scrollIntoView({behavior:"smooth",inline:"start"})})})}function b(){return X(this,null,function*(){if(!n.value)return;const{scrollbarWidth:O}=p();r.value=n.value.scrollWidth>O})}const C=oa(({left:O,right:M})=>{i.value=O,d.value=M},100);function H({deltaY:O}){var M;(M=n.value)==null||M.scrollBy({left:O*3})}return me(()=>l.active,()=>X(null,null,function*(){h()}),{flush:"post"}),me(()=>l.styleType,()=>{v()}),Ye(v),Lt(()=>{a==null||a.disconnect(),t==null||t.disconnect(),a=null,t=null}),{handleScrollAt:C,handleWheel:H,initScrollbar:v,scrollbarRef:s,scrollDirection:f,scrollIsAtLeft:i,scrollIsAtRight:d,showScrollButton:r}}const bc={class:"flex h-full flex-1 overflow-hidden"},vc=L({name:"TabsView",__name:"tabs-view",props:{active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{},draggable:{type:Boolean,default:!0},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{default:"chrome"},tabs:{},wheelable:{type:Boolean,default:!0}},emits:["close","sortTabs","unpin"],setup(l,{emit:a}){const t=l,o=a,s=We(t,o),{handleScrollAt:n,handleWheel:r,scrollbarRef:i,scrollDirection:d,scrollIsAtLeft:p,scrollIsAtRight:f,showScrollButton:v}=hc(t);function h(b){t.wheelable&&(r(b),b.stopPropagation(),b.preventDefault())}return mc(t,o),(b,C)=>(u(),k("div",bc,[Le(B("span",{class:N([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(p),"pointer-events-none opacity-30":e(p)},"border-r px-2"]),onClick:C[0]||(C[0]=H=>e(d)("left"))},[m(e(dn),{class:"size-4 h-full"})],2),[[Pe,e(v)]]),B("div",{class:N([{"pt-[3px]":b.styleType==="chrome"},"size-full flex-1 overflow-hidden"])},[m(e(Bt),{ref_key:"scrollbarRef",ref:i,"shadow-bottom":!1,"shadow-top":!1,class:"h-full",horizontal:"","scroll-bar-class":"z-10 hidden ",shadow:"","shadow-left":"","shadow-right":"",onScrollAt:e(n),onWheel:h},{default:c(()=>[b.styleType==="chrome"?(u(),x(e(rc),Fe(ve({key:0},Q(Q(Q({},e(s)),b.$attrs),b.$props))),null,16)):(u(),x(e(fc),Fe(ve({key:1},Q(Q(Q({},e(s)),b.$attrs),b.$props))),null,16))]),_:1},8,["onScrollAt"])],2),Le(B("span",{class:N([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(f),"pointer-events-none opacity-30":e(f)},"hover:bg-muted text-muted-foreground cursor-pointer border-l px-2"]),onClick:C[1]||(C[1]=H=>e(d)("right"))},[m(e(na),{class:"size-4 h-full"})],2),[[Pe,e(v)]])]))}});function gc(){const l=ct(),a=lt(),t=pt(),o=yt(),{contentIsMaximize:s,toggleMaximize:n}=xl(),{closeAllTabs:r,closeCurrentTab:i,closeLeftTabs:d,closeOtherTabs:p,closeRightTabs:f,closeTabByKey:v,getTabDisableState:h,openTabInNewWindow:b,refreshTab:C,toggleTabPin:H}=kl(),O=y(()=>qe(a)),{locale:M}=tl(),W=Y();me([()=>o.getTabs,()=>o.updateTime,()=>M.value],([$])=>{W.value=$.map(R=>K(R))});const F=()=>{const $=On(l.getRoutes(),R=>{var q;return!!((q=R.meta)!=null&&q.affixTab)});o.setAffixTabs($)},P=$=>{const{fullPath:R,path:q}=o.getTabByKey($);l.push(R||q)},V=$=>X(null,null,function*(){yield v($)});function K($){var R;return we(Q({},$),{meta:we(Q({},$==null?void 0:$.meta),{title:g((R=$==null?void 0:$.meta)==null?void 0:R.title)})})}return me(()=>t.accessMenus,()=>{F()},{immediate:!0}),me(()=>a.fullPath,()=>{var R,q;const $=(q=(R=a.matched)==null?void 0:R[a.matched.length-1])==null?void 0:q.meta;o.addTab(we(Q({},a),{meta:$||a.meta}))},{immediate:!0}),{createContextMenus:$=>{var Ce,Ve;const{disabledCloseAll:R,disabledCloseCurrent:q,disabledCloseLeft:Z,disabledCloseOther:re,disabledCloseRight:ue,disabledRefresh:G}=h($),ae=(Ve=(Ce=$==null?void 0:$.meta)==null?void 0:Ce.affixTab)!=null?Ve:!1;return[{disabled:q,handler:()=>X(null,null,function*(){yield i($)}),icon:Et,key:"close",text:g("preferences.tabbar.contextMenu.close")},{handler:()=>X(null,null,function*(){yield H($)}),icon:ae?bl:Gt,key:"affix",text:ae?g("preferences.tabbar.contextMenu.unpin"):g("preferences.tabbar.contextMenu.pin")},{handler:()=>X(null,null,function*(){s.value||(yield l.push($.fullPath)),n()}),icon:s.value?hl:fl,key:s.value?"restore-maximize":"maximize",text:s.value?g("preferences.tabbar.contextMenu.restoreMaximize"):g("preferences.tabbar.contextMenu.maximize")},{disabled:G,handler:()=>C(),icon:fa,key:"reload",text:g("preferences.tabbar.contextMenu.reload")},{handler:()=>X(null,null,function*(){yield b($)}),icon:fs,key:"open-in-new-window",separator:!0,text:g("preferences.tabbar.contextMenu.openInNewWindow")},{disabled:Z,handler:()=>X(null,null,function*(){yield d($)}),icon:as,key:"close-left",text:g("preferences.tabbar.contextMenu.closeLeft")},{disabled:ue,handler:()=>X(null,null,function*(){yield f($)}),icon:os,key:"close-right",separator:!0,text:g("preferences.tabbar.contextMenu.closeRight")},{disabled:re,handler:()=>X(null,null,function*(){yield p($)}),icon:ms,key:"close-other",text:g("preferences.tabbar.contextMenu.closeOther")},{disabled:R,handler:r,icon:ls,key:"close-all",text:g("preferences.tabbar.contextMenu.closeAll")}].filter(Oe=>o.getMenuList.includes(Oe.key))},currentActive:O,currentTabs:W,handleClick:P,handleClose:V}}const yc={class:"flex-center h-full"},xc=L({name:"LayoutTabbar",__name:"tabbar",props:{showIcon:{type:Boolean},theme:{}},setup(l){const a=lt(),t=yt(),{contentIsMaximize:o,toggleMaximize:s}=xl(),{unpinTab:n}=kl(),{createContextMenus:r,currentActive:i,currentTabs:d,handleClick:p,handleClose:f}=gc(),v=y(()=>{const h=t.getTabByKey(i.value);return r(h).map(C=>we(Q({},C),{label:C.text,value:C.key}))});return U.tabbar.persist||t.closeOtherTabs(a),(h,b)=>(u(),k(J,null,[m(e(vc),{active:e(i),class:N(h.theme),"context-menus":e(r),draggable:e(U).tabbar.draggable,"show-icon":h.showIcon,"style-type":e(U).tabbar.styleType,tabs:e(d),wheelable:e(U).tabbar.wheelable,"middle-click-to-close":e(U).tabbar.middleClickToClose,onClose:e(f),onSortTabs:e(t).sortTabs,onUnpin:e(n),"onUpdate:active":e(p)},null,8,["active","class","context-menus","draggable","show-icon","style-type","tabs","wheelable","middle-click-to-close","onClose","onSortTabs","onUnpin","onUpdate:active"]),B("div",yc,[e(U).tabbar.showMore?(u(),x(e(Zu),{key:0,menus:v.value},null,8,["menus"])):I("",!0),e(U).tabbar.showMaximize?(u(),x(e(Qu),{key:1,screen:e(o),onChange:e(s),"onUpdate:screen":e(s)},null,8,["screen","onChange","onUpdate:screen"])):I("",!0)])],64))}}),zc=L({name:"BasicLayout",__name:"layout",emits:["clearPreferencesAndLogout","clickLogo"],setup(l,{emit:a}){const t=a,{isDark:o,isHeaderNav:s,isMixedNav:n,isMobile:r,isSideMixedNav:i,isHeaderMixedNav:d,isHeaderSidebarNav:p,layout:f,preferencesButtonPosition:v,sidebarCollapsed:h,theme:b}=ut(),C=pt(),{refresh:H}=wl(),O=y(()=>o.value||U.theme.semiDarkSidebar?"dark":"light"),M=y(()=>o.value||U.theme.semiDarkHeader?"dark":"light"),W=y(()=>{const{collapsedShowTitle:ie}=U.sidebar,be=[];return ie&&h.value&&!n.value&&be.push("mx-auto"),i.value&&be.push("flex-center"),be.join(" ")}),F=y(()=>U.navigation.styleType==="rounded"),P=y(()=>r.value&&h.value?!0:s.value||n.value||p.value?!1:h.value||i.value||d.value),V=y(()=>!r.value&&(s.value||n.value||d.value)),{handleMenuSelect:K,handleMenuOpen:z,headerActive:$,headerMenus:R,sidebarActive:q,sidebarMenus:Z,mixHeaderMenus:re,sidebarVisible:ue}=Xu(),{extraActiveMenu:G,extraMenus:ae,handleDefaultSelect:xe,handleMenuMouseEnter:Ce,handleMixedMenuSelect:Ve,handleSideMouseLeave:Oe,sidebarExtraVisible:Be}=Yu(re);function j(ie,be=!0){return be?sl(ie,pe=>we(Q({},Ia(pe)),{name:g(pe.name)})):ie.map(pe=>we(Q({},Ia(pe)),{name:g(pe.name)}))}function ne(){ot({sidebar:{hidden:!U.sidebar.hidden}})}function ee(){t("clearPreferencesAndLogout")}function ge(){t("clickLogo")}me(()=>U.app.layout,ie=>X(null,null,function*(){ie==="sidebar-mixed-nav"&&U.sidebar.hidden&&ot({sidebar:{hidden:!1}})})),me(un.global.locale,H,{flush:"post"});const Se=Ke(),Ae=y(()=>Object.keys(Se).filter(ie=>ie.startsWith("header-")));return(ie,be)=>(u(),x(e(Cu),{"sidebar-extra-visible":e(Be),"onUpdate:sidebarExtraVisible":be[0]||(be[0]=pe=>da(Be)?Be.value=pe:null),"content-compact":e(U).app.contentCompact,"content-compact-width":e(U).app.contentCompactWidth,"content-padding":e(U).app.contentPadding,"content-padding-bottom":e(U).app.contentPaddingBottom,"content-padding-left":e(U).app.contentPaddingLeft,"content-padding-right":e(U).app.contentPaddingRight,"content-padding-top":e(U).app.contentPaddingTop,"footer-enable":e(U).footer.enable,"footer-fixed":e(U).footer.fixed,"footer-height":e(U).footer.height,"header-height":e(U).header.height,"header-hidden":e(U).header.hidden,"header-mode":e(U).header.mode,"header-theme":M.value,"header-toggle-sidebar-button":e(U).widget.sidebarToggle,"header-visible":e(U).header.enable,"is-mobile":e(U).app.isMobile,layout:e(f),"sidebar-collapse":e(U).sidebar.collapsed,"sidebar-collapse-show-title":e(U).sidebar.collapsedShowTitle,"sidebar-enable":e(ue),"sidebar-collapsed-button":e(U).sidebar.collapsedButton,"sidebar-fixed-button":e(U).sidebar.fixedButton,"sidebar-expand-on-hover":e(U).sidebar.expandOnHover,"sidebar-extra-collapse":e(U).sidebar.extraCollapse,"sidebar-extra-collapsed-width":e(U).sidebar.extraCollapsedWidth,"sidebar-hidden":e(U).sidebar.hidden,"sidebar-mixed-width":e(U).sidebar.mixedWidth,"sidebar-theme":O.value,"sidebar-width":e(U).sidebar.width,"side-collapse-width":e(U).sidebar.collapseWidth,"tabbar-enable":e(U).tabbar.enable,"tabbar-height":e(U).tabbar.height,"z-index":e(U).app.zIndex,onSideMouseLeave:e(Oe),onToggleSidebar:ne,"onUpdate:sidebarCollapse":be[1]||(be[1]=pe=>e(ot)({sidebar:{collapsed:pe}})),"onUpdate:sidebarEnable":be[2]||(be[2]=pe=>e(ot)({sidebar:{enable:pe}})),"onUpdate:sidebarExpandOnHover":be[3]||(be[3]=pe=>e(ot)({sidebar:{expandOnHover:pe}})),"onUpdate:sidebarExtraCollapse":be[4]||(be[4]=pe=>e(ot)({sidebar:{extraCollapse:pe}}))},vt({logo:c(()=>[e(U).logo.enable?(u(),x(e(Wa),{key:0,fit:e(U).logo.fit,class:N(W.value),collapsed:P.value,src:e(U).logo.source,text:e(U).app.name,theme:V.value?M.value:e(b),onClick:ge},vt({_:2},[ie.$slots["logo-text"]?{name:"text",fn:c(()=>[E(ie.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","class","collapsed","src","text","theme"])):I("",!0)]),header:c(()=>[m(e(Iu),{theme:e(b),onClearPreferencesAndLogout:ee},vt({"user-dropdown":c(()=>[E(ie.$slots,"user-dropdown")]),notification:c(()=>[E(ie.$slots,"notification")]),_:2},[!V.value&&e(U).breadcrumb.enable?{name:"breadcrumb",fn:c(()=>[m(e(Pr),{"hide-when-only-one":e(U).breadcrumb.hideOnlyOne,"show-home":e(U).breadcrumb.showHome,"show-icon":e(U).breadcrumb.showIcon,type:e(U).breadcrumb.styleType},null,8,["hide-when-only-one","show-home","show-icon","type"])]),key:"0"}:void 0,V.value?{name:"menu",fn:c(()=>[m(e(Ga),{"default-active":e($),menus:j(e(R)),rounded:F.value,theme:M.value,class:"w-full",mode:"horizontal",onSelect:e(K)},null,8,["default-active","menus","rounded","theme","onSelect"])]),key:"1"}:void 0,de(Ae.value,pe=>({name:pe,fn:c(()=>[E(ie.$slots,pe)])}))]),1032,["theme"])]),menu:c(()=>[m(e(Ga),{accordion:e(U).navigation.accordion,collapse:e(U).sidebar.collapsed,"collapse-show-title":e(U).sidebar.collapsedShowTitle,"default-active":e(q),menus:j(e(Z)),rounded:F.value,theme:O.value,mode:"vertical",onOpen:e(z),onSelect:e(K)},null,8,["accordion","collapse","collapse-show-title","default-active","menus","rounded","theme","onOpen","onSelect"])]),"mixed-menu":c(()=>[m(e(qu),{"active-path":e(G),menus:j(e(re),!1),rounded:F.value,theme:O.value,onDefaultSelect:e(xe),onEnter:e(Ce),onSelect:e(Ve)},null,8,["active-path","menus","rounded","theme","onDefaultSelect","onEnter","onSelect"])]),"side-extra":c(()=>[m(e(Gu),{accordion:e(U).navigation.accordion,collapse:e(U).sidebar.extraCollapse,menus:j(e(ae)),rounded:F.value,theme:O.value},null,8,["accordion","collapse","menus","rounded","theme"])]),"side-extra-title":c(()=>[e(U).logo.enable?(u(),x(e(Wa),{key:0,fit:e(U).logo.fit,text:e(U).app.name,theme:e(b)},vt({_:2},[ie.$slots["logo-text"]?{name:"text",fn:c(()=>[E(ie.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","text","theme"])):I("",!0)]),tabbar:c(()=>[e(U).tabbar.enable?(u(),x(e(xc),{key:0,"show-icon":e(U).tabbar.showIcon,theme:e(b)},null,8,["show-icon","theme"])):I("",!0)]),content:c(()=>[m(e(Vu))]),extra:c(()=>[E(ie.$slots,"extra"),e(U).app.enableCheckUpdates?(u(),x(e(Ar),{key:0,"check-updates-interval":e(U).app.checkUpdatesInterval},null,8,["check-updates-interval"])):I("",!0),e(U).widget.lockScreen?(u(),x(dt,{key:1,name:"slide-up"},{default:c(()=>[e(C).isLockScreen?E(ie.$slots,"lock-screen",{key:0}):I("",!0)]),_:3})):I("",!0),e(v).fixed?(u(),x(e(Sl),{key:2,class:"z-100 fixed bottom-20 right-0",onClearPreferencesAndLogout:ee})):I("",!0),m(e(er))]),_:2},[e(U).transition.loading?{name:"content-overlay",fn:c(()=>[m(e(Su))]),key:"0"}:void 0,e(U).footer.enable?{name:"footer",fn:c(()=>[m(e(Eu),null,{default:c(()=>[e(U).copyright.enable?(u(),x(e(jn),Fe(ve({key:0},e(U).copyright)),null,16)):I("",!0)]),_:1})]),key:"1"}:void 0]),1032,["sidebar-extra-visible","content-compact","content-compact-width","content-padding","content-padding-bottom","content-padding-left","content-padding-right","content-padding-top","footer-enable","footer-fixed","footer-height","header-height","header-hidden","header-mode","header-theme","header-toggle-sidebar-button","header-visible","is-mobile","layout","sidebar-collapse","sidebar-collapse-show-title","sidebar-enable","sidebar-collapsed-button","sidebar-fixed-button","sidebar-expand-on-hover","sidebar-extra-collapse","sidebar-extra-collapsed-width","sidebar-hidden","sidebar-mixed-width","sidebar-theme","sidebar-width","side-collapse-width","tabbar-enable","tabbar-height","z-index","onSideMouseLeave"]))}});export{Lc as N,zc as _,$u as a,Pr as b,Ar as c,li as d,ii as e,Vc as f,uu as g,Sl as h,Ec as i,Vt as u};
