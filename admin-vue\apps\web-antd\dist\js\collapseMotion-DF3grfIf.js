import{n as r}from"../jse/index-index-BAMHRxBA.js";function a(s,e){return s.classList?s.classList.contains(e):` ${s.className} `.indexOf(` ${e} `)>-1}function l(s,e){s.classList?s.classList.add(e):a(s,e)||(s.className=`${s.className} ${e}`)}function i(s,e){if(s.classList)s.classList.remove(e);else if(a(s,e)){const t=s.className;s.className=` ${t} `.replace(` ${e} `," ")}}const n=function(){let s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"ant-motion-collapse",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return{name:s,appear:e,css:!0,onBeforeEnter:t=>{t.style.height="0px",t.style.opacity="0",l(t,s)},onEnter:t=>{r(()=>{t.style.height=`${t.scrollHeight}px`,t.style.opacity="1"})},onAfterEnter:t=>{t&&(i(t,s),t.style.height=null,t.style.opacity=null)},onBeforeLeave:t=>{l(t,s),t.style.height=`${t.offsetHeight}px`,t.style.opacity=null},onLeave:t=>{setTimeout(()=>{t.style.height="0px",t.style.opacity="0"})},onAfterLeave:t=>{t&&(i(t,s),t.style&&(t.style.height=null,t.style.opacity=null))}}};export{n as c};
