import{d as i,q as p,g as l,D as c,l as h,n,u as a,k as _,S as y,T as z,U as g,e as u,f,m,E as d,h as b,G as x,t as C}from"../jse/index-index-SSqEGcIT.js";import{X as k,Y as w,Z as B,a0 as $}from"./bootstrap-CYivmKoJ.js";const S=k("inline-flex items-center justify-center font-normal text-foreground select-none shrink-0 bg-secondary overflow-hidden",{variants:{shape:{circle:"rounded-full",square:"rounded-md"},size:{base:"h-16 w-16 text-2xl",lg:"h-32 w-32 text-5xl",sm:"h-10 w-10 text-xs"}}}),P=i({__name:"Avatar",props:{class:{},shape:{default:"circle"},size:{default:"sm"}},setup(t){const e=t;return(s,o)=>(l(),p(a(w),{class:n(a(_)(a(S)({size:s.size,shape:s.shape}),e.class))},{default:c(()=>[h(s.$slots,"default")]),_:3},8,["class"]))}}),A=i({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{}},setup(t){const e=t;return(s,o)=>(l(),p(a(B),y(z(e)),{default:c(()=>[h(s.$slots,"default")]),_:3},16))}}),V=i({__name:"AvatarImage",props:{src:{},referrerPolicy:{},asChild:{type:Boolean},as:{}},setup(t){const e=t;return(s,o)=>(l(),p(a($),g(e,{class:"h-full w-full object-cover"}),null,16))}}),D=i({inheritAttrs:!1,__name:"avatar",props:{alt:{default:"avatar"},class:{},dot:{type:Boolean,default:!1},dotClass:{default:"bg-green-500"},fit:{default:"cover"},size:{},delayMs:{},asChild:{type:Boolean},as:{default:"button"},src:{},referrerPolicy:{}},setup(t){const e=t,s=u(()=>{const{fit:r}=e;return r?{objectFit:r}:{}}),o=u(()=>e.alt.slice(-2).toUpperCase()),v=u(()=>e.size!==void 0&&e.size>0?{height:`${e.size}px`,width:`${e.size}px`}:{});return(r,j)=>(l(),f("div",{class:n([e.class,"relative flex flex-shrink-0 items-center"]),style:m(v.value)},[d(a(P),{class:n([e.class,"size-full"])},{default:c(()=>[d(a(V),{alt:r.alt,src:r.src,style:m(s.value)},null,8,["alt","src","style"]),d(a(A),null,{default:c(()=>[x(C(o.value),1)]),_:1})]),_:1},8,["class"]),r.dot?(l(),f("span",{key:0,class:n([r.dotClass,"border-background absolute bottom-0 right-0 size-3 rounded-full border-2"])},null,2)):b("",!0)],6))}});export{D as _};
