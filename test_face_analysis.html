<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面诊接口测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { color: green; }
        .error { color: red; }
        input[type="text"] { width: 100%; padding: 8px; margin: 5px 0; }
        textarea { width: 100%; height: 200px; padding: 8px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>面诊接口测试</h1>
        
        <div class="section">
            <h2>1. 获取面诊配置</h2>
            <button class="btn" onclick="getConfig()">获取配置</button>
            <div id="configResult" class="result"></div>
        </div>
        
        <div class="section">
            <h2>2. 面诊分析</h2>
            <label>面部图片URL:</label>
            <input type="text" id="imageUrl" value="https://kuaifengimg.azheteng.cn/upload/62/20250718/1eec09874b35d946a110dd1d8505c6dd.png" placeholder="请输入面部图片URL">
            
            <label>
                <input type="checkbox" id="useFree"> 使用免费次数
            </label>
            
            <br><br>
            <button class="btn" onclick="analyzeImage()">开始面诊分析</button>
            <div id="analysisResult" class="result"></div>
        </div>
        
        <div class="section">
            <h2>3. 测试结果</h2>
            <textarea id="testLog" readonly placeholder="测试日志将显示在这里..."></textarea>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost/shangchengquan/shangcheng';
        
        function log(message) {
            const logArea = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logArea.value += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function getConfig() {
            log('开始获取面诊配置...');
            
            fetch(`${baseUrl}/ApiFaceAnalysis/getConfig`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    aid: 62,
                    session_id: 'test_session_' + Date.now()
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                
                if (data.code === 1) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ 配置获取成功</div>
                        <p><strong>价格:</strong> ${data.data.price} 元</p>
                        <p><strong>免费次数:</strong> ${data.data.free_times}</p>
                        <p><strong>今日已用:</strong> ${data.data.today_used}</p>
                        <p><strong>可用免费:</strong> ${data.data.can_use_free ? '是' : '否'}</p>
                        <p><strong>接口类型:</strong> ${data.data.use_own_aliyun ? '用户自有阿里云' : '平台公共接口'}</p>
                        <p><strong>平台剩余次数:</strong> ${data.data.platform_calls_remaining}</p>
                    `;
                    log('配置获取成功: ' + JSON.stringify(data.data));
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ ${data.msg}</div>`;
                    log('配置获取失败: ' + data.msg);
                }
            })
            .catch(error => {
                const resultDiv = document.getElementById('configResult');
                resultDiv.innerHTML = `<div class="error">✗ 请求失败: ${error.message}</div>`;
                log('配置获取异常: ' + error.message);
            });
        }
        
        function analyzeImage() {
            const imageUrl = document.getElementById('imageUrl').value;
            const useFree = document.getElementById('useFree').checked;
            
            if (!imageUrl) {
                alert('请输入面部图片URL');
                return;
            }
            
            log('开始面诊分析...');
            log('图片URL: ' + imageUrl);
            log('使用免费: ' + (useFree ? '是' : '否'));
            
            fetch(`${baseUrl}/ApiFaceAnalysis/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    aid: 62,
                    session_id: 'test_session_' + Date.now(),
                    image_url: imageUrl,
                    use_free: useFree ? 1 : 0
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('analysisResult');
                
                if (data.code === 1) {
                    const analysisData = data.data.analysis_result || {};
                    
                    resultDiv.innerHTML = `
                        <div class="success">✓ 面诊分析成功</div>
                        <p><strong>订单号:</strong> ${data.data.order_no}</p>
                        <p><strong>记录ID:</strong> ${data.data.record_id}</p>
                        <p><strong>体质类型:</strong> ${data.data.constitution_type || '未知'}</p>
                        <p><strong>体质评分:</strong> ${data.data.constitution_score || 0}</p>
                        <p><strong>接口类型:</strong> ${data.data.api_type}</p>
                        <p><strong>诊疗类型:</strong> ${data.data.diagnosis_type_name || '面诊'}</p>
                        <details>
                            <summary>查看详细分析结果</summary>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        </details>
                    `;
                    log('面诊分析成功: 订单号=' + data.data.order_no + ', 体质=' + data.data.constitution_type);
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ ${data.msg}</div>`;
                    log('面诊分析失败: ' + data.msg);
                }
            })
            .catch(error => {
                const resultDiv = document.getElementById('analysisResult');
                resultDiv.innerHTML = `<div class="error">✗ 请求失败: ${error.message}</div>`;
                log('面诊分析异常: ' + error.message);
            });
        }
        
        // 页面加载时自动获取配置
        window.onload = function() {
            log('页面加载完成，开始测试面诊接口...');
            getConfig();
        };
    </script>
</body>
</html>
