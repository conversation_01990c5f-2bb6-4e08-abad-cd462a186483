var m=Object.defineProperty,v=Object.defineProperties;var y=Object.getOwnPropertyDescriptors;var i=Object.getOwnPropertySymbols;var S=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable;var a=(r,t,e)=>t in r?m(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,n=(r,t)=>{for(var e in t||(t={}))S.call(t,e)&&a(r,e,t[e]);if(i)for(var e of i(t))_.call(t,e)&&a(r,e,t[e]);return r},l=(r,t)=>v(r,y(t));import{k as h,l as b,m as g,n as P,w as k}from"./bootstrap-CYivmKoJ.js";import{d as u,e as z,f as d,g as c,h as C,n as p,u as o,l as D,m as E}from"../jse/index-index-SSqEGcIT.js";const w=h({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:b(String),default:"solid"}}),B=u({name:"ElDivider"}),N=u(l(n({},B),{props:w,setup(r){const t=r,e=P("divider"),f=z(()=>e.cssVar({"border-style":t.borderStyle}));return(s,$)=>(c(),d("div",{class:p([o(e).b(),o(e).m(s.direction)]),style:E(o(f)),role:"separator"},[s.$slots.default&&s.direction!=="vertical"?(c(),d("div",{key:0,class:p([o(e).e("text"),o(e).is(s.contentPosition)])},[D(s.$slots,"default")],2)):C("v-if",!0)],6))}}));var V=g(N,[["__file","divider.vue"]]);const q=k(V);export{q as ElDivider,q as default,w as dividerProps};
