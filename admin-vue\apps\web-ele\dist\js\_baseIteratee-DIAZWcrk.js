import{S as E,b as A,k as O}from"./isEqual-racMrmQ-.js";import{af as P,aw as c,ax as R,ay as m,az as M,aA as _}from"./bootstrap-CYivmKoJ.js";import{h as g,i as C}from"./index-DIXeP0hR.js";var I=1,L=2;function d(n,r,e,t){var i=e.length,u=i;if(n==null)return!u;for(n=Object(n);i--;){var f=e[i];if(f[2]?f[1]!==n[f[0]]:!(f[0]in n))return!1}for(;++i<u;){f=e[i];var s=f[0],a=n[s],p=f[1];if(f[2]){if(a===void 0&&!(s in n))return!1}else{var y=new E,o;if(!(o===void 0?A(p,a,I|L,t,y):o))return!1}}return!0}function h(n){return n===n&&!P(n)}function w(n){for(var r=O(n),e=r.length;e--;){var t=r[e],i=n[t];r[e]=[t,i,h(i)]}return r}function l(n,r){return function(e){return e==null?!1:e[n]===r&&(r!==void 0||n in Object(e))}}function D(n){var r=w(n);return r.length==1&&r[0][2]?l(r[0][0],r[0][1]):function(e){return e===n||d(e,n,r)}}var G=1,b=2;function F(n,r){return c(n)&&h(r)?l(R(n),r):function(e){var t=m(e,n);return t===void 0&&t===r?g(e,n):A(r,t,G|b)}}function S(n){return function(r){return r==null?void 0:r[n]}}function x(n){return function(r){return M(r,n)}}function K(n){return c(n)?S(R(n)):x(n)}function $(n){return typeof n=="function"?n:n==null?C:typeof n=="object"?_(n)?F(n[0],n[1]):D(n):K(n)}export{$ as b};
