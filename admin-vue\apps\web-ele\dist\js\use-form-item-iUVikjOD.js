import{u as I,d as f}from"./bootstrap-CYivmKoJ.js";import{v as i,x as p,u as m,r as c,e as h,o as j,w as y,y as w,z as E}from"../jse/index-index-SSqEGcIT.js";import{f as F,a as K}from"./use-form-common-props-DZjBwEkr.js";const l={prefix:Math.floor(Math.random()*1e4),current:0},N=Symbol("elIdInjection"),R=()=>p()?i(N,l):l,_=u=>{const e=R(),a=I();return f(()=>m(u)||`${a.value}-id-${e.prefix}-${e.current++}`)},U=()=>{const u=i(F,void 0),e=i(K,void 0);return{form:u,formItem:e}},z=(u,{formItemContext:e,disableIdGeneration:a,disableIdManagement:s})=>{a||(a=c(!1)),s||(s=c(!1));const n=c();let r;const v=h(()=>{var o;return!!(!(u.label||u.ariaLabel)&&e&&e.inputIds&&((o=e.inputIds)==null?void 0:o.length)<=1)});return j(()=>{r=y([w(u,"id"),a],([o,d])=>{const t=o!=null?o:d?void 0:_().value;t!==n.value&&(e!=null&&e.removeInputId&&(n.value&&e.removeInputId(n.value),!(s!=null&&s.value)&&!d&&t&&e.addInputId(t)),n.value=t)},{immediate:!0})}),E(()=>{r&&r(),e!=null&&e.removeInputId&&n.value&&e.removeInputId(n.value)}),{isLabeledByFormItem:v,inputId:n}};export{z as a,_ as b,R as c,U as u};
